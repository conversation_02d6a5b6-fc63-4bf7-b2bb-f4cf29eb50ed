{"editor.formatOnType": true, "editor.formatOnSave": true, "editor.tabSize": 2, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "eslint.enable": true, "typescript.tsdk": "node_modules/typescript/lib", "[prisma]": {"editor.defaultFormatter": "Prisma.prisma"}, "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[typescriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[javascript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[javascriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}}