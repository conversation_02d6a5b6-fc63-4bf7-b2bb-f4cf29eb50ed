// import AntdDayjsWebpackPlugin from 'antd-dayjs-webpack-plugin'
import { defineConfig } from '@umijs/max'

import routes from './routes'
import theme from './theme'

const isDev = process.env.NODE_ENV !== 'production'

console.log(process.env?.npm_package_version)

// const mainHost = 'http://*************:8000'

const mainHost = 'http://www.xfrp.tk:18000'

const mainProxy = {
  target: mainHost,
  changeOrigin: true,
  headers: {
    Referer: mainHost
    // 可以添加其他自定义header
  }
}
// const mutProxy = { target: 'http://127.0.0.1:8001', changeOrigin: true }
const mutProxy = { target: 'http://*************:31363', changeOrigin: true }
// const datasourceProxy = { target: 'http://127.0.0.1:8011' }
const datasourceProxy = { target: 'http://*************:30121', changeOrigin: true }

// const extraBabelPlugins: any[] = [
//   {
//     libraryName: 'ahooks',
//     libraryDirectory: 'es',
//     camel2UnderlineComponentName: false,
//     camel2DashComponentName: false
//   }
// ].map(i => ['import', i, i.libraryName])

const extraBabelPlugins: any[] = [
  [
    'import',
    {
      'libraryName': '@nutui/nutui-react',
      'libraryDirectory': 'dist/esm',
      'style': 'css',
      'camel2DashComponentName': false
    },
    'nutui-react'
  ]
]

export default defineConfig({
  base: '/console/abi',
  // publicPath: isDev ? '/' : '/static-apps/abi/',
  publicPath: '/static-apps/abi/',
  outputPath: '../server/public/abi',
  runtimePublicPath: {},

  antd: {
    configProvider: {},
    dark: false,
    compact: false,
    // babel-plugin-import
    import: true,
    // less or css, default less
    style: 'less'
  },

  define: {
    'process.env.MAIN_HOST': process.env?.NODE_ENV !== 'production' ? mainHost : '',
    'process.env.THEME': process.env?.THEME,
    'APP_VERSION': process.env?.npm_package_version,
    'process.env.THEME_COLOR': theme['@primary-color'] || theme['primary-color']
  },

  qiankun: {
    slave: {
      name: 'sugo-abi',
      devSourceMap: false,
      // 主应用开发模式 publicPath 也生效
      shouldNotModifyRuntimePublicPath: true,
      autoSetLoading: true
    }
  },

  // ....
  hash: true,
  mfsu: true, // 主题不生效或者编译报错，把这个改为 false/true 触发重新编译，默认是 {}
  routes,
  fastRefresh: true,
  locale: {
    default: 'zh-CN'
  },

  extraBabelPlugins,
  theme,
  ignoreMomentLocale: true,
  devtool: isDev ? 'eval-source-map' : false,
  // // 先开启，方便调试
  // devtool: isDev ? 'eval-source-map' : false,
  // TODO 生产最低支持到 chrome 49，但自定义图表的异步引入需要 chrome 63，打包时需要实现编译
  targets: isDev ? { chrome: 131 } : { chrome: 49, firefox: 64, safari: 10, edge: 13, ios: 10 },
  extraBabelIncludes: ['@monaco-editor/react', 'monaco-editor'],
  // 解决 targets 报错 https://github.com/umijs/umi/issues/8744#issuecomment-1197720704
  jsMinifier: 'terser',
  dva: false,
  mountElementId: 'abi-app', // 指定根节点 id
  proxy: {
    '/_bc': mainProxy,
    '/app': mainProxy,
    '/css': mainProxy,
    '/js': mainProxy,
    '/common': mainProxy,
    '/logout': mainProxy,
    '/common/captcha': mainProxy,
    '/custom-charts': mainProxy,
    '/api/python-algorithms': mainProxy,
    '/api/docs': mainProxy,
    '/ai/api/cloud': mainProxy,

    // 智能助手
    '/chat/share': mainProxy,

    // 数据源代理
    '/console/datasource-manager': datasourceProxy,
    '/static-apps/datasource-manager': datasourceProxy,

    // 指标代理
    '/static-apps/mut': mutProxy,
    '/console/mut/': mutProxy,

    // 不加上 /api 不能调试 /abi/preview/xxx
    '/abi/api': mainProxy
  },
  chainWebpack: (config => {
    // markdown 以源码方式引入 https://webpack.docschina.org/guides/asset-modules/
    config.module.rule('md').test(/\.md$/).type('asset/source')
    return config
  }) as () => any,
  tailwindcss: {},
  // 使用 useModel 所需配置
  model: {}
})
