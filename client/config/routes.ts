type Router = {
  path?: string
  component?: string
  title?: string
  exact?: boolean
  routes?: any[]
  wrappers?: string[]
}

// const isDev = process.env.NODE_ENV !== 'production'
const isTieke = process.env.THEME === 'tieke'

const defaultWrappers = [
  // '@/pages/_wrappers/use-animate', // 不能开启
  '@/pages/_wrappers/use-user'
]

const routers: Router[] = [
  { path: '/', component: '@/pages/index' },
  {
    path: '/project',
    component: '@/pages/project',
    title: '项目门户',
    exact: true,
    wrappers: defaultWrappers
  },
  {
    path: '/mini-app-manage',
    component: '@/pages/mini-app',
    title: '数据应用管理中心',
    exact: true,
    wrappers: defaultWrappers
  },
  {
    path: '/report',
    component: '@/pages/project',
    title: '数据报告',
    exact: true,
    wrappers: defaultWrappers
  },
  {
    path: '/dataset',
    component: '@/pages/dataset',
    title: isTieke ? '数据集' : '数据视图',
    exact: true,
    wrappers: defaultWrappers
  },
  {
    path: '/project/:id',
    component: '@/pages/screen',
    title: '应用设计器 - 项目',
    exact: true,
    wrappers: [...defaultWrappers, '@/pages/_wrappers/use-master-jwt']
  },
  {
    path: '/preview/:sign',
    component: '@/pages/preview',
    title: '应用设计预览',
    exact: true,
    wrappers: [...defaultWrappers, '@/pages/_wrappers/use-master-jwt']
  },
  {
    path: '/abi/preview/:sign',
    component: '@/pages/preview',
    title: '应用设计预览',
    exact: true,
    wrappers: [...defaultWrappers, '@/pages/_wrappers/use-master-jwt']
  },

  {
    path: '/charts/gallery',
    component: '@/pages/charts-gallery',
    title: '自定义图表',
    exact: true,
    wrappers: defaultWrappers
  },
  {
    path: '/charts/gallery/:key',
    component: '@/pages/charts-gallery/containers/editor',
    title: '自定义图表-编辑器',
    exact: true,
    wrappers: defaultWrappers
  },
  {
    path: '/pc-app-manage',
    component: '@/pages/pc-app',
    title: '桌面应用商店',
    exact: true,
    wrappers: [...defaultWrappers, '@/pages/_wrappers/use-master-jwt']
  },

  {
    path: '/theme-analysis',
    component: '@/pages/theme-analysis/list',
    title: '我的分析',
    exact: true,
    wrappers: defaultWrappers
  },

  {
    path: '/theme-analysis/editor/:id',
    component: '@/pages/theme-analysis/editor',
    title: '主题分析编辑',
    exact: true,
    wrappers: defaultWrappers
  },

  {
    path: '/theme-analysis/preview/:id',
    component: '@/pages/theme-analysis/editor',
    title: '主题分析预览',
    exact: true,
    wrappers: defaultWrappers
    // wrappers: [...defaultWrappers, '@/pages/_wrappers/use-master-jwt']
  },

  {
    path: '/theme-analysis/preview-all', // groupId&id
    component: '@/pages/theme-analysis/editor/preview-all',
    title: '主题分析预览',
    exact: true,
    wrappers: defaultWrappers
  },

  {
    path: '/causation-analysis',
    component: '@/pages/causation-analysis/list',
    title: '因果分析',
    exact: true,
    wrappers: defaultWrappers
  },

  {
    path: '/causation-analysis/editor/:id',
    component: '@/pages/causation-analysis/editor',
    title: '因果分析编辑',
    exact: true,
    wrappers: defaultWrappers
  },

  {
    path: '/table-model',
    component: '@/pages/table-model/list',
    title: '数据模型',
    exact: true,
    wrappers: defaultWrappers
  },

  // AI 管理平台 / 库表数据
  {
    path: '/ai-table-model',
    component: '@/pages/table-model/list',
    title: '库表数据',
    exact: true,
    wrappers: defaultWrappers
  },
  {
    path: '/ai-table-model/:id',
    component: '@/pages/table-model/editor/iframe-container',
    title: 'AI 数据模型编辑',
    exact: true,
    wrappers: defaultWrappers
  },
  {
    path: '/ai-table-model/editor/:id',
    component: '@/pages/table-model/editor',
    title: 'AI 数据模型编辑',
    exact: true,
    wrappers: defaultWrappers
  },

  {
    path: '/table-model/:id',
    component: '@/pages/table-model/editor/iframe-container',
    title: '数据模型编辑',
    exact: true,
    wrappers: defaultWrappers
  },


  {
    path: '/table-model/editor/:id',
    component: '@/pages/table-model/editor',
    title: '数据模型编辑',
    exact: true,
    wrappers: defaultWrappers
  },

  {
    path: '/web-log',
    component: '@/pages/web-log',
    title: '监控错误日志',
    exact: true,
    wrappers: defaultWrappers
  },

  {
    path: '/framework/dashboards',
    component: '@/pages/framework/apps/dashboards',
    exact: true,
    wrappers: defaultWrappers
  },
  {
    path: '/framework/workspace',
    component: '@/pages/framework/apps/workspace',
    exact: true,
    wrappers: defaultWrappers
  },
  {
    path: '/framework/templates',
    component: '@/pages/framework/apps/templates',
    exact: true,
    wrappers: defaultWrappers
  },

  { path: '*', component: '@/pages/404', title: '404' }
]

routers.unshift(
  {
    path: '/admin-manage',
    component: '@/pages/admins',
    wrappers: ['@/pages/_wrappers/admin-sign'],
    title: '后台管理',
    exact: true
  }
)


export default routers
