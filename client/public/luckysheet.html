<!DOCTYPE html>
<html lang='zh-CN'>

<head>
  <meta charset='utf-8' />
  <meta name='viewport' content='width=device-width, initial-scale=1' />
  <meta name='theme-color' content='#000000' />
  <meta name='description' content='Luckysheet micro App' />

  <title>Luckysheet micro App</title>
  <link rel='stylesheet' href='/custom-charts/_deps/cdn.jsdelivr.net/npm/luckysheet/dist/plugins/css/pluginsCss.css' />
  <link rel='stylesheet' href='/custom-charts/_deps/cdn.jsdelivr.net/npm/luckysheet/dist/plugins/plugins.css' />
  <link rel='stylesheet' href='/custom-charts/_deps/cdn.jsdelivr.net/npm/luckysheet/dist/css/luckysheet.css' />
  <link rel='stylesheet'
    href='/custom-charts/_deps/cdn.jsdelivr.net/npm/luckysheet/dist/assets/iconfont/iconfont.css' />
  <script>
    // 绕开 amd 的逻辑，确保写入 window 对象
    window.define_bak = window.define
    window.define = undefined
  </script>
  <script src='/custom-charts/_deps/cdn.jsdelivr.net/npm/luckysheet/dist/plugins/js/plugin.js'></script>
  <script src='/custom-charts/_deps/cdn.jsdelivr.net/npm/luckysheet/dist/luckysheet.umd.js'></script>
  <script src="/custom-charts/_deps/cdn.jsdelivr.net/npm/exceljs@4.3.0/dist/exceljs.min.js"></script>
  <script src="/custom-charts/_deps/cdn.jsdelivr.net/npm/file-saver@2.0.5/dist/FileSaver.min.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/antd/4.19.2/antd.min.css" />
  <script>
    window.define = window.define_bak
    window.define_bak = undefined
  </script>

  <style>
    /*html {width: 100%; height: 100%;}*/
    /*body {position: relative; width: 100%; height: 100%; margin: 0;}*/
    #luckysheet {
      margin: 0;
      padding: 0;
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
    }

    #luckysheet-icon-morebtn {
      right: 25px;
    }

    .luckysheet-bottom-controll-row {
      display: none;
    }

    /*.toolbar { position: absolute; top: 25px; right: 25px; z-index: 10; display: none; }
    .toolbar > #export-excel {
      width: 16px;
      height: 16px;
      cursor: pointer;
      background-color: #fff;
      box-shadow: 1px 2px 3px rgba(1, 1, 1, 0.12);
      border-radius: 3px;
      border: 1px solid #bbb;
      font-size: 12px;
      padding: 0 1px;
    }
    #luckysheet:hover > .toolbar { display: block; }*/
  </style>
</head>

<body>
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id='luckysheet'>
    <!--
  <div class="toolbar">
    <span id="export-excel" title="导出 excel">导出</span>
  </div>
-->
  </div>

  <script>
    let luckysheetMounted = false
    if (!window.microApp) {
      // 兼容 micro-zoe, 无界
      window.microApp = window.microApp || window.$wujie ? {
        listeners: [],
        dispatch: val => {
          window.$wujie.props.onChange(val)
        },
        getData: () => window.$wujie.props.getData(),
        addDataListener: function (cb) {
          this.listeners.push(cb)
          return window.$wujie.bus.$on(`${window.__WUJIE.id}:dataUpdated`, cb)
        },
        clearDataListener: function () {
          this.listeners.forEach(cb => {
            window.$wujie.bus.$off(`${window.__WUJIE.id}:dataUpdated`, cb)
          })
          this.listeners = []
        },
      } : {}
    }
    const microAppName = window.__MICRO_APP_NAME__ || (window.__WUJIE && window.__WUJIE.id) || ''
    // console.log('w:', window)

    const debug = (...args) => {
      const win = window.parent || window
      if (!win.isDev) return
      console.log(...args)
    }

    const tryGetLastUpdatedAt = json => {
      try {
        if (json) {
          return _.max(_.map(json.data, d => d.config.lastUpdatedAt || 0))
        }
        return window.luckysheet.getConfig().lastUpdatedAt
      } catch (e) {
        return 0
      }
    }

    // 解除挟持锁定
    const cloneDeepSafe = obj => {
      return _.cloneDeepWith(obj, o => {
        if (_.isArrayLikeObject(o)) {
          return _.map(o, cloneDeepSafe)
        }
        return _.isObject(o) ? _.mapValues(o, cloneDeepSafe) : o
      })
    }

    const updateData = (targetSheets, isPreview) => {
      const sheets = window.luckysheet.getLuckysheetfile()
      _.forEach(targetSheets, (s, i) => {
        if (!sheets[i]) {
          sheets[i] = {}
        }
        // 不能覆盖原选区
        Object.assign(
          sheets[i],
          _.cloneDeep(_.omit(s, 'celldata', 'data', 'luckysheet_selection_range', 'luckysheet_select_save'))
        )
        sheets[i].celldata = null
        let datum = sheets[i].data
        if (_.isEmpty(datum)) {
          const emptyRow = Array.from({ length: 120 }, () => null)
          datum = Array.from({ length: 84 }, () => [...emptyRow])
          sheets[i].data = datum
        }

        // 初始化数据
        const cellDataDict = _.keyBy(s.celldata, c => `${c.r}_${c.c}`)
        for (let r = 0; r < datum.length; r++) {
          for (let c = 0; c < datum[r].length; c++) {
            const cellData = cellDataDict[`${r}_${c}`]
            const cellVal = cellData && cellData.v
            const cv = cellVal && Object.isFrozen(cellVal) ? cloneDeepSafe(cellVal) : cellVal || null
            if (isPreview) {
              // 需要更新显示值 m
              window.luckysheet.setcellvalue(r, c, datum, cv)
            } else {
              // 不能调用 setcellvalue，否则会导致数值格式被覆盖
              datum[r][c] = cv
            }
          }
        }
      })
      // 初始化
      window.luckysheet.refreshFormula(onUpdate)
      const activeSheetIdx = Math.max(_.findIndex(targetSheets, s => +s.status > 0), 0)
      window.luckysheet.setSheetActive(activeSheetIdx)
      window.luckysheet.refresh()
      window.luckysheet.sheetmanage.setSheetParam(false) // 初始化超链接

      const activeSheet = targetSheets[activeSheetIdx]
      // 是否隐藏网格线
      if (!_.isNil(activeSheet.showGridLines) && !activeSheet.showGridLines) {
        window.luckysheet.hideGridLines()
      } else {
        window.luckysheet.showGridLines()
      }
      // 冻结
      if (activeSheet.frozen) {
        const { type, range } = activeSheet.frozen
        if (/row/i.test(type)) {
          window.luckysheet.setHorizontalFrozen(_.startsWith(type, 'range'), { range })
        } else if (/column/i.test(type)) {
          window.luckysheet.setVerticalFrozen(_.startsWith(type, 'range'), { range })
        } else if (/both/i.test(type)) {
          window.luckysheet.setBothFrozen(_.startsWith(type, 'range'), { range })
        } else {
          window.luckysheet.cancelFrozen()
        }
      } else {
        window.luckysheet.cancelFrozen()
      }
    }
    // 拖拽结束
    const cellDragStop = (cell, position, sheet, ctx, event) => {
      const text = event.dataTransfer.getData('text')
      window.luckysheet.setcellvalue(position.r, position.c, luckysheet.flowdata(), text);
      window.luckysheet.jfrefreshgrid();
    }
    const initLuckysheet = (nextJson, onUpdate) => {
      let dataInited = false
      const basicConfig = {
        container: 'luckysheet',
        lang: 'zh',
        plugins: [],
        showinfobar: false,
        showtoolbar: false,
        sheetFormulaBar: false,
        showsheetbar: false,
        showstatisticBar: false,
        data: [
          {
            name: 'Sheet1',
            color: '',
            status: '1',
            order: '0',
            data: [],
            chart: [],
            column: 120,
            config: { lastUpdatedAt: Date.now() },
            index: 0
          }
        ],
        hook: {
          updated: onUpdate,
          workbookCreateAfter: (ev) => {
            // 设置冻结后，手动触发保存（解决 frozen hook 无效）
            const delayedUpdate = () => setTimeout(onUpdate, 500)
            $('#luckysheet-freezen-btn-horizontal').click(delayedUpdate)
            $(document).on('click', '#luckysheet-icon-freezen-menu-menuButton', delayedUpdate)

            if (dataInited) {
              updateData(nextJson.data, nextJson.allowEdit === false)
              return
            }
            dataInited = true
            updateData(nextJson.data, nextJson.allowEdit === false)
            // console.log(`sheet update finish, tabName: ${tabName}, data hash: ${hash(data)}`, data)
            window.luckysheet.setRangeShow({ row: [83, 83], column: [0, 0] }, { show: false })
            if (nextJson && nextJson.hook && nextJson.hook.workbookCreateAfter) {
              nextJson.hook.workbookCreateAfter(ev)
            }
          },
          // frozenCreateAfter: onUpdate,
          // frozenCancelAfter: onUpdate,
          rangeSelect: onUpdate,
          sheetActivate: onUpdate,
          sheetMoveAfter: onUpdate,
          cellDragStop
        }
      }

      try {
        const sData = _.merge(basicConfig, _.omit(nextJson, 'hook', 'data'))
        debug('luckysheet created:', sData)
        window.luckysheet.create(sData)
      } catch (e) {
        console.error(e)
      } finally {
        luckysheetMounted = true
      }
    }

    const onUpdate = _.debounce(() => {
      if (!luckysheetMounted) {
        return
      }
      const cfg = window.luckysheet.getConfig()
      window.luckysheet.setConfig({ ...(cfg || {}), lastUpdatedAt: Date.now() })
      // 去掉不必要的中间数据

      try {
        const output = window.luckysheet.toJson()
        debug('luckysheet updated by modal', output)
        const optimized = {
          ...output,
          hook: undefined,
          data: _.map(output.data, d => {
            const celldata = window.luckysheet.transToCellData(d.data)
            // data 运算公式时需要，运算完公式可以清空
            return {
              ...d,
              // 清除无效的 calcChain
              calcChain: _.filter(d.calcChain, c => d.data[c.r] && d.data[c.r][c.c] && d.data[c.r][c.c].f),
              celldata: celldata,
              data: [],
              visibledatarow: undefined,
              visibledatacolumn: undefined,
            }
          })
        }
        if (window.microApp.dispatch) {
          window.microApp.dispatch(optimized)
        }
      } catch (e) {
        console.error(e)
      }
    }, 1000)

    const onData = nextCfg => {
      debug('luckysheet reveive props:', microAppName, nextCfg)
      if (!nextCfg || !luckysheetMounted) {
        return
      }
      // 优化：如果数据没有变化，不需要重新渲染
      if (tryGetLastUpdatedAt(nextCfg) <= tryGetLastUpdatedAt()) {
        return
      }

      updateData(nextCfg.data, nextCfg.allowEdit === false)
      // console.log(`sheet update finish, tabName: ${tabName}, data hash: ${hash(data)}`, data)
      window.luckysheet.setRangeShow({ row: [83, 83], column: [0, 0] }, { show: false })
    }

    const alertBak = window.alert

    // 👇 将渲染操作放入 mount 函数，子应用初始化时会自动执行
    window.mount = () => {
      const data = window.microApp.getData && window.microApp.getData()
      debug('luckysheet mounted on:', microAppName, data)

      initLuckysheet(data, onUpdate)
      if (window.microApp.addDataListener) {
        window.microApp.addDataListener(onData)
      }

      // 屏蔽 luckysheet alert
      window.alert = (msg) => {
        if (/已合并/.test(msg)) {
          return
        }
        return alertBak(msg)
      }
    }

    // 👇 将卸载操作放入 unmount 函数，就是上面步骤2中的卸载函数
    window.unmount = () => {
      luckysheetMounted = false
      // 清空当前子应用的所有绑定函数(全局数据函数除外)
      window.microApp.clearDataListener()
      debug('luckysheet unmounted on:', microAppName)
      window.luckysheet.destroy()

      // 还原 alert
      window.alert = alertBak
    }

    // 如果不在微前端环境，则直接执行mount渲染
    if (!window.__MICRO_APP_ENVIRONMENT__) {
      window.mount()
    }
  </script>
</body>

</html>
