<svg id="组_2" data-name="组 2" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" full='#fff'>
  <defs>
    <style>
      .cls-1, .cls-2 {
        fill: #fa1010;
      }
      .cls-1 {
        opacity: 0.2;
      }
      .cls-2 {
        opacity: 0.4;
      }
      .cls-3 {
        fill: #ffe2e2;
        stroke: #fa1010;
        stroke-width: 2px;
      }
    </style>
  </defs>
  <circle id="椭圆_3_拷贝" data-name="椭圆 3 拷贝" class="cls-1" cx="10" cy="10" r="10"/>
  <circle id="椭圆_3_拷贝_2" data-name="椭圆 3 拷贝 2" class="cls-2" cx="10" cy="10" r="7"/>
  <circle id="椭圆_3" data-name="椭圆 3" class="cls-3" cx="10" cy="10" r="4"/>
</svg>
