
.abi-app-loading {
  margin-right: 12px;
  background: linear-gradient(to right bottom, #4567ff 10%, #7451ff 90%);
  border-radius: 5px;
  padding: 3px 4px;
  transition: background 0.5s ease-in-out;
  width: 34px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;

  .anticon {
    font-size: 26px;
  }

  path {
    transition: all 0.5s ease-in-out;
  }
  path:nth-child(1) {
    animation: abiAppLoading1 2s ease-in-out forwards infinite;
    fill: rgba(#fff, 0.65);  // 鱼
  }
  path:nth-child(3) {
    animation: abiAppLoading2 2s ease-in-out forwards infinite;
    fill: rgba(#fff, 0.85); // 手
  }

}

@keyframes abiAppLoading1 {
  0% { fill: rgba(#fff, 0.65); }
  50% { fill: rgba(@primary-color, 0.8); }
  100% { fill: rgba(#fff, 0.65); }
}

@keyframes abiAppLoading2 {
  0% { fill: rgba(#fff, 0.85); }
  50% { fill: @primary-color; }
  100% { fill: rgba(#fff, 0.85); }
}
