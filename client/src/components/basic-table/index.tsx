import './index.less'

import { useSize } from 'ahooks'
import { Table } from 'antd'
import type { ColumnGroupType, ColumnType } from 'antd/lib/table'
import classNames from 'classnames'
import type { TableProps } from 'rc-table/lib/Table'
import type { CSSProperties } from 'react'
import React, { memo, Ref, useRef } from 'react'

export type ColumnsType = (ColumnGroupType<any> | ColumnType<any>)

export interface BasicTableProps {
  columns: ColumnsType[]
  dataSource: any[]
  current?: number
  pageSize?: number
  loading?: boolean
  scroll?: TableProps['scroll'] & {
    scrollToFirstRowOnChange?: boolean
  }
  className?: string
  rowKey?: string | ((row: any) => string)
  onChange?: (page: number, pageSize?: number) => any
  onShowSizeChange?: (page: number, pageSize?: number) => any
  total?: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
}

/**
 * 基础表格
 */
function BasicTable(props: BasicTableProps) {
  const {
    columns = [],
    dataSource = [],
    current = 1,
    pageSize = 10,
    onChange,
    total = 0,
    loading = false,
    rowKey = 'id',
    showSizeChanger,
    className,
    showQuickJumper,
    onShowSizeChange,
    scroll
  } = props

  return (
    <Table
      loading={loading}
      columns={columns}
      rowKey={rowKey}
      className={classNames('my-basic-table', className)}
      dataSource={dataSource}
      size='middle'
      scroll={scroll}
      pagination={{
        size: 'default',
        current,
        onChange,
        onShowSizeChange,
        pageSize,
        total,
        showSizeChanger,
        showQuickJumper,
        showTotal: all => `共 ${all} 条`
      }}
    />
  )
}

export default memo(BasicTable)
