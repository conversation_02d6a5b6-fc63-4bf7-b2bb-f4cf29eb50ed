
.card-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  width: max-content;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 1px 0 4px rgba(@primary-color, 0.16);
  padding: 2px;

  .ant-pagination-item,
  .ant-pagination-next,
  .ant-pagination-prev {
    height: 25px;
    min-width: 25px;
    width: 25px;
    margin-right: 4px;
  }

  .ant-pagination-total-text {
    margin-left: 12px;
    margin-right: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ant-pagination-item,
  .ant-pagination-item-link {
    background-color: transparent;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: tint(@primary-color, 92%);
    }
  }

  .ant-pagination-item-active {
    background-color: tint(@primary-color, 86%);
  }

  .ant-pagination-options {
    margin-left: 1px;
    margin-right: 5px;

    .ant-select-selector {
      height: 28px;
      .ant-select-selection-item {
        line-height: 28px;
      }
    }
  }

}

.card-pagination-fixed {
  left: 50%;
  transform: translateX(-50%);
  position: fixed;
  bottom: 10px;
  z-index: 3;
  margin: auto;
}

.card-pagination-fix-fixed {
  left: calc(50% + 100px);
}
