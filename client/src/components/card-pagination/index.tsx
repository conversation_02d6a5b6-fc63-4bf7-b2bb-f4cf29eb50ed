
import './index.less'

import { fastMemo } from '@sugo/design/functions'
import { Pagination, PaginationProps } from 'antd'
import cn from 'classnames'
import React from 'react'

import { useNewBi } from '@/hooks/use-new-bi'

export interface CardPaginationProps extends PaginationProps {
  title?: string
  isFixed?: boolean
}

/**
 * 卡片用的分页器
 * @returns
 */
function _CardPagination(props: CardPaginationProps) {
  const { isFixed, showSizeChanger = false, ...rest } = props
  const { isNewBi } = useNewBi()

  return (
    <Pagination
      {...rest}
      showSizeChanger={showSizeChanger}
      className={cn('card-pagination', props.className, {
        'card-pagination-fixed': isFixed,
        'card-pagination-fix-fixed': isNewBi && isFixed
      })}
    />
  )
}

export const CardPagination = fastMemo(_CardPagination)
