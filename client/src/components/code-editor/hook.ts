import type { Monaco } from '@monaco-editor/react'
import type { editor } from 'monaco-editor'
import { useEffect, useRef } from 'react'

/**
 * 绑定事件监听相关方法
 * @param onFocus
 * @param onBlur
 * @param onMount
 */
export function useEventListeners(
  onFocus: ((e: any) => any) | undefined,
  onBlur: ((e: any) => any) | undefined,
  onMount: ((editorInst: editor.IStandaloneCodeEditor, monaco: Monaco) => any) | undefined
) {
  const onFocusRef = useRef(onFocus)
  const onBlurRef = useRef(onBlur)

  const handleEditorDidMount = (editorInst: editor.IStandaloneCodeEditor, monaco: Monaco) => {
    // https://stackoverflow.com/a/67070190/1745885
    editorInst.onDidFocusEditorWidget(() => {
      onFocusRef.current?.(editorInst)
    })
    editorInst.onDidBlurEditorWidget(() => {
      onBlurRef.current?.(editorInst)
    })
    onMount?.(editorInst, monaco)
  }

  useEffect(() => {
    onFocusRef.current = onFocus
  }, [onFocus])

  useEffect(() => {
    onBlurRef.current = onBlur
  }, [onBlur])

  return handleEditorDidMount
}
