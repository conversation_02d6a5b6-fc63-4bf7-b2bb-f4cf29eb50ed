.monaco-editor-warpper {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 4px;

  & > .monaco-editor-toolbox {
    position: absolute;
    z-index: 10;
    top: 2px;
    right: 15px;
    display: flex;
    align-items: center;
    .anticon {
      margin-left: 6px;
      padding: 3px;
      cursor: pointer;
      user-select: none;
      &:first-of-type {
        margin: 0;
      }
    }
  }
}

// 修改样式
.monaco-editor-light {
  .scrollbar.vertical .slider {
    border-radius: 2px;
    background-color: var(--primary-color-15) !important;
  }
  .scrollbar.horizontal .slider {
    border-radius: 2px;
    background-color: var(--primary-color-15) !important;
  }

  .current-line,
  .view-overlays .current-line {
    background-color: #fffeeb; /* 淡黄色背景 */
  }
  // 对齐线
  .monaco-editor .lines-content .core-guide-indent {
    border-left: 1px dashed var(--tint-color-80);
    box-shadow: none;
  }
  .monaco-editor .lines-content .core-guide-indent-active {
    border-left: 1px solid var(--tint-color-60);
    box-shadow: none;
    background-color: var(--tint-color-90);
  }
  // 收起的图标大小
  .monaco-editor .cldr.codicon {
    font-size: 16px;
  }

  // code 颜色
  .mtk8 {
    color: #999;
  }
  .mtk1 {
    color: #666;
  }
  .mtk20 {
    color: #3a9;
  }
  .mtk6 {
    color: #39f;
  }
  .mtk7 {
    color: #e67;
  }
  .mtk9 + .mtk1 {
    color: rgba(#78f, 0.8);
  }
  .mtk9 + .mtk1 + .mtk9 + .mtk1 {
    color: rgba(#c90, 0.8);
  }
  .mtk9 + .mtk1 + .mtk9 + .mtk1 + .mtk9 + .mtk1 {
    color: rgba(#f78, 0.8);
  }

  .monaco-editor.showUnused .squiggly-inline-unnecessary {
    opacity: 0.48;
    text-decoration: underline;
    text-decoration-style: wavy; /* 波浪线 */
  }
}

.monaco-editor-dark {
  .monaco-editor,
  .monaco-editor-background {
    background-color: #263238;
  }
  .monaco-editor .margin {
    background-color: darken(#263238, 2%);
  }
  .monaco-editor-toolbox .anticon {
    color: #eee;
  }

  // code 颜色
  .mtk8 {
    color: #f56;
  }
  .mtk1 {
    color: rgba(#999, 1);
  }
  .mtk20 {
    color: #3a9;
  }
  .mtk6 {
    color: #39f;
  }
  .mtk7 {
    color: rgba(#999, 0.65);
  }
  .mtk9 + .mtk1 {
    color: rgba(#78f, 0.8);
  }
  .mtk9 + .mtk1 + .mtk9 + .mtk1 {
    color: rgba(#c90, 0.8);
  }
  .mtk9 + .mtk1 + .mtk9 + .mtk1 + .mtk9 + .mtk1 {
    color: rgba(#f78, 0.8);
  }

  .monaco-editor.showUnused .squiggly-inline-unnecessary {
    opacity: 0.48;
    text-decoration: underline;
    text-decoration-style: wavy; /* 波浪线 */
  }
}
