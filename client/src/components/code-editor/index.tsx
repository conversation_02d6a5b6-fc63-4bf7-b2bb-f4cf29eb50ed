/* eslint-disable no-bitwise */
/* eslint-disable no-restricted-globals */
import './index.less'

import { FullscreenOutlined, NumberOutlined } from '@ant-design/icons'
import type { Monaco } from '@monaco-editor/react'
import { loader } from '@monaco-editor/react'
import { useFullscreen, useKeyPress, useMemoizedFn } from 'ahooks'
import { Tooltip } from 'antd'
import cn from 'classnames'
import type { editor } from 'monaco-editor'
import React, { CSSProperties, lazy, Suspense, useEffect, useRef, useState } from 'react'

import { useEventListeners } from './hook'
import { loadAnyBiTyped, loadJsxSupport, loadLanguageSupport, loadLodashTyped, loadReactTyped } from './load-editor-language'

const CDN_PATH = '/_bc/monaco-editor/min/vs'

// 配置 worker
self.MonacoEnvironment = {
  getWorkerUrl(moduleId, label) {
    // if (label === 'typescript' || label === 'javascript') {
    //   return `${CDN_PATH}/language/typescript/tsWorker.js`
    // }
    // return 'https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs/language/typescript/tsWorker.js'
    return `${CDN_PATH}/base/worker/workerMain.js`
  }
}

loader.config({
  paths: {
    vs: CDN_PATH
  },
  'vs/nls': {
    availableLanguages: {
      '*': 'zh-cn'
    }
  }
})

const defaultOptions: editor.IStandaloneEditorConstructionOptions = {
  useShadowDOM: true,
  automaticLayout: true, // 自动布局
  minimap: { enabled: false },
  tabSize: 2,
  wordBasedSuggestions: 'allDocuments', // 开启基于单词的补全
  quickSuggestions: true,
  renderLineHighlight: 'line',
  suggest: {
    showWords: true,  // 启用单词自动补全
    showSnippets: true,  // 启用代码片段提示
    showFunctions: true,  // 启用函数补全
    showConstants: true,  // 启用常量补全
    showVariables: true,  // 启用变量补全
    showEnums: true,
    showFields: true,
    showInterfaces: true // 显示接口
  }
}

const MonacoEditor = lazy(() => import('@monaco-editor/react'))

export interface CodeEditorProps {
  value?: string
  defaultValue?: string
  onChange?: (value: string, event?: any) => any
  className?: string
  style?: CSSProperties
  options?: editor.IStandaloneEditorConstructionOptions
  language?: string
  onFocus?: (e: any) => any
  onBlur?: (e: any) => any
  onMount?: (editorInst: editor.IStandaloneCodeEditor, monaco: Monaco) => any
  onSave?: (e: any) => any
  theme?: 'vs-light' | 'vs-dark'
  readOnly?: boolean
  onLoadLanguageSupport?: (monaco: Monaco) => any
}

/**
 * 代码编辑器
 * @param props
 */
export function CodeEditor(props: CodeEditorProps) {
  const { language = 'javascript', onFocus, onBlur, onMount, theme = 'vs-light' } = props
  const { value = props.defaultValue, onChange, className, style, options, readOnly } = props

  const [id] = useState(Math.random().toString(32).slice(2))
  const handleEditorDidMount = useEventListeners(onFocus, onBlur, onMount)
  const containerRef = useRef<HTMLDivElement | null>(null)
  const editorInstRef = useRef<editor.IStandaloneCodeEditor | null>(null)
  const monacoRef = useRef<Monaco | null>(null)
  const editorLayoutInfo = useRef<editor.IDimension | null>(null)

  const [isFullscreen, { toggleFullscreen }] = useFullscreen(containerRef)

  useEffect(() => {
    const handleError = (e: ErrorEvent) => {
      if (window.__POWERED_BY_WUJIE__) {
        // 处理 monaco 编辑器报错( wujie 模式下光标报错 )
        if (e.message?.includes('Failed to execute \'setStart\' on \'Range\': parameter 1 is not of type \'Node\'')) {
          // 阻止错误上报
          e.preventDefault()
        }
      }
    }
    window.addEventListener('error', handleError)
    return () => {
      window.removeEventListener('error', handleError)
    }
  }, [])

  useEffect(() => {
    // 退出全屏时，恢复编辑器布局
    if (isFullscreen === false && editorLayoutInfo.current) {
      editorInstRef.current?.layout(editorLayoutInfo.current)
    }
  }, [isFullscreen])

  useKeyPress(['ctrl.s'], e => {
    e.stopPropagation()
    e.preventDefault()
    props.onSave?.(e)
  }, {
    exactMatch: true,
    target: containerRef
  })

  // 禁用 ctrl + w
  useKeyPress(['ctrl.w'], e => {
    e.stopPropagation()
    e.preventDefault()
  }, {
    exactMatch: true,
    target: containerRef
  })

  const onEditorChange = useMemoizedFn((val, event) => {
    onChange?.(val || '', event)
  })

  const onEditorMount = useMemoizedFn(async (_editor: editor.IStandaloneCodeEditor, monaco: Monaco) => {
    if (!_editor || !monaco) {
      console.log('实例无法初始化')
      return
    }

    if (readOnly) {
      // 设置编辑器为只读模式
      _editor.updateOptions({
        readOnly: true,
        // 可选的其他只读相关配置
        domReadOnly: true,      // 更严格的只读模式
        contextmenu: false,     // 禁用右键菜单
        hover: {                // 优化悬停体验
          enabled: true,
          sticky: true
        },
        renderWhitespace: 'none' // 不显示空白字符
      })
    }

    editorInstRef.current = _editor
    editorLayoutInfo.current = _editor.getLayoutInfo()
    monacoRef.current = monaco

    // 注册 Shift + Space 快捷键来触发智能提示
    _editor.addCommand(monaco.KeyMod.Shift | monaco.KeyCode.Space, () => {
      // 显示建议（智能提示）
      _editor.trigger('keyboard', 'editor.action.triggerSuggest', null)
    })

    // 加载语言支持
    if (/javascript|typescript/.test(language)) {
      await loadLanguageSupport('javascript', monaco)

      await Promise.all([
        loadJsxSupport(monaco),
        loadAnyBiTyped(monaco),
        loadLodashTyped(monaco),
        loadReactTyped(monaco)
      ])
    }

    if (/typescript|ts/.test(language)) {
      // 设置编译器选项
      monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
        target: monaco.languages.typescript.ScriptTarget.ESNext,
        allowNonTsExtensions: true,
        moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
        module: monaco.languages.typescript.ModuleKind.ESNext,
        strict: false,
        esModuleInterop: true,
        noImplicitAny: false,  // 明确允许隐式 any
        strictFunctionTypes: false,  // 可选：关闭函数类型严格检查
        baseUrl: '.',
        paths: {
          'lodash': ['node_modules/lodash/index.js'],  // 添加 lodash 路径映射
          'dayjs': ['node_modules/dayjs/index.js'],  // dayjs
          'request': ['node_modules/request/index.js']  // request
        }
      })
    }

    await props.onLoadLanguageSupport?.(monaco)

    handleEditorDidMount(_editor, monaco)
  })

  return (
    <Suspense fallback={null}>
      <div
        className={cn('monaco-editor-warpper', className, {
          'monaco-editor-light': theme === 'vs-light',
          'monaco-editor-dark': theme === 'vs-dark'
        })}
        style={style}
        ref={containerRef}
      >
        <div className='monaco-editor-toolbox'>
          <Tooltip title='格式化'>
            <NumberOutlined
              onClick={e => {
                e.stopPropagation()
                editorInstRef.current?.getAction('editor.action.formatDocument')?.run()
              }}
            />
          </Tooltip>
          <Tooltip title='全屏编辑'>
            <FullscreenOutlined onClick={toggleFullscreen} />
          </Tooltip>
        </div>
        <MonacoEditor
          key={id}
          height='100%'
          className='monaco-editor-class'
          value={value}
          language={language}
          theme={theme}
          keepCurrentModel
          onChange={onEditorChange}
          onMount={onEditorMount}
          options={{
            ...defaultOptions,
            ...options
          }}
        />
      </div>
    </Suspense>
  )
}

export default CodeEditor
