/* eslint-disable import/no-webpack-loader-syntax */
/* eslint-disable no-unsafe-optional-chaining */
/* eslint-disable no-template-curly-in-string */
import type { Monaco } from '@monaco-editor/react'
import _ from 'lodash'

// 语言配置
const LANGUAGE_CONFIGS = {
  sql: {
    keywords: ['SHOW', 'DATABASES', 'TABLES', 'DATE_FORMAT', 'STR_TO_DATE', 'GET_FORMAT', 'TIMESTAMPDIFF', 'TIMESTAMPADD', 'FROM_UNIXTIME', 'UNIX_TIMESTAMP'],
    snippets: [
      {
        label: 'SELECT',
        insertText: ['SELECT ${1:columns}', 'FROM ${2:table}', 'WHERE ${3:condition}'].join('\n'),
        detail: 'SELECT 查询语句'
      }
    ]
  },
  javascript: {
    keywords: [
      'console.log', 'Promise.all', 'async',
      'await', 'try', 'catch'
    ],
    snippets: [
      {
        label: 'async',
        insertText: [
          'async function ${1:name}() {',
          '\ttry {',
          '\t\t${0}',
          '\t} catch (error) {',
          '\t\tconsole.error(error)',
          '\t}',
          '}'
        ].join('\n'),
        detail: '异步函数'
      }
    ]
  }
}

// 动态加载语言支持
export const loadLanguageSupport = async (language: string, monaco: Monaco) => {
  if (!language || !monaco) return
  const languageType = language.toLowerCase()

  try {
    // 获取语言的内置定义
    const languages = monaco.languages.getLanguages()
    const langModule: any = languages.find(lang => lang.id === languageType)
    if (langModule?.loader) {
      // 获取语言定义 currentLang: { conf, language }
      const currentLang = await langModule.loader()
      // console.log('langModule', currentLang)

      // 获取内置的关键字
      const builtInKeywords = currentLang?.language?.keywords || []

      // 合并内置关键字和自定义关键字
      currentLang.language.keywords = builtInKeywords.concat(LANGUAGE_CONFIGS[languageType]?.keywords || [])

      // 更新 Monarch 语言提供者
      monaco.languages.setMonarchTokensProvider(languageType, currentLang?.language)

      const suggestions = [
        // 关键字提示
        ...currentLang?.language?.keywords.map(keyword => ({
          label: keyword,
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: keyword
        })),

        // 函数提示
        ...(LANGUAGE_CONFIGS[languageType]?.functions || []).map(func => ({
          label: func,
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: func
        })),

        // 代码片段
        ...(LANGUAGE_CONFIGS[languageType]?.snippets || []).map(snippet => ({
          label: snippet.label,
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: snippet.insertText,
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          detail: snippet.detail
        }))
      ]
      // 注册代码提示
      monaco.languages.registerCompletionItemProvider(languageType, {
        provideCompletionItems: () => ({
          suggestions: _.uniqBy(suggestions, 'label')
        })
      })
    }
  } catch (error) {
    console.error(`Failed to load language support for ${language}:`, error)
  }
}

export const loadJsxSupport = async (monaco: Monaco) => {
  // 设置 TypeScript 编译选项，启用 JSX 支持
  // monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
  //   jsx: monaco.languages.typescript.JsxEmit.ReactJSX
  //   // jsx: monaco.languages.typescript.JsxEmit.React,  // 启用 React JSX 语法支持
  //   // allowJs: true,  // 允许 JavaScript 和 TypeScript 混合
  //   // target: monaco.languages.typescript.ScriptTarget.ESNext,  // 使用 ESNext 作为目标
  //   // moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,  // Node.js 模块解析
  //   // noEmit: true,  // 不进行代码输出
  //   // lib: ['esnext', 'dom']  // 引入 ESNext 和 DOM 库
  // })
}

export const loadLodashTyped = async (monaco: Monaco) => {
  const lodashTyped = await import('!!raw-loader!../../types/typed/lodash/v3/index.d')

  monaco.languages.typescript.javascriptDefaults.addExtraLib(lodashTyped.default, 'ts:node_modules/lodash/index.d.ts')
}

export const loadReactTyped = async (monaco: Monaco) => {

  const [reactTyped, reactDefGlobal, reactDefinitions] = await Promise.allSettled([
    import('!!raw-loader!../../types/typed/react/v17/min.d'),
    import('!!raw-loader!../../../node_modules/@types/react/global.d.ts'),
    import('!!raw-loader!../../../node_modules/@types/react/index.d.ts')
  ]) as any

  if (reactDefGlobal.value) {
    monaco.languages.typescript.javascriptDefaults.addExtraLib(String(reactDefGlobal.value.default), 'ts:node_modules/react/global.d.ts')
  }
  if (reactTyped.value) {
    // 加载 import { xxx } from 'react' 的类型
    monaco.languages.typescript.javascriptDefaults.addExtraLib(String(reactTyped.value.default), 'ts:node_modules/react/min.d.ts')
  }
  if (reactDefinitions.value) {
    // 加载全局类型，包括 jsx 的
    monaco.languages.typescript.javascriptDefaults.addExtraLib(String(reactDefinitions.value.default), 'ts:node_modules/react/index.d.ts')
  }
}

export const loadAnyBiTyped = async (monaco: Monaco) => {

  const [anybiTyped, anyuiTyped, anypageTyped] = await Promise.allSettled([
    import('!!raw-loader!../../types/typed/anybi/v1/ui.d'),
    import('!!raw-loader!../../types/typed/anybi/v1/card.d'),
    import('!!raw-loader!../../types/typed/anybi/v1/page.d')
  ]) as any

  if (anybiTyped.value) {
    monaco.languages.typescript.javascriptDefaults.addExtraLib(String(anybiTyped.value.default), 'ts:node_modules/anybi/ui.d.ts')
  }
  if (anyuiTyped.value) {
    monaco.languages.typescript.javascriptDefaults.addExtraLib(String(anyuiTyped.value.default), 'ts:node_modules/anybi/card.d.ts')
  }
  if (anypageTyped.value) {
    monaco.languages.typescript.javascriptDefaults.addExtraLib(String(anypageTyped.value.default), 'ts:node_modules/anybi/page.d.ts')
  }
}
