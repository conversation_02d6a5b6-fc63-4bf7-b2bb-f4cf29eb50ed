
import '@/components/code-editor/index.less'
import './index.less'

import { FullscreenOutlined, NumberOutlined } from '@ant-design/icons'
import type { Monaco } from '@monaco-editor/react'
import { loader } from '@monaco-editor/react'
import { useFullscreen, useMemoizedFn } from 'ahooks'
import { Tooltip } from 'antd'
import cn from 'classnames'
import type { editor, Position } from 'monaco-editor'
import { Position as PointerPosition, Range } from 'monaco-editor'
import React, { CSSProperties, lazy, Suspense, useEffect, useImperativeHandle, useRef } from 'react'

// 低版本游览器报错，在config的extraBabelIncludes属性中添加了sql-formatter不起效果, 暂时先禁用
// import { format } from 'sql-formatter'
import { useEventListeners } from '@/components/code-editor/hook'
import { loadLanguageSupport } from '@/components/code-editor/load-editor-language'
import { Cloud } from '@/services'

loader.config({
  paths: {
    vs: '/_bc/monaco-editor/min/vs'
  },
  'vs/nls': {
    availableLanguages: {
      '*': 'zh-cn'
    }
  }
})

const MonacoEditor = lazy(() => import('@monaco-editor/react'))

export interface CodeEditorProps {
  value?: string
  onChange?: (value: string, event?: any) => any
  className?: string
  style?: CSSProperties
  options?: editor.IStandaloneEditorConstructionOptions
  onFocus?: (e: any) => any
  onBlur?: (e: any) => any
  onMount?: (editorInst: editor.IStandaloneCodeEditor, monaco: Monaco) => any
  loading?: boolean
  theme?: string
}

export interface SqlEditorInstance {
  autoAddNewValue: (text: string) => any
}

const defaultOptions: editor.IStandaloneEditorConstructionOptions = {
  useShadowDOM: true,
  insertSpaces: true,
  minimap: { enabled: false },
  tabSize: 2
}

/**
 * 代码编辑器
 * @param props
 */
function SqlEditor(props: CodeEditorProps, ref) {
  const { value, onChange, className, style, options, onFocus, onBlur, onMount, loading = false, theme = 'vs-light' } = props

  const containerRef = useRef<HTMLDivElement | null>(null)
  const editorInstRef = useRef<editor.IStandaloneCodeEditor | null>(null)
  const monacoRef = useRef<Monaco | null>(null)
  const [, { toggleFullscreen }] = useFullscreen(containerRef)
  const cursorPosition = useRef<Position | null>(null)



  useEffect(() => {
    const handleError = (e: ErrorEvent) => {
      if (window.__POWERED_BY_WUJIE__) {
        // 处理 monaco 编辑器报错( wujie 模式下光标报错 )
        if (e.message?.includes('Failed to execute \'setStart\' on \'Range\': parameter 1 is not of type \'Node\'')) {
          // 阻止错误上报
          e.preventDefault()
        }
      }
    }

    window.addEventListener('error', handleError)
    return () => {
      window.removeEventListener('error', handleError)
    }
  }, [])

  const handleBlur = useMemoizedFn(e => {
    cursorPosition.current = editorInstRef.current?.getPosition() as Position
    onBlur?.(e)
  })

  const handleEditorDidMount = useEventListeners(onFocus, handleBlur, onMount)

  // 自动填入新值
  const autoAddNewValue = useMemoizedFn(text => {
    const insertText = text || ''
    const position = cursorPosition.current || editorInstRef.current?.getPosition()
    if (!position) {
      return
    }
    editorInstRef.current?.executeEdits('', [
      {
        range: new Range(position?.lineNumber, position.column, position.lineNumber, position.column),
        text: insertText
      }
    ])
    editorInstRef.current?.setPosition(new PointerPosition(position.lineNumber, position.column + insertText.length))
    editorInstRef.current?.focus()
  })

  useImperativeHandle(ref, () => ({
    autoAddNewValue
  }))

  const handleDragover = useMemoizedFn(e => {
    e.preventDefault()
  })

  const handleDrop = useMemoizedFn(e => {
    e.preventDefault()
    const data = JSON.parse(e.dataTransfer.getData('text'))
    const sql = editorInstRef.current?.getValue()
    if (!sql) {
      autoAddNewValue(`SELECT * FROM ${data?.realName} LIMIT 100`)
    } else {
      autoAddNewValue(data?.realName)
    }
  })

  const onEditorChange = useMemoizedFn((val, event) => {
    onChange?.(val || '', event)
  })

  const onEditorMount = useMemoizedFn(async (e, monaco) => {
    if (!e || !monaco) {
      return
    }
    editorInstRef.current = e
    monacoRef.current = monaco

    // 添加语言支持
    await loadLanguageSupport('sql', monaco)

    handleEditorDidMount(e, monaco)
  })

  const formatSql = async e => {
    e.stopPropagation()
    try {
      if (editorInstRef.current) {
        editorInstRef.current.getAction('editor.action.formatDocument')?.run()
        const sql = editorInstRef.current?.getValue()
        const res = await Cloud.$execute('formatSql', { sql })
        editorInstRef.current?.setValue(res)
      }
    } catch (err) {
      console.error(err)
    }
  }

  return (
    <Suspense fallback={null}>
      <div
        className={cn('monaco-editor-warpper monaco-editor-light', 'sql-eidtor', className)}
        onDragOver={handleDragover}
        onDrop={handleDrop}
        style={style}
        ref={containerRef}
      >
        <div className='monaco-editor-toolbox'>
          <Tooltip title='格式化'>
            <NumberOutlined onClick={formatSql} />
          </Tooltip>
          <Tooltip title='全屏编辑'>
            <FullscreenOutlined onClick={toggleFullscreen} />
          </Tooltip>
        </div>
        <MonacoEditor
          height='100%'
          className='monaco-editor-class '
          value={value}
          language='sql'
          onChange={onEditorChange}
          onMount={onEditorMount}
          theme={theme}
          keepCurrentModel
          options={{
            ...defaultOptions,
            ...options
          }}
        />
      </div>
    </Suspense>
  )
}

export default React.forwardRef(SqlEditor)
