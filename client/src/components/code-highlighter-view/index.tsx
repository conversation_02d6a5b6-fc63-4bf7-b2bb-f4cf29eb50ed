import './index.less'

import { LoadingOutlined } from '@ant-design/icons'
import cn from 'classnames'
import React, { CSSProperties, lazy, Suspense } from 'react'
// 使用 React.lazy 动态导入 react-syntax-highlighter
const Code = lazy(() => import('react-syntax-highlighter').then(module => ({ default: module.default })))

export interface CodeHighlighterViewProps {
  code?: string
  children?: any
  language?: string
  className?: string
  style?: CSSProperties
}

/**
 * 高亮代码
 * @param props
 * @returns
 */
export function CodeHighlighterView(props: CodeHighlighterViewProps) {
  const { children, code, language = 'javascript', className, style } = props

  return (
    <Suspense fallback={<div><LoadingOutlined /> loading...</div>}>
      <Code data-language={language} className={cn('design-code-highlighter-view', className)} style={style}>
        {code || children}
      </Code>
    </Suspense>
  )
}

export default CodeHighlighterView
