.color-picker-popover {
  z-index: 2030;

  .ant-popover-inner-content {
    padding: 0;

    // 针对 color picker 的阴影做的特殊处理
    > div > div {
      box-shadow: none !important;
    }
  }
  .sketch-picker {
    box-shadow: none !important;
    padding: 0 !important;
  }

  .flexbox-fix {
    cursor: pointer;
  }

  #rc-editable-input-1,
  #rc-editable-input-2,
  #rc-editable-input-3,
  #rc-editable-input-4,
  #rc-editable-input-5 {
    box-shadow: none !important;
    border-radius: 3px;
    border-bottom: 1px solid #eee !important;
    outline: none !important;
    padding: 2px !important;

    &:focus {
      background-color: #f9f9f9;
    }
  }

  .historical-colors {
    > h4 {
      font-size: 13px;
      padding: 0 8px;
      color: #888;
      margin: 0;
    }
    > div {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      padding: 0 5px 8px;
      padding-right: 0;
      max-height: 50px;
      > span {
        cursor: pointer;
        width: 16px;
        height: 16px;
        border-radius: 3px;
        margin: 3px 4px;
        border: 1px solid #aaa;
      }
    }
  }

  .color-picker-panel-content {
    display: flex;
    padding: 6px;
    .right-panel {
      width: 173px;
    }
  }
}
