import './color-picker.less'

import { useDeepCompareEffect, useLocalStorageState } from 'ahooks'
import { PopoverProps } from 'antd'
import _ from 'lodash'
import React, { useState } from 'react'
import { SketchPicker as ColorPicker } from 'react-color'

import { CustomPopover } from '@/components/customs/custom-popover'
import { isCloseToWhite } from '@/utils'

export interface ColorPickerPopoverProps extends PopoverProps {
  value?: string | undefined
  onChange?: (rgba: string) => any
  mode?: 'rgba' | 'rgb' | 'hex'
}

/**
 * 颜色选择框
 * @param props
 */
export function ColorPickerPopover(props: ColorPickerPopoverProps) {
  const { children, value, onChange, mode, ...rest } = props

  const [innerVal, setVal] = useState(value)
  const [local, setLocal] = useLocalStorageState<string[]>('color-picker-historical-colors', { defaultValue: [] })
  const [frequency, setFrequency] = useLocalStorageState<{ c: string, n: number }[]>('color-picker-high-frequency', { defaultValue: [] })
  const presetColors = [
    '#D0021B', '#F5A623', '#F8E71C', '#8B572A', '#7ED321', '#417505',
    '#BD10E0', '#9013FE', '#4A90E2', '#50E3C2', '#B8E986',
    '#4A4A4A', '#9B9B9B', '#FFFFFF'
  ]

  const bindOnSet = (val, update) => {
    let color = ''
    if (mode === 'rgba') {
      color = `rgba(${val.rgb.r}, ${val.rgb.g}, ${val.rgb.b}, ${val.rgb.a})`
    }
    if (mode === 'hex') color = val.hex
    if (mode === 'rgb') color = `rgb(${val.rgb.a}, ${val.rgb.g}, ${val.rgb.b})`

    update?.(color)
    if (_.isArray(local)) {
      local.unshift(color)
    }
    setLocal(_.uniq(local).slice(0, 8))
  }

  const renderColorList = (title: string, arr: string[]) => (
    <div className='historical-colors'>
      <h4>{title}</h4>
      <div>
        {_.map(arr, v => (
          <span
            title={v}
            key={v}
            style={{ backgroundColor: v, borderColor: isCloseToWhite(v) ? '#eee' : v }}
            onClick={() => onChange?.(v)}
          />
        ))}
      </div>
    </div>
  )

  useDeepCompareEffect(() => {
    setVal(value)
  }, [value])

  const content = (
    <div className='color-picker-panel-content'>
      <ColorPicker
        color={innerVal}
        onChange={val => bindOnSet(val, setVal)}
        onChangeComplete={val => bindOnSet(val, onChange)}
        presetColors={[]}
        disableAlpha={mode !== 'rgba'}
        className='color-picker-panel'
      />
      <div className='right-panel'>
        {renderColorList('预设颜色', presetColors)}
        {!_.isEmpty(frequency) && renderColorList('高频使用', local || [])}
        {!_.isEmpty(local) && renderColorList('历史颜色', local || [])}
      </div>
    </div>
  )

  return (
    <CustomPopover
      trigger={['click']}
      {...rest}
      overlayClassName='color-picker-popover'
      content={content}
    >
      {children}
    </CustomPopover>
  )
}

export default ColorPickerPopover
