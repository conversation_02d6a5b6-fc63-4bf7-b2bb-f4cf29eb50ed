.abi-color-select {
  display: flex;
  align-items: center;
  border-radius: 3px;
  padding: 4px;
  border: 1px solid var(--border-color-base);
  width: 112px;
  display: flex;
  background-color: #fff;

  &.hide-text {
    width: 32px;
    height: 32px;
    padding: 2px;
    .abi-color-select-dot {
      width: 100%;
      height: 100%;
    }
  }

  > span:first-child {
    flex: 1;
    color: #666;
    width: 65px;
    font-size: 14px;
    user-select: text;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    margin-right: 3px;
  }

  > .abi-color-select-dot {
    width: 20px;
    height: 20px;
    border-radius: 3px;
    cursor: pointer;
    border: 1px solid var(--border-color-base);
  }

  > .abi-color-select-clean {
    margin-right: 10px;
    color: #777;
    cursor: pointer;
    user-select: none;
    display: none;
  }

  &:hover > .abi-color-select-clean {
    display: inline-block;
  }
}

// 颜色组件修改
input[id*='rc-editable-input'] {
  width: 100% !important;
}
