import './color-select.less'

import { CloseCircleOutlined } from '@ant-design/icons'
import { fastMemo } from '@sugo/design/functions'
import cn from 'classnames'
import Color from 'color'
import _ from 'lodash'
import React, { CSSProperties } from 'react'

import ColorPicker from '@/components/color-picker/color-picker'

export interface ColorSelectProps {
  value?: string
  onChange?: (value?: string) => any
  mode?: 'rgba' | 'rgb' | 'hex'
  className?: string
  style?: CSSProperties
  allowClear?: boolean // 允许清空为无颜色
  showText?: boolean
  defaultValue?: string
  defaultOpen?: boolean
}

/**
 * 颜色选择组件
 * @param props
 */
export const ColorSelect = fastMemo((props: ColorSelectProps) => {
  const { value, onChange, mode = 'hex', className = '', style, allowClear, showText = true, defaultValue } = props

  const actualValue = value === undefined && defaultValue ? defaultValue : value

  const colorToHash = (str?: string) => {
    try {
      if (!str) return 'transparent'
      const color = Color(str)
      return color.hex()
    } catch (err) {
      console.error(err)
      return actualValue
    }
  }

  const color = (actualValue === undefined || actualValue === 'transparent') ? 'inherit' : colorToHash(actualValue)
  const _value = _.isString(actualValue) ? actualValue : 'transparent'

  return (
    <div className={cn('abi-color-select', className, { 'hide-text': !showText })} style={style}>
      {showText && <span title={color}>{color}</span>}
      {allowClear && (
        <CloseCircleOutlined title='无颜色' className='abi-color-select-clean' onClick={() => onChange?.(undefined)} />
      )}
      <ColorPicker mode={mode} value={_value} onChange={val => onChange?.(val)} defaultOpen={props.defaultOpen}>
        <div className='abi-color-select-dot' style={{ backgroundColor: actualValue }} />
      </ColorPicker>
    </div>
  )
})

export default ColorSelect
