.context-menu-view {
  position: fixed;
  min-width: 100px;
  max-width: 200px;
  z-index: 308;
  width: 150px;

  background-color: rgba(#222, 88%);
  border-radius: 4px;
  padding: 3px 0;
  overflow: hidden;
  box-shadow: 1px 3px 20px rgba(#111, 0.2);

  &-title {
    margin-bottom: 0;
    border-bottom: 1px solid rgba(#fff, 0.08);
    padding: 6px 12px;
    font-size: 14px;
    color: #fff;
    font-weight: bold;
  }

  &-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    padding: 2px 8px;
    height: 30px;
    border-radius: 3px;
    color: #eee;

    .anticon {
      margin-right: 6px;
      color: #eee;
    }

    &:hover {
      cursor: pointer;
      user-select: none;
      background-color: rgba(#fff, 0.24);
    }

    &-level-1 {
      padding-left: 16px;
      border-radius: 0;
      background-color: rgba(#fff, 0.1);
    }
  }
}

.context-menu-view-show {
  animation: context-menu-view-start 0.21s ease-in-out;
  animation-fill-mode: forwards;
}

.context-menu-view-light {
  background-color: #fff;
  box-shadow: 1px 2px 8px rgba(1, 1, 1, 0.175);

  .context-menu-view-title {
    color: #333;
    border-color: #f1f1f1;
  }
  .context-menu-view-item {
    color: #555;
    .anticon {
      color: #666;
    }
    &:hover {
      background-color: #f9f9f9;
    }
    &-level-1 {
      background-color: #f6f6f6;
    }
  }
}

@keyframes context-menu-view-start {
  0% {
    opacity: 0.3;
    transform: translateY(-5px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px);
  }
}
