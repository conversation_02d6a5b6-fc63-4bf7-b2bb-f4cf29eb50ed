import './index.less'

import cn from 'classnames'
import isEqual from 'fast-deep-equal'
import React, { memo, useEffect } from 'react'

import withModal from '@/components/with-ref-modal'

export interface ContentMenuProps {
  top: number // 位置
  left: number // 位置
  actions: { key: string; title: string; icon: any; parentKey?: string }[]
  onAction: (item: { key: string; title: string; icon: any }, info: { top: number, left: number }) => any
  title: string
  /** 默认是暗黑主题 */
  theme: 'light' | 'dark'
}

/**
 * 右键呼出的菜单栏
 *
 * @example 使用方式，用 ref 命令式调用
 *
 * <ContextMenu ref={contextMenuRef} />
 *
 * onMouseDown={e => contextMenuRef.current?.hide()}
 *
 */
const ContentMenu = withModal<Partial<ContentMenuProps>>(props => {
  const { visible, top, left, modal, actions = [], title, onAction, theme } = props

  useEffect(() => {
    const onClick = () => modal.hide()
    document.addEventListener('click', onClick)
    return () => {
      document.removeEventListener('click', onClick)
    }
  }, [modal])

  const onClick = action => e => {
    e.stopPropagation()
    e.preventDefault()
    modal.hide()
    onAction?.(action, { top: top || 0, left: left || 0 })
  }

  return (
    <div
      className={cn({
        'context-menu-view': true,
        'context-menu-view-show': visible,
        'context-menu-view-light': theme === 'light'
      })}
      onClick={e => {
        e.stopPropagation()
        e.preventDefault()
      }}
      style={{
        display: visible ? 'block' : 'none',
        top: visible ? top : -10000,
        left: visible ? left : -10000
      }}
    >
      <h4 className='context-menu-view-title'>{title}</h4>
      {actions
        .filter(i => i)
        .map(item => (
          <div
            key={item.key}
            className={cn({
              'context-menu-view-item': true,
              'context-menu-view-item-level-1': !!item.parentKey
            })}
            onClick={onClick(item)}
          >
            {item.icon && <item.icon />}
            {item.title}
          </div>
        ))}
    </div>
  )
})

export default memo(ContentMenu, isEqual)
