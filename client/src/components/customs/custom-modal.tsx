
import { Modal } from 'antd'
import type { ModalProps } from 'antd/lib/modal/Modal'
import cn from 'classnames'
import React from 'react'

export type CustomModalViewProps = ModalProps

/**
 * 自定义的 modal
 * @returns
 */
export default function CustomModalView(props: CustomModalViewProps) {
  const { visible, open } = props

  return (
    <Modal
      open={visible || open}
      okText='确定'
      cancelText='取消'
      {...props}
      className={cn('custom-modal-view', props.className)}
      getContainer={() => document.getElementById('abi-app-modal') || document.body}
    >
      {props.children}
    </Modal>
  )
}
