.custom-select-content {
  font-size: 15px;
  cursor: pointer;
  user-select: none;
  border: 1px solid var(--border-color-base);
  border-radius: 3px;
  padding: 0 6px;
  display: flex;
  align-items: center;

  &.disabled {
    opacity: 0.35;
    cursor: not-allowed;
  }

  > span:first-child {
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  > .anticon-down {
    margin-left: 5px;
    color: #bbb;
  }
}

.custom-select-overlay {
  width: 120px;
  max-height: 260px;
  overflow-y: auto;

  background-color: rgba(#222, 88%);
  border-radius: 3px;
  padding: 5px 0;
  box-shadow: 1px 3px 20px rgba(#111, 0.2);

  .custom-select-option {
    color: rgba(#fff, 0.95);
    padding: 0 8px;
    font-size: 14px;
    line-height: 1.6;
    &:hover {
      cursor: pointer;
      background-color: rgba(#fff, 0.24);
    }
  }
}
