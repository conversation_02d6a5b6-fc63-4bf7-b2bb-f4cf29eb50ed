import './custom-select.less'

import { DownOutlined } from '@ant-design/icons'
import { useReactive } from 'ahooks'
import { Dropdown } from 'antd'
import cn from 'classnames'
import React, { CSSProperties, useMemo } from 'react'

export interface CustomSelectProps {
  options: {
    label: string
    value: string | number
    [key: string]: any
  }[]
  defaultValue?: string | number
  value?: string | number
  onChange?: (value: string | number) => any

  // 一堆类名样式
  className?: string
  style?: CSSProperties
  placeholder?: string
  overlayClassName?: string
  useFontFamily?: boolean
  disabled?: boolean
  optionClassName?: string
  getPopupContainer?: (el: any) => any
}

/**
 * 用 Dropdown 实现的一个定制 select
 * 只适合数据量比较少的
 */
export default function CustomSelect(props: CustomSelectProps) {
  const { options = [], value, onChange, useFontFamily, defaultValue, style, disabled } = props
  const { className, overlayClassName, optionClassName, getPopupContainer } = props

  const val = useMemo(() => options.find(i => i.value === (value || defaultValue)), [options, value])
  const state = useReactive({ visible: false })

  const renderOverlay = () => (
    <div className={cn('custom-select-overlay', overlayClassName)}>
      {options.map(item => (
        <div
          key={item.value + item.label}
          className={cn('custom-select-option', optionClassName)}
          style={{ fontFamily: useFontFamily ? (item.value as string) : 'initial' }}
          onClick={e => {
            e.stopPropagation()
            onChange?.(item.value)
            state.visible = false
          }}
        >
          {item.label}
        </div>
      ))}
    </div>
  )

  const onClick = e => {
    e.stopPropagation()
    state.visible = !state.visible
  }

  return (
    <Dropdown
      open={state.visible}
      overlay={renderOverlay()}
      trigger={['click']}
      onOpenChange={v => (state.visible = v)}
      getPopupContainer={getPopupContainer}
      className={cn({ disabled })}
    >
      <div
        style={{ ...style, fontFamily: useFontFamily ? (val?.value as string) : 'initial' }}
        className={cn('custom-select-content', className)}
        onClick={onClick}
      >
        <span>{val?.label || value}</span>
        <DownOutlined rotate={state.visible ? -180 : 0} />
      </div>
    </Dropdown>
  )
}
