// 定制 tab 样式修改
.tabs-theme {
  height: 100%;

  &.ant-tabs {
    .ant-tabs-nav {
      height: 33px;
      margin-bottom: 8px;
      box-shadow: 1px 2px 3px rgba(#111, 0.05);
    }
    .ant-tabs-nav-list {
      width: 100%;
      padding: 0;
      background-color: #fff;
      .ant-tabs-tab {
        flex: 1;
        justify-content: center;
      }
    }
    .ant-tabs-extra-content {
      margin: 0 6px;
    }
    .ant-tabs-ink-bar {
      display: none;
    }
    .ant-tabs-tab .anticon {
      margin-right: 4px;
    }
    .ant-tabs-tab + .ant-tabs-tab {
      margin: 0;
    }
    .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
      color: var(--primary-color);
    }
  }

  .custom-tabs-content {
    overflow-y: auto;
    height: 100%;
  }
}

.tabs-is-lock {
  height: 100%;

  .ant-tabs-content-holder > .ant-tabs-content {
    position: relative;
    user-select: none;
    pointer-events: none;
    height: 100%;

    &::after {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 10000;
      background-color: rgba(1, 11, 22, 0.24);
      content: '页面被锁定';
      text-align: center;
      padding-top: 10%;
      font-size: 16px;
      color: #fff;
    }
  }

  .custom-tabs-content {
    overflow: hidden;
  }
}
