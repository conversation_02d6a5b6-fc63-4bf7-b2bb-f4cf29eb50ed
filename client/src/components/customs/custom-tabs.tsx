// import ResizeObserver from 'resize-observer-polyfill'
import './custom-tabs.less'

import { TabPaneProps, Tabs, TabsProps } from 'antd'
import cn from 'classnames'
import isEqual from 'fast-deep-equal'
import React, { memo } from 'react'

const { TabPane } = Tabs

export interface CustomTabsProps extends TabsProps {
  options: { key: string; title: string; icon?: any, tabProps?: TabPaneProps }[]
  renderContent?: (tab: CustomTabsProps['options'][number], index: number) => any
  /** tabs 的映射表 */
  tabsMap?: Record<string, JSX.Element | ((p: any) => JSX.Element) | null | false | undefined>
  renderTitle?: (item: CustomTabsProps['options'][number]) => string | JSX.Element
  className?: string
  contentMaxHeight?: string
}

/**
 * 统一风格的自定义 tabs 组件
 * @param props
 */
export function CustomTabs(props: CustomTabsProps) {
  const { options = [], tabsMap = {}, className, contentMaxHeight, renderContent, renderTitle, ...rest } = props

  const renderTab = item => (
    <span>
      {' '}
      {item.icon ? <item.icon /> : null}
      {renderTitle ? renderTitle(item) : item.title}
    </span>
  )

  return (
    <Tabs className={cn('tabs-theme', className)} animated={false} {...rest}>
      {options.map((item, index) => (
        <TabPane
          key={item.key}
          tab={renderTab(item)}
          className='custom-tabs-content'
          style={{ maxHeight: contentMaxHeight }}
          {...item.tabProps}
        >
          {renderContent ? renderContent(item, index) : tabsMap[item.key]}
        </TabPane>
      ))}
    </Tabs>
  )
}

export default memo(CustomTabs, isEqual)
