/**
 * 替换 Moment.js
 * https://ant.design/docs/react/replace-moment-cn
 */
import 'antd/es/date-picker/style/index'
import 'dayjs/locale/zh-cn'

import generatePicker, { RangePickerProps as BaseRangePickerProps } from 'antd/es/date-picker/generatePicker'
import generateRangePicker from 'antd/es/date-picker/generatePicker/generateRangePicker'
import locale from 'antd/es/date-picker/locale/zh_CN'
import { PickerProps } from 'antd/lib/date-picker/generatePicker'
import dayjs, { Dayjs } from 'dayjs'
import dayjsGenerateConfig from 'rc-picker/es/generate/dayjs'
import React from 'react'

dayjs.locale('zh-cn')

const DDatePicker: any = generatePicker<Dayjs>(dayjsGenerateConfig)
const DRangePicker: any = generateRangePicker<Dayjs>(dayjsGenerateConfig)

/**
 * 日期选择器
 * @param props
 */
function _DatePicker(props: PickerProps<Dayjs>) {
  return <DDatePicker {...props} locale={locale} />
}

/**
 * 日期范围选择器
 * @param props
 */
function _RangePicker(props: BaseRangePickerProps<Dayjs>) {
  return <DRangePicker {...props} locale={locale} />
}

// TODO 补全
_DatePicker.RangePicker = _RangePicker

export default _DatePicker
export const DatePicker = _DatePicker
