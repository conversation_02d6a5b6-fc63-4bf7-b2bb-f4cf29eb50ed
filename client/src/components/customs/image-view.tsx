import React from 'react'

import { getImageUrl } from '@/utils/image'

export interface ImageViewProps {
  src: string
}

/**
 * 图片组件
 * 带有大图预览功能
 * @param props
 */
export default function ImageView(props: ImageViewProps) {
  const { src } = props

  const hasProtocol = /^http(s?)/.test(src)
  const _src = hasProtocol ? src : getImageUrl(src)

  return <img src={_src} alt='' crossOrigin='anonymous' />
}
