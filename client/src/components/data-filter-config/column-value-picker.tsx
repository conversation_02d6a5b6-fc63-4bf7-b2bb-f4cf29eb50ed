import { useReactive, useRequest } from 'ahooks'
import { message, Select, SelectProps } from 'antd'
import dayjs from 'dayjs'
import _ from 'lodash'
import React from 'react'

import { DruidColumnType, EMPTY_VALUE_OR_NULL, INDEX_TIME_PERIOD } from '@/consts/data-source'
import type {
  ColumnInfo,
  DataFilterCondition,
  DataLoaderConfig,
  DataSourceQueryConfig,
  OrderConfig
} from '@/types/editor-core/data-source'
import { doQueryChartData, doQuerySlice } from '@/utils/query'

export interface ColumnValuePickerProps extends SelectProps {
  columnInfo?: ColumnInfo
  dataSourceQueryConfig?: DataLoaderConfig
  queryAllVals?: boolean // 是否查询全部维度值，默认只按指标查
  hasNullOption?: boolean // 是否添加空值，默认为 true
}

/** 根据第一个指标查询维度值 */
async function queryDimValByFirstMetrics(
  dataSourceQueryConfig: DataSourceQueryConfig,
  columnInfo: ColumnInfo,
  search: string
) {
  // 随便选个指标
  const firstSpec = _.find(
    dataSourceQueryConfig.fieldsBinding,
    (c, fieldName) => !_.includes(fieldName, '$') && (c?.type === 'indicesSpec' || c?.type === 'customAgg')
  )

  const searchFlt = search ? { col: columnInfo.name, op: 'contains', eq: [search] } : null
  const filters = _.filter(dataSourceQueryConfig.filters, flt => flt.col !== columnInfo.name)

  const preQueryDataSourceConfig: DataSourceQueryConfig = {
    ...dataSourceQueryConfig,
    queryMode: 'groupBy' as const,
    filters: searchFlt ? [...filters, searchFlt] : filters,
    fieldsBinding: firstSpec ? { sum: firstSpec, value: columnInfo } : { value: columnInfo },
    orderBy: [{ field: firstSpec ? 'sum' : 'value', dir: 'desc' }] as OrderConfig[],
    limit: 10
  }
  const res = await doQueryChartData(preQueryDataSourceConfig)
  return _.map(res, d => d.value)
}

/** 使用查询单图维度的方式查询全部维度值 */
async function queryDimValBySlice(
  dataSourceQueryConfig: DataSourceQueryConfig,
  columnInfo: ColumnInfo,
  search: string
) {
  const timeBucket = dataSourceQueryConfig.timeBucket?.replace(/_ACC/i, '').replace(/DATE/i, 'DAY') || 'month'
  const timeNumFormat = { year: 'YYYY', month: 'YYYYMM', day: 'YYYYMMDD' }[_.toLower(timeBucket)]

  const searchFlt = search ? { col: columnInfo.name, op: 'contains', eq: [search] } : null
  const filters = _.filter(dataSourceQueryConfig.filters, flt => flt.col !== columnInfo.name).map(flt =>
    flt.col === 'time_date'
      ? ({
        col: 'time_date_num',
        op: 'in',
        type: 'number',
        eq: _.map(flt.eq, v => (v ? +dayjs(`${v}`).format(timeNumFormat) : null))
      } as DataFilterCondition)
      : flt
  )

  const res = await doQuerySlice({
    druid_datasource_id: `${dataSourceQueryConfig.tableId}`,
    dimensions: [columnInfo.name],
    metrics: [],
    filters: searchFlt ? [...filters, searchFlt] : filters,
    customMetrics: [{ name: '_tempMetric_cnt', formula: '$main.count()' }],
    dimensionExtraSettings: [{ sortCol: '_tempMetric_cnt', sortDirect: 'desc', limit: 10 }],
    customDimensions: [
      // eslint-disable-next-line quotes
      { id: 'time_date_num', type: DruidColumnType.Int, name: 'time_date_num', formula: "$time_date.cast('NUMBER')" }
    ],
    groupByAlgorithm: 'topN'
  })
  return _.map(res?.[0]?.resultSet, d => d[columnInfo.name])
}

/** 数据列值选择器，一般用于筛选条件 */
export function ColumnValuePicker(props: ColumnValuePickerProps) {
  const { columnInfo, dataSourceQueryConfig, queryAllVals = false, options, hasNullOption = true, ...rest } = props

  const reactiveState = useReactive({ search: '' })
  const { data, run } = useRequest(
    () => {
      const type = dataSourceQueryConfig?.type
      if (!dataSourceQueryConfig || !columnInfo || type === 'static' || type === 'api' || type === 'combine' || type === 'repeater') {
        return Promise.resolve([])
      }
      if (queryAllVals || columnInfo.id === INDEX_TIME_PERIOD) {
        return queryDimValBySlice(dataSourceQueryConfig, columnInfo, reactiveState.search)
      }
      return queryDimValByFirstMetrics(dataSourceQueryConfig, columnInfo, reactiveState.search)
    },
    {
      manual: true,
      ready: Boolean(dataSourceQueryConfig?.tableId),
      onError: e => message.error(e.message),
      debounceWait: 1000
    }
  )
  // 目前都是 string 类型，TODO 根据类型输入值?
  // if (colInfo.dataType === 'number') {}
  return (
    <Select
      mode='tags'
      allowClear
      getPopupContainer={triggerNode => triggerNode.parentNode}
      // style={{ width: '100%' }}
      placeholder='请选择'
      dropdownMatchSelectWidth={false}
      onDropdownVisibleChange={show => {
        if (show) {
          run()
        } else {
          reactiveState.search = ''
        }
      }}
      maxTagCount='responsive'
      onSearch={val => {
        reactiveState.search = val
        run()
      }}
      options={
        options ||
        _.map(hasNullOption ? [EMPTY_VALUE_OR_NULL, ..._.compact(data)] : _.compact(data), str => ({
          label: str,
          value: str
        }))
      }
      {...rest}
    />
  )
}
