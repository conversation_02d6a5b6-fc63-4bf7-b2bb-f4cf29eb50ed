import { TreeSelect, TreeSelectProps } from 'antd'
import _ from 'lodash'
import React, { CSSProperties, useMemo } from 'react'

import { treeNodeToColumnInfo, useColumnPickerLazyInit, useDataSourceTableColumnTree } from '@/components/data-filter-config/utils'
import HighlightText from '@/components/highlight'
import { DataSourceInfo, DataSourceInfoType, FieldNode } from '@/types/data-source'
import { ChartField } from '@/types/editor-core/component'
import { ColumnInfo, DataLoaderConfig, DataSourceQueryConfig } from '@/types/editor-core/data-source'


export interface DataSourceColumnPickerProps extends TreeSelectProps {
  dataSourcePickerInfo: DataSourceInfo | null
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id?: string) => Promise<any>
  dataSourceConfigInfo: DataLoaderConfig
  fieldType: ChartField['type']
  value?: ColumnInfo | ColumnInfo[] | null
  onChange?: (next: ColumnInfo | ColumnInfo[] | null) => any
  style?: CSSProperties
  className?: string
  disabledPredicate?: (item: FieldNode) => boolean
}

/** 数据源表字段选择器，用于关联筛选表单 */
export default function DataSourceColumnPicker(props: DataSourceColumnPickerProps) {
  const {
    dataSourceConfigInfo,
    fieldType,
    dataSourcePickerInfo,
    loadMoreDataSourceInfo,
    value,
    onChange,
    disabledPredicate,
    ...rest
  } = props

  const { treeData, flatTree, reactiveState } = useDataSourceTableColumnTree(
    dataSourcePickerInfo,
    dataSourceConfigInfo,
    fieldType,
    false,
    // 不显示同环比口径
    node => !(node.isLeaf && node.type === 'indicesSpec')
  )
  const queryMode = (dataSourceConfigInfo as DataSourceQueryConfig).queryMode || 'groupBy'

  const onInitTree = useColumnPickerLazyInit(dataSourceConfigInfo, fieldType, loadMoreDataSourceInfo)

  const cols = _.isArray(value) ? value : [value]
  const treeDataDict = useMemo(() => _.keyBy(flatTree, 'key'), [flatTree])
  // treeData 是懒加载的，所以先显示 title 解决显示了英文名的问题
  const valueArr = _.map(cols, c => (!treeDataDict[c?.id || ''] ? c?.title : c?.id))
  const highlightedTree = useMemo(() => {
    const validNodes = treeData?.filter(n => !n._hideForFilter)
    if (!reactiveState.search) {
      return validNodes
    }
    return _.map(validNodes, n => ({
      ...n,
      title: <HighlightText text={n.title as string} highlight={reactiveState.search} />
    }))
  }, [treeData, reactiveState.search])
  return (
    <TreeSelect
      placeholder='请选择'
      dropdownMatchSelectWidth={false}
      treeData={
        !disabledPredicate
          ? highlightedTree
          : _.map(highlightedTree, item => ({ ...item, disabled: disabledPredicate(item) }))
      }
      showSearch
      onSearch={k => {
        reactiveState.search = k
      }}
      treeNodeFilterProp='title'
      onDropdownVisibleChange={visible => visible && onInitTree()}
      value={valueArr}
      popupClassName='data-source-column-picker'
      filterTreeNode={() => true}
      onChange={(vals, _labels, { triggerValue, selected }) => {
        if (!rest.multiple) {
          const fieldNode: FieldNode = treeDataDict[triggerValue]
          return onChange?.(selected ? treeNodeToColumnInfo(fieldNode, fieldType, queryMode) : null)
        }
        const selectedKeys = _.isArray(vals) ? vals : [vals]
        onChange?.(_.map(selectedKeys, k => treeNodeToColumnInfo(treeDataDict[k], fieldType, queryMode)))
      }}
      {...rest}
    />
  )
}
