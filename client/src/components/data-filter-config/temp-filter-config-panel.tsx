/* eslint-disable no-nested-ternary */
import './temp-filter-config-panel.less'

import { BlockOutlined, CloseCircleOutlined, <PERSON>Outlined, LockOutlined, UnlockOutlined } from '@ant-design/icons'
import { RelativeTimePicker } from '@sugo/design'
import { Button, Input, Select, Tooltip } from 'antd'
import classNames from 'classnames'
import _ from 'lodash'
import React, { useMemo } from 'react'

import { ColumnValuePicker } from '@/components/data-filter-config/column-value-picker'
import DataSourceColumnPicker from '@/components/data-filter-config/data-source-column-picker'
import { getDimEntities, getToTreeNode, treeNodeToColumnInfo } from '@/components/data-filter-config/utils'
import DebounceInput from '@/components/debounce-input'
import { CONDITIONAL_OPTS, DruidColumnType, GRANULARITY_OPTIONS_DICT, GRANULARITY_TO_PICKER_DICT, INDEX_TIME_PERIOD } from '@/consts/data-source'
import { DataSourceInfo, DataSourceInfoType } from '@/types/data-source'
import { DatasetDetaiInfo } from '@/types/dataset'
import { ColumnInfo, DataFilterCondition, DataSourceConfig, DataSourceQueryConfig } from '@/types/editor-core/data-source'
import { Field } from '@/types/entitys/data-source'
import { IndicesDimension } from '@/types/entitys/indices-table'
import { parseDayjsAdaptWeekOfYear } from '@/utils/date-time'


const MUTEX_OPTS = [
  { key: 'no', label: '交集（默认）', icon: BlockOutlined, hint: '交集（默认）：此筛选条件将与筛选器的条件同时生效，最终效果是两者的条件交集' },
  { key: 'force', label: '禁止覆盖', icon: LockOutlined, hint: '禁止覆盖：此筛选条件强制生效，筛选器的条件将不生效' },
  { key: 'drop', label: '被覆盖', icon: UnlockOutlined, hint: '被覆盖：此筛选条件将被筛选器的条件覆盖，最终效果是按筛选器的条件筛选' }
]

interface UseTempFilterSettingPanelParams {
  value: DataSourceConfig
  onChange: (next: DataSourceConfig) => any
  // 可以不传 dataSourcePickerInfo 和 loadMoreDataSourceInfo，但是这样的话只能取到已选的字段
  dataSourcePickerInfo: DataSourceInfo | null
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id?: string) => Promise<any>
  chartData?: any // 图表数据，用于静态数据源的 eq 值选择
  className?: string
  // mode: 'default' | 'external' // external 是扩展的查询条件，只需要填参数名称即可
  props?: Partial<{ mode: 'default' | 'external'; title: string; addText: string }>
}

// 筛选面板
export function useTempFilterSettingPanel({
  value,
  dataSourcePickerInfo,
  onChange,
  loadMoreDataSourceInfo,
  props,
  chartData,
  className
}: UseTempFilterSettingPanelParams) {
  const { mode = 'default', title = '数据筛选', addText = '添加条件' } = props || {}

  const { dataSourceType } = value
  const queryCfg = value[dataSourceType]

  const isStaticData = dataSourceType === 'static' || dataSourceType === 'repeater' || dataSourceType === 'combine' || !dataSourcePickerInfo
  const dimEntities = getDimEntities(dataSourceType, dataSourcePickerInfo)
  const dimNameDict = useMemo(() => {
    const tableId = queryCfg?.tableId

    if (isStaticData || !dataSourcePickerInfo) {
      // 静态数据只能取已选的列进行筛选
      return (queryCfg?.fieldsBinding || {}) as Record<string, ColumnInfo>
    }
    if (queryCfg?.type === 'dataset') {
      const datasetId = queryCfg?.datasetId
      const datasets: Record<string, DatasetDetaiInfo> = _(dataSourcePickerInfo.datasetDetailMap.entities)
        .filter(dim => dim.datasetId === datasetId)
        .keyBy(dim => dim?.name || '')
        .value()
      return datasets
    }
    if (dataSourceType === 'indicesTable') {
      // 数仓指标指定了部分列为时间列
      const timeCols = _(queryCfg?.fieldsBinding)
        .filter(c => c?.type === 'indicesSpec')
        .map(c => c && dataSourcePickerInfo.indicesSpecMap.entities[c.id]?._sqlMappingTimeCol)
        .compact()
        .value()
      const timeColsSet = new Set(timeCols)
      const indicesTableRes: Record<string, IndicesDimension> = _(dataSourcePickerInfo.indicesDimensionMap.entities)
        .filter(dim => dim._tableId === tableId)
        .keyBy(dim => dim.name)
        .mapValues(d => timeColsSet.has(d.name) ? { ...d, type: DruidColumnType.DateString } : d)
        .value()
      return indicesTableRes
    }
    const others: Record<string, Field> = _(dataSourcePickerInfo.fieldMap.entities)
      .filter(dim => dim.tableId === tableId)
      .keyBy(dim => dim.columnName)
      .value()
    return others
  }, [dataSourceType, queryCfg?.tableId, queryCfg?.fieldsBinding, dimEntities])

  // 时间筛选范围变更回调
  const onFltUpdate = (currFlt: DataFilterCondition | null, nextFlt: DataFilterCondition | null) => {
    const nextFilters = currFlt
      ? _.map(queryCfg?.filters, flt => (flt === currFlt ? nextFlt : flt))
      : [...(queryCfg?.filters || []), nextFlt]
    onChange({
      ...value,
      [dataSourceType]: {
        ...(value[dataSourceType] || {}),
        type: dataSourceType,
        filters: _.compact(nextFilters)
        // filters: _.filter(_.compact(nextFilters), flt => !_.isEmpty(_.compact(flt.eq)))
      }
    })
  }

  // 已选择的列表
  const disabledList = _.map(queryCfg?.filters, t => t.col)

  const getColumnInfo = (col: string) => {
    const dim = dimNameDict[col]
    if (isStaticData) {
      // 静态数据只能取已选的列进行筛选；某些列需要查找，例如 {dim0_idx_2 : {id: 'queryName', name: 'queryName'}}
      return (dim as ColumnInfo) || _.find(dimNameDict, { name: col })
    }
    const fieldNode = getToTreeNode(dataSourceType, dim)
    return (
      dim &&
      treeNodeToColumnInfo(
        fieldNode,
        fieldNode.dataType === 'number' ? 'number' : 'string',
        (queryCfg as DataSourceQueryConfig).queryMode
      )
    )
  }

  // 渲染筛选的操作项
  const renderFilterFrom = ({ info, flt, eqList, col }) => {
    if (info.dataType === 'date') {
      // 按周筛选，时间值需使用标准格式 2022W01 以便查指标接口的 moment 区分（也可以直接传 YYYYMMDD 格式）
      // w 表示根据 locale，W 表示 iso 标准，每周从周一开始
      // const parsePattern = GRANULARITY_OPTIONS_DICT[queryCfg?.timeBucket || 'DAY']?.format

      const weekFormat = GRANULARITY_OPTIONS_DICT.WEEK.format
      const parseDayjsAdaptWeekOfYearByEq = (v: string) =>
        !v ? null : parseDayjsAdaptWeekOfYear(v as string, /w/i.test(v as string) ? weekFormat : undefined)
      // 兼容 周 格式，将格式转换为 dayjs 对象
      const valAdaptHistory = /w/i.test(`${flt.eq}`)
        ? _.map(_.castArray(flt.eq || []), parseDayjsAdaptWeekOfYearByEq)
        : eqList
      return (
        <RelativeTimePicker
          className='w-[14rem]'
          rangeProps={{ picker: GRANULARITY_TO_PICKER_DICT[queryCfg?.timeBucket || 'DAY'] }}
          value={valAdaptHistory}
          onChange={(_next, formatStr) => {
            onFltUpdate(flt, { ...flt, eq: formatStr })
          }}
        />
      )
    }

    if (info?.dataType === 'string') {
      // 如果是含有类型的，支持可输入
      if (flt.op === 'contains' || flt.op === 'not contains' || flt.op.indexOf('contains') > -1) {
        return (
          <Input
            placeholder='请输入'
            value={_.first(flt.eq)}
            onChange={e => onFltUpdate(flt, { ...flt, eq: [e.target.value || null] })}
          />
        )
      }
      return (
        <ColumnValuePicker
          value={eqList}
          columnInfo={info}
          className='w-[14rem]'
          dataSourceQueryConfig={queryCfg || { type: 'indicesTable' }}
          onChange={vals => onFltUpdate(flt, { ...flt, eq: flt.op === 'equal' ? _.last(vals) : vals })}
          options={
            isStaticData
              ? _(chartData)
                .thru(arr => {
                  const field = dimNameDict[col] ? col : _.findKey(dimNameDict, { name: col }) || col
                  return _(arr)
                    .uniqBy(field)
                    .map(v => ({ label: v[field] || '空值', value: v[field] }))
                    .value()
                })
                .value()
              : undefined
          }
        />
      )
    }

    if (info?.dataType === 'number')
      return (
        <Input.Group className='w-[14rem]' compact>
          <Input
            value={flt?.eq[0] as number}
            className='!w-[6rem] !text-center'
            placeholder='最小值'
            onChange={e => onFltUpdate(flt, { ...flt, eq: [e.target.value || null, flt.eq[1]] })}
          />
          <Input className='!w-[2rem] !border-l-0 !border-r-0 !pointer-events-none' placeholder='~' disabled />
          <Input
            value={flt?.eq[1] as number}
            className='!w-[6rem] !text-center'
            placeholder='最大值'
            onChange={e => onFltUpdate(flt, { ...flt, eq: [flt.eq[0], e.target.value || null] })}
          />
        </Input.Group>
      )
  }

  return (
    <div
      className={classNames('p-2 border-gray-100 border-solid border-0 border-b temp-filter-config-panel', className)}
    >
      <div className='mb-2'>{title}</div>

      {_.map(queryCfg?.filters, (flt, i) => {
        const { col, op, eq } = flt
        const eqList = _.isArray(eq) ? eq : _.compact([eq])
        // 静态数据只能取已选的列进行筛选
        const colInfo = getColumnInfo(col)
        // 操作选项
        const selectOpts = _.filter(CONDITIONAL_OPTS, a => {
          if (flt.col === INDEX_TIME_PERIOD) {
            // 目前时间粒度只支持单选
            return a.value === 'equal'
          }
          return a.type.includes(colInfo?.dataType || '')
        })

        const removeBtn = (
          <CloseCircleOutlined
            onClick={() => onFltUpdate(flt, null)}
            className={classNames(
              'cursor-pointer text-danger-700 leading-8 absolute right-[-6px] top-[2px]',
              'opacity-0 group-hover:opacity-100'
            )}
          />
        )

        const mutex = flt?.mutex || 'no'
        const mutexOptIdx = Math.max(0, _.findIndex(MUTEX_OPTS, { key: mutex }))
        const SwitchMutexIcon = MUTEX_OPTS[mutexOptIdx].icon
        const onSwitchMutexIconClick = () => onFltUpdate(flt, {
          ...flt,
          mutex: MUTEX_OPTS[(mutexOptIdx + 1) % MUTEX_OPTS.length].key as DataFilterCondition['mutex']
        })
        // 选择维度
        return (
          <div className='mb-2 flex group' key={i}>
            <div className='w-[2rem] text-center pt-[5px] flex flex-col justify-around'>
              <div className='flex-none w-[1.2rem] h-[1.2rem] text-gray-600 bg-gray-300 rounded text-sm'>{i + 1}</div>
              <div
                className={classNames('flex-none transition-opacity text-gray text-left pl-1', {
                  // 如果是互斥模式不为 no，始终显示图标，否则悬浮再显示
                  'opacity-0 group-hover:opacity-100': mutex === 'no',
                  'opacity-100': mutex !== 'no'
                })}
              >
                <Tooltip title={MUTEX_OPTS[mutexOptIdx].hint}>
                  <SwitchMutexIcon
                    title={MUTEX_OPTS[mutexOptIdx].label}
                    className='text-sm cursor-pointer text-gray-600'
                    onClick={onSwitchMutexIconClick}
                  />
                </Tooltip>
              </div>
            </div>
            <div className='w-[16rem]'>
              <Input.Group key={i} compact className='w-full !mb-2 relative group'>
                <DataSourceColumnPicker
                  treeIcon
                  value={colInfo}
                  showArrow={false}
                  fieldType='string' // 目前只支持指标项目，除列指标列其他都是字符串列
                  className='!w-[9rem]'
                  dataSourceConfigInfo={queryCfg!}
                  dataSourcePickerInfo={dataSourcePickerInfo}
                  loadMoreDataSourceInfo={loadMoreDataSourceInfo}
                  dropdownStyle={{ width: '180px', whiteSpace: 'nowrap' }}
                  disabledPredicate={item => _.includes(disabledList, item.name)}
                  onChange={nextVals => {
                    const nextCol = _.isArray(nextVals) ? nextVals[0] : nextVals
                    const nextFlt = nextCol
                      ? {
                        ...flt,
                        // 目前时间粒度只支持单选
                        op: nextCol.name === INDEX_TIME_PERIOD ? 'equal' : flt.op,
                        col: nextCol.name,
                        eq: [],
                        type: nextCol.dataType
                      }
                      : null
                    onFltUpdate(flt, nextFlt)
                  }}
                />
                <Select
                  getPopupContainer={triggerNode => triggerNode.parentNode}
                  value={op}
                  className='w-[6rem]'
                  dropdownMatchSelectWidth={false}
                  options={selectOpts}
                  onChange={val => onFltUpdate(flt, { ...flt, op: val })}
                />
                {removeBtn}
              </Input.Group>

              {/* 外部维度筛选绑定 */}
              {mode === 'external' && colInfo && (
                <DebounceInput
                  value={flt?.bind || undefined}
                  placeholder='输入参数变量名'
                  onChange={e => onFltUpdate(flt, { ...flt, bind: e.target.value })}
                  className='!w-[87%]'
                  addonBefore={<LinkOutlined />}
                />
              )}

              {mode !== 'external' && renderFilterFrom({ col, info: colInfo || {}, eqList, flt })}
            </div>
          </div>
        )
      })}

      {_.some(queryCfg?.filters, flt => !flt.col) ? null : (
        <Button
          type='primary'
          onClick={() => onFltUpdate(null, { col: '', op: 'in', eq: [] })}
          size='small'
          className='mt-1'
        >
          {addText}
        </Button>
      )}
    </div>
  )
}
