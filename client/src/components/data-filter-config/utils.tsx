import { FieldDataIcon } from '@sugo/design'
import { useMemoizedFn, useReactive, useSessionStorageState } from 'ahooks'
import arrayToTree from 'array-to-tree'
import { produce } from 'immer'
import _ from 'lodash'
import { nanoid } from 'nanoid'
import React, { useEffect, useMemo } from 'react'

import { AGG_MODE_TRANSLATE_DICT, DimJsTypeInvertDict, DruidColumnType, TIME_DATE } from '@/consts/data-source'
import { COMPARE_SPEC_TRANSLATE_DICT } from '@/consts/define'
import { ALL_ID } from '@/services/indices-table'
import { DataSourceInfo, DataSourceInfoType, FieldNode } from '@/types/data-source'
import { DatasetDetaiInfo } from '@/types/dataset'
import { ChartField } from '@/types/editor-core/component'
import {
  ColumnInfo, CombineQueryConfig, CustomDim, DataLoaderConfig, DataSourceConfig,
  DataSourceQueryConfig, DataSourceType, GRANULARITY_OPTIONS_ID, StaticDataSourceConfig
} from '@/types/editor-core/data-source'
import { Field } from '@/types/entitys/data-source'
import { IndicesBase, IndicesDimension, IndicesMajor, IndicesSpec } from '@/types/entitys/indices-table'
import smartSearch from '@/utils'
import { csvToJsObjs } from '@/utils/json-utils'
import { BUILD_IN_QUARTER_DIM, guessDruidTypeByDbDataType } from '@/utils/query'


/** 递归获取指标主题下的所有子分类 */
function recurGetChildrenIds(entities: Record<string, IndicesMajor>, majorId: string | null | undefined) {
  const children = _.filter(entities, m => m.parentId === majorId)
  return _.flatMap(children, m => [m.id, ...recurGetChildrenIds(entities, m.id)])
}

/** 递归获取所有父节点 */
const getParents = (idGroup: Record<string, FieldNode[]>, node: FieldNode, path: string[] = []) => {
  const pNodes = _.flatMap(idGroup[node.value || node.key], n => (n?.pId ? idGroup[n.pId] || [] : []))
  const pathIdSet = new Set(path)
  return _.flatMap(pNodes, n => {
    const id = `${n.value || n.key}`
    return pathIdSet.has(id) ? [] : [...getParents(idGroup, n, [...path, id]), n]
  }) as FieldNode[]
}

/** 根据 dataType 取得 icon，包一层 getter 避免 arrayToTree 深克隆报错 */
export const getIconGetter = _.memoize(
  (type: DataSourceInfoType, dataType: 'number' | 'string' | 'date') =>

    function () {
      return <FieldDataIcon dataType={dataType} />
    }
  // if (dataType === 'date') {
  //   return function () {
  //     return <FieldDataIcon dataType='data' />
  //     // return <FieldTimeOutlined className='!text-[#0065EE]' />
  //   }
  // }
  // const numIconCls = type === 'indicesSpec' ? '!text-[#0065EE]' : '!text-[#009F43]'
  // return dataType === 'number'
  //   ? () => <FieldNumberOutlined className={numIconCls} />
  //   : () => <FieldStringOutlined className='!text-[#009F43]' />
  ,
  (type: DataSourceInfoType, dataType: 'number' | 'string' | 'date') => `${type}-${dataType}`
)

/** 转换指标表维度信息到树节点的格式 */
export function dimToTreeNode(dim: IndicesDimension | CustomDim) {
  const dataType = dim?.name === TIME_DATE ? 'date' : DimJsTypeInvertDict[dim.type] || 'string'
  return {
    key: dim.id || dim.name,
    value: dim.id || dim.name,
    name: dim.name,
    title: dim.title || dim.name,
    type: 'indicesDims',
    dataType,
    selectable: true,
    isLeaf: true,
    icon: getIconGetter('indicesDims', dataType),
    _hideForFilter: dim._hideForFilter, // 前端补的，用于标记是否在筛选中隐藏
    _hideForGroupBy: dim._hideForGroupBy // 前端补的，用于标记是否在分组中隐藏
  } as FieldNode
}

/** 转换数据库表字段信息到树节点的格式 */
export function fieldToTreeNode(f: Field) {
  const guessedType = guessDruidTypeByDbDataType(f.columnType)
  const dataType = DimJsTypeInvertDict[guessedType] || 'string'
  return {
    key: f.id || f.columnName,
    value: f.id || f.columnName,
    name: f.columnName,
    title: f.columnAlias || f.columnName,
    type: 'field',
    dataType,
    selectable: true,
    isLeaf: true,
    icon: getIconGetter('field', dataType)
  } as FieldNode
}

/** 转换数据集表字段信息到树节点的格式 */
export function datasetToTreeNode(d: DatasetDetaiInfo): FieldNode {
  const type: any = d.dataType === 'text' ? 'string' : d.dataType
  return {
    key: `${d.id}`,
    value: d.id,
    name: d.name,
    title: d.title,
    type: 'field',
    dataType: type,
    selectable: true,
    isLeaf: true,
    icon: getIconGetter('field', type)
  }
}

/** 合并查询配置，获取可选维度和度量的选项 */
function combineQueryCfgToFieldNodes(dataSourceConfigInfo: DataLoaderConfig) {
  // combine 类型的子查询，chartFields 都是一样的，但是最终汇总后的数据，当作静态数据处理
  // TODO 目前只考虑了 concat，考虑 join
  const { combineOrders, combineCfgDict } = dataSourceConfigInfo as CombineQueryConfig
  const sameColNameQueryGroup = {}
  return _(combineOrders)
    .flatMap((key, i) => {
      const { query, combineMode, name } = combineCfgDict[key]
      return _.map(query[query.dataSourceType]?.fieldsBinding, (col, chartFieldName) => {
        const id = chartFieldName.replace(/\$/g, '__') // 这里有 $ 会导致识别为同环比，所以替换掉
        const isConcatByName = combineMode === 'concatByName' || (i === 0 && combineCfgDict[combineOrders[1]]?.combineMode === 'concatByName')
        const colName = col?.name || ''
        // 如果是按名称合并，key 有可能重复，title 需要列出重复的表名
        if (isConcatByName && name) {
          sameColNameQueryGroup[colName] = sameColNameQueryGroup[colName] || []
          sameColNameQueryGroup[colName].push(name)
        }
        return {
          key: isConcatByName ? colName : id,
          value: isConcatByName ? colName : id,
          name: isConcatByName ? colName : chartFieldName,
          title: _.some(sameColNameQueryGroup[colName]) || name
            ? `${sameColNameQueryGroup[colName]?.join(',') || name}.${col?.title}`
            : col?.title,
          type: 'staticField',
          dataType: col?.dataType || 'string',
          selectable: true,
          isLeaf: true,
          icon: getIconGetter('field', col?.dataType || 'string')
        } as FieldNode
      })
    })
    .thru(arr => {
      if (_.isEmpty(sameColNameQueryGroup)) {
        return _.uniqBy(arr, n => n.value || n.key)
      }
      // 去重，但是优先取后面的
      return _.uniqBy(arr.reverse(), n => n.value || n.key).reverse()
    })
    .concat({
      key: 'queryName',
      value: 'queryName',
      name: 'queryName',
      title: '数据源名称',
      type: 'staticField',
      dataType: 'string',
      selectable: true,
      isLeaf: true,
      icon: getIconGetter('field', 'string')
    } as FieldNode)
    .value()
}

/** 静态数据，获取可选的维度和度量 */
function staticDataCfgToFieldNodes(dataSourceConfigInfo: DataLoaderConfig | undefined) {
  const { data, fieldsBinding } = (dataSourceConfigInfo || {}) as StaticDataSourceConfig

  // 静态数据源
  const jsObjsData = csvToJsObjs(data)
  if (_.isEmpty(jsObjsData)) {
    return []
  }
  return _.keys(jsObjsData[0]).map(k => {
    const guessNum = _.every(jsObjsData, d => d[k] === '--' || !d[k] || Number.isFinite(+d[k]))
    const col = fieldsBinding?.[k]
    const dataType = col?.dataType ?? (guessNum ? 'number' : 'string')
    return {
      key: k,
      value: k,
      name: k,
      title: col?.title || k,
      type: 'staticField',
      dataType,
      selectable: true,
      isLeaf: true,
      icon: getIconGetter('field', dataType)
    } as FieldNode
  })
}

/**
 * 生成指标口径树节点
 * 指标口径支持进一步展开，展开后的节点为对比口径，但其实调的接口还是通过基础指标查指标口径
 */
function getTreeNodesForIndicesSpec(
  baseMetric: IndicesBase,
  spec: IndicesSpec | undefined | null,
  pId: string,
  timeBucket: GRANULARITY_OPTIONS_ID | undefined
): FieldNode | FieldNode[] {
  const code = spec?._simpleCode || baseMetric?.code // 这个不含有口径名称（全含），所以不能用

  // base 自带全含口径 specId，所以可以直接生成全含口径的树节点
  const key = spec ? spec._key || `${spec.indiceId}:${spec.id}` : `${baseMetric.id}:${baseMetric.specId}`
  const columnNameForIndicesTable = code ? `_tempMetric_${code}` : undefined // 如果没有 name，则生成列时需要查询口径，在生成 name
  const disabledByTimeBucket = spec ? !_.includes(spec?.fixType, timeBucket) : false // 不传口径则无法判断，只能在用户拖进去后判断
  const disabled = disabledByTimeBucket

  const hints = _.compact([
    disabledByTimeBucket ? '不支持当前时间粒度' : ''
  ])
  // 修改过这里后考虑同步修改 inflateMeasureTreeNode
  const curValSpec = {
    key, // 基础指标:口径@版本$对比口径，@ 后面可不填，默认最新版本，$对比口径只是为了区分，查询时去掉
    value: key,
    pId,
    name: columnNameForIndicesTable,
    title: spec?._title || baseMetric?.title,
    type: 'indicesSpec', // 看起来是指标，其实是本期值口径
    dataType: 'number',
    isComposeMetric: spec?._isComposeMetric || baseMetric.indiceType === 'composite',
    selectable: true,
    disabled,
    hints: _.isEmpty(hints) ? undefined : hints,
    isLeaf: false,
    icon: getIconGetter('indicesSpec', 'number')
  } as FieldNode

  // spec 已查询时，返回全部对比口径，否则只返回本期值口径
  // 数仓指标只有本期值口径
  return !spec
    ? { ...curValSpec, loadType: 'indicesSpec' }
    : [
      curValSpec,
      ..._(COMPARE_SPEC_TRANSLATE_DICT)
        .pickBy((_v, k) => /^(?:COMPARED|SEQUENTIAL|PLAN)/.test(k))
        .map((v, k) => ({
          ...curValSpec,
          key: `${curValSpec.key}$${k}`,
          pId: `${curValSpec.key}`,
          title: `${curValSpec.title}_${v}`,
          isLeaf: true
        }))
        .value()
    ]
}

/**
 * 如果度量 node 不包含 name，则需要查询口径信息后更新
 * 需要更新 name，disabled
 */
export async function inflateMeasureTreeNode(
  node: FieldNode,
  timeBucket: DataSourceQueryConfig['timeBucket'],
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id: string) => Promise<any>
) {
  if (node.type !== 'indicesSpec') {
    return node
  }
  const [baseId, specId] = `${node.key}`.split(':')
  const res = await loadMoreDataSourceInfo('indicesSpec', baseId)
  const spec: IndicesSpec = _.find(res?.payload, (s: any) => s.id === specId)
  return !spec
    ? node
    : {
      ...node,
      name: `_tempMetric_${spec._simpleCode}`,
      disabled: node.disabled || !_.includes(spec?.fixType, timeBucket)
    }
}

/** 接口数据源，生成字段选择器树 */
function apiDataCfgToFieldNodes(dataSourcePickerInfo: DataSourceInfo, tableId) {
  const api = dataSourcePickerInfo.apiMap.entities[tableId]
  if (!api) return []
  return _.get(api, 'fields', []).map(i => ({
    key: i.name,
    value: i.name,
    title: i.title,
    dataType: i.dataType,
    type: 'field',
    selectable: true,
    isLeaf: true,
    icon: getIconGetter('field', i.dataType)
  }))
}

/** 接口数据源，生成字段选择器树 */
function dataApiDataCfgToFieldNodes(dataSourcePickerInfo: DataSourceInfo, tableId) {
  const api = dataSourcePickerInfo.dataApiMap.entities[tableId]
  if (!api) return []

  return _.get(api, 'fields', []).map(i => ({
    key: i.id,
    value: i.id,
    title: i.title,
    columnName: i.name,
    name: i.name,
    dataType: i.dataType,
    type: 'field',
    selectable: true,
    isLeaf: true,
    icon: getIconGetter('field', i.dataType)
  }))
}

/** 数据集数据源，生成字段选择器树 */
function datasetDataCfgToFieldNodes(
  dataSourceConfigInfo: DataSourceQueryConfig,
  dataSourcePickerInfo: DataSourceInfo,
  fiedlType: 'string' | 'number'
) {
  const { datasetId, timeBucket, customDims } = (dataSourceConfigInfo || {}) as DataSourceQueryConfig

  const fields = _.filter(dataSourcePickerInfo.datasetDetailMap.entities, f => f.datasetId === datasetId)
    .map(datasetToTreeNode)
    .filter(col => (fiedlType === 'number' ? col.dataType === 'number' : col.dataType !== 'number')) as FieldNode[]
  if (fiedlType === 'string') {
    // 如果时间粒度是天/月，内置季度维度
    const buildInCustomDims = timeBucket === 'MONTH' || timeBucket === 'DAY' ? [BUILD_IN_QUARTER_DIM] : []
    const customStrDims = _.filter(
      [...(customDims || []), ...buildInCustomDims],
      d => d.type === DruidColumnType.String
    )
    fields.push(...customStrDims.map(dimToTreeNode))
  }
  return fields
}

/** 数据开发中心数据源，生成字段选择器树 */
function dataCenterDataCfgToFieldNodes(dataSourceConfigInfo: DataSourceQueryConfig, dataSourcePickerInfo: DataSourceInfo, fieldType: 'string' | 'number') {
  const { tableId, timeBucket, customDims } = (dataSourceConfigInfo || {}) as DataSourceQueryConfig

  // 数据开发中心的表，没有区分维度和指标，总是返回原生字段
  const numTableId = +tableId!
  const fields = _.filter(dataSourcePickerInfo.fieldMap.entities, f => f.tableId === numTableId)

  // 根据需求，度量只能选数值型
  const fieldNodes = fields
    .map(fieldToTreeNode)
    .filter(col => (fieldType === 'number' ? col.dataType === 'number' : col.dataType !== 'number'))
  if (fieldType === 'string') {
    // 如果时间粒度是天/月，内置季度维度
    const buildInCustomDims = (timeBucket === 'MONTH' || timeBucket === 'DAY') ? [BUILD_IN_QUARTER_DIM] : []
    const customStrDims = _.filter(
      [...(customDims || []), ...buildInCustomDims],
      d => d.type === DruidColumnType.String
    )
    fieldNodes.push(...customStrDims.map(dimToTreeNode))
  }
  return fieldNodes
}

/** 指标表数据源，生成字段选择器树 */
function indicesTableDataCfgToFieldNodes(
  dataSourceConfigInfo: DataSourceQueryConfig,
  dataSourcePickerInfo: DataSourceInfo,
  fieldType: 'string' | 'number'
) {
  const { fieldsBinding, timeBucket, customDims } = (dataSourceConfigInfo || {}) as DataSourceQueryConfig

  const indicesSpecMap = dataSourcePickerInfo.indicesSpecMap
  // 维度
  if (fieldType === 'string') {
    // 根据指标筛选维度，因为如果是指标表，则需要先选指标，才能选维度
    const selectedMetrics = _.filter(fieldsBinding, colInfo => colInfo?.type === 'indicesSpec') as ColumnInfo[]
    // 选取了多个指标的话，取可选维度的交集
    const validDimIds = _(selectedMetrics)
      .uniqBy(m => m.id)
      .map(m => _.get(indicesSpecMap.entities[m.id], 'indicesSpecLogicData[0].dimensionIds', '').split(','))
      .thru(arr2d => _.intersection(...arr2d))
      .value()
    const validDimIdSet = new Set(validDimIds)
    // 选择维度
    const indicesDimensionMap = dataSourcePickerInfo.indicesDimensionMap
    const strDims = _.map(indicesDimensionMap.keys, id => indicesDimensionMap.entities[id]).filter(
      dim =>
        (validDimIdSet.has(dim.id) || (dim.order || 0) < 0) &&
        (dim.type === DruidColumnType.String || dim.type === DruidColumnType.DateString)
    )
    // 如果时间粒度是天/月，内置季度维度
    const buildInCustomDims = timeBucket === 'MONTH' || timeBucket === 'DAY' ? [BUILD_IN_QUARTER_DIM] : []
    const customStrDims = _.filter(
      [...(customDims || []), ...buildInCustomDims],
      d => d.type === DruidColumnType.String
    )
    const dims = [...customStrDims, ...strDims]
    return _.map(dims, d => dimToTreeNode(d))
  }

  // 指标
  const indicesBaseMap = dataSourcePickerInfo.indicesBaseMap

  const indicesBaseSpecDict = _.groupBy(
    _.map(indicesSpecMap.keys, id => indicesSpecMap.entities[id]),
    spec => spec.indiceId
  )
  // 目前只会有一个根主题
  const rootMajorDict = _.pickBy(dataSourcePickerInfo.indicesMajorMap.entities, m => !m.parentId)
  return [
    // 指标专业
    ..._.map(dataSourcePickerInfo.indicesMajorMap.keys, k => {
      const major = dataSourcePickerInfo.indicesMajorMap.entities[k]
      return {
        key: k,
        value: k,
        title: major?.title || major?.name,
        pId: major?.parentId,
        type: 'indicesMajor',
        loadType: 'indicesBase',
        selectable: false
      } as FieldNode
    }),

    ..._.flatMap(indicesBaseMap.keys, k => {
      const baseIndex = indicesBaseMap.entities[k]
      const specs = indicesBaseSpecDict[k]
      // base 自带全含口径 specId
      const specId = baseIndex.specId || 'default_spec'
      // 用户自定义的分组，如果未分组，则放到未分组下
      const categoryDict = _.keyBy(baseIndex.indicesBaseModel.indicesCategory, 'majorId')
      if (_.isEmpty(specs) || _.every(specs, s => s.id === specId)) {
        // 一般情况下，baseIndex 只有一个口径，所以这里直接生成单个口径
        // 需要注意的是，现在指标可能有多个 category，所以这里需要根据 category 的数量拷贝基础指标
        return _.flatMap(rootMajorDict, (_rootMajor, mId) =>
          getTreeNodesForIndicesSpec(
            baseIndex,
            specs?.[0],
            categoryDict[mId]?.id || `UNGROUPED_${mId}`,
            timeBucket as GRANULARITY_OPTIONS_ID
          )
        )
      }
      // 需要注意的是，现在指标可能有多个 category，所以这里需要根据 category 的数量拷贝基础指标（后面改了，目前只会有一个根主题）
      return _.flatMap(
        rootMajorDict,
        (_rootMajor, mId) =>
          // 口径不仅有一个全含，需要支持展开对比口径
          [
            // 基础指标
            {
              key: baseIndex.id,
              value: baseIndex.id,
              pId: categoryDict[mId]?.id || `UNGROUPED_${mId}`,
              title: baseIndex.title || baseIndex.name,
              type: 'indicesBase',
              loadType: 'indicesSpec',
              selectable: false
            } as FieldNode,
            // 指标口径
            ..._.flatMap(specs, spec =>
              getTreeNodesForIndicesSpec(baseIndex, spec, spec.indiceId, timeBucket as GRANULARITY_OPTIONS_ID)
            )
          ] as FieldNode[]
      )
    })
  ]
}

/** 生成字段选择器树（打平的树结构） */
export function toFieldPickerTree(
  dataSourcePickerInfo: DataSourceInfo | null, // 传 null 表示只取已经选择的字段
  dataSourceConfigInfo: DataLoaderConfig | undefined,
  fieldType: ChartField['type']
): FieldNode[] {
  const { tableId, type: dsType, datasetId } = dataSourceConfigInfo || ({} as any)
  if (!tableId) return []

  if (dsType === 'combine' && dataSourceConfigInfo) {
    return combineQueryCfgToFieldNodes(dataSourceConfigInfo)
  }
  if (dsType === 'static' || dsType === 'repeater') {
    return staticDataCfgToFieldNodes(dataSourceConfigInfo)
  }

  if (!dataSourcePickerInfo) {
    // dataSourcePickerInfo 为 null 表示只取已经选择的字段，一般用于静态数据
    return _.map(dataSourceConfigInfo?.fieldsBinding || {}, c => datasetToTreeNode(c as any))
  }

  // 拿到这个接口的字段信息
  if (dsType === 'api') {
    return apiDataCfgToFieldNodes(dataSourcePickerInfo, tableId)
  }
  if (dsType === 'dataApi') {
    return dataApiDataCfgToFieldNodes(dataSourcePickerInfo, tableId)
  }
  if (dsType === 'dataset') {
    return datasetDataCfgToFieldNodes(dataSourceConfigInfo as DataSourceQueryConfig, dataSourcePickerInfo, fieldType)
  }
  if (dsType === 'dataCenter') {
    return dataCenterDataCfgToFieldNodes(dataSourceConfigInfo as DataSourceQueryConfig, dataSourcePickerInfo, fieldType)
  }
  return indicesTableDataCfgToFieldNodes(dataSourceConfigInfo as DataSourceQueryConfig, dataSourcePickerInfo, fieldType)
}

/** hook，用于快捷生成选择数据源表数据列的树结构 */
export function useDataSourceTableColumnTree(
  dataSourcePickerInfo: DataSourceInfo | null,
  dataSourceConfigInfo: DataLoaderConfig,
  fieldType: ChartField['type'],
  searchCache = true,
  filterNode = (_n: FieldNode) => true
) {
  const [sState, setSState] = useSessionStorageState(`search-column-${fieldType}`, { defaultValue: { search: '' } })
  const reactiveState = useReactive<{ search: string; majorId?: string }>((searchCache && sState) || { search: '' })
  const keyword = reactiveState.search
  const majorId =
    fieldType === 'number' && dataSourceConfigInfo.type === 'indicesTable'
      ? reactiveState.majorId || 'ALL'
      : reactiveState.majorId
  const filterNodeMemo = useMemoizedFn(filterNode)
  useEffect(() => {
    if (searchCache) {
      setSState({ search: keyword })
    }
  }, [keyword])
  const flatData = useMemo(() => {
    // 根据用户选的主题，筛选出对应的指标分组
    const partialPickerInfo =
      dataSourcePickerInfo &&
      produce(dataSourcePickerInfo, draft => {
        draft!.indicesMajorMap.keys = [
          majorId,
          ...recurGetChildrenIds(dataSourcePickerInfo!.indicesMajorMap.entities, majorId)
        ]
        draft!.indicesMajorMap.entities = _.pick(
          dataSourcePickerInfo!.indicesMajorMap.entities,
          draft!.indicesMajorMap.keys
        )
      })
    const flatTree = toFieldPickerTree(partialPickerInfo, dataSourceConfigInfo, fieldType)
    if (!keyword) {
      return _(flatTree)
        .filter(filterNodeMemo)
        .orderBy((n: FieldNode) => (n.disabled ? 1 : 0), 'asc') // 禁用的节点排在后面
        .value()
    }
    const filtered = _.filter(flatTree, filterNodeMemo)
    // 可能存在多个父节点
    const idGroup = _.groupBy(filtered, n => n.value || n.key)
    // 搜索时，需要将搜索不到的节点置灰
    return _(filtered)
      .flatMap(n => [...getParents(idGroup, n), n])
      .uniqBy(n => `${n.key}`)
      .orderBy((n: FieldNode) => (n.disabled ? 1 : 0), 'asc') // 禁用的节点排在后面
      .orderBy((n: FieldNode) => (keyword && smartSearch(keyword, n.title as string) ? 0 : 1), 'asc')
      .value() as FieldNode[]
  }, [dataSourcePickerInfo, dataSourceConfigInfo, fieldType, keyword, majorId])

  const treeData = useMemo(() => {
    // 根据所选的主题，筛选出对应的子树
    const tree = arrayToTree(flatData, { parentProperty: 'pId', customID: 'key' })
    const subTree = majorId ? _.find(tree, n => n.key === majorId)?.children : tree
    // 匹配到关键字的树根排在前面
    return !keyword
      ? subTree
      : _.orderBy(
        subTree,
        (n: FieldNode) => {
          if (smartSearch(keyword, n.title as string)) {
            return -1
          }
          return _.some(n.children, l => smartSearch(keyword, l.title as string)) ? 0 : 1
        },
        'asc'
      )
  }, [flatData, majorId, keyword])
  return {
    reactiveState,
    treeData,
    flatTree: flatData,
    keyword,
    cleanKeyword: () => (reactiveState.search = '')
  }
}

/** 树节点信息转换为列信息格式 */
export function treeNodeToColumnInfo(
  fieldNode: FieldNode,
  fieldType: ChartField['type'],
  queryMode: DataSourceQueryConfig['queryMode'] = 'groupBy'
) {
  const { key, dataType: nodeDataType, value, isComposeMetric, name, title, type: nodeType } = fieldNode
  const colDataType = nodeDataType || (nodeType === 'indicesSpec' ? 'number' : 'string')
  const colTypeResolveStatic = nodeType === 'staticField' ? 'field' : nodeType
  const colType =
    (nodeType === 'field' || nodeType === 'staticField') && fieldType === 'number' && queryMode === 'groupBy'
      ? 'customAgg'
      : colTypeResolveStatic
  // 指标表：除了复合指标，其余都是 sum
  // 数据库表：字符/日期维默认 count，数值维默认 sum
  const aggModeForIndicesSpec = isComposeMetric ? 'unknown' : 'sum'
  const aggModeForDataCenter = colDataType === 'number' ? 'sum' : 'count'
  const aggMode = { indicesSpec: aggModeForIndicesSpec, field: aggModeForDataCenter }[nodeType] || 'unknown'
  return {
    type: colType,
    dataType: colDataType,
    id: `${value || key}`,
    name: colType === 'customAgg' && nodeType === 'field' ? `_tempMetric_${name}_${nanoid(5)}` : name,
    title: colType === 'customAgg' && nodeType === 'field' ? `${title}（${AGG_MODE_TRANSLATE_DICT[aggMode]}）` : title,
    aggMode: fieldType === 'string' ? undefined : aggMode
  } as ColumnInfo
}

/** hook，字段选择器初始化逻辑，一般把返回值传给 onPopupVisibleChange，visible 的时候再调用加载 */
export function useColumnPickerLazyInit(
  dataSourceConfigInfo: DataLoaderConfig | null | undefined,
  fieldType: ChartField['type'],
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id?: string) => Promise<any>
) {
  const { type: dataSourceType, tableId, fieldsBinding } = dataSourceConfigInfo || {}

  return useMemoizedFn(() => {
    // 静态数据不需要加载数据源信息
    if (!_.includes(['indicesTable', 'dataCenter'], dataSourceType)) {
      return
    }
    if (!tableId) {
      loadMoreDataSourceInfo('indicesTable')
      loadMoreDataSourceInfo('connection')
    }
    const indicesTableLoadType = fieldType === 'string' ? 'indicesDims' : 'indicesMajor'
    const type = dataSourceType === 'dataCenter' ? 'field' : indicesTableLoadType
    loadMoreDataSourceInfo(type, `${tableId ?? ''}`)
    if (type === 'indicesMajor') {
      loadMoreDataSourceInfo('indicesBase', ALL_ID)
    }
    if (type === 'indicesDims') {
      // 取得全部基础指标 ID
      _(fieldsBinding)
        .filter(v => v?.type === 'indicesSpec')
        .map(colInfo => colInfo!.id.split(':')[0])
        .uniq()
        .forEach(baseMeasureId => loadMoreDataSourceInfo('indicesSpec', baseMeasureId))
    }
  })
}

/** 根据不同数据源，获取数据实体 */
export const getDimEntities = (type: DataSourceType, data: DataSourceInfo | null) => {
  if (type === 'indicesTable') return data?.indicesDimensionMap?.entities
  if (type === 'dataset') return data?.datasetDetailMap?.entities
  return data?.fieldMap?.entities
}

/**
 * 获取树节点
 * @param type 数据源类型
 * @param data 值
 * @returns
 */
export const getToTreeNode = (type: DataSourceConfig['dataSourceType'], data) => {
  if (_.isEmpty(data)) return {} as FieldNode
  if (type === 'indicesTable') return dimToTreeNode(data as IndicesDimension) as FieldNode
  if (type === 'dataset') return datasetToTreeNode(data as DatasetDetaiInfo) as FieldNode
  return fieldToTreeNode(data as Field) as FieldNode
}
