import { Input } from 'antd'
import _ from 'lodash'
import React, { ChangeEventHandler, CSSProperties, useEffect, useMemo, useState } from 'react'

// import { InputProps } from 'antd/lib/input/Input'

export interface DebounceInputProps {
  Component?: any
  wait?: number
  mode?: 'default' | 'enter' // enter 时会在按 enter 时触发
  value?: string
  placeholder?: string
  suffix?: React.ReactNode
  prefix?: React.ReactNode
  className?: string
  style?: CSSProperties
  allowClear?: boolean
  onChange?: ((value: string) => any) | ChangeEventHandler<HTMLInputElement>
  [key: string]: any
}

/**
 * 防抖的 input 组件
 * @param props
 */
export default function DebounceInput(props: DebounceInputProps) {
  const { mode = 'default', onChange, wait = 500 } = props

  const [value, setValue] = useState(props.defaultValue || props.value)
  const func = useMemo(() => _.debounce(v => onChange?.(v), wait), [onChange, wait])

  useEffect(() => {
    setValue(props.value)
  }, [props.value])

  const Com = props.Component || Input

  let injectProps = {}
  if (mode === 'enter') {
    injectProps = {
      onKeyPress: e => e.code === 'Enter' && onChange?.(value as any),
      onBlur: e => onChange?.(value as any)
    }
  }

  return (
    <Com
      {..._.omit(props, 'Component')}
      {...injectProps}
      value={value}
      onChange={e => {
        setValue( _.has(e, 'target') ? e.target.value : e)
        if (mode === 'default') func(e)
      }}
    />
  )
}
