import React, { memo } from 'react'

export interface DivWrapProps extends React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> {
  title?: string
}

/**
 * Div 包装
 * @returns
 */
function _DivWrap(props: DivWrapProps) {
  const { isNewBi, croppY = window.isDev ? 58 : 0 } = window.wujieProps || {}
  const style = isNewBi ? { height: `calc(100vh - ${Math.max(croppY, 58)}px)` } : {}

  return (
    <div {...props} style={{ ...props.style, ...style }}>
      {props.children}
    </div>
  )
}

export const Div = memo(_DivWrap)
export default Div
