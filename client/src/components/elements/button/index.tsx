import './index.less'

// import Color from 'color'
import cn from 'classnames'
import React, { CSSProperties } from 'react'

import type { BaseProps } from '@/components/elements/type'

export type ButtonElementProps = BaseProps<{
  config: {
    text: string
  }
}>

/**
 * 按钮
 * @name element-button
 */
export default function ButtonElement(props: ButtonElementProps) {
  const { config, style, meta } = props
  const text = config.text === undefined ? '按钮' : config.text

  const btnStyle: CSSProperties = {
    // ..._.pick(style, [...FONT_FIELD, 'backgroundImage']),
    ...style,
    backgroundColor: style.backgroundColor
  }

  // const lightColor = new Color(btnStyle.backgroundColor)
  const classKey = `button-${meta.key}`.replace('@', '-')

  return (
    <>
      {/* <style>
        {`
        .${classKey}:hover {
          background-color: ${lightColor.lighten(0.08).toString()} !important;
        }
        // .${classKey}:active {
        //   background-color: ${lightColor.darken(0.075).toString()} !important;
        // }
      `}
      </style> */}
      <button
        type='button'
        className={cn({
          'local-element-button': true,
          [classKey]: true
        })}
        style={btnStyle}
      >
        {text}
      </button>
    </>
  )
}
