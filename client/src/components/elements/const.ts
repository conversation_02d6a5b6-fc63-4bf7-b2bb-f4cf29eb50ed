import Button from '@/components/elements/button'
import Container from '@/components/elements/container'
import FilterRuleLayoutHelper from '@/components/elements/filter-rule'
import Iframe from '@/components/elements/iframe'
import Image from '@/components/elements/image'
import Line from '@/components/elements/line'
import LuckysheetComponent from '@/components/elements/luckysheet'
import Qrcode from '@/components/elements/qrcode'
import Rectangle from '@/components/elements/rectangle'
import TabsIndices from '@/components/elements/tabs-indices'
import TabsLayout from '@/components/elements/tabs-layout'
import Text from '@/components/elements/text'
import TimePicker from '@/components/elements/time-picker'
import Tooltip from '@/components/elements/tooltip'
import Triangle from '@/components/elements/triangle'
import Video from '@/components/elements/video'

/**
 * 内置组件的 name-path 映射表
 *
 * 本地内置组件
 * key 组成：组件类型 + 名称
 * @mode local
 */
export const ELEMENT_MAP = {
  'element-text': Text,
  'element-triangle': Triangle,
  'element-rectangle': Rectangle,
  'element-circle': Rectangle,
  'element-vline': Line,
  'element-hline': Line,
  'component-filter-rule': FilterRuleLayoutHelper,
  'component-luckysheet': LuckysheetComponent,
  'element-button': Button,
  'element-indices-tabs': TabsIndices,
  'layout-tabs': TabsLayout,
  'element-image': Image,
  'component-iframe': Iframe,
  'element-video': Video,
  'element-tooltip': Tooltip,
  'element-qrcode': Qrcode,
  'layout-container': Container,
  'element-time-picker': TimePicker
} as const

/**
 * font 字体取的字段
 */
export const FONT_FIELD = [
  'fontWeight',
  'color',
  'fontSize',
  'fontFamily',
  'fontStyle',
  'lineHeight',
  'textShadow',
  'textDecoration',
  'textAlign'
]
