import './index.less'

import _ from 'lodash'
import React from 'react'

import type { BaseProps } from '@/components/elements/type'

export type ContainerProps = BaseProps<{
  config: {
    // 容器组件的 layouts 永远只有一个
    layouts: {
      // component-key（子组件）: info
      [key: string]: {
        key: string // component key（子组件）
        name: string
      }
    }[]
  }
}>

/**
 * container 容器组件
 * @name container
 */
export default function Container(props: ContainerProps & { renderTabContent: any }) {
  const { style, renderTabContent, config } = props

  return (
    <div
      style={_.omit(style, ['width', 'height'])}
      className='local-element-container'
    >
      {renderTabContent?.({ data: config.layouts[0], index: 0 })}
    </div>
  )
}
