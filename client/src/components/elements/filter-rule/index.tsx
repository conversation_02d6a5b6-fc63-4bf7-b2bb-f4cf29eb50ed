import { useReactive } from 'ahooks'
import { Button } from 'antd'
import classnames from 'classnames'
import _ from 'lodash'
import lzstring from 'lz-string'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import * as transform from 'transform-parser'

import Icon from '@/components/icons/iconfont-icon'
import { PREVIEW_CONTAINER_ID } from '@/consts/elements'
import FilterRuleComponentRuntimeAPI from '@/cores/evaction/rumtime-api/filter-rule-component'
import {
  bindInputCompToFilterRule,
  updateInputCompQuerySettings
} from '@/pages/screen/containers/workbench/config-panel/column-picker'
import memoryStorage from '@/storages/memory'
import type { Component, ComponentDefine, ComponentKey } from '@/types/editor-core/component'
import type { ColumnInfo } from '@/types/editor-core/data-source'
import type { FilterRuleCompConfig, FilterRuleConfig } from '@/types/editor-core/filter-linkage'
import { escapeForRegex } from '@/utils'
import { escapeForCssSelector, getCoords } from '@/utils/dom'
import { tryJsonParse } from '@/utils/json-utils'
import { delayPromised } from '@/utils/promise-utils'

import type { BaseProps } from '../type'

/** 输入组件位置操作器：生成一个宽高一样的 div，放在筛选器 div 内，得出位置，然后同步到实际的组件上 */
function InputComponentRigger(props: { compKey: string; canvasDom: HTMLElement | null; isPreview: boolean }) {
  const { compKey, canvasDom, isPreview } = props
  const [riggerDom, setRootDom] = useState<HTMLDivElement | null>(null)
  const riggerRef: (div: HTMLDivElement) => any = useCallback(setRootDom, [])

  const canvasScale = useMemo(() => {
    const s = transform.parse(canvasDom?.style.transform || '').scale
    return !_.isNil(s) && _.isFinite(+s) ? +s : 1
  }, [canvasDom?.style.transform || ''])

  // getElementById 性能不慢，安全起见总是查询
  // https://stackoverflow.com/questions/12514970/is-getelementbyid-efficient
  const compDom = document.getElementById(compKey)
  const { clientWidth: compWidth, clientHeight: compHeight } = compDom || {}

  // 记录最大值，避免压缩后无法再变大
  const reactiveState = useReactive({ maxWidth: compWidth || 0, maxHeight: compHeight || 0 })
  useEffect(() => {
    reactiveState.maxWidth = Math.max(reactiveState.maxWidth, compWidth || 0)
    reactiveState.maxHeight = Math.max(reactiveState.maxHeight, compHeight || 0)
  }, [compWidth, compHeight])

  // 算出自身当前位置
  // 因为：canvasInWindowPos + riggerOffset * canvasScale = riggerInWindowPos
  // 所以：riggerOffset = (riggerInWindowPos - canvasInWindowPos) / canvasScale
  const canvasPos = canvasDom && getCoords(canvasDom)
  const myPos = riggerDom && getCoords(riggerDom)
  const tf =
    canvasPos && myPos
      ? { translate: [myPos.left - canvasPos.left, myPos.top - canvasPos.top].map(v => v / canvasScale) }
      : null

  // 构造控制实际控件的样式
  const compKeyEsc = escapeForCssSelector(compKey)
  // language=CSS
  const dynamicStyle =
    tf &&
    `#${compKeyEsc} {
      transform: ${transform.stringify(tf)} !important;
      max-width: ${riggerDom?.clientWidth || compWidth}px;
      max-height: ${riggerDom?.clientHeight || compHeight}px;
    }`

  return (
    <div
      ref={riggerRef}
      className={classnames('mr-4 mb-4 shrink-0', {
        'border border-solid border-[rgba(204,204,204,0.5)]': !isPreview
      })}
      style={{ width: reactiveState.maxWidth || compWidth, height: reactiveState.maxHeight || compHeight }}
    >
      <style>{dynamicStyle}</style>
    </div>
  )
}

export type FilterRuleLayoutHelperProps = BaseProps<{
  config: FilterRuleCompConfig
}>

/** 生成 FilterRuleConfigurator 组件默认值 */
export const generateDefaultFilterRuleConfig = (inputComps: Component[]) =>
({
  inputMap: { keys: [], entities: {} },
  inputChartColumnMap: _.zipObject(
    inputComps.map(c => c.key),
    inputComps.map(() => ({}))
  ),
  inputSubQueryMap: _.zipObject(
    inputComps.map(c => c.key),
    inputComps.map(() => ({}))
  )
} as FilterRuleConfig)


/** 数据筛选器流式布局组件 */
export default function FilterRuleLayoutHelper(props: FilterRuleLayoutHelperProps) {
  const { style, isPreview, config, onConfigChange, meta, runtimeAPI, idPrefix = '', onEvents } = props

  const { filterRule, freeLayout, showQueryBtn, showResetBtn } = config
  const { inputMap } = filterRule || {}
  const canvasDom: HTMLElement | null = useMemo(
    () =>
      isPreview
        ? document.querySelector(PREVIEW_CONTAINER_ID)
        : document.getElementById(
          'abi-canvas' // ...
        ),
    [isPreview]
  )

  useEffect(() => {
    if (!isPreview) {
      return
    }
    // 调用页面装载完成事件
    onEvents?.didMounted?.(null)
  }, [isPreview])

  // 这里只读取信息，不影响原画布添加组件逻辑
  const onDrop = function (ev) {
    const colInfoJson = ev.dataTransfer.getData('text/x-column-info')
    const compDefineJson = ev.dataTransfer.getData('text/plain')
    if (!colInfoJson) {
      const {
        key: defineKey,
        name
      } = tryJsonParse(window.isDev ? compDefineJson : lzstring.decompressFromEncodedURIComponent(compDefineJson))
      if (new RegExp(`^${escapeForRegex(name)}@\\d+$`).test(defineKey)) {
        // 是从组件列表拖拽过来的
        ev.onCompCreated = async (
          compKey: string,
          _compDefine: ComponentDefine,
          onUpdateComponent: (value: Partial<Component> | ((comp: Partial<Component>) => any), key: ComponentKey) => any
        ) => {
          if (!_.startsWith(compKey, defineKey)) return
          await delayPromised(50) // 避免 redux 报错
          bindInputCompToFilterRule(meta.key, compKey, null, onUpdateComponent)
        }
      }
      return
    }

    // 注入回调到 event，等组件创建后再触发，这样才能获取 compKey
    ev.onCompCreated = async (
      compKey: string,
      compDefine: ComponentDefine,
      onUpdateComponent: (value: Partial<Component> | ((comp: Partial<Component>) => any), key: ComponentKey) => any
    ) => {
      await delayPromised(50) // 避免 redux 报错
      const colInfo: Partial<ColumnInfo> = tryJsonParse(colInfoJson)
      bindInputCompToFilterRule(meta.key, compKey, colInfo as ColumnInfo, onUpdateComponent)
      updateInputCompQuerySettings(compKey, colInfo as ColumnInfo, compDefine, onUpdateComponent)
      const compGroupLimitsDict = {
        date: ['timePicker'],
        number: ['numPicker'],
        string: ['selector', 'textInput']
      }
      memoryStorage.get('shitchComponentDefineRef')?.show({
        sourceComponentKey: compKey,
        componentGroupLimits: compGroupLimitsDict[colInfo?.dataType || ''] || _.flatMap(compGroupLimitsDict)
      })
    }
  }

  const finalStyle = _.omit(style, ['width', 'height', 'pointerEvents'])
  const onDragOver = ev => ev.preventDefault()

  if (!_.isEmpty(inputMap?.keys)) {
    // FIXME 查询逻辑通过默认事件实现
    const onClickQuery = () =>
      (runtimeAPI?.getComponentByKey(meta.key) as FilterRuleComponentRuntimeAPI).applyFilterRule()
    const onClickReset = () =>
      (runtimeAPI?.getComponentByKey(meta.key) as FilterRuleComponentRuntimeAPI).resetFilterRule()
    return (
      <div
        className={classnames({
          'bg-primary-100/25 border !border-solid !border-[#ccc]': !isPreview,
          'pt-4 pl-6 pr-2 flex content-start flex-wrap': !freeLayout
        })}
        style={finalStyle}
        onDrop={onDrop}
        onDragOver={onDragOver}
      >
        {freeLayout
          ? null
          : _.map(inputMap?.keys, k => (
            <InputComponentRigger key={k} compKey={`${idPrefix}${k}`} canvasDom={canvasDom} isPreview={isPreview} />
          ))}
        {!showQueryBtn ? null : (
          <Button type='primary' className='mr-4 mb-4 shrink-0 ml-auto' onClick={onClickQuery}>
            查询
          </Button>
        )}

        {!showResetBtn ? null : (
          <Button className={`mr-4 mb-4 shrink-0 ${showQueryBtn ? '' : 'ml-auto'}`} onClick={onClickReset}>
            重置
          </Button>
        )}
      </div>
    )
  }
  return isPreview ? null : (
    <div
      className='py-4 px-6 flex justify-center items-center bg-primary-100/25 border !border-solid !border-[#ccc]'
      style={finalStyle}
      onDrop={onDrop}
      onDragOver={onDragOver}
    >
      <Icon name='数据筛选' className='mr-2' />
      <span className='text-sm text-[#666]'>拖拽维度到这里试试</span>
    </div>
  )
}
