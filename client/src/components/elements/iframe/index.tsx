import './index.less'

import qs from 'querystring'
import React, { useMemo } from 'react'
import Wujie from 'wujie-react'

import type { BaseProps } from '@/components/elements/type'
import { addUrlQuery } from '@/utils'

export type IframeProps = BaseProps<{
  config: {
    mode: 'inner' | 'external' // 内部和外部
    screenId?: string
    pageUrl?: string
    pageTitle?: string
    srcDoc?: string
    strategy?: 'wujie' | 'default' // 无界让组件渲染在一个 context 中，解决运营资源那边的状态同步问题
  }
  // 发布配置
  releaseConfig?: any
}>

/**
 * iframe 组件
 * @name component-iframe
 */
export default function IframeView(props: IframeProps) {
  const { meta, idPrefix, style, config, releaseConfig } = props
  const { mode, pageTitle, pageUrl = '', screenId, strategy } = config

  const url = useMemo(() => {
    let baseUrl = new URL(window.location.href)
    const search = window.location.search.replace(/^\?/, '')
    const query = qs.parse(search)

    if (config?.srcDoc) return undefined

    if (mode === 'external') {
      return addUrlQuery(pageUrl, search)
    }
    if (releaseConfig.sign) {
      baseUrl = new URL(`${window.origin}/abi/preview/${releaseConfig.sign}`)
    }

    if (screenId) {
      return addUrlQuery(baseUrl, { ...query, screenId })
    }

    return ''
  }, [pageUrl, screenId, mode, releaseConfig.sign, config?.srcDoc])

  if (!config?.srcDoc && mode === 'external' && !/^http(s?):/.test(url || '')) {
    return <div className='local-element-iframe external-error'>输入请正确的网址，目前地址为：{url}</div>
  }

  if (strategy === 'wujie') {
    const componentKey = `${idPrefix}${meta.key}`
    const appName = componentKey.replace(/@/g, '-')

    return (
      <div
        id={`iframe-${meta.key}`}
        className='local-element-iframe'
        style={{ width: style.width, height: style.height }}
      >
        <Wujie
          width='100%'
          height='100%'
          name={appName}
          url={url}
        />
      </div>
    )
  }

  return (
    <iframe
      className='local-element-iframe'
      title={pageTitle}
      name={meta.key}
      src={url}
      srcDoc={config?.srcDoc}
      width={style.width}
      height={style.height}
      id={`iframe-${meta.key}`}
    />
  )
}
