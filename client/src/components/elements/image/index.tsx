import _ from 'lodash'
import React, { CSSProperties, useEffect, useMemo, useState } from 'react'

import type { BaseProps } from '@/components/elements/type'

import { getTotalNumber } from '../text/hooks'

export type ImageElementProps = BaseProps<{
  config: {}
}>

/**
 * 图片
 * @name element-image
 */
export default function ImageElement(props: ImageElementProps) {
  const { style, dataSource, data } = props
  const [globalValue, setGlobalValue] = useState({
    // username: '666'
  })

  // ${gv.name} -> ['name']
  const bindGlobalKey = useMemo(() => {
    const arr = style.backgroundImage?.match(/\$\{gv\.(\w+)(?=\})\}/g)
    const names = arr ? arr.map(match => match.slice(5, -1)) : []
    return names as string[]
  }, [style.backgroundImage])

  // 支持动态插值
  const backgroundImage = useMemo(() => {
    let img = style.backgroundImage
    // 判断是否有 ${value}
    const hasBindValue = /(\$\{dim)|(\$\{total)/.test(img)
    let obj = { dim: [], total: [], gv: {} }

    if (hasBindValue) {
      const _obj = _.chain(data)
        .flatMap(o => _.entries(o))
        .groupBy(0)
        .mapValues((values: any[]) => _.map(values, value => {
          if (values[0] === 'total') return getTotalNumber(value[1], dataSource)
          return value[1]
        }))
        .value()
      obj = { ...obj, ..._obj }
    }

    if (!_.isEmpty(bindGlobalKey)) {
      obj.gv = _.mapKeys(globalValue, (_v, key) => key.replace(/^gv_/, ''))
    }

    try {
      const temp = _.template(img)
      img = temp(obj) // 通过模板变量设置
    } catch (err) {
      console.error(err)
    }

    return img
  }, [style.backgroundImage, data, dataSource, globalValue, bindGlobalKey])

  useEffect(() => {

    const listener = e => {
      if (bindGlobalKey.includes(e.key)) {
        setGlobalValue(s => {
          s[e.key] = e.newValue
          return { ...s }
        })
      }
    }

    window.addEventListener('storage', listener)
    return () => {
      window.removeEventListener('storage', listener)
    }
  }, [bindGlobalKey])

  const innerStyle: CSSProperties = {
    ...style,
    backgroundImage,
    backgroundRepeat: style.backgroundRepeat || 'no-repeat'
  }

  return <div className='local-element-image' style={innerStyle}>
    {!backgroundImage ? <span style={{ color: '#666' }}>未设置图片</span> : ''}
  </div>
}
