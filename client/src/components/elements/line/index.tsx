import './index.less'

import _ from 'lodash'
import React, { CSSProperties } from 'react'

import type { BaseProps } from '@/components/elements/type'

import { useDrag } from './use-drag'

export type LineProps = BaseProps<{
  config: {
    /** 是否允许水平拖拽 */
    enableHorizontalDrag?: boolean
    /** 是否允许垂直拖拽 */
    enableVerticalDrag?: boolean
  }
}>

/**
 * 线条
 * @name element-Line
 */
export default function LineElement(props: LineProps) {
  const { isPreview, onStyleChange, config } = props
  const { enableHorizontalDrag, enableVerticalDrag } = config || {}

  const allowDrag = !isPreview ? undefined : {
    horizontal: enableHorizontalDrag,
    vertical: enableVerticalDrag
  }

  const domRef = useDrag(allowDrag, transform => onStyleChange({ ...props.style, transform }))

  const style: CSSProperties = {
    ..._.omit(props.style, ['width', 'height']),
    cursor: (allowDrag?.horizontal || allowDrag?.vertical) ? 'move' : undefined
  }

  return <div className='local-element-line' style={style} ref={domRef} />
}
