import Gesto from 'gesto'
import { useEffect, useRef } from 'react'
import * as transform from 'transform-parser'

/** 拖拽组件 */
export const useDrag = (
  enable?: { horizontal?: boolean, vertical?: boolean },
  onTransformChange?: (tf: string) => any
) => {
  const domRef = useRef<any>(null)

  useEffect(() => {
    if (!enable) return
    if (!enable.horizontal && !enable.vertical) return
    const xy = [0, 0]
    const node = domRef.current as HTMLElement
    const element = node.parentNode?.parentNode as HTMLDivElement
    let tf: transform.TransformObject
    if (!element) return

    const getso = new Gesto(node, {
      container: document,
      pinchOutside: false,
      checkWindowBlur: true
    })
      .on('dragStart', () => {
        tf = transform.parse(element.style.transform)
        xy[0] = tf.translate[0]
        xy[1] = tf.translate[1]
      })
      .on('drag', e => {
        if (enable.horizontal) xy[0] += e.deltaX
        if (enable.vertical) xy[1] += e.deltaY // 偏移量

        if (element) {
          tf.translate = xy
          element.style.transform = transform.stringify(tf)
        }
      }).on('dragEnd', () => {
        onTransformChange?.(transform.stringify(tf))
      })
    return () => getso.unset()
  }, [enable])

  return domRef
}
