import _ from 'lodash'

import { InflatedResult, LoopState, LuckysheetCompConfig } from '@/components/elements/luckysheet/types'
import { calcFooterRowCnt, calcHeaderRowCnt, extractBorderInfo, extractCellSize, extractCondFormatInfo, extractHyperLinkInfo, extractImageInfo, generateCalcChain, genMergeConfig, getRowHeight, inflatedTemplateEachRow, mergeConstCellsByZones, patchOutsideCellData, takeGenWhile, templateCelldataCombineBorderInfo, templateCelldataCombineCellSize, templateCellMarkCondFormat, templateCellMarkHyperLink, templateCellMarkImageInfo, templateCellMarkMergedCell } from '@/components/elements/luckysheet/utils'
import {
  LuckysheetCellData,
  LuckysheetConfig,
  LuckysheetImageInfo,
  LuckysheetTemplateZone
} from '@/types/editor-core/config'
import { DataFilterCondition } from '@/types/editor-core/data-source'


/** 明细表的形式填充表格数据 */
function* basicInflatePerPage(
  zone: LuckysheetTemplateZone,
  templateCelldata: LuckysheetCellData[],
  chartData: any[],
  extraConfig: Partial<LuckysheetCompConfig> & {
    pageSize: number;
    rootLoopState: LoopState;
    containerWidth: number;
    getFilterPredicate: (flts: DataFilterCondition[] | undefined) => (data: any[]) => boolean
  }
) {
  const {
    pageSize, pageHeight = 0, pagePadding = 0, showFooterOnEveryPage,
    printMode, renderer, getFilterPredicate
  } = extraConfig
  const { rootLoopState: loopState } = extraConfig
  // 设置循环体的深度
  zone = { ...zone, depth: 0 }

  const pageLimitHeight = pageHeight > 0 ? pageHeight - pagePadding * 2 : Number.MAX_SAFE_INTEGER
  const joinToFirstSheet = printMode === 'printing' && renderer !== 'ce' // 打印模式下，合并到第一个 sheet
  const {
    r: [rs, re],
    c: [cs, ce],
    filters,
    filtersDisabled
  } = zone

  const minRow = rs - calcHeaderRowCnt(templateCelldata, rs)
  const maxRow = re + calcFooterRowCnt(templateCelldata, re)
  const headerTemplate = _.filter(templateCelldata, ({ r, c }) => minRow <= r && r < rs && c >= cs && c <= ce)
  const bodyTemplate = _.filter(templateCelldata, ({ r, c }) => rs <= r && r <= re && c >= cs && c <= ce)
  const footerTemplate = _.filter(templateCelldata, ({ r, c }) => re < r && r <= maxRow && c >= cs && c <= ce)
  const footerRowCnt = Math.max(0, maxRow - re)
  const footerHeight = getRowHeight(footerTemplate, re + 1, maxRow)

  const filterDataMemo = _.memoize((disabledFilters: boolean) =>
    disabledFilters ? chartData : _.filter(chartData, getFilterPredicate(filters))
  )
  const [headData, bodyData, footerData] = _.range(3).map(i => filterDataMemo(filtersDisabled?.[i] || false))

  let bodyGen = inflatedTemplateEachRow(bodyData, bodyTemplate, zone, loopState)

  let done = false
  while (!done) {
    const headerGen = inflatedTemplateEachRow(
      [{ children: chartData, seq: () => _.chain(headData) }],
      headerTemplate,
      { r: [0, rs - 1], c: [cs, ce], field: '__header__' },
      loopState
    )
    const headerRows = [...headerGen]
    // 页头的渲染不算 ig
    loopState.ig = 0

    const {
      values: bodyRows,
      restGen,
      done: allContentDone
    } = takeGenWhile(
      bodyGen,
      (_lastRow, i) =>
        // 无论 pageHeight 多么小，每页至少要输出一行，不然会死循环
        i === 0 || (i < pageSize && loopState.rowY + footerHeight < pageLimitHeight)
    )
    bodyGen = restGen
    done = allContentDone
    loopState.totalContentRowCnt = bodyRows.length

    let footerRows: LuckysheetCellData[][] = []
    if (showFooterOnEveryPage || done) {
      const igBak = loopState.ig
      const footerGen = inflatedTemplateEachRow(
        [{ children: chartData, seq: () => _.chain(footerData) }],
        footerTemplate,
        { r: [re + 1, maxRow], c: [cs, ce], field: '__footer__' },
        loopState
      )
      footerRows = [...footerGen]
      // 页脚的渲染不算 ig
      loopState.ig = igBak
    } else if (joinToFirstSheet) {
      // 页脚不显示，但是要保留页脚的高度
      loopState.rowOffset += footerRowCnt
      loopState.rowY += footerHeight
    }

    yield _.flatten([...headerRows, ...bodyRows, ...footerRows])
  }
}

/** 明细表的形式填充单页表格数据 */
export function* basicInflate(
  sheetCfg: LuckysheetConfig['data'][number],
  templateZones: LuckysheetTemplateZone[],
  chartData: any[],
  extraConfig: Partial<LuckysheetCompConfig> & {
    pageSize: number;
    rootLoopState: LoopState;
    containerWidth: number;
    getFilterPredicate: (flts: DataFilterCondition[] | undefined) => (data: any[]) => boolean
  }
): Generator<InflatedResult> {
  const { celldata, config } = sheetCfg
  const cellDataWithMergeConfig = mergeConstCellsByZones(celldata, templateZones)
  const celldataWithRowHeight = templateCelldataCombineCellSize(
    {
      celldata: cellDataWithMergeConfig || [],
      rowHeightDict: config?.rowlen || [],
      colWidthDict: config?.columnlen || {},
      rowHidden: config?.rowhidden || {},
      colHidden: config?.colhidden || {}
    }
  )
  const templateCellWithBorder = templateCelldataCombineBorderInfo(
    celldataWithRowHeight || [],
    config?.borderInfo || []
  )

  // 为合并单元格分配 id，数据复制填充后，相同的 id 会被合并
  const templateCellWithMergedCellInfo = templateCellMarkMergedCell(templateCellWithBorder)

  const condFormats = sheetCfg.luckysheet_conditionformat_save
  const templateCellWithCondFormat = templateCellMarkCondFormat(templateCellWithMergedCellInfo, condFormats)
  const templateCellWithHyperlink = templateCellMarkHyperLink(templateCellWithCondFormat, sheetCfg.hyperlink)
  const templateCellWithImg = templateCellMarkImageInfo(templateCellWithHyperlink, sheetCfg.images as LuckysheetImageInfo[])

  // 分页逻辑：通过 generator 实现，分页时传入分页命令，offset 重新从表头开始输出，数据接着上一页输出
  const inflatedCellWithBorderAndMarkMcPageGen = basicInflatePerPage(
    templateZones[0],
    templateCellWithImg,
    chartData,
    extraConfig
  )

  let sheetIdx = 0
  for (const inflatedCellWithBorderAndMarkMc of inflatedCellWithBorderAndMarkMcPageGen) {
    const { rowlen, columnlen, rowhidden, colhidden } = extractCellSize(
      inflatedCellWithBorderAndMarkMc,
      extraConfig.widthAdaptive ? extraConfig.containerWidth : undefined
    )
    const { inflatedCelldata, borderInfo } = extractBorderInfo(inflatedCellWithBorderAndMarkMc, config?.borderInfo)
    const { mergeConfig, inflatedCelldata: inflatedCelldata2 } = genMergeConfig(inflatedCelldata, config?.merge, templateZones)
    const { inflatedCelldata: cellsWithoutCondFormat, condFormatInfos } = extractCondFormatInfo(
      inflatedCelldata2,
      condFormats
    )
    const { inflatedCelldata: cellsWithoutLink, hyperlink } = extractHyperLinkInfo(cellsWithoutCondFormat)
    const { inflatedCelldata: cellsWithoutImgs, images } = extractImageInfo(cellsWithoutLink, sheetCfg.images as LuckysheetImageInfo[])
    const { calcChain, computedCells } = generateCalcChain(cellsWithoutImgs, sheetIdx)
    const patchedInflatedCells = patchOutsideCellData(computedCells, celldata)
    const computedCellsIds = computedCells.map(cell => `${cell.r}_${cell.c}`)

    sheetIdx += 1
    yield {
      templateSheet: sheetCfg,
      inflatedCelldata: patchedInflatedCells,
      borderInfo,
      merge: { ..._.omit((sheetCfg.config?.merge || {}), computedCellsIds), ...mergeConfig },
      rowlen,
      columnlen,
      rowhidden,
      colhidden,
      totalContentRowCnt: extraConfig.rootLoopState.totalContentRowCnt,
      condFormatInfos,
      calcChain,
      hyperlink,
      images
    }
  }
}
