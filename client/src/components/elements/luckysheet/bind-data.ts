import { produce } from 'immer'
import _ from 'lodash'

import { basicInflate } from '@/components/elements/luckysheet/basic-inflate-helper'
import { groupInflate } from '@/components/elements/luckysheet/pivot-inflate-helper'
import { InflatedResult, LoopState, LuckysheetCompConfig } from '@/components/elements/luckysheet/types'
import {
  calcHeaderRowCnt,
  combinePages,
  encodeCell,
  extractCellSize,
  templateCelldataCombineCellSize
} from '@/components/elements/luckysheet/utils'
import type { LuckysheetConfig, LuckysheetTemplateZone } from '@/types/editor-core/config'
import type { DataSourceConfig } from '@/types/editor-core/data-source'
import { filtersCondToPredicate } from '@/utils/query'

/** 判断当前循环体是否是堆叠在上一个循环体之上 */
function isLoopZoneGroupStackedAfterPrevious(zoneGroupDict: Record<string, LuckysheetTemplateZone[]>, gi: number) {
  if (gi === 0) return false
  const keys = _.keys(zoneGroupDict)
  const prevGroupZone = zoneGroupDict[keys[gi - 1]][0]
  const currGroupZone = zoneGroupDict[keys[gi]][0]

  const {
    c: [pcs, pce]
  } = prevGroupZone
  const {
    c: [ccs, cce]
  } = currGroupZone

  // 列有重叠则认为是堆叠
  return ccs <= pce && pcs <= cce
}

/** 计算当前循环体的起始行数 */
function calcLoopZoneGroupMinRows(
  zoneGroupDictNotEmpty: Record<string, LuckysheetTemplateZone[]>,
  groupIdx: number,
  templateSheet: LuckysheetConfig['data'][number]
) {
  const keys = _.keys(zoneGroupDictNotEmpty)
  const currGroupZone = zoneGroupDictNotEmpty[keys[groupIdx]][0]
  const {
    r: [rs, re]
  } = currGroupZone

  const celldata = templateSheet.celldata || []
  return rs - calcHeaderRowCnt(celldata, rs)
}

/** 如果没有配置数据或循环体，则直接渲染模板 */
const mockInflated = function* (
  sheet: LuckysheetConfig['data'][number],
  config: Omit<LuckysheetCompConfig, 'luckysheetConfig'>,
  compPos: { left: number; top: number; width: number },
  chartData,
  rootLoopState
): Generator<InflatedResult> {
  const celldataWithRowHeight = templateCelldataCombineCellSize(
    {
      celldata: sheet.celldata || [],
      rowHeightDict: sheet.config?.rowlen || [],
      colWidthDict: sheet.config?.columnlen || {},
      colHidden: sheet.config?.colhidden || {},
      rowHidden: sheet.config?.rowhidden || {}
    }
  )
  const {
    rowlen,
    columnlen,
    rowhidden,
    colhidden
  } = extractCellSize(celldataWithRowHeight, config.widthAdaptive ? compPos.width : undefined)

  // // 输出模板页
  // const maxC = _.maxBy(sheet?.celldata, 'c')?.c || 0
  // const minC = _.minBy(sheet?.celldata, 'c')?.c || 0
  // const cellData = inflatedTemplateWithData(
  //   [{ children: chartData, seq: () => _.chain(chartData) }],
  //   sheet.celldata || [],
  //   { r: [0, (sheet?.celldata?.[0]?.r || 0) - 1], c: [minC, maxC], field: '' },
  //   rootLoopState,
  //   true
  // )
  // const _cellData = [...cellData]

  yield {
    templateSheet: sheet,
    inflatedCelldata: sheet.celldata || [],
    borderInfo: sheet.config?.borderInfo || [],
    merge: sheet.config?.merge || {},
    rowlen: rowlen || {},
    columnlen: columnlen || {},
    rowhidden,
    colhidden,
    totalContentRowCnt: sheet.config?.totalContentRowCnt || _.maxBy(sheet.celldata, 'r')?.r || 0,
    condFormatInfos: sheet.luckysheet_conditionformat_save || [],
    calcChain: sheet.calcChain || [],
    hyperlink: sheet.hyperlink || {},
    images: sheet.images || {}
  }
}

/** 绑定数据到 luckysheet */
export function bindDataToLuckySheet(
  luckysheetCfg: Partial<LuckysheetConfig>,
  dataSourceCfg: DataSourceConfig,
  chartData: any[],
  config: Omit<LuckysheetCompConfig, 'luckysheetConfig'>,
  hook: Record<string, Function>,
  compPos: { left: number; top: number; width: number }
) {
  const { showGridLines, page, printMode, renderer, showSheetBar } = config
  const { pageHeight = 0, pagePadding = 0 } = config
  const { fieldsBinding, sortedFieldNames } = dataSourceCfg[dataSourceCfg.dataSourceType] || {}
  const sheets = luckysheetCfg.data || []
  const now = Date.now()
  const pageLimitHeight = pageHeight > 0 ? pageHeight - pagePadding * 2 : Number.MAX_SAFE_INTEGER
  const compOffsetCurrPage = compPos.top % pageLimitHeight
  const titleFieldDict = _(fieldsBinding)
    .mapValues((v, k) => v?.title || k)
    .invert()
    .value()
  const rootLoopState: LoopState = {
    rowOffset: 0,
    columnOffset: 0,
    totalContentRowCnt: 0,
    rowY: compOffsetCurrPage,
    titleFieldDict,
    i: [],
    ig: 0,
    getGlobalFNEData: Function
  }

  // 目前只有一个模板 sheet
  const templateSheet0: LuckysheetConfig['data'][number] | undefined = sheets[0]
  const inflateMode = templateSheet0?.config?.inflateMode || 'basic'

  // 支持多分组 templateZone 渲染
  // 1. 按范围划分 templateZone，相互嵌套的归为一组（添加后自动设置）
  // 2. 按分组顺序，从最外层开始渲染，渲染时根据 filters 过滤数据

  // 兼容旧数据，旧数据没有 group，只有一组
  const zoneGroupDict = _.groupBy(templateSheet0?.config?.templateZones || [], zone => zone.group || 'default')

  const zoneGroupDictNotEmpty = _.isEmpty(zoneGroupDict) ? { default: [] } : zoneGroupDict

  // 看看是否选择了关联字段，如果选择了需要对多个数据源进行分类，这里为了实现后端分页
  const dataSourceFields = _.values(templateSheet0?.config?.sourceFields)

  const chartList = dataSourceFields.length ? _(chartData)
    .map(obj => _.assign({}, obj, _.transform(dataSourceFields, (result, field) => {
      if (obj[field] !== '') {
        result[field] = obj[field]
      } else {
        const otherField = _.find(dataSourceFields, f => f !== field)
        result[field] = obj[otherField!]
      }
    }, {})))
    .groupBy(_.first(dataSourceFields))
    .pickBy(arr => _.some(arr, obj => !_.every(dataSourceFields, field => obj[field] === '' || _.isNaN(obj[field]) || obj[field] === undefined)))
    .values()
    .value()
    : [chartData]

  const pageSize = dataSourceFields.length ? 0 : (config.pageSize || 0)
  const joinToFirstSheet = printMode === 'printing' && renderer !== 'ce'
  const zoneGroupGensFn = (data: { [key: string]: any }[]) => _.map(_.keys(zoneGroupDictNotEmpty), group => {
    const zones = zoneGroupDictNotEmpty[group]
    if (_.isEmpty(zones) || _.isEmpty(fieldsBinding) || !templateSheet0) {
      return templateSheet0 ? mockInflated(templateSheet0, config, compPos, data, rootLoopState) : null
    }
    const getFilterPredicate = fltConds => filtersCondToPredicate(fltConds, fieldsBinding)
    const myChartData = data || []

    function globalFNEObjCache(cacheChartData) {
      let globalFNEObj
      return function (field) {
        if (globalFNEObj === undefined) {
          globalFNEObj = _.reduce(cacheChartData, (acc, obj) => {
            _.defaults(acc, _.pickBy(obj, value => value !== '' && value !== null && value !== undefined))
            return acc
          }, {})
        }
        return globalFNEObj[field]
      }
    }

    const getCacheData = globalFNEObjCache(data)
    rootLoopState.getGlobalFNEData = getCacheData

    if (inflateMode === 'basic') {
      // basic 模式下，只有一个 templateZones
      const demandPageSize = pageSize > 0 ? pageSize : myChartData.length
      return basicInflate(templateSheet0, zones, myChartData, {
        ...config,
        pageSize: demandPageSize,
        pageHeight,
        pagePadding,
        rootLoopState,
        containerWidth: compPos.width,
        getFilterPredicate
      })
    }
    // 1. 对 celldata 进行 groupBy，优先按 templateZones 中的顺序
    // 如果没有用户设置对顺序，默认字段顺序：日期，字符，数字
    const orderDict = { date: 0, string: 1, number: 2 }
    const orderedFieldNames = _.isEmpty(sortedFieldNames)
      ? _.orderBy(_.keys(fieldsBinding), k => orderDict[fieldsBinding?.[k]?.dataType || ''] || 2)
      : (sortedFieldNames as string[])

    const nonNumFields = _.filter(orderedFieldNames, f => /date|string/.test(fieldsBinding![f]?.dataType || ''))
    const templateZoneValidFields = zones.map(zone => zone.field).filter(f => _.includes(orderedFieldNames, f))
    const groupByFields = [...templateZoneValidFields, ..._.difference(nonNumFields, templateZoneValidFields)]
    return groupInflate(templateSheet0, zones, myChartData, groupByFields, {
      ...config,
      pageSize: pageSize > 0 ? pageSize : Number.MAX_SAFE_INTEGER,
      pageHeight,
      pagePadding,
      rootLoopState,
      containerWidth: compPos.width,
      getFilterPredicate
    })
  }).filter(_.identity)
  // 多个 gen 轮流取，每取一轮为一个 sheet
  const combinedGen = function* (zoneGroupGens) {
    let pageIndex = 0
    const curPage = dataSourceFields.length ? 0 : (config.page || 0)
    // 如果处于打印模式且设置了 page，说明用户只需要第 page 页的数据，不需要全部数据，也不需要知道总页数
    const maxPageIndex = curPage > 0 && printMode === 'printing' ? curPage - 1 : Number.MAX_SAFE_INTEGER
    while (pageIndex <= maxPageIndex) {
      // 打印模式，不重置 offset，数据持续在第一页输出；跟下一页的间隔为 footer 的高度（如果有 footer 则无间隔）
      if (!joinToFirstSheet) {
        rootLoopState.rowOffset = 0
        rootLoopState.rowY = pageIndex === 0 ? compOffsetCurrPage : 0
        rootLoopState.totalContentRowCnt = 0
      }
      // 如果设置了 page，说明用户只需要第 page 页的数据，不需要全部数据，但是需要知道总页数
      rootLoopState.mockRender = curPage > 0 && printMode !== 'reportPrint' && pageIndex !== curPage - 1
      const currLoopState = { ...rootLoopState }
      const inflated = zoneGroupGens.map((g, gi) => {
        // 不同的循环体组，并排的话，每次循环都要重置 offset 至上一页的末尾；堆叠的话，则需要减去循环体组的最小行数
        if (isLoopZoneGroupStackedAfterPrevious(zoneGroupDictNotEmpty, gi)) {
          const skipRows = 0// calcLoopZoneGroupMinRows(zoneGroupDictNotEmpty, gi, templateSheet0) - 1 // -1 留白
          Object.assign(rootLoopState, {
            ...currLoopState,
            rowOffset: rootLoopState.rowOffset - skipRows,
            rowY: rootLoopState.rowY - skipRows * (rootLoopState.rowY / rootLoopState.rowOffset),
            totalContentRowCnt: rootLoopState.totalContentRowCnt - skipRows
          })
        } else {
          Object.assign(rootLoopState, currLoopState)
        }
        return g!.next()
      })
      if (inflated.every(c => c.done)) {
        break
      }
      const inflatedRes = inflated.map(c => c.value!).filter(_.identity)
      yield combinePages(inflatedRes, { isMergingZonesInSinglePage: true })
      pageIndex += 1
    }
  }
  const combinedGens = _.map(chartList, data => {
    const zoneGroupGens = zoneGroupGensFn(data)
    return [...combinedGen(zoneGroupGens)]
  })
  const pageContents = joinToFirstSheet ? _.flatten(_.map(combinedGens, curCombinedGen => combinePages(curCombinedGen))) : _.flatten(combinedGens)
  const inflatedSheets: LuckysheetConfig['data'] = _.map(pageContents, (pageContent, i) => {
    const {
      templateSheet,
      borderInfo,
      inflatedCelldata,
      merge,
      rowlen,
      columnlen,
      rowhidden,
      colhidden,
      totalContentRowCnt,
      condFormatInfos,
      ...rest
    } = pageContent
    return {
      ...templateSheet,
      order: i,
      status: i === page - 1 ? 1 : 0,
      showGridLines: showGridLines ? 1 : 0,
      name: `第 ${i + 1} 页`,
      index: i,
      celldata: inflatedCelldata,
      config: {
        ...templateSheet.config,
        lastUpdatedAt: now,
        borderInfo,
        merge,
        rowlen,
        columnlen,
        // 如果设置了关联字段，需要进行分页，这里需要重新设置条数
        totalContentRowCnt: dataSourceFields.length ? pageContents.length / (chartList.length || 1) : totalContentRowCnt,
        rowhidden,
        colhidden
      },
      luckysheet_conditionformat_save: condFormatInfos,
      ...rest
    } as LuckysheetConfig['data'][number]
  })

  return produce(luckysheetCfg, draft => {
    draft.showsheetbar = showSheetBar
    draft.hook = hook
    draft.data = inflatedSheets
    draft.showstatisticBar = false
    draft.showstatisticBarConfig = {
      count: false, // 计数栏
      view: false, // 打印视图
      zoom: false // 缩放
    }
  })
}
