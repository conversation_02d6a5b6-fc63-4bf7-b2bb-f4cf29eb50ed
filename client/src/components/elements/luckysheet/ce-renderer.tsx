import { useDebounceFn, useDeepCompareEffect, useRequest } from 'ahooks'
import classNames from 'classnames'
import { format as d3Format } from 'd3-format'
import type { Column } from 'jspreadsheet-ce'
import _ from 'lodash'
import { nanoid } from 'nanoid'
import * as React from 'react'
import { useEffect, useMemo, useRef } from 'react'
import { useDeepCompareMemo } from 'use-deep-compare'

import { LuckysheetCompConfig } from '@/components/elements/luckysheet/types'
import { encodeCell, encodeColMemo, getActiveSheet } from '@/components/elements/luckysheet/utils'
import type { ComponentKey } from '@/types/editor-core/component'
import type { FrozenType, LuckysheetBorderInfo, LuckysheetCellData, LuckysheetCellVal, LuckysheetConditionFormat, LuckysheetConfig, LuckysheetDataConfig } from '@/types/editor-core/config'
import { escapeForCssSelector } from '@/utils/dom'


/** 中文翻译 https://github.com/jspreadsheet/ce/blob/master/resources/lang/zh_CN.json */
export const i18nDict = {
  noRecordsFound: '未找到',
  showingPage: '显示 {1} 条中的第 {0} 条',
  show: '显示 ',
  search: '搜索',
  entries: ' 条目',
  columnName: '列标题',
  insertANewColumnBefore: '在此前插入列',
  insertANewColumnAfter: '在此后插入列',
  deleteSelectedColumns: '删除选定列',
  renameThisColumn: '重命名列',
  orderAscending: '升序',
  orderDescending: '降序',
  insertANewRowBefore: '在此前插入行',
  insertANewRowAfter: '在此后插入行',
  deleteSelectedRows: '删除选定行',
  editComments: '编辑批注',
  addComments: '插入批注',
  comments: '批注',
  clearComments: '删除批注',
  copy: '复制...',
  paste: '粘贴...',
  saveAs: '保存为...',
  about: '关于',
  areYouSureToDeleteTheSelectedRows: '确定删除选定行?',
  areYouSureToDeleteTheSelectedColumns: '确定删除选定列?',
  thisActionWillDestroyAnyExistingMergedCellsAreYouSure: '这一操作会破坏所有现存的合并单元格，确认操作？',
  thisActionWillClearYourSearchResultsAreYouSure: '这一操作会清空搜索结果，确认操作？',
  thereIsAConflictWithAnotherMergedCell: '与其他合并单元格有冲突',
  invalidMergeProperties: '无效的合并属性',
  cellAlreadyMerged: '单元格已合并',
  noCellsSelected: '未选定单元格'
}

export const importCe = () =>
  Promise.all([import('jspreadsheet-ce/dist/jspreadsheet.css'), import('jsuites/dist/jsuites.css')])
    .then(() => import('jspreadsheet-ce'))
    .then(r => r.default)

/** 将 react 样式对象转换为 css 样式 */
function reactStyleToCss(style) {
  return _.map(style, (v, k) => (v ? `${_.kebabCase(k)}: ${v}` : ''))
    .filter(_.identity)
    .join(';')
}

// 添加冻结样式
const getFrozenStyle = ({
  frozenType,
  columnFocus,
  rowFocus,
  d,
  columns,
  rowHeight,
  backgroundColor,
  mergeInfo,
  customRowlen,
  showColumnBoxShadow
}: {
  frozenType?: FrozenType
  columnFocus?: number
  rowFocus?: number
  d: LuckysheetCellData
  columns: Column[]
  rowHeight: number
  backgroundColor?: string
  mergeInfo?: LuckysheetDataConfig['merge']
  customRowlen: Record<string, number>
  showColumnBoxShadow?: boolean
}) => {
  if (!frozenType || frozenType === 'cancel' || !d) {
    return {}
  }
  let style: Record<string, string | number> = {}
  const commonStyle = {
    position: 'sticky',
    backgroundColor: backgroundColor || '#ffffff',
    zIndex: 1
  }
  // 列冻结
  if (
    _.includes(['rangeBoth', 'rangeColumn', 'column', 'both'], frozenType) &&
    _.isNumber(columnFocus) &&
    columnFocus >= d.c
  ) {
    style = {
      ...commonStyle,
      boxShadow: showColumnBoxShadow && +columnFocus === +d.c ? '1px 0px 0px 0px #cccccc' : 'none',
      left: `${_.sumBy(_.take(columns, d.c), 'width')}px`
      // borderRight: +columnFocus === +d.c ? '1.5px solid #CCCCCC' : 'none'
    }
  }
  let r = d.r // 定义一个变量r存储行数
  if (mergeInfo) { // 如果有合并单元格配置
    // eslint-disable-next-line guard-for-in
    for (const key in mergeInfo) { // 遍历每个合并单元格
      const item = mergeInfo[key] // 获取合并单元格信息
      if (d.r >= item.r && d.r < item.r + item.rs && d.c >= item.c && d.c < item.c + item.cs) { // 如果当前单元格在合并单元格范围内
        r = item.r // 把r赋值为合并单元格的左上角单元格的行数
        break // 跳出循环
      }
    }
  }
  // 行冻结
  if (_.includes(['rangeBoth', 'rangeRow', 'row', 'both'], frozenType) && _.isNumber(rowFocus) && rowFocus >= d.r) {// 在后续逻辑中获取所有与当前列相等的列的高度加起来
    const top = _.sumBy(_.range(r), num => (customRowlen[num] || 19))
    style = {
      ...commonStyle,
      ...style,
      boxShadow: +rowFocus === +d.r ? `${+(columnFocus ?? 0) === +d.c ? 1 : 0}px 2px 0px 0px #cccccc` : style.boxShadow,
      zIndex: (columnFocus ?? 0) >= +d.c ? 4 : 2,
      borderBottom: +rowFocus === +d.r ? '1px solid #CCCCC' : 'none',
      top: `${r === 0 ? 0 : top}px`
    }
  }
  return style
}
// https://mengshukeji.gitee.io/LuckysheetDocs/zh/guide/cell.html#基本单元格
// fontfamily映射
const luckysheetFontfamilyMap = [
  'Times New Roman',     // 0
  'Arial',               // 1
  'Tahoma',              // 2
  'Verdana',             // 3
  '微软雅黑',             // 4
  '宋体',                 // 5
  '黑体',                 // 6
  '楷体',                 // 7
  '仿宋',                 // 8
  '新宋体',               // 9
  '华文新魏',             // 10
  '华文行楷',             // 11
  '华文隶书'             // 12
]

// https://dream-num.github.io/LuckysheetDocs/zh/guide/sheet.html#config-borderinfo
// 部分样式不支持，取接近的样式
const borderStyleDict = {
  0: 'none',
  1: '1px solid',
  2: '0.5px solid',
  3: '1px dotted',
  4: '1px dashed',
  5: '1px dashed',
  6: '1px dashed',
  7: '1px double',
  8: '2px solid',
  9: '2px dashed',
  10: '2px dotted',
  11: '2px dotted',
  12: '2px dotted',
  13: '3px solid'
}

/** 将 luckysheet 的 borderInfo 转换为 react 样式 */
function borderInfosToStyle(myBorderInfos: LuckysheetBorderInfo[], cell: LuckysheetCellData) {
  const { r: cellRow, c: cellCol } = cell
  const cellStyle: Partial<CSSStyleDeclaration> = {}
  for (let i = 0; i < myBorderInfos.length; i++) {
    const borderInfo = myBorderInfos[i]
    if (borderInfo.rangeType === 'cell') {
      const { value } = borderInfo
      const { t, r, b, l } = value
      cellStyle.borderTop = t && `${borderStyleDict[t.style]} ${t.color}`
      cellStyle.borderRight = r && `${borderStyleDict[r.style]} ${r.color}`
      cellStyle.borderBottom = b && `${borderStyleDict[b.style]} ${b.color}`
      cellStyle.borderLeft = l && `${borderStyleDict[l.style]} ${l.color}`
      continue
    }
    const { borderType, style, color, range } = borderInfo
    const {
      row: [fr, tr],
      column: [fc, tc]
    } = range[0]
    const borderStyleStr = `${borderStyleDict[style]} ${color}`
    if (/top|right|bottom|left/.test(borderType)) {
      cellStyle[_.kebabCase(borderType)] = borderStyleStr
    } else if (_.endsWith(borderType, 'all')) {
      // cellStyle.border = borderStyleStr
      if (cellRow === fr) {
        cellStyle.borderTop = borderStyleStr
      }
      if (cellCol === fc) {
        cellStyle.borderLeft = borderStyleStr
      }
      cellStyle.borderBottom = borderStyleStr
      cellStyle.borderRight = borderStyleStr
    } else if (_.endsWith(borderType, 'none')) {
      // cellStyle.border = 'none' 不直接用这个，用下面的，可以被其他样式覆盖
      cellStyle.borderTop = 'none'
      cellStyle.borderLeft = 'none'
      cellStyle.borderBottom = 'none'
      cellStyle.borderRight = 'none'
    } else if (_.endsWith(borderType, 'outside')) {
      if (cellRow === fr) {
        cellStyle.borderTop = borderStyleStr
      }
      if (cellRow === tr) {
        cellStyle.borderBottom = borderStyleStr
      }
      if (cellCol === fc) {
        cellStyle.borderLeft = borderStyleStr
      }
      if (cellCol === tc) {
        cellStyle.borderRight = borderStyleStr
      }
    } else if (_.endsWith(borderType, 'inside')) {
      if (cellRow > fr && cellRow < tr) {
        cellStyle.borderTop = borderStyleStr
        cellStyle.borderBottom = borderStyleStr
      }
      if (cellCol > fc && cellCol < tc) {
        cellStyle.borderLeft = borderStyleStr
        cellStyle.borderRight = borderStyleStr
      }
    } else if (_.endsWith(borderType, 'horizontal')) {
      if (cellRow > fr && cellRow < tr) {
        cellStyle.borderTop = borderStyleStr
        cellStyle.borderBottom = borderStyleStr
      }
    } else if (_.endsWith(borderType, 'vertical')) {
      if (cellCol > fc && cellCol < tc) {
        cellStyle.borderLeft = borderStyleStr
        cellStyle.borderRight = borderStyleStr
      }
    }
  }
  return cellStyle
}

/** 根据边框配置，生成单元格样式 */
function borderInfosToCells(borderInfos: LuckysheetBorderInfo[]) {
  return _.flatMap(borderInfos, borderInfo => {
    if (borderInfo.rangeType === 'cell') {
      const { row_index: r, col_index: c } = borderInfo.value
      return { r, c, s: borderInfosToStyle([borderInfo], { r, c, v: '' }) }
    }
    const { row, column } = borderInfo.range[0]
    const [fr, tr] = row
    const [fc, tc] = column
    const cells: { r: number; c: number; s: Partial<CSSStyleDeclaration> }[] = []
    for (let r = fr; r <= tr; r++) {
      for (let c = fc; c <= tc; c++) {
        cells.push({ r, c, s: borderInfosToStyle([borderInfo], { r, c, v: '' }) })
      }
    }
    return cells
  })
}

/** 将 luckysheet 的 单元格格式 转换为 d3-format 的 格式 */
function numeralJsPatternToD3Format(pattern: string): string {
  // 0 -> .0f
  // #,##0 -> ,.0f
  // #,##0.00 -> ,.2f
  // 0.00 -> .2f
  // #0.00% -> .2%
  // #,##0.00 -> ,.2f
  // #,##0.00;(#,##0.00) -> ,.2f
  // #,##0.00;[Red](#,##0.00) -> ,.2f

  const hasComma = pattern.includes(',')
  const hasPercent = pattern.includes('%')
  const precision = pattern.match(/\.(0+)/)?.[1]?.length || 0
  return `${hasComma ? ',' : ''}.${precision}${hasPercent ? '%' : 'f'}`
}

const numeralFormatMemo = _.memoize(p => d3Format(numeralJsPatternToD3Format(p)))
const generalFormatMemo = d3Format('.2~f')

/** 根据单元格格式，格式化单元格值 */
export function formatVal(cv: LuckysheetCellVal) {
  const { ct, v, m } = cv
  if (!Number.isFinite(+v)) {
    return _.isNil(m) ? v : m
  }
  // {t: 'n', fa: '#0.00%'}
  if (ct?.t === 'n' && ct.fa !== 'General') {
    return numeralFormatMemo(ct.fa)(v)
  }
  // 处理换行后的结果
  if (ct?.fa === 'General' && ct.s) {
    const [value] = ct.s
    return value.v
  }
  // General 时，如果是数字，保留两位小数
  if (ct?.fa === 'General') {
    return generalFormatMemo(v)
  }
  return _.isNil(m) ? v : m
}

/** 为 ce 报表 sheet 生成隐藏行列的 css */
function useCeTableHideColumnCss(
  rowhidden: Record<number, number> | undefined,
  colhidden: Record<number, number> | undefined
) {
  const tableCls = useMemo(() => escapeForCssSelector(`sheet-${nanoid(5)}`), [])
  const css = useDeepCompareMemo(
    () =>
      _.compact([
        // 隐藏行，通过 tr[data-x=20] {display: none} 实现
        ..._.map(rowhidden, (v, k) => (+v ? '' : `.${tableCls} .jexcel tbody tr[data-y="${k}"] {display: none}`)),
        // 隐藏列，通过 td[data-y=7] {display: none} 实现
        ..._.map(colhidden, (v, k) => (+v ? '' : `.${tableCls} .jexcel tbody td[data-x="${k}"] {display: none}`))
      ]).join('\n'),
    [rowhidden, colhidden]
  )
  return { tableCls, css }
}

/** 生成获取条件格式的函数 */
function genGetCondFormatFn(
  condFormatInfos: LuckysheetConditionFormat[] | undefined,
  allCells: LuckysheetCellData[] | undefined
) {
  const defaultCondFormats = _.filter(condFormatInfos, cf => cf.type === 'default')
  const predicateFns = _.map(defaultCondFormats, cf => {
    const { cellrange, conditionName, conditionValue } = cf
    const { row: rows, column: cols } = cellrange[0]
    const [fr, tr] = rows
    const [fc, tc] = cols
    const allCellsInZone = _.filter(allCells, cell0 => {
      const { r: cr, c: cc } = cell0
      return cr >= fr && cr <= tr && cc >= fc && cc <= tc
    })
    const allValsInZone = _.map(allCellsInZone, cell0 => (_.isObject(cell0.v) ? cell0.v.v : cell0.v))
    const sortedAllVals = _.sortBy(allValsInZone)
    const avg = _.sum(allValsInZone) / allValsInZone.length

    return (cell: LuckysheetCellData) => {
      const { r, c, v } = cell
      const val = _.isObject(v) ? v.v : v
      const inZone = r >= fr && r <= tr && c >= fc && c <= tc
      if (!inZone) {
        return false
      }
      const [a = 0, b = 0] = conditionValue || []
      switch (conditionName as string) {
        case 'lessThan':
          return val < a
        case 'greaterThan':
          return val > a
        case 'betweenness':
          return val >= a && val <= b
        case 'equal':
          return `${val}` === `${a}`
        case 'textContains':
          return `${val}`.includes(`${a}`)
        case 'duplicateValue':
          if (+a === 0) {
            // 判断重复值
            return _.filter(allValsInZone, x => x === val).length > 1
          }
          // 判断唯一值
          return _.filter(allValsInZone, x => x === val).length === 1
        case 'top10':
          return _.takeRight(sortedAllVals, a).includes(val)
        case 'top10%':
          return _.takeRight(sortedAllVals, Math.ceil((a * sortedAllVals.length) / 100)).includes(val)
        case 'last10':
          return _.take(sortedAllVals, a).includes(val)
        case 'last10%':
          return _.take(sortedAllVals, Math.ceil((a * sortedAllVals.length) / 100)).includes(val)
        case 'AboveAverage':
          return val > avg
        case 'SubAverage':
          return val < avg
        default:
          return false
      }
    }
  })

  return (cell: LuckysheetCellData) => {
    const condFormats = _.filter(defaultCondFormats, (_cf, i) => predicateFns[i](cell))
    return _.assign({}, ..._.map(condFormats, cf => cf.format))
  }
}

/** 将 luckysheet 的 sheet 转换为 ce 的配置 */
function convertLuckysheetSheetToCeConfig(luckysheetSheetConfig: LuckysheetConfig['data'][number]) {
  // https://dream-num.github.io/LuckysheetDocs/zh/guide/sheet.html#%E5%88%9D%E5%A7%8B%E5%8C%96%E9%85%8D%E7%BD%AE
  const {
    column = 18, row = 36, defaultColWidth = 73, config, celldata,
    hyperlink, frozen, luckysheet_conditionformat_save: condFormatInfos
  } = luckysheetSheetConfig
  const getCondFormat = genGetCondFormatFn(condFormatInfos || [], celldata || [])
  // 冻结配置
  const frozenType = frozen?.type
  const columnFocus = _.includes(['both', 'column'], frozenType) ? 0 : frozen?.range?.column_focus
  const rowFocus = _.includes(['both', 'row'], frozenType) ? 0 : frozen?.range?.row_focus

  const borderInfos = config?.borderInfo
  const customColumnlen = config?.columnlen || {}
  const customRowlen = config?.rowlen || {}

  const cellDataGroupByRow = _.groupBy(celldata, 'r')
  const cellDataGroupByColumn = _.groupBy(celldata, 'c')
  const cellsWithBorderStyle = borderInfosToCells(borderInfos || [])
  const cellsWithBorderStyleDict = _(cellsWithBorderStyle)
    .groupBy(encodeCell)
    .mapValues(cells => _.assign({}, ...cells.map(c => c.s)))
    .value()
  let extraCelldata: LuckysheetCellData[]
  if (!frozenType || (frozenType as any) === 'cancel') {
    extraCelldata = []
  } else {
    extraCelldata = [{ r: 0, c: 0, v: { v: '' } as LuckysheetCellVal }]
    _.forEach(cellDataGroupByRow, (_value, key) => {
      extraCelldata.push({ c: 0, r: +key, v: { v: '' } as LuckysheetCellVal })
    })
    _.forEach(cellDataGroupByColumn, (_value, key) => {
      extraCelldata.push({ r: 0, c: +key, v: { v: '' } as LuckysheetCellVal })
    })
    extraCelldata = _.differenceWith(
      extraCelldata,
      celldata || [],
      (obj1, obj2) => obj1.c === obj2.c && obj1.r === obj2.r
    )
  }

  const cellDataDict = _.keyBy([...(celldata ?? []), ...extraCelldata], encodeCell)
  const maxCol = _.maxBy(celldata, d => d.c)?.c || column
  const maxRow = _.maxBy(celldata, d => d.r)?.r || row

  const hasLinkColSet = new Set(
    _(hyperlink)
      .keys()
      .map(k => k.split('_')[1])
      .uniq()
      .value()
  )

  const columns: Column[] = _.range(maxCol + 1).map(i => ({
    type: hasLinkColSet.has(`${i}`) ? 'html' : ('text' as const),
    name: encodeColMemo(i),
    width: customColumnlen[i] ?? defaultColWidth
    // readOnly: true
  }))
  return {
    data: _.range(maxRow + 1).map(rowIndex => {
      const colDict = _.keyBy(cellDataGroupByRow[rowIndex], 'c')
      return _(colDict)
        .mapKeys((_v, k) => encodeColMemo(+k))
        .mapValues(d => {
          const linkInfo = hyperlink?.[`${d.r}_${d.c}`]
          const hyperLink = linkInfo?.linkAddress
          const v = d?.v
          const cellContent = !_.isObject(v) ? v || '' : formatVal(v)
          if (hyperLink) {
            const target = linkInfo._target
            const hint = linkInfo.linkTooltip
            return `<a href='${hyperLink}' target='${target}' title='${hint}'>${cellContent}</a>`
          }
          return cellContent
        })
        .value()
    }),
    columns,
    style: {
      // 没有单元格，仅有边框的格子的样式生成
      ..._.zipObject(
        _.map(cellsWithBorderStyle, encodeCell),
        _.map(cellsWithBorderStyle, cell => {
          const d = cellDataDict[encodeCell(cell)]
          return _.isObject(d?.v) ? '' : reactStyleToCss(cell.s)
        })
      ),
      // 单元格样式生成
      ..._.mapValues(cellDataDict, d => {
        const borderStyle = cellsWithBorderStyleDict[encodeCell(d)] || {}
        const v = d?.v
        const rowHeight = customRowlen[d.r] || 19
        if (!_.isObject(v)) {
          return reactStyleToCss(borderStyle)
        }
        const condFormat = getCondFormat(d)
        const ht = _.isNil(v.ht) ? 1 : +v.ht
        const vt = _.isNil(v.vt) ? 0 : +v.vt
        return reactStyleToCss({
          height: rowHeight === 19 ? undefined : `${rowHeight}px`,
          backgroundColor: condFormat?.cellColor || v.bg,
          color: condFormat?.textColor || v.fc,
          fontWeight: +(v.bl || 0) > 0 ? 'bold' : undefined,
          fontStyle: +(v.it || 0) > 0 ? 'italic' : undefined,
          fontFamily: luckysheetFontfamilyMap[v.ff || ''] || undefined,
          fontSize: `${v.fs || 10}pt`,
          textAlign: ht === 1 ? 'left' : ht === 0 ? 'center' : 'right',
          verticalAlign: vt === 1 ? 'top' : vt === 0 ? 'middle' : 'bottom',
          ...getFrozenStyle({
            frozenType, columnFocus, rowFocus, d, columns, rowHeight, backgroundColor: v.bg, mergeInfo: config?.merge, customRowlen,
            showColumnBoxShadow: !!borderStyle.borderRight
          }),
          textDecorationLine: (v.cl || 0) > 0 ? 'line-through' : undefined,
          ...borderStyle
        })
      })
    },
    // {A1: [2, 1],}
    mergeCells: _(config?.merge)
      .mapKeys(encodeCell)
      .mapValues(c => [c.cs, c.rs] as [number, number])
      .value()
  }
}

interface CESheetRendererProps {
  className?: string
  style?: React.CSSProperties
  sheetConfig: LuckysheetConfig['data'][number]
  showGridLines?: boolean | undefined
  autoWhiteSpace?: boolean | undefined
  enableAndDisableEditing?: boolean | undefined
  onLoaded?: () => any
}

/** 单 sheet 渲染组件 */
function CESheetRenderer(props: CESheetRendererProps) {
  const { sheetConfig, showGridLines, className, onLoaded, style, autoWhiteSpace, enableAndDisableEditing } = props
  const { data: ce } = useRequest(importCe, { cacheKey: 'import-ce' })
  const containerDomRef = useRef<HTMLDivElement | null>(null)
  const instRef = useRef<any>()
  const readOnly = true // 只读

  const onContainerRefChange = useDebounceFn((dom: HTMLDivElement | null) => {
    containerDomRef.current = dom

    if (ce && !instRef.current && dom) {
      const { columns, data, style: cellStyleDict, mergeCells } = convertLuckysheetSheetToCeConfig(sheetConfig)
      // 屏蔽 alert
      const originAlert = window.alert
      window.alert = console.log
      const inst = ce(dom, {
        data: _.isEmpty(data) ? [{}] : data, // 空数组会报错
        columns,
        editable: enableAndDisableEditing,
        style: cellStyleDict,
        mergeCells,
        minDimensions: readOnly ? [0, 0] : [8, 10],
        text: i18nDict,
        onload: onLoaded
      })
      inst.hideIndex()
      inst.resetSelection(true)
      instRef.current = inst

      window.alert = originAlert
    }
  }, { wait: 200 }).run

  // 🚀 [重构] 废弃销毁重建逻辑，改为增量更新
  useDeepCompareEffect(() => {
    const ceInst = instRef.current
    if (!ceInst) {
      return // 实例尚未创建，等待 onContainerRefChange
    }

    const startTime = performance.now()
    // if (process.env.NODE_ENV === 'development') {
    //   console.log('[CERenderer] Detected sheetConfig change, applying incremental update.')
    // }

    // 1. 重新计算配置
    const { columns, data, style: cellStyleDict, mergeCells } = convertLuckysheetSheetToCeConfig(sheetConfig)

    try {
      // 2. 调用 API 进行增量更新，而不是销毁重建
      // 注意：jspreadsheet 的 setData 会清空旧数据，所以需要先设置数据
      ceInst.setData(data)
      ceInst.setStyle(cellStyleDict)

      // 🎯 修复合并单元格错误：安全地设置合并单元格
      try {
        if (mergeCells && Object.keys(mergeCells).length > 0) {
          ceInst.setMerge(mergeCells, true) // 第二个参数 true 表示重置合并
        }
      } catch (mergeError) {
        if (process.env.NODE_ENV === 'development') {
          console.warn('[CERenderer] 合并单元格设置失败，跳过合并:', mergeError)
        }
        // 合并失败不应该影响整体渲染，继续执行
      }

      // columns 定义可能也需要更新，但 jspreadsheet-ce 似乎没有直接的列更新 API，
      // 如果列定义在轮播中会变化，这可能是个问题。此处假设它不变。
    } catch (error) {
      console.error('[CERenderer] Incremental update failed:', error)
      if (process.env.NODE_ENV === 'development') {
        console.error('错误详情:', error instanceof Error ? error.message : String(error))
        console.error('数据样本:', { dataLength: data.length, styleKeys: Object.keys(cellStyleDict).length })
      }
      // 如果更新失败，可以考虑记录日志，但不应回退到销毁重建模式
    }

    // 🎯 性能监控
    // const duration = performance.now() - startTime
    // if (process.env.NODE_ENV === 'development') {
    //   console.log(`[CERenderer] 增量更新完成，耗时 ${duration.toFixed(2)}ms`)
    // }
    // if (duration > 100) {
    //   console.warn(`[CERenderer] 更新耗时过长: ${duration.toFixed(2)}ms，建议检查数据量`)
    // }
  }, [sheetConfig]) // 依赖项依然是 sheetConfig

  const {
    tableCls: tableClsForHideColAndRow,
    css: tableExtraCss
  } = useCeTableHideColumnCss(sheetConfig.config?.rowhidden, sheetConfig.config?.colhidden)
  // 样式尽量跟 luckysheet 保持一致
  const sheetDom = <div
    className={classNames(
      // 打印的元素
      'print-luckysheet-table',
      className,
      'block [&_.jexcel\\_content]:!block',
      'text-xs [&_thead]:hidden [&_.jexcel_tbody_td]:h-5 [&_.jexcel_tbody_td]:leading-[10px]',
      {
        'readonly': readOnly,
        'ce-table-panel': true,
        // 冻结的时候需要设置border-collapse为separate，不然边框会丢失
        '[&_.jexcel]:border-collapse': (sheetConfig?.frozen as any)?.type !== 'cancel' || showGridLines === false,
        // 冻结的时候需要显示网格线，不然边框会丢失
        '[&_.jexcel_tbody_td]:border-none [&_.jexcel]:border-none': showGridLines === false,
        // 设置自动换行
        '[&_.jexcel_tbody_td]:whitespace-pre-wrap [&_.jexcel_tbody_td]:break-all': autoWhiteSpace,
        // 设置行高
        '[&_.jexcel_tbody_td]:leading-snug': autoWhiteSpace
      },
      tableClsForHideColAndRow
    )}
    style={style}
    ref={onContainerRefChange}
  />

  if (_.isEmpty(sheetConfig?.images)) {
    if (tableExtraCss) {
      return (
        <>
          <style>{tableExtraCss}</style>
          {sheetDom}
        </>
      )
    }
    return sheetDom
  }
  return (
    <div className='relative'>
      {tableExtraCss && <style>{tableExtraCss}</style>}
      {sheetDom}
      {/* 渲染图片 */}
      {_.map(sheetConfig?.images, (imgInfo: any, key) => {
        const { src } = imgInfo
        const { height, width, top, left } = imgInfo.default
        const imgStyle: React.CSSProperties = {
          position: 'absolute',
          top: `${top}px`,
          left: `${left}px`,
          width: `${width}px`,
          height: `${height}px`
          // zIndex: zIndex || 0
        }
        return <img key={key} src={src} style={imgStyle} alt='插图' />
      })}
    </div>
  )
}

interface CERendererProps {
  componentKey: ComponentKey
  luckysheetCompConfig: LuckysheetCompConfig

  inflatedLuckysheetConfig: Partial<LuckysheetConfig>
}

/**
 * ce 渲染器，没有完全适配 luckysheet，但是渲染速度较快，适合打印
 * 目前只适配了合并单元格，边框，背景色
 */
export function CERenderer(props: CERendererProps) {
  const { luckysheetCompConfig, inflatedLuckysheetConfig } = props

  const onLoaded = (inflatedLuckysheetConfig.hooks as any)?.workbookCreateAfter || _.noop
  const {
    pagePadding, printMode, page,
    autoWhiteSpace, showGridLines, enableAndDisableEditing
  } = luckysheetCompConfig

  // 事件统一用 luckysheet 的回调
  const { run: onLoadedDebounced } = useDebounceFn(() => onLoaded?.(inflatedLuckysheetConfig), { wait: 500 })

  if (printMode === 'reportPrint') {
    return (
      <div className='h-full w-full overflow-auto'>
        {_.map(inflatedLuckysheetConfig.data, (d, i) => (
          <CESheetRenderer
            key={i}
            style={{ padding: `${pagePadding || 0}px 0px`, display: i === ((page || 1) - 1) ? 'block' : 'none' }}
            sheetConfig={d}
            enableAndDisableEditing={enableAndDisableEditing}
            showGridLines={showGridLines}
            autoWhiteSpace={autoWhiteSpace}
            onLoaded={onLoadedDebounced}
          />
        ))}
      </div>
    )
  }

  if (printMode !== 'printing' || (page || 0) > 0) {
    const currSheet = getActiveSheet(inflatedLuckysheetConfig)

    if (!currSheet) return null

    return (
      <CESheetRenderer
        className={classNames('h-full w-full', {
          // 打印时屏蔽滚动条
          'overflow-hidden': printMode === 'printing',
          'overflow-auto': printMode !== 'printing'
        })}
        style={{ padding: `${pagePadding || 0}px 0px` }}
        sheetConfig={currSheet}
        enableAndDisableEditing={enableAndDisableEditing}
        autoWhiteSpace={autoWhiteSpace}
        showGridLines={showGridLines}
        onLoaded={onLoadedDebounced}
      />
    )
  }

  return (
    <div className='h-full w-full overflow-auto'>
      {_.map(inflatedLuckysheetConfig.data, (d, i) => (
        <CESheetRenderer
          key={i}
          style={{ padding: `${pagePadding || 0}px 0px` }}
          sheetConfig={d}
          enableAndDisableEditing={enableAndDisableEditing}
          showGridLines={showGridLines}
          autoWhiteSpace={autoWhiteSpace}
          onLoaded={onLoadedDebounced}
        />
      ))}
    </div>
  )
}
