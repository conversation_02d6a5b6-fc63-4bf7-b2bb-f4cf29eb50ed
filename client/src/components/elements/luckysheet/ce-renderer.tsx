import { useDebounceFn, useDeepCompareEffect, useRequest } from 'ahooks'
import classNames from 'classnames'
import { format as d3Format } from 'd3-format'
import type { Column } from 'jspreadsheet-ce'
import _ from 'lodash'
import { nanoid } from 'nanoid'
import * as React from 'react'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useDeepCompareMemo } from 'use-deep-compare'

import { LuckysheetCompConfig } from '@/components/elements/luckysheet/types'
import { encodeCell, encodeColMemo, getActiveSheet } from '@/components/elements/luckysheet/utils'
import type { ComponentKey } from '@/types/editor-core/component'
import type { FrozenType, LuckysheetBorderInfo, LuckysheetCellData, LuckysheetCellVal, LuckysheetConditionFormat, LuckysheetConfig, LuckysheetDataConfig } from '@/types/editor-core/config'
import { escapeForCssSelector } from '@/utils/dom'


/** 中文翻译 https://github.com/jspreadsheet/ce/blob/master/resources/lang/zh_CN.json */
export const i18nDict = {
  noRecordsFound: '未找到',
  showingPage: '显示 {1} 条中的第 {0} 条',
  show: '显示 ',
  search: '搜索',
  entries: ' 条目',
  columnName: '列标题',
  insertANewColumnBefore: '在此前插入列',
  insertANewColumnAfter: '在此后插入列',
  deleteSelectedColumns: '删除选定列',
  renameThisColumn: '重命名列',
  orderAscending: '升序',
  orderDescending: '降序',
  insertANewRowBefore: '在此前插入行',
  insertANewRowAfter: '在此后插入行',
  deleteSelectedRows: '删除选定行',
  editComments: '编辑批注',
  addComments: '插入批注',
  comments: '批注',
  clearComments: '删除批注',
  copy: '复制...',
  paste: '粘贴...',
  saveAs: '保存为...',
  about: '关于',
  areYouSureToDeleteTheSelectedRows: '确定删除选定行?',
  areYouSureToDeleteTheSelectedColumns: '确定删除选定列?',
  thisActionWillDestroyAnyExistingMergedCellsAreYouSure: '这一操作会破坏所有现存的合并单元格，确认操作？',
  thisActionWillClearYourSearchResultsAreYouSure: '这一操作会清空搜索结果，确认操作？',
  thereIsAConflictWithAnotherMergedCell: '与其他合并单元格有冲突',
  invalidMergeProperties: '无效的合并属性',
  cellAlreadyMerged: '单元格已合并',
  noCellsSelected: '未选定单元格'
}

export const importCe = async () => {
  try {
    // 并行加载样式文件，但允许部分失败
    const stylePromises = [
      import('jspreadsheet-ce/dist/jspreadsheet.css').catch(err => {
        console.warn('加载 jspreadsheet.css 失败:', err)
        return null
      }),
      import('jsuites/dist/jsuites.css').catch(err => {
        console.warn('加载 jsuites.css 失败:', err)
        return null
      })
    ]

    await Promise.allSettled(stylePromises)

    // 加载核心库
    const ceModule = await import('jspreadsheet-ce')
    return ceModule.default
  } catch (error) {
    console.error('加载 jspreadsheet-ce 失败:', error)
    throw new Error('表格组件加载失败，请检查网络连接')
  }
}

/** 将 react 样式对象转换为 css 样式 */
function reactStyleToCss(style) {
  return _.map(style, (v, k) => (v ? `${_.kebabCase(k)}: ${v}` : ''))
    .filter(_.identity)
    .join(';')
}

// 添加冻结样式
const getFrozenStyle = ({
  frozenType,
  columnFocus,
  rowFocus,
  d,
  columns,
  rowHeight,
  backgroundColor,
  mergeInfo,
  customRowlen,
  showColumnBoxShadow
}: {
  frozenType?: FrozenType
  columnFocus?: number
  rowFocus?: number
  d: LuckysheetCellData
  columns: Column[]
  rowHeight: number
  backgroundColor?: string
  mergeInfo?: LuckysheetDataConfig['merge']
  customRowlen: Record<string, number>
  showColumnBoxShadow?: boolean
}) => {
  if (!frozenType || frozenType === 'cancel' || !d) {
    return {}
  }
  let style: Record<string, string | number> = {}
  const commonStyle = {
    position: 'sticky',
    backgroundColor: backgroundColor || '#ffffff',
    zIndex: 1
  }
  // 列冻结
  if (
    _.includes(['rangeBoth', 'rangeColumn', 'column', 'both'], frozenType) &&
    _.isNumber(columnFocus) &&
    columnFocus >= d.c
  ) {
    style = {
      ...commonStyle,
      boxShadow: showColumnBoxShadow && +columnFocus === +d.c ? '1px 0px 0px 0px #cccccc' : 'none',
      left: `${_.sumBy(_.take(columns, d.c), 'width')}px`
      // borderRight: +columnFocus === +d.c ? '1.5px solid #CCCCCC' : 'none'
    }
  }
  let r = d.r // 定义一个变量r存储行数
  if (mergeInfo) { // 如果有合并单元格配置
    // eslint-disable-next-line guard-for-in
    for (const key in mergeInfo) { // 遍历每个合并单元格
      const item = mergeInfo[key] // 获取合并单元格信息
      if (d.r >= item.r && d.r < item.r + item.rs && d.c >= item.c && d.c < item.c + item.cs) { // 如果当前单元格在合并单元格范围内
        r = item.r // 把r赋值为合并单元格的左上角单元格的行数
        break // 跳出循环
      }
    }
  }
  // 行冻结
  if (_.includes(['rangeBoth', 'rangeRow', 'row', 'both'], frozenType) && _.isNumber(rowFocus) && rowFocus >= d.r) {// 在后续逻辑中获取所有与当前列相等的列的高度加起来
    const top = _.sumBy(_.range(r), num => (customRowlen[num] || 19))
    style = {
      ...commonStyle,
      ...style,
      boxShadow: +rowFocus === +d.r ? `${+(columnFocus ?? 0) === +d.c ? 1 : 0}px 2px 0px 0px #cccccc` : style.boxShadow,
      zIndex: (columnFocus ?? 0) >= +d.c ? 4 : 2,
      borderBottom: +rowFocus === +d.r ? '1px solid #CCCCC' : 'none',
      top: `${r === 0 ? 0 : top}px`
    }
  }
  return style
}
// https://mengshukeji.gitee.io/LuckysheetDocs/zh/guide/cell.html#基本单元格
// fontfamily映射
const luckysheetFontfamilyMap = [
  'Times New Roman',     // 0
  'Arial',               // 1
  'Tahoma',              // 2
  'Verdana',             // 3
  '微软雅黑',             // 4
  '宋体',                 // 5
  '黑体',                 // 6
  '楷体',                 // 7
  '仿宋',                 // 8
  '新宋体',               // 9
  '华文新魏',             // 10
  '华文行楷',             // 11
  '华文隶书'             // 12
]

// https://dream-num.github.io/LuckysheetDocs/zh/guide/sheet.html#config-borderinfo
// 部分样式不支持，取接近的样式
const borderStyleDict = {
  0: 'none',
  1: '1px solid',
  2: '0.5px solid',
  3: '1px dotted',
  4: '1px dashed',
  5: '1px dashed',
  6: '1px dashed',
  7: '1px double',
  8: '2px solid',
  9: '2px dashed',
  10: '2px dotted',
  11: '2px dotted',
  12: '2px dotted',
  13: '3px solid'
}

/** 将 luckysheet 的 borderInfo 转换为 react 样式 */
function borderInfosToStyle(myBorderInfos: LuckysheetBorderInfo[], cell: LuckysheetCellData) {
  const { r: cellRow, c: cellCol } = cell
  const cellStyle: Partial<CSSStyleDeclaration> = {}
  for (let i = 0; i < myBorderInfos.length; i++) {
    const borderInfo = myBorderInfos[i]
    if (borderInfo.rangeType === 'cell') {
      const { value } = borderInfo
      const { t, r, b, l } = value
      cellStyle.borderTop = t && `${borderStyleDict[t.style]} ${t.color}`
      cellStyle.borderRight = r && `${borderStyleDict[r.style]} ${r.color}`
      cellStyle.borderBottom = b && `${borderStyleDict[b.style]} ${b.color}`
      cellStyle.borderLeft = l && `${borderStyleDict[l.style]} ${l.color}`
      continue
    }
    const { borderType, style, color, range } = borderInfo
    const {
      row: [fr, tr],
      column: [fc, tc]
    } = range[0]
    const borderStyleStr = `${borderStyleDict[style]} ${color}`
    if (/top|right|bottom|left/.test(borderType)) {
      cellStyle[_.kebabCase(borderType)] = borderStyleStr
    } else if (_.endsWith(borderType, 'all')) {
      // cellStyle.border = borderStyleStr
      if (cellRow === fr) {
        cellStyle.borderTop = borderStyleStr
      }
      if (cellCol === fc) {
        cellStyle.borderLeft = borderStyleStr
      }
      cellStyle.borderBottom = borderStyleStr
      cellStyle.borderRight = borderStyleStr
    } else if (_.endsWith(borderType, 'none')) {
      // cellStyle.border = 'none' 不直接用这个，用下面的，可以被其他样式覆盖
      cellStyle.borderTop = 'none'
      cellStyle.borderLeft = 'none'
      cellStyle.borderBottom = 'none'
      cellStyle.borderRight = 'none'
    } else if (_.endsWith(borderType, 'outside')) {
      if (cellRow === fr) {
        cellStyle.borderTop = borderStyleStr
      }
      if (cellRow === tr) {
        cellStyle.borderBottom = borderStyleStr
      }
      if (cellCol === fc) {
        cellStyle.borderLeft = borderStyleStr
      }
      if (cellCol === tc) {
        cellStyle.borderRight = borderStyleStr
      }
    } else if (_.endsWith(borderType, 'inside')) {
      if (cellRow > fr && cellRow < tr) {
        cellStyle.borderTop = borderStyleStr
        cellStyle.borderBottom = borderStyleStr
      }
      if (cellCol > fc && cellCol < tc) {
        cellStyle.borderLeft = borderStyleStr
        cellStyle.borderRight = borderStyleStr
      }
    } else if (_.endsWith(borderType, 'horizontal')) {
      if (cellRow > fr && cellRow < tr) {
        cellStyle.borderTop = borderStyleStr
        cellStyle.borderBottom = borderStyleStr
      }
    } else if (_.endsWith(borderType, 'vertical')) {
      if (cellCol > fc && cellCol < tc) {
        cellStyle.borderLeft = borderStyleStr
        cellStyle.borderRight = borderStyleStr
      }
    }
  }
  return cellStyle
}

/** 根据边框配置，生成单元格样式 */
function borderInfosToCells(borderInfos: LuckysheetBorderInfo[]) {
  return _.flatMap(borderInfos, borderInfo => {
    if (borderInfo.rangeType === 'cell') {
      const { row_index: r, col_index: c } = borderInfo.value
      return { r, c, s: borderInfosToStyle([borderInfo], { r, c, v: '' }) }
    }
    const { row, column } = borderInfo.range[0]
    const [fr, tr] = row
    const [fc, tc] = column
    const cells: { r: number; c: number; s: Partial<CSSStyleDeclaration> }[] = []
    for (let r = fr; r <= tr; r++) {
      for (let c = fc; c <= tc; c++) {
        cells.push({ r, c, s: borderInfosToStyle([borderInfo], { r, c, v: '' }) })
      }
    }
    return cells
  })
}

/** 将 luckysheet 的 单元格格式 转换为 d3-format 的 格式 */
function numeralJsPatternToD3Format(pattern: string): string {
  // 0 -> .0f
  // #,##0 -> ,.0f
  // #,##0.00 -> ,.2f
  // 0.00 -> .2f
  // #0.00% -> .2%
  // #,##0.00 -> ,.2f
  // #,##0.00;(#,##0.00) -> ,.2f
  // #,##0.00;[Red](#,##0.00) -> ,.2f

  const hasComma = pattern.includes(',')
  const hasPercent = pattern.includes('%')
  const precision = pattern.match(/\.(0+)/)?.[1]?.length || 0
  return `${hasComma ? ',' : ''}.${precision}${hasPercent ? '%' : 'f'}`
}

const numeralFormatMemo = _.memoize(p => d3Format(numeralJsPatternToD3Format(p)))
const generalFormatMemo = d3Format('.2~f')

/** 根据单元格格式，格式化单元格值 */
export function formatVal(cv: LuckysheetCellVal) {
  const { ct, v, m } = cv
  if (!Number.isFinite(+v)) {
    return _.isNil(m) ? v : m
  }
  // {t: 'n', fa: '#0.00%'}
  if (ct?.t === 'n' && ct.fa !== 'General') {
    return numeralFormatMemo(ct.fa)(v)
  }
  // 处理换行后的结果
  if (ct?.fa === 'General' && ct.s) {
    const [value] = ct.s
    return value.v
  }
  // General 时，如果是数字，保留两位小数
  if (ct?.fa === 'General') {
    return generalFormatMemo(v)
  }
  return _.isNil(m) ? v : m
}

/** 为 ce 报表 sheet 生成隐藏行列的 css */
function useCeTableHideColumnCss(
  rowhidden: Record<number, number> | undefined,
  colhidden: Record<number, number> | undefined
) {
  const tableCls = useMemo(() => escapeForCssSelector(`sheet-${nanoid(5)}`), [])
  const css = useDeepCompareMemo(
    () =>
      _.compact([
        // 隐藏行，通过 tr[data-x=20] {display: none} 实现
        ..._.map(rowhidden, (v, k) => (+v ? '' : `.${tableCls} .jexcel tbody tr[data-y="${k}"] {display: none}`)),
        // 隐藏列，通过 td[data-y=7] {display: none} 实现
        ..._.map(colhidden, (v, k) => (+v ? '' : `.${tableCls} .jexcel tbody td[data-x="${k}"] {display: none}`))
      ]).join('\n'),
    [rowhidden, colhidden]
  )
  return { tableCls, css }
}

/** 生成获取条件格式的函数 */
function genGetCondFormatFn(
  condFormatInfos: LuckysheetConditionFormat[] | undefined,
  allCells: LuckysheetCellData[] | undefined
) {
  const defaultCondFormats = _.filter(condFormatInfos, cf => cf.type === 'default')
  const predicateFns = _.map(defaultCondFormats, cf => {
    const { cellrange, conditionName, conditionValue } = cf
    const { row: rows, column: cols } = cellrange[0]
    const [fr, tr] = rows
    const [fc, tc] = cols
    const allCellsInZone = _.filter(allCells, cell0 => {
      const { r: cr, c: cc } = cell0
      return cr >= fr && cr <= tr && cc >= fc && cc <= tc
    })
    const allValsInZone = _.map(allCellsInZone, cell0 => (_.isObject(cell0.v) ? cell0.v.v : cell0.v))
    const sortedAllVals = _.sortBy(allValsInZone)
    const avg = _.sum(allValsInZone) / allValsInZone.length

    return (cell: LuckysheetCellData) => {
      const { r, c, v } = cell
      const val = _.isObject(v) ? v.v : v
      const inZone = r >= fr && r <= tr && c >= fc && c <= tc
      if (!inZone) {
        return false
      }
      const [a = 0, b = 0] = conditionValue || []
      switch (conditionName as string) {
        case 'lessThan':
          return val < a
        case 'greaterThan':
          return val > a
        case 'betweenness':
          return val >= a && val <= b
        case 'equal':
          return `${val}` === `${a}`
        case 'textContains':
          return `${val}`.includes(`${a}`)
        case 'duplicateValue':
          if (+a === 0) {
            // 判断重复值
            return _.filter(allValsInZone, x => x === val).length > 1
          }
          // 判断唯一值
          return _.filter(allValsInZone, x => x === val).length === 1
        case 'top10':
          return _.takeRight(sortedAllVals, a).includes(val)
        case 'top10%':
          return _.takeRight(sortedAllVals, Math.ceil((a * sortedAllVals.length) / 100)).includes(val)
        case 'last10':
          return _.take(sortedAllVals, a).includes(val)
        case 'last10%':
          return _.take(sortedAllVals, Math.ceil((a * sortedAllVals.length) / 100)).includes(val)
        case 'AboveAverage':
          return val > avg
        case 'SubAverage':
          return val < avg
        default:
          return false
      }
    }
  })

  return (cell: LuckysheetCellData) => {
    const condFormats = _.filter(defaultCondFormats, (_cf, i) => predicateFns[i](cell))
    return _.assign({}, ..._.map(condFormats, cf => cf.format))
  }
}

/** 将 luckysheet 的 sheet 转换为 ce 的配置 */
function convertLuckysheetSheetToCeConfig(luckysheetSheetConfig: LuckysheetConfig['data'][number]) {
  // https://dream-num.github.io/LuckysheetDocs/zh/guide/sheet.html#%E5%88%9D%E5%A7%8B%E5%8C%96%E9%85%8D%E7%BD%AE
  const {
    column = 18, row = 36, defaultColWidth = 73, config, celldata,
    hyperlink, frozen, luckysheet_conditionformat_save: condFormatInfos
  } = luckysheetSheetConfig
  const getCondFormat = genGetCondFormatFn(condFormatInfos || [], celldata || [])
  // 冻结配置
  const frozenType = frozen?.type
  const columnFocus = _.includes(['both', 'column'], frozenType) ? 0 : frozen?.range?.column_focus
  const rowFocus = _.includes(['both', 'row'], frozenType) ? 0 : frozen?.range?.row_focus

  const borderInfos = config?.borderInfo
  const customColumnlen = config?.columnlen || {}
  const customRowlen = config?.rowlen || {}

  const cellDataGroupByRow = _.groupBy(celldata, 'r')
  const cellDataGroupByColumn = _.groupBy(celldata, 'c')
  const cellsWithBorderStyle = borderInfosToCells(borderInfos || [])
  const cellsWithBorderStyleDict = _(cellsWithBorderStyle)
    .groupBy(encodeCell)
    .mapValues(cells => _.assign({}, ...cells.map(c => c.s)))
    .value()
  let extraCelldata: LuckysheetCellData[]
  if (!frozenType || (frozenType as any) === 'cancel') {
    extraCelldata = []
  } else {
    extraCelldata = [{ r: 0, c: 0, v: { v: '' } as LuckysheetCellVal }]
    _.forEach(cellDataGroupByRow, (_value, key) => {
      extraCelldata.push({ c: 0, r: +key, v: { v: '' } as LuckysheetCellVal })
    })
    _.forEach(cellDataGroupByColumn, (_value, key) => {
      extraCelldata.push({ r: 0, c: +key, v: { v: '' } as LuckysheetCellVal })
    })
    extraCelldata = _.differenceWith(
      extraCelldata,
      celldata || [],
      (obj1, obj2) => obj1.c === obj2.c && obj1.r === obj2.r
    )
  }

  const cellDataDict = _.keyBy([...(celldata ?? []), ...extraCelldata], encodeCell)
  const maxCol = _.maxBy(celldata, d => d.c)?.c || column
  const maxRow = _.maxBy(celldata, d => d.r)?.r || row

  const hasLinkColSet = new Set(
    _(hyperlink)
      .keys()
      .map(k => k.split('_')[1])
      .uniq()
      .value()
  )

  const columns: Column[] = _.range(maxCol + 1).map(i => ({
    type: hasLinkColSet.has(`${i}`) ? 'html' : ('text' as const),
    name: encodeColMemo(i),
    width: customColumnlen[i] ?? defaultColWidth
    // readOnly: true
  }))
  return {
    data: _.range(maxRow + 1).map(rowIndex => {
      const colDict = _.keyBy(cellDataGroupByRow[rowIndex], 'c')
      return _(colDict)
        .mapKeys((_v, k) => encodeColMemo(+k))
        .mapValues(d => {
          const linkInfo = hyperlink?.[`${d.r}_${d.c}`]
          const hyperLink = linkInfo?.linkAddress
          const v = d?.v
          const cellContent = !_.isObject(v) ? v || '' : formatVal(v)
          if (hyperLink) {
            const target = linkInfo._target
            const hint = linkInfo.linkTooltip
            return `<a href='${hyperLink}' target='${target}' title='${hint}'>${cellContent}</a>`
          }
          return cellContent
        })
        .value()
    }),
    columns,
    style: {
      // 没有单元格，仅有边框的格子的样式生成
      ..._.zipObject(
        _.map(cellsWithBorderStyle, encodeCell),
        _.map(cellsWithBorderStyle, cell => {
          const d = cellDataDict[encodeCell(cell)]
          return _.isObject(d?.v) ? '' : reactStyleToCss(cell.s)
        })
      ),
      // 单元格样式生成
      ..._.mapValues(cellDataDict, d => {
        const borderStyle = cellsWithBorderStyleDict[encodeCell(d)] || {}
        const v = d?.v
        const rowHeight = customRowlen[d.r] || 19
        if (!_.isObject(v)) {
          return reactStyleToCss(borderStyle)
        }
        const condFormat = getCondFormat(d)
        const ht = _.isNil(v.ht) ? 1 : +v.ht
        const vt = _.isNil(v.vt) ? 0 : +v.vt
        return reactStyleToCss({
          height: rowHeight === 19 ? undefined : `${rowHeight}px`,
          backgroundColor: condFormat?.cellColor || v.bg,
          color: condFormat?.textColor || v.fc,
          fontWeight: +(v.bl || 0) > 0 ? 'bold' : undefined,
          fontStyle: +(v.it || 0) > 0 ? 'italic' : undefined,
          fontFamily: luckysheetFontfamilyMap[v.ff || ''] || undefined,
          fontSize: `${v.fs || 10}pt`,
          textAlign: ht === 1 ? 'left' : ht === 0 ? 'center' : 'right',
          verticalAlign: vt === 1 ? 'top' : vt === 0 ? 'middle' : 'bottom',
          ...getFrozenStyle({
            frozenType, columnFocus, rowFocus, d, columns, rowHeight, backgroundColor: v.bg, mergeInfo: config?.merge, customRowlen,
            showColumnBoxShadow: !!borderStyle.borderRight
          }),
          textDecorationLine: (v.cl || 0) > 0 ? 'line-through' : undefined,
          ...borderStyle
        })
      })
    },
    // {A1: [2, 1],}
    mergeCells: _(config?.merge)
      .mapKeys(encodeCell)
      .mapValues(c => [c.cs, c.rs] as [number, number])
      .value()
  }
}

// 性能优化：使用 useMemo 缓存配置转换结果
function useMemoizedCeConfig(sheetConfig: LuckysheetConfig['data'][number]) {
  return useMemo(() => {
    return convertLuckysheetSheetToCeConfig(sheetConfig)
  }, [sheetConfig])
}

interface CESheetRendererProps {
  className?: string
  style?: React.CSSProperties
  sheetConfig: LuckysheetConfig['data'][number]
  showGridLines?: boolean | undefined
  autoWhiteSpace?: boolean | undefined
  enableAndDisableEditing?: boolean | undefined
  onLoaded?: () => any
}

/** 错误边界组件 */
class CERendererErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error; retryCount: number }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props)
    this.state = { hasError: false, retryCount: 0 }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('[CERenderer] 组件错误:', error, errorInfo)
  }

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: undefined,
      retryCount: prevState.retryCount + 1
    }))
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex items-center justify-center h-32 text-red-500">
          <div className="text-center">
            <div>表格渲染出错</div>
            <button
              className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              onClick={this.handleRetry}
            >
              重试 ({this.state.retryCount + 1})
            </button>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// 添加超时处理的 Hook
function useTimeout(callback: () => void, delay: number | null) {
  const savedCallback = useRef(callback)

  useEffect(() => {
    savedCallback.current = callback
  }, [callback])

  useEffect(() => {
    if (delay === null) return

    const id = setTimeout(() => savedCallback.current(), delay)
    return () => clearTimeout(id)
  }, [delay])
}

/** 单 sheet 渲染组件 */
function CESheetRenderer(props: CESheetRendererProps) {
  const { sheetConfig, showGridLines, className, onLoaded, style, autoWhiteSpace, enableAndDisableEditing } = props
  const { data: ce, loading: ceLoading, error: ceError } = useRequest(importCe, {
    cacheKey: 'import-ce',
    retryCount: 3,
    retryInterval: 1000
  })
  const containerDomRef = useRef<HTMLDivElement | null>(null)
  const instRef = useRef<any>()
  const [isInitialized, setIsInitialized] = useState(false)
  const [initError, setInitError] = useState<Error | null>(null)
  const [isTimeout, setIsTimeout] = useState(false)
  const abortControllerRef = useRef<AbortController | null>(null)
  const readOnly = true // 只读

  // 性能优化：使用缓存的配置
  const ceConfig = useMemoizedCeConfig(sheetConfig)

  // 超时处理
  useTimeout(() => {
    if (ceLoading && !isInitialized) {
      setIsTimeout(true)
    }
  }, ceLoading ? 10000 : null) // 10秒超时

  // 清理函数 - 防止内存泄漏
  useEffect(() => {
    return () => {
      // 取消正在进行的异步操作
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      if (instRef.current) {
        try {
          instRef.current.destroy?.()
        } catch (error) {
          console.warn('[CERenderer] 清理实例时出错:', error)
        }
        instRef.current = null
      }
      setIsInitialized(false)
      setInitError(null)
      setIsTimeout(false)
    }
  }, [])

  // 初始化逻辑 - 修复竞态条件和依赖项问题
  // 数据变化应该通过增量更新处理，而不是重新初始化
  useEffect(() => {
    if (!ce || !containerDomRef.current || instRef.current || ceLoading || ceError) {
      return
    }

    // 重置超时状态
    setIsTimeout(false)

    // 创建新的 AbortController
    abortControllerRef.current = new AbortController()
    const { signal } = abortControllerRef.current

    const initializeInstance = async () => {
      if (!containerDomRef.current || signal.aborted) return

      try {
        // 修复：初始化时使用实时配置，确保配置一致性
        // 避免使用缓存配置可能导致的配置不同步问题
        const { columns, data, style: cellStyleDict, mergeCells } = convertLuckysheetSheetToCeConfig(sheetConfig)

        // 检查是否已被取消
        if (signal.aborted) return

        // 使用更安全的方式处理全局状态
        const originalAlert = window.alert
        let alertRestored = false

        const restoreAlert = () => {
          if (!alertRestored) {
            window.alert = originalAlert
            alertRestored = true
          }
        }

        try {
          window.alert = console.log

          const inst = ce(containerDomRef.current, {
            data: _.isEmpty(data) ? [{}] : data, // 空数组会报错
            columns,
            editable: enableAndDisableEditing,
            style: cellStyleDict,
            mergeCells,
            minDimensions: readOnly ? [0, 0] : [8, 10],
            text: i18nDict,
            onload: () => {
              if (!signal.aborted) {
                setIsInitialized(true)
                setInitError(null)
                setIsTimeout(false)
                onLoaded?.()
              }
            }
          })

          if (signal.aborted) {
            inst.destroy?.()
            return
          }

          inst.hideIndex()
          inst.resetSelection(true)
          instRef.current = inst
        } finally {
          restoreAlert()
        }
      } catch (error) {
        if (!signal.aborted) {
          console.error('[CERenderer] 初始化失败:', error)
          setInitError(error instanceof Error ? error : new Error(String(error)))
        }
      }
    }

    // 使用 requestAnimationFrame 替代 setTimeout
    const frameId = requestAnimationFrame(initializeInstance)

    return () => {
      cancelAnimationFrame(frameId)
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [ce, ceLoading, ceError, enableAndDisableEditing]) // 修复：移除 sheetConfig 依赖项，避免与增量更新逻辑冲突

  const onContainerRefChange = useCallback((dom: HTMLDivElement | null) => {
    containerDomRef.current = dom
  }, [])

  // 增量更新逻辑 - 修复依赖项和取消机制
  useDeepCompareEffect(() => {
    const ceInst = instRef.current
    if (!ceInst || !isInitialized) {
      return // 实例尚未创建或初始化，等待初始化完成
    }

    // 创建新的 AbortController 用于取消之前的更新
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    abortControllerRef.current = new AbortController()
    const { signal } = abortControllerRef.current

    const updateInstance = async () => {
      try {
        if (signal.aborted) return

        const startTime = performance.now()
        const { data, style: cellStyleDict, mergeCells } = ceConfig

        if (signal.aborted) return

        // 使用 requestAnimationFrame 避免阻塞 UI
        await new Promise(resolve => {
          if (signal.aborted) {
            resolve(null)
            return
          }
          requestAnimationFrame(resolve)
        })

        if (signal.aborted) return

        // 调用 API 进行增量更新，而不是销毁重建
        ceInst.setData(data)
        ceInst.setStyle(cellStyleDict)

        // 安全地设置合并单元格
        if (mergeCells && Object.keys(mergeCells).length > 0) {
          try {
            ceInst.setMerge(mergeCells, true) // 第二个参数 true 表示重置合并
          } catch (mergeError) {
            console.warn('[CERenderer] 合并单元格设置失败:', mergeError)
          }
        }

        const duration = performance.now() - startTime
        if (duration > 100) {
          console.warn(`[CERenderer] 更新耗时过长: ${duration.toFixed(2)}ms，建议检查数据量`)
        }
      } catch (error) {
        if (!signal.aborted) {
          console.error('[CERenderer] 增量更新失败:', error)
          if (process.env.NODE_ENV === 'development') {
            console.error('错误详情:', error instanceof Error ? error.message : String(error))
          }
        }
      }
    }

    updateInstance()
  }, [sheetConfig, isInitialized])

  // 错误状态处理 - 添加重试机制
  if (ceError || initError) {
    return (
      <div className="flex items-center justify-center h-32 text-red-500">
        <div className="text-center">
          <div>加载表格组件失败</div>
          <button
            className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            onClick={() => {
              setInitError(null)
              setIsInitialized(false)
              setIsTimeout(false)
              if (instRef.current) {
                instRef.current.destroy?.()
                instRef.current = null
              }
            }}
          >
            重试
          </button>
        </div>
      </div>
    )
  }

  // 超时状态处理
  if (isTimeout) {
    return (
      <div className="flex items-center justify-center h-32 text-yellow-600">
        <div className="text-center">
          <div>加载超时，请检查网络连接</div>
          <button
            className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            onClick={() => {
              setIsTimeout(false)
              setInitError(null)
              setIsInitialized(false)
              if (instRef.current) {
                instRef.current.destroy?.()
                instRef.current = null
              }
            }}
          >
            重试
          </button>
        </div>
      </div>
    )
  }

  // 加载状态处理 - 添加超时处理
  if (ceLoading) {
    return (
      <div className="flex items-center justify-center h-32 text-gray-500">
        正在加载表格组件...
      </div>
    )
  }

  const {
    tableCls: tableClsForHideColAndRow,
    css: tableExtraCss
  } = useCeTableHideColumnCss(sheetConfig.config?.rowhidden, sheetConfig.config?.colhidden)
  // 样式尽量跟 luckysheet 保持一致
  const sheetDom = <div
    className={classNames(
      // 打印的元素
      'print-luckysheet-table',
      className,
      'block [&_.jexcel\\_content]:!block',
      'text-xs [&_thead]:hidden [&_.jexcel_tbody_td]:h-5 [&_.jexcel_tbody_td]:leading-[10px]',
      {
        'readonly': readOnly,
        'ce-table-panel': true,
        // 冻结的时候需要设置border-collapse为separate，不然边框会丢失
        '[&_.jexcel]:border-collapse': (sheetConfig?.frozen as any)?.type !== 'cancel' || showGridLines === false,
        // 冻结的时候需要显示网格线，不然边框会丢失
        '[&_.jexcel_tbody_td]:border-none [&_.jexcel]:border-none': showGridLines === false,
        // 设置自动换行
        '[&_.jexcel_tbody_td]:whitespace-pre-wrap [&_.jexcel_tbody_td]:break-all': autoWhiteSpace,
        // 设置行高
        '[&_.jexcel_tbody_td]:leading-snug': autoWhiteSpace
      },
      tableClsForHideColAndRow
    )}
    style={style}
    ref={onContainerRefChange}
  />

  if (_.isEmpty(sheetConfig?.images)) {
    if (tableExtraCss) {
      return (
        <>
          <style>{tableExtraCss}</style>
          {sheetDom}
        </>
      )
    }
    return sheetDom
  }
  return (
    <div className='relative'>
      {tableExtraCss && <style>{tableExtraCss}</style>}
      {sheetDom}
      {/* 渲染图片 */}
      {_.map(sheetConfig?.images, (imgInfo: any, key) => {
        const { src } = imgInfo
        const { height, width, top, left } = imgInfo.default
        const imgStyle: React.CSSProperties = {
          position: 'absolute',
          top: `${top}px`,
          left: `${left}px`,
          width: `${width}px`,
          height: `${height}px`
          // zIndex: zIndex || 0
        }
        return <img key={key} src={src} style={imgStyle} alt='插图' />
      })}
    </div>
  )
}

interface CERendererProps {
  componentKey: ComponentKey
  luckysheetCompConfig: LuckysheetCompConfig

  inflatedLuckysheetConfig: Partial<LuckysheetConfig>
}

/**
 * ce 渲染器，没有完全适配 luckysheet，但是渲染速度较快，适合打印
 * 目前只适配了合并单元格，边框，背景色
 */
function CERendererInner(props: CERendererProps) {
  const { luckysheetCompConfig, inflatedLuckysheetConfig } = props

  const onLoaded = (inflatedLuckysheetConfig.hooks as any)?.workbookCreateAfter || _.noop
  const {
    pagePadding, printMode, page,
    autoWhiteSpace, showGridLines, enableAndDisableEditing
  } = luckysheetCompConfig

  // 优化：使用 useCallback 避免不必要的重新创建
  const memoizedOnLoaded = useCallback(() => {
    onLoaded?.(inflatedLuckysheetConfig)
  }, [onLoaded, inflatedLuckysheetConfig])

  // 事件统一用 luckysheet 的回调
  const { run: onLoadedDebounced } = useDebounceFn(memoizedOnLoaded, { wait: 500 })

  // 优化：使用 useMemo 缓存配置计算
  const renderConfig = useMemo(() => ({
    pagePadding: pagePadding || 0,
    currentPage: page || 1,
    printMode,
    enableAndDisableEditing,
    showGridLines,
    autoWhiteSpace
  }), [pagePadding, page, printMode, enableAndDisableEditing, showGridLines, autoWhiteSpace])

  if (printMode === 'reportPrint') {
    return (
      <div className='h-full w-full overflow-auto'>
        {_.map(inflatedLuckysheetConfig.data, (d, i) => (
          <CESheetRenderer
            key={i}
            style={{
              padding: `${renderConfig.pagePadding}px 0px`,
              display: i === (renderConfig.currentPage - 1) ? 'block' : 'none'
            }}
            sheetConfig={d}
            enableAndDisableEditing={renderConfig.enableAndDisableEditing}
            showGridLines={renderConfig.showGridLines}
            autoWhiteSpace={renderConfig.autoWhiteSpace}
            onLoaded={onLoadedDebounced}
          />
        ))}
      </div>
    )
  }

  if (printMode !== 'printing' || (page || 0) > 0) {
    const currSheet = getActiveSheet(inflatedLuckysheetConfig)

    if (!currSheet) return null

    return (
      <CESheetRenderer
        className={classNames('h-full w-full', {
          // 打印时屏蔽滚动条
          'overflow-hidden': printMode === 'printing',
          'overflow-auto': printMode !== 'printing'
        })}
        style={{ padding: `${renderConfig.pagePadding}px 0px` }}
        sheetConfig={currSheet}
        enableAndDisableEditing={renderConfig.enableAndDisableEditing}
        autoWhiteSpace={renderConfig.autoWhiteSpace}
        showGridLines={renderConfig.showGridLines}
        onLoaded={onLoadedDebounced}
      />
    )
  }

  return (
    <div className='h-full w-full overflow-auto'>
      {_.map(inflatedLuckysheetConfig.data, (d, i) => (
        <CESheetRenderer
          key={i}
          style={{ padding: `${renderConfig.pagePadding}px 0px` }}
          sheetConfig={d}
          enableAndDisableEditing={renderConfig.enableAndDisableEditing}
          showGridLines={renderConfig.showGridLines}
          autoWhiteSpace={renderConfig.autoWhiteSpace}
          onLoaded={onLoadedDebounced}
        />
      ))}
    </div>
  )
}

/**
 * 导出的 CERenderer 组件，包装了错误边界
 */
export function CERenderer(props: CERendererProps) {
  return (
    <CERendererErrorBoundary>
      <CERendererInner {...props} />
    </CERendererErrorBoundary>
  )
}
