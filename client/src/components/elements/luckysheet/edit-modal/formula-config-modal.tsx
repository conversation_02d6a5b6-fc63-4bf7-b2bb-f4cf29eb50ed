import { ArrowDownOutlined, <PERSON>UpOutlined, DeleteOutlined, PlusCircleOutlined } from '@ant-design/icons'
import { FieldDataIcon, IndiceCaseWhenRule, IndiceCaseWhenRuleProps, IndiceRulePanel, TreeLayerSelectProps } from '@sugo/design'
import { Rule as CaseWhenRule, State as CaseWhenState } from '@sugo/design/dist/esm/components/indice-case-when-rule/type'
import { Rule } from '@sugo/design/dist/esm/components/indice-rule/type'
import type { BaseNode } from '@sugo/design/dist/esm/components/tree-layer-select/panel'
import { useMemoizedFn } from 'ahooks'
import { Button, Form, Modal } from 'antd'
import _ from 'lodash'
import React, { useMemo, useState } from 'react'

import CodeEditor from '@/components/code-editor'
import { rowCollByCellIndex } from '@/components/elements/luckysheet/utils'
import { ColumnInfo, DataFilterCondition } from '@/types/editor-core/data-source'
import { escapeForRegex } from '@/utils'


/** 将筛选条件转换为 js */
function filtersToJsPredicate(
  filters: (DataFilterCondition | Rule)[],
  allFieldDict: Record<string, ColumnInfo | null | undefined>,
  joinCond = '&&'
): string {
  if (_.isEmpty(filters)) {
    return 'true'
  }

  function getInExp(col, eq, neg = false) {
    if (eq.length === 1) {
      return neg ? `d['${col}']!==${JSON.stringify(eq[0])}` : `d['${col}']===${JSON.stringify(eq[0])}`
    }
    return neg ? `!_.includes(${JSON.stringify(eq)}, d['${col}'])` : `_.includes(${JSON.stringify(eq)}, d['${col}'])`
  }

  const inFn = (col, eq, dataType: 'string' | 'number' | 'date') =>
    dataType === 'string'
      ? getInExp(col, eq)
      : dataType === 'number'
        ? `_.inRange(d['${col}'], ${eq[0]}, ${eq[1]})`
        : `_.inRange(dayjs(d['${col}']), dayjs('${eq[0]}'), dayjs('${eq[1]}'))`
  const opHandler = {
    equal: (col, eq) => `d['${col}']==='${eq[0]}'`,
    'not equal': (col, eq) => `d['${col}']!=='${eq[0]}'`,
    in: inFn,
    'not in': (col, eq, dataType: 'string' | 'number' | 'date') =>
      dataType === 'string'
        ? getInExp(col, eq, true)
        : dataType === 'number'
          ? `!_.inRange(d['${col}'], ${eq[0]}, ${eq[1]})`
          : `!_.inRange(dayjs(d['${col}']), dayjs('${eq[0]}'), dayjs('${eq[1]}'))`,
    contains: (col, eq) => `_.includes(d['${col}'], '${eq}')`,
    'not contains': (col, eq) => `!_.includes(d['${col}'], '${eq}')`,
    'in-ranges': inFn,
    matchRegex: (col, eq) => `/${escapeForRegex(eq[0])}/.test(d['${col}'])`,
    startsWith: (col, eq) => `d['${col}'].startsWith('${eq}')`,
    endsWith: (col, eq) => `d['${col}'].endsWith('${eq}')`,
    nullOrEmpty: (col, eq) => `!d['${col}']`,
    'not nullOrEmpty': (col, eq) => `!!d['${col}']`,
    greaterThan: (col, eq) => `d['${col}']>${eq[0]}`,
    greaterThanOrEqual: (col, eq) => `d['${col}']>=${eq[0]}`,
    lessThan: (col, eq) => `d['${col}']<${eq[0]}`,
    lessThanOrEqual: (col, eq) => `d['${col}']<=${eq[0]}`
  }

  return _.map(filters, ({ col, eq, op }) => {
    const sEq = _.castArray(eq)
    if (op === 'and' || op === 'or') {
      if (_.size(sEq) === 1) {
        return filtersToJsPredicate(sEq as DataFilterCondition[], allFieldDict)
      }
      return `(${filtersToJsPredicate(sEq as DataFilterCondition[], allFieldDict, op === 'or' ? '||' : '&&')})`
    }
    const fn = opHandler[op || 'equal'] || opHandler.equal
    return fn(col, sEq, allFieldDict[col || '']?.dataType || 'string')
  }).join(joinCond)
}

function caseWhenHistoryCfgToNew(data: CaseWhenState): CaseWhenState {
  const isOld =
    data.caseElse !== undefined || (!_.isEmpty(data.rules) && _.every(data.rules, r => r.type === undefined))

  if (!isOld) {
    return data
  }
  const newData: any = { ...data }
  newData.rules = _.map(data.rules, r => ({
    type: 'case',
    op: r.op,
    key: r.key,
    current: { value: r.current, name: r.currentName, funcName: r.currentFuncName, config: r.currentConfig },
    target: { value: r.target, name: r.targetName, funcName: r.targetFuncName, config: r.targetConfig },
    then: { value: r.then, name: r.thenName, funcName: r.thenFuncName, config: r.thenConfig }
  }))
  if (data.caseElse) {
    newData.rules.push({
      type: 'else',
      op: data.caseElse.op,
      key: data.caseElse.key,
      else: {
        value: data.caseElse.else,
        name: data.caseElse.elseName,
        funcName: data.caseElse.elseFuncName,
        config: data.caseElse.elseConfig
      }
    })
    delete newData.caseElse
  }
  return newData
}


function caseWhenToJsIter(
  caseWhenState: CaseWhenState,
  allFieldDict: Record<string, ColumnInfo | null | undefined>
) {
  const newState = caseWhenHistoryCfgToNew(caseWhenState)
  const [cases, caseElseArr] = _.partition(newState.rules, r => r.type !== 'else')
  const caseElseVal = _.get(caseElseArr, '[0].else.value')
  if (_.isEmpty(cases)) {
    return caseElseVal || '0'
  }
  const [r, ...rest] = cases
  const type = allFieldDict[r.current?.value as string || '']?.dataType || 'string'
  const flt: DataFilterCondition | Rule = {
    col: r.current?.value as string,
    op: r.op,
    eq: _.castArray(r.target?.value),
    type,
    dataType: type
  }
  const cond = filtersToJsPredicate([flt], allFieldDict)
  const elseVal = _.isEmpty(rest) ? caseElseVal : `(${caseWhenToJsIter({...newState, rules: [...rest, ...caseElseArr]}, allFieldDict)})`
  return `${cond} ? ${r.then?.value} : ${elseVal}`
}


export interface LuckySheetFormulaConfig {
  configs: (
    { type: 'filters'; value: DataFilterCondition[] }
    | { type: 'mapping'; value: CaseWhenState }
    | { type: 'rowAccumulate'; value: CaseWhenState } // 按行累计，将以往的值收集后，再进行输出
    | { type: 'rowCutOff', value: {} } // 无值的时候屏蔽掉，不显示 0
    | { type: 'dataOff', value: {} } // 当需要计算的数据为空时，显示为空
  )[]
  gen: string
  custom?: string
}

interface SeqFilterConfigModalProps {
  defaultValue: LuckySheetFormulaConfig
  onChange: (v: LuckySheetFormulaConfig) => any
  fieldsBinding: Record<string, ColumnInfo | null | undefined>
  chartData?: any[] // 图表数据，用于静态数据源的 eq 值选择
  fieldName: string
  func: 'sum' | 'mean' | 'max' | 'min' | 'first' | 'last'
  fieldTitle?: string
}

function genFormula({
  configs,
  fieldsBinding,
  fieldTitle,
  fieldName,
  func
}: {
  configs: LuckySheetFormulaConfig['configs']
  fieldsBinding: Record<string, ColumnInfo | null | undefined>
  fieldTitle: string | undefined
  fieldName: string
  func: 'sum' | 'mean' | 'max' | 'min' | 'first' | 'last'
}) {
  // const cond = filtersToJsPredicate(filters, fieldsBinding)
  const [pres, posts] = _.partition(configs, c => !_.startsWith(c.type, 'row'))
  const iteratee = (c: LuckySheetFormulaConfig['configs'][number]) => {
    const { type, value } = c || {}
    if (!value) {
      return ''
    }

    if (type === 'filters') {
      const cond = filtersToJsPredicate(value as DataFilterCondition[], fieldsBinding)
      return `.filter(d=>${cond})`
    }
    if (type === 'mapping') {
      const iter = caseWhenToJsIter(value, fieldsBinding)
      return `.map(d=>${iter})`
    }
    if (type === 'rowAccumulate') {
      // .thru(rowCollect).thru(res => _.endsWith(obj[t('日期')], '08') ? res + '--' + rowCollect().sum() : res)
      const iter = caseWhenToJsIter(value, fieldsBinding).replace(/\bd\[/g, 'obj[')
      return `.thru(rowCollect).thru(res=>${iter})`
    }
    if (type === 'rowCutOff') {
      // .thru(v => v || '')
      return '.thru(v=>v||"")'
    }
    if (type === 'dataOff') {
      return `.thru(v=> (v.length ? _.${func}(v) : "" ))`
    }
    return ''
  }
  const preExpr = _.map(pres, iteratee).join('')
  const postExprs = _.map(posts, iteratee).join('')
  // const by = fieldTitle ? `t('${fieldTitle}')` : `'${fieldName}'`
  if (_.some(configs, { type: 'dataOff' })) {
    return `seq()${preExpr}${postExprs}`
  }
  // return `\${${seqExpr}.map(${by}).${func}()}`
  return `seq()${preExpr}.${func}()${postExprs}`
}


/** 表达式筛选条件配置弹窗 */
export function FormulaConfigModal(props: SeqFilterConfigModalProps) {
  const {
    defaultValue, onChange, fieldsBinding, chartData,
    fieldName, fieldTitle, func
  } = props
  const [pendingState, setPendingState] = useState(defaultValue)
  const onCfgChange = useMemoizedFn((next: SeqFilterConfigModalProps['defaultValue']) => {
    const gen = genFormula({
      configs: next.configs,
      fieldsBinding,
      fieldTitle,
      fieldName,
      func
    })
    const n = { ...next, gen }
    setPendingState(n)
    onChange(n)
  })

  const options = useMemo(() => _.compact(_.map(fieldsBinding, (col, field) => {
    if (!col) {
      return null
    }
    const label = col.title || col.name
    // 日期类型的数据，当成字符串数据处理
    return ({ label, title: label, value: field, key: field, dataType: col.dataType === 'date' ? 'string' : col.dataType })
  })), [fieldsBinding])

  const indiceTreeLayerProps: TreeLayerSelectProps = useMemo(() => ({
    title: '请选择数据列',
    treeData: options as TreeLayerSelectProps['treeData'],
    renderValue: (_s, info: BaseNode[]) => _.last(info)?.title || '',
    renderIcon: ({ level, dataType }) => ((level || 0) > 0 ? <FieldDataIcon dataType={dataType || 'string'} /> : null)
  }), [options])

  // 用于获取指标的 code，dataType
  const onGenerateRuleCol = useMemoizedFn((_value: string[], infos: any[]) => {
    const item = _.last(infos)
    return { col: item?.key, colName: item?.label, dataType: item.dataType || 'string' }
  })

  const hotWordDict = useMemo(() => {
    if (!chartData || _.isEmpty(chartData)) {
      return {}
    }
    return _.mapValues(chartData[0], (v, k) => _.isNumber(v) ? null : _.uniq(chartData.map(d => d[k])))
  }, [chartData])

  const onFieldQueryHotwords = useMemoizedFn((
    rule: Rule & { triggerType?: 'current' | 'target' | 'then'; },
    keyword?: string
  ) => {
    const col = 'currentConfig' in rule ? rule.currentConfig?.col : (rule as Rule).col
    if (!col || !hotWordDict[col]) {
      return []
    }
    if (!keyword) {
      return _.map(hotWordDict[col], v => ({ label: v, value: v }))
    }
    return _.filter(hotWordDict[col], v => _.includes(v, keyword)).map(v => ({ label: v, value: v }))
  })
  const previewExpr = pendingState.custom || pendingState.gen

  const testExprVal = useMemo(() => {
    const titleFieldDict = _(fieldsBinding)
      .mapValues((v, k) => v?.title || k)
      .invert()
      .value()

    const translateTitle = title => titleFieldDict[title] || title
    const getRowCollectFn = rowCollByCellIndex()
    const exData = { t: translateTitle, seq: () => _.chain(chartData), rowCollect: getRowCollectFn(0) }
    try {
      return _.template(`\${${previewExpr}}`)(exData)
    } catch (e: any) {
      return `表达式错误: ${e.message}`
    }
  }, [previewExpr, fieldsBinding, chartData])

  const tempCss = '.insert-formula-menu { z-index: 999; }'
  return (
    <>
      <style>{tempCss}</style>
      <Form layout='vertical'>
        {_.map(pendingState.configs, (cfg, i, arr) => {
          const onDelClick = () => {
            const configs = _.filter(pendingState.configs, (_c, idx) => idx !== i)
            onCfgChange({ ...pendingState, configs })
          }
          const onMoveUpClick = () => {
            if (i === 0) {
              return
            }
            const configs = _.map(pendingState.configs, (c, idx) => {
              if (idx === i - 1) {
                return pendingState.configs[i]
              }
              return idx === i ? pendingState.configs[i - 1] : c
            })
            onCfgChange({ ...pendingState, configs })
          }
          const onMoveDownClick = () => {
            if (i === pendingState.configs.length - 1) {
              return
            }
            const configs = _.map(pendingState.configs, (c, idx) => {
              if (idx === i + 1) {
                return pendingState.configs[i]
              }
              return idx === i ? pendingState.configs[i + 1] : c
            })
            onCfgChange({ ...pendingState, configs })
          }
          const btns = (
            <div className='absolute right-0 top-[-32px]'>
              <Button type='link' disabled={i === 0} icon={<ArrowUpOutlined />} onClick={onMoveUpClick} />
              <Button type='link' className='ml-2' disabled={i === arr.length - 1} icon={<ArrowDownOutlined />} onClick={onMoveDownClick} />
              <Button type='link' danger className='ml-2' onClick={onDelClick} icon={<DeleteOutlined />} />
            </div>
          )
          if (cfg.type === 'filters') {
            return (
              <Form.Item
                label={`${i + 1}. 筛选条件`}
                key={i}
                className='relative'
              >
                <IndiceRulePanel
                  treeLayerProps={indiceTreeLayerProps}
                  onGenerateRuleCol={onGenerateRuleCol}
                  onFieldQueryHotwords={onFieldQueryHotwords}
                  value={cfg.value as Rule[]}
                  onChange={next => {
                    const flts = _.map(next, r => ({ ...r, eq: _.castArray(r.eq) })) as DataFilterCondition[]
                    const configs = _.map(pendingState.configs, (c, idx) => idx === i ? { type: 'filters' as const, value: flts } : c)
                    onCfgChange({ ...pendingState, configs })
                  }}
                />
                {btns}
              </Form.Item>
            )
          }
          if (cfg.type === 'mapping') {
            return (
              <Form.Item
                label={`${i + 1}. 取值逻辑`}
                key={i}
                className='relative'
              >
                <IndiceCaseWhenRule
                  value={cfg.value as IndiceCaseWhenRuleProps['value']}
                  onChange={next => {
                    const configs = _.map(
                      pendingState.configs,
                      (c, idx) => idx === i ? { ...c, value: next } : c)
                    onCfgChange({ ...pendingState, configs })
                  }}
                  treeLayerProps={indiceTreeLayerProps}
                  onGenerateRuleCol={onGenerateRuleCol}
                  onFieldQueryHotwords={onFieldQueryHotwords}
                />
                {btns}
              </Form.Item>
            )
          }
          if (cfg.type === 'rowAccumulate') {
            return (
              <Form.Item
                label={`${i + 1}. 行累计`}
                key={i}
                className='relative'
              >
                <IndiceCaseWhenRule
                  value={cfg.value as IndiceCaseWhenRuleProps['value']}
                  onChange={next => {
                    const configs = _.map(
                      pendingState.configs,
                      (c, idx) => idx === i ? { ...c, value: next } : c)
                    onCfgChange({ ...pendingState, configs })
                  }}
                  treeLayerProps={indiceTreeLayerProps}
                  onGenerateRuleCol={onGenerateRuleCol}
                  onFieldQueryHotwords={onFieldQueryHotwords}
                />
                {btns}
              </Form.Item>
            )
          }
          if (cfg.type === 'rowCutOff') {
            return (
              <Form.Item
                label={`${i + 1}. 行截断`}
                key={i}
                className='relative'
              >
                <div>无值时屏蔽掉，不显示 0，无需配置</div>
                {btns}
              </Form.Item>
            )
          }

          return null
        })}

        <Form.Item label='添加配置'>
          <Button
            size='small'
            icon={<PlusCircleOutlined />}
            type='primary'
            onClick={() => {
              const configs: LuckySheetFormulaConfig['configs'] = [{ type: 'filters', value: [] }, ...pendingState.configs]
              onCfgChange({ ...pendingState, configs })
            }}
          >筛选条件</Button>
          <Button
            size='small'
            icon={<PlusCircleOutlined />}
            className='ml-2'
            type='primary'
            onClick={() => {
              const by = fieldTitle ? `t('${fieldTitle}')` : `'${fieldName}'`
              const initConfig = {
                type: 'mapping' as const,
                value: caseWhenHistoryCfgToNew({
                  treeLayerProps: {},
                  caseAs: undefined,
                  rules: [] as CaseWhenRule[],
                  caseElse: {
                    op: 'equal',
                    key: Math.random().toString(36).slice(2),
                    else: `d[${by}]`,
                    elseFuncName: 'CONSTANT_STRING',
                    elseConfig: { 'dataType': 'string' }
                  }
                })
              }
              const configs: LuckySheetFormulaConfig['configs'] = [...pendingState.configs, initConfig]
              onCfgChange({ ...pendingState, configs })
            }}
          >取值逻辑</Button>
          <Button
            size='small'
            icon={<PlusCircleOutlined />}
            className='ml-2'
            type='primary'
            disabled={_.some(pendingState.configs, c => c.type === 'rowAccumulate')}
            onClick={() => {
              // .thru(rowCollect).thru(res => _.endsWith(obj[t('日期')], '08') ? res + '--' + rowCollect().sum() : res)
              const initVal = caseWhenHistoryCfgToNew({
                treeLayerProps: {},
                caseAs: undefined,
                caseElse: {
                  op: 'equal',
                  key: Math.random().toString(36).slice(2),
                  else: 'res',
                  elseFuncName: 'CONSTANT_STRING',
                  elseConfig: { 'dataType': 'string' }
                },
                rules: [
                  {
                    key: Math.random().toString(36).slice(2),
                    currentConfig: { col: '', colName: '', dataType: 'string' },
                    op: 'contains',
                    target: '',
                    thenFuncName: 'CONSTANT_STRING',
                    thenConfig: { dataType: 'string' },
                    then: 'res + "--" + rowCollect().sum()'
                  }
                ]
              })
              const configs: LuckySheetFormulaConfig['configs'] = [...pendingState.configs, { type: 'rowAccumulate', value: initVal }]
              onCfgChange({ ...pendingState, configs })
            }}
          >行累计</Button>
          <Button
            size='small'
            icon={<PlusCircleOutlined />}
            className='ml-2'
            type='primary'
            disabled={_.some(pendingState.configs, c => c.type === 'rowCutOff')}
            onClick={() => {
              // .thru(v => v || '')
              const configs: LuckySheetFormulaConfig['configs'] = [...pendingState.configs, { type: 'rowCutOff', value: {} }]
              onCfgChange({ ...pendingState, configs })
            }}
          >行截断</Button>
          <Button
            title='当需要计算的数据为空时，显示为空'
            size='small'
            icon={<PlusCircleOutlined />}
            className='ml-2'
            type='primary'
            disabled={_.some(pendingState.configs, c => c.type === 'dataOff')}
            onClick={() => {
              // .thru(v => v || '')
              const configs: LuckySheetFormulaConfig['configs'] = [...pendingState.configs, { type: 'dataOff', value: {} }]
              onCfgChange({ ...pendingState, configs })
            }}
          >值截断</Button>
        </Form.Item>

        <Form.Item
          label='预览脚本（可编辑）'
          help={!pendingState.custom ? null : (
            <Button
              type='link'
              size='small'
              onClick={() => onCfgChange({ ...pendingState, custom: undefined })}
            >重置</Button>
          )}
        >
          <CodeEditor
            className='h-[120px]'
            value={previewExpr}
            onChange={v => onCfgChange({ ...pendingState, custom: v })}
          />
        </Form.Item>

        <Form.Item label='试算结果（输入数据为当前图表数据）'>
          <span >{testExprVal}</span>
        </Form.Item>

      </Form>
    </>
  )
}

/** 显示筛选条件配置弹窗 */
export async function showAskFilterAndCaseWhenModal(
  { allFieldDict, chartData, func, fieldName, fieldTitle }: {
    allFieldDict: Record<string, ColumnInfo | null | undefined>,
    chartData: any[] | undefined,
    func: 'sum' | 'mean' | 'max' | 'min' | 'first' | 'last'
    fieldName: string,
    fieldTitle?: string
  }
): Promise<string> {
  const by = fieldTitle ? `t('${fieldTitle}')` : `'${fieldName}'`
  const initConfigs: LuckySheetFormulaConfig['configs'] = [{
    type: 'mapping',
    value: caseWhenHistoryCfgToNew({
      treeLayerProps: {},
      caseAs: undefined,
      caseElse: {
        'op': 'equal',
        'key': Math.random().toString(36).slice(2),
        'else': `d[${by}]`,
        'elseFuncName': 'CONSTANT_STRING',
        'elseConfig': { 'dataType': 'string' }
      },
      rules: []
    })
  }
  ]
  let pendingVal: LuckySheetFormulaConfig = {
    configs: initConfigs,
    gen: genFormula({
      configs: initConfigs,
      fieldsBinding: allFieldDict,
      fieldTitle,
      fieldName,
      func
    }),
    custom: ''
  }
  const fnCnDict = {
    sum: '求和',
    mean: '均值',
    max: '最大值',
    min: '最小值',
    first: '首个值',
    last: '末尾值'
    // read: '取值'
  }
  return new Promise((resolve, reject) => {
    Modal.confirm({
      title: `${fnCnDict[func]}公式配置：${fieldTitle}`,
      centered: true,
      width: 900,
      content: (
        <FormulaConfigModal
          defaultValue={pendingVal}
          onChange={v => (pendingVal = v)}
          fieldsBinding={allFieldDict}
          chartData={chartData}
          fieldName={fieldName}
          func={func}
          fieldTitle={fieldTitle}
        />
      ),
      onOk: () => resolve(pendingVal.custom || pendingVal.gen),
      onCancel: () => reject(new Error('用户取消'))
    })
  })
}
