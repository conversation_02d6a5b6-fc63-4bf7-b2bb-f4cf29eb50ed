import { produce } from 'immer'
import _ from 'lodash'
import { nanoid } from 'nanoid'

import { LuckysheetCellData, LuckysheetConfig } from '@/types/editor-core/config'

import { assignZoneIdDict } from './template-zone-config'

/** 调整到编辑模式，显示 toolbar，进行某些初始化设置 */
export function prepareEdit(luckysheetCfg: Partial<LuckysheetConfig>, extra = {}): Partial<LuckysheetConfig> {
  const luckysheetCfgWithZoneId = produce(luckysheetCfg, draft => {
    delete draft.allowEdit
    draft.showSheetTabs = false
    draft.showToolbar = true
    draft.showFormulaBar = true
    // draft.showStatisticBar = true
    // draft.showToolbarConfig = {
    //   chart: false // '图表'（图标隐藏，但是如果配置了chart插件，右击仍然可以新建图表）
    // }
    _.forEach(extra, (value, key) => {
      _.set(draft, key, value)
    })

    _.each(draft.data, sheet => {
      // 如果没有 id，是旧数据，需要补充 id
      sheet.config = sheet.config || {}
      sheet.config.templateZones = _.map(sheet!.config?.templateZones, zone =>
        zone.id ? zone : { ...zone, id: nanoid(5) }
      )
    })
  })
  return assignZoneIdDict(luckysheetCfgWithZoneId)
}

/** 完成编辑，隐藏 toolbar，一般用于预览模式 */
export function doneEdit(luckysheetCfg: Partial<LuckysheetConfig>): Partial<LuckysheetConfig> {
  // 不能设置 allowEdit: false，否则下次打开也不能编辑，这个是 luckysheet 的 bug，一旦设置就锁定了

  return produce(luckysheetCfg, draft => {
    _.each(draft.data, sheet => {
      _.each(sheet?.celldata, cell => {
        if (_.isObject(cell.v) && 'zoneIdDict' in cell.v) {
          delete cell.v.zoneIdDict
        }
      })
    })
    // draft.showinfobar = false
    draft.showToolbar = false
    draft.showFormulaBar = false
    draft.showSheetTabs = false
    // draft.showstatisticBar = false
    // draft.showstatisticBarConfig = {
    //   count: false, // 计数栏
    //   view: false, // 打印视图
    //   zoom: false // 缩放
    // }
  })
}

/** 清除边框、空的单元格和隐藏行列配置 */
export function cleanBorderAndEmptyCells(luckysheetCfg: Partial<LuckysheetConfig>): Partial<LuckysheetConfig> {
  return produce(luckysheetCfg, draft => {
    _.each(draft.data, sheet => {
      sheet.config!.borderInfo = []
      sheet.config!.rowhidden = {}
      sheet.config!.colhidden = {}
      sheet.celldata = sheet.celldata?.filter(cell => {
        const { v } = cell
        // 有内容或被合并的单元格都不清掉
        return _.isObject(v) ? v.bg || v.mc || v.f || v.m || !(_.isNil(v.v) || v.v === '') : !_.isNil(v)
      })
    })
  })
}
