import { ClearOutlined, LoadingOutlined } from '@ant-design/icons'
import type { Selection, Sheet } from '@fortune-sheet/core/dist/types'
import type { WorkbookInstance } from '@fortune-sheet/react'
import { useDebounce, useDebounceEffect, useMemoizedFn, useReactive } from 'ahooks'
import { Button, Menu, message, ModalProps, Popconfirm, Space, Switch } from 'antd'
import { produce } from 'immer'
import _ from 'lodash'
import * as React from 'react'
import { Suspense, useEffect, useMemo, useRef, useState } from 'react'

import CustomModal from '@/components/customs/custom-modal'
import { updateSheets, WorkbookLazy } from '@/components/elements/luckysheet/luckysheet-renderer'
import { LuckysheetCompConfig } from '@/components/elements/luckysheet/types'
import { genLuckysheetInitConfig, getActiveSheet } from '@/components/elements/luckysheet/utils'
import { Component } from '@/types/editor-core/component'
import { LuckysheetConfig } from '@/types/editor-core/config'
import { ColumnInfo } from '@/types/editor-core/data-source'
import { ProjectType } from '@/types/entitys'

import { cleanBorderAndEmptyCells, doneEdit, prepareEdit } from './logic-helper'
import { useFieldSelectMenu, useFormulaInsertMenu } from './page-expr-insert'
import { interceptImageBtn, useEditImageModal } from './page-image-insert'
import { interceptLinkBtn, useEditLinkModal } from './page-link-insert'
import { assignZoneIdDict, cancelIndicateTemplateZoneBg, refreshLuckysheetCfgZoneByCellPos, useTemplateZoneCfg } from './template-zone-config'


interface LuckysheetConfigModalProps {
  value: LuckysheetCompConfig
  onChange: (value: LuckysheetCompConfig) => any
  pages: ProjectType['directory']['nodes']
  previewUrl: string
  dataSourceConfig: Component['dataSource']
  chartData: any[]
  moduleAddress: string
  compVersion: string
}

/** 试算表组件配置对话框 */
function LuckysheetConfigModal(props: LuckysheetConfigModalProps & ModalProps) {
  const {
    value,
    onChange,
    onOk,
    onCancel,
    dataSourceConfig,
    chartData,
    moduleAddress,
    compVersion,
    pages,
    previewUrl
  } = props

  let showEditLinkModal
  let showEditImageModal
  const injectHooks = useMemo(
    () => ({
      workbookCreateAfter: (_luckysheetConfig: LuckysheetConfig) => {
        const appIframe = document.querySelector('iframe[name=component-luckysheet-config]') as HTMLIFrameElement
        interceptLinkBtn(appIframe?.contentWindow, showEditLinkModal)
        interceptImageBtn(appIframe?.contentWindow, showEditImageModal)
      }
    }),
    []
  )
  const [pendingLuckysheetCfg, setPendingLuckysheetCfg] = useState<Partial<LuckysheetConfig>>(() =>
    prepareEdit(value.luckysheetConfig || {}, { hook: injectHooks })
  )
  showEditLinkModal = useEditLinkModal(
    dataSourceConfig,
    pages,
    previewUrl,
    pendingLuckysheetCfg,
    setPendingLuckysheetCfg
  )
  showEditImageModal = useEditImageModal(dataSourceConfig, pendingLuckysheetCfg, setPendingLuckysheetCfg)

  const { allFieldDict, numFieldDict } = useMemo(() => {
    const { fieldsBinding, sortedFieldNames } = dataSourceConfig[dataSourceConfig.dataSourceType] || {}
    // 如果没有用户设置对顺序，默认字段顺序：日期，字符，数字
    const orderDict = { date: 0, string: 1, number: 2 }
    const orderedFieldNames = _.isEmpty(sortedFieldNames)
      ? _.orderBy(_.keys(fieldsBinding), k => orderDict[fieldsBinding?.[k]?.dataType || 'string'])
      : (sortedFieldNames as string[])
    const fieldsDict = _.pick(fieldsBinding, orderedFieldNames) as Record<string, ColumnInfo | null>
    return {
      allFieldDict: fieldsDict,
      numFieldDict: _.pickBy(fieldsDict, f => f?.dataType === 'number')
    }
  }, [dataSourceConfig])
  const {
    reState,
    dom: templateDomCfgDom,
    pendingLuckysheetCfgWithBg
  } = useTemplateZoneCfg({
    pendingLuckysheetCfg,
    setPendingLuckysheetCfg,
    dataSourceConfig,
    chartData,
    allFieldDict
  })

  // 试算表 props 配置变更时，更新试算表
  useEffect(() => {
    const nextCfg = prepareEdit(value.luckysheetConfig || {}, { hook: injectHooks })
    setPendingLuckysheetCfg(nextCfg)
  }, [value])

  // 透视表模式下，数据会全部按列进行 groupBy 再输出
  const currInflateMode = useMemo(
    () => getActiveSheet(pendingLuckysheetCfg)?.config?.inflateMode,
    [pendingLuckysheetCfgWithBg]
  )

  const onFinish = e => {
    // 这里也要处理一遍，避免用户保存太快导致循环体错位
    const luckysheetCfgWithoutZoneColor = cancelIndicateTemplateZoneBg(pendingLuckysheetCfg)
    const pendingLuckysheetCfgWithZoneUpdated = refreshLuckysheetCfgZoneByCellPos(luckysheetCfgWithoutZoneColor)
    const pendingLuckysheetCfgWithZoneIdDict = assignZoneIdDict(pendingLuckysheetCfgWithZoneUpdated)
    const next = doneEdit(pendingLuckysheetCfgWithZoneIdDict)
    onChange(next)
    onOk?.(e)
  }
  const onResetCurrSheet = () => {
    const dsQueryCfg = dataSourceConfig[dataSourceConfig.dataSourceType]
    if (!dsQueryCfg?.tableId || _.isEmpty(dsQueryCfg?.fieldsBinding)) {
      message.error('请先配置数据源，并且绑定度量或维度')
      return
    }
    setPendingLuckysheetCfg(prevLuckySheetConfig => {
      const currentInflateMode = getActiveSheet(prevLuckySheetConfig)?.config?.inflateMode
      const nextCfg = genLuckysheetInitConfig(dataSourceConfig, currentInflateMode || 'basic')
      const nextLuckySheetConfig = prepareEdit(nextCfg, { hook: injectHooks })
      reState.showTemplateZoneBg = getActiveSheet(nextLuckySheetConfig)?.config?.templateZones?.length ? 0 : -1
      return nextLuckySheetConfig
    })
  }

  useEffect(() => {
    if (!currInflateMode) {
      // 首次进入，自动初始化
      onResetCurrSheet()
    }
  }, [currInflateMode])

  const insertFormulaMenu = useFormulaInsertMenu(
    pendingLuckysheetCfg,
    setPendingLuckysheetCfg,
    allFieldDict,
    numFieldDict,
    chartData
  )
  // 如果是有几个数据源那么就增加几个 能够选择关联字段的弹出框

  // 先判断是否是多数据源
  const isDataSources = _.size(dataSourceConfig.combine?.combineCfgDict) > 1

  const fieldSelectSource = useFieldSelectMenu(
    dataSourceConfig,
    pendingLuckysheetCfg,
    setPendingLuckysheetCfg
  )

  const ref = useRef<WorkbookInstance>(null)

  // 编辑窗口占满屏幕高度，可解决右键偏移问题
  const onChangeMemo = useMemoizedFn((nextSheets: Sheet[]) => {
    const workbook = ref.current
    if (!workbook) return
    // 需要将 next data 转换为 celldata，因为 luckysheet 数据填充是基于 celldata 的
    const cellData = workbook.dataToCelldata(nextSheets[0].data)
    const nextSheetsWithCellData = produce(nextSheets, draft => {
      draft[0].celldata = cellData
    })
    setPendingLuckysheetCfg(prev => {
      // 判断是否修改，如果没有修改了就不更新
      const prevIgnoreTimeStamp = _.omit(prev.data, '[0].config.lastUpdatedAt')
      const nextIgnoreTimeStamp = _.omit(nextSheetsWithCellData, '[0].config.lastUpdatedAt')
      if (_.isEqual(prevIgnoreTimeStamp, nextIgnoreTimeStamp)) {
        return prev
      }
      return {
        ...prev,
        data: nextSheetsWithCellData || [{ name: 'Sheet1' }]
      } as LuckysheetConfig
    })
  })

  useDebounceEffect(() => {
    const inst: WorkbookInstance | null = ref.current
    if (!inst) return
    const currSheets = inst.getAllSheets()
    const next = updateSheets(currSheets, pendingLuckysheetCfgWithBg.data || [], false)
    inst.updateSheet(next)
  }, [pendingLuckysheetCfgWithBg], { wait: 600 })

  // images 改为数组，可能是对象，fortune-sheet 暂未支持图片
  const sheetsWithImagesFix = _.map(pendingLuckysheetCfgWithBg?.data as Sheet[], sheet =>
    ({ ...sheet, images: [] })
  )

  const sheets = useDebounce(sheetsWithImagesFix, { wait: 1000 })

  const hooks = useMemo(() => ({
      afterSelectionChange: (_sheetId: string, selection: Selection) => {
        // 将选区信息同步到 pendingLuckysheetCfg
        setPendingLuckysheetCfg(prev => produce(prev, draft => {
            const sheet = draft.data?.[0]
            if (sheet) {
              sheet.luckysheet_select_save = [selection]
            }
          })
        )
      }
    }), [setPendingLuckysheetCfg])

  return (
    <CustomModal
      width='100%'
      open
      destroyOnClose
      onOk={onFinish}
      onCancel={onCancel}
      maskClosable={false}
      centered
      bodyStyle={{ padding: 0, position: 'relative', height: '90vh', overflow: 'hidden', display: 'flex' }}
      footer={
        [
          <Space key='btns' className='float-left' align='baseline'>
            <Button onClick={onResetCurrSheet} size='small'>
              重置
            </Button>

            <span className='leading-8'>
              <Popconfirm
                title='切换渲染模式会重置当前表格数据，是否继续？'
                onConfirm={() => {
                  const nextIsPivot = currInflateMode !== 'pivot'
                  setPendingLuckysheetCfg(
                    produce(pendingLuckysheetCfg, draft => {
                      _.merge(draft, { data: [{ config: {} }] })
                      const sheet = getActiveSheet(draft)!
                      sheet.config = sheet.config || {}
                      sheet.config.templateZones = []
                      sheet.config.inflateMode = nextIsPivot ? 'pivot' : 'basic'
                      sheet.config.lastUpdatedAt = Date.now() // 强制重新加载数据
                    })
                  )
                  reState.showTemplateZoneBg = -1

                  setTimeout(onResetCurrSheet, 150)
                }}
                okText='确认'
                cancelText='取消'
              >
                <Switch checkedChildren='透视表' unCheckedChildren='明细表' checked={currInflateMode === 'pivot'} />
              </Popconfirm>
            </span>

            {/* <Dropdown.Button
              icon={<DownOutlined />}
              size='small'
              menu={insertFormulaMenu}
              onClick={() => message.info('请从下拉框中选择公式')}
              trigger={['click']}
            >
              插入公式...
            </Dropdown.Button> */}

            {templateDomCfgDom}

            {isDataSources &&
              <Popconfirm
                {...fieldSelectSource}
              >
                <Button key='cancel' size='small' >
                  多数据源
                </Button>
              </Popconfirm>
            }

            <Button
              icon={<ClearOutlined />}
              title='清理【边框】和【空白单元格】，遇到边框问题或感到卡顿时可以试试'
              size='small'
              onClick={() => {
                const next = cleanBorderAndEmptyCells(pendingLuckysheetCfg)
                setPendingLuckysheetCfg(
                  produce(next, draft => {
                    _.merge(draft, { data: [{ config: {} }] })
                    const sheet = getActiveSheet(draft)!
                    sheet.config!.lastUpdatedAt = Date.now() // 强制重新加载数据
                  })
                )
                message.info('已清理 边框、空白单元格和隐藏行列配置')
              }}
            />
          </Space>,

          <Button key='cancel' onClick={onCancel}>
            取消
          </Button>,
          <Button key='confirm' type='primary' onClick={onFinish}>
            确定
          </Button>
        ]
      }
    >
      <div style={{ width: '256px' }}>
        <h2 className='mx-4 mt-2'>插入公式：</h2>
        <Menu
          className='overflow-auto'
          style={{ height: 'calc(100% - 45px)' }}
          triggerSubMenuAction='click'
          {...insertFormulaMenu}
        />
      </div>

      <div
        style={{
          width: 'calc(100% - 256px)',
          height: '100%'
        }}
      >
        <Suspense fallback={<LoadingOutlined />}>
          <WorkbookLazy
            ref={ref}
            data={sheets?.length ? sheets : [{ name: 'Sheet1' }]}
            onChange={onChangeMemo}
            showSheetTabs={false}
            hooks={hooks}
          />
        </Suspense>
      </div>
    </CustomModal>
  )
}

interface LuckysheetConfigModalState {
  modalVisible: boolean
}

/** 便捷使用 luckysheet 数据绑定配置的弹窗 */
export function useLuckysheetConfigModal(props: LuckysheetConfigModalProps) {
  const state = useReactive<LuckysheetConfigModalState>({
    modalVisible: false
  })
  const hideModal = () => (state.modalVisible = false)
  return {
    reactiveState: state,
    modal: !state.modalVisible ? null : <LuckysheetConfigModal onOk={hideModal} onCancel={hideModal} {...props} />
  }
}
