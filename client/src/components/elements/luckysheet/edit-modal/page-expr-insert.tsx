/* eslint-disable no-template-curly-in-string */
import { DragOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import { useMemoizedFn } from 'ahooks'
import { Select, Space, Tooltip } from 'antd'
import { produce } from 'immer'
import _ from 'lodash'
import React, { useMemo, useState } from 'react'

import { showAskFilterAndCaseWhenModal } from '@/components/elements/luckysheet/edit-modal/formula-config-modal'
import { getActiveSheet, getLuckysheetSelectionRange } from '@/components/elements/luckysheet/utils'
import { LuckysheetConfig } from '@/types/editor-core/config'
import { ColumnInfo, DataSourceConfig } from '@/types/editor-core/data-source'

import { genGlobalVarMenu } from './page-link-insert'

/** 根据 key 快捷设置公式 */
async function insertFormulaForCurrSelectRange(
  luckysheetCfg: Partial<LuckysheetConfig>,
  key: string,
  allFieldDict: Record<string, ColumnInfo | null | undefined>,
  chartData?: any[],
  isReturnFormula?: boolean // 只返回formula
) {
  const selRange = getLuckysheetSelectionRange(luckysheetCfg)
  if (!isReturnFormula && !selRange) {
    // 没有选择区域，不做任何处理
    return luckysheetCfg
  }
  const { column: sc, row: sr } = selRange || { column: [0, 0], row: [0, 0] }
  const [cmd, fieldName = 'unknown_field_name'] = key.split(':')
  const field = allFieldDict[fieldName]
  const fieldTitle = field?.title
  let formula = ''
  switch (cmd) {
    case 'idxGlobal':
      formula = '${ig+1}'
      break
    case 'idxLv0':
      formula = '${i[0]+1}'
      break
    case 'idxLv1':
      formula = '${i[0]+1}.${i[1]+1}'
      break
    case 'idxLvAll':
      formula = '${i.map(v=>v+1).join(`.`)}'
      break
    case 'readField':
      formula = fieldTitle ? `\${obj[t('${fieldTitle}')]}` : `\${${fieldName}}`
      break
    case 'sumByField':
    case 'meanByField':
    case 'firstFieldVal':
    case 'lastFieldVal':
    case 'maxFieldVal':
    case 'minFieldVal': {
      const aggFn = cmd.replace(/(By)?Field(Val)?$/, '') as 'first' | 'last' | 'max' | 'min' | 'sum' | 'mean'
      const jsFormula = await showAskFilterAndCaseWhenModal({
        allFieldDict,
        chartData,
        func: aggFn,
        fieldName,
        fieldTitle
      })
      formula = jsFormula && `\${${jsFormula}}`
      break
    }
    case 'globalReadField':
      formula = fieldTitle ? `\${getGlobalFNEData(t('${fieldTitle}'))}` : `\${${fieldName}}`
      break
    case 'globalVar':
      formula = `\${_.flow([()=>window.localStorage['${fieldName}']||'null', JSON.parse, String])()}`
      break
    case 'globalVarTimeRange':
      formula = `\${_.flow([()=>window.localStorage['${fieldName}']||'[]', JSON.parse, n=>fmtDateRange(n, 'YYYY年M月D日')])()}`
      break
    case 'globalVarBaseTimeRange':
      formula = `\${_.flow([()=>window.localStorage['${fieldName}']||'[]', JSON.parse, n=>timeSub(n, 1, 'year'), n=>fmtDateRange(n, 'YYYY年M月D日')])()}`
      break
    case 'globalVarStartTime':
      formula = `\${_.flow([()=>window.localStorage['${fieldName}']||'[]', JSON.parse, _.head, dayjs, n=>n.format('YYYY年MM月DD日')])()}`
      break
    case 'globalVarEndTime':
      formula = `\${_.flow([()=>window.localStorage['${fieldName}']||'[]', JSON.parse, _.last, dayjs, n=>n.format('YYYY年MM月DD日')])()}`
      break
    case 'globalVarStartTimeDaysInMonth':
      formula = `\${_.flow([()=>window.localStorage['${fieldName}']||'[]', JSON.parse, _.head, dayjs, n=>n.daysInMonth()])()}`
      break
  }
  if (isReturnFormula) {
    return formula
  }
  return produce(luckysheetCfg, draft => {
    _.merge(draft, { data: [{ config: {} }] })
    const sheet = getActiveSheet(draft)!
    sheet.config!.lastUpdatedAt = Date.now() // 强制重新加载数据
    const selectionIdx = _.findIndex(sheet.celldata, ({ r, c }) => r === sr[0] && c === sc[0])
    if (selectionIdx === -1) {
      sheet.celldata!.push({ r: sr[0], c: sc[0], v: formula })
      return
    }
    const ori = sheet.celldata![selectionIdx]
    // 不能删掉其他属性
    if (!_.isObject(ori.v)) {
      ori.v = formula
      return
    }
    ori.v.v = formula
    ori.v.m = formula
  })
}

/** 生成字段菜单 */
function genFieldItemMenu(numFieldDict: Record<string, ColumnInfo | null | undefined>, prefix, onDragStart?: Function) {
  return _.compact(
    _.map(
      numFieldDict,
      (f, name) =>
        f && {
          label: f.title,
          key: `${prefix}${name}`,
          draggable: !!onDragStart,
          onDragStart: e => onDragStart?.(e, `${prefix}${name}`),
          icon: onDragStart ? <DragOutlined title='拖拽' /> : null
        }
    )
  )
}

/** 生成字段选择菜单 */
export function useFieldSelectMenu(
  dataSourceConfig: DataSourceConfig,
  pendingLuckysheetCfg: Partial<LuckysheetConfig>,
  setPendingLuckysheetCfg: (
    value: ((prevState: Partial<LuckysheetConfig>) => Partial<LuckysheetConfig>) | Partial<LuckysheetConfig>
  ) => void
) {
  const templateZones = useMemo(
    () => getActiveSheet(pendingLuckysheetCfg)?.config?.sourceFields || {},
    [pendingLuckysheetCfg]
  )

  // 生成一个Popconfirm来使用
  const [selectValues, setSelectValues] = useState<Record<string, string>>(templateZones)
  const title = useMemo(
    () =>
      _.map(dataSourceConfig?.combine?.combineCfgDict, (cfg, name) => {
        const dataSourceName = cfg.name
        const cfgDataSourceType = cfg?.query?.dataSourceType
        const fields = cfg.query?.[cfgDataSourceType]?.fieldsBinding
        const fieldDict = _.values(_.mapValues(fields, f => f?.title))
        // allFieldDict 下面所有title等于fieldDict的数据
        const allFieldDict = dataSourceConfig?.combine?.fieldsBinding
        const filteredData = _.mapValues(
          _.pickBy(allFieldDict, i => fieldDict.includes(_.last((i?.title || '').split('.')))),
          i => i?.title
        )
        // 改成选择框的options
        const options = _.map(filteredData, (t, n) => ({ label: t, value: n }))
        // 生成选择框
        return (
          <p>
            {dataSourceName} :
            <Select
              key={name}
              value={selectValues[dataSourceName]}
              dropdownStyle={{ zIndex: 9999 }}
              style={{ width: 200 }}
              placeholder='请选择字段'
              allowClear
              options={options}
              onChange={value => {
                setSelectValues(prev => ({ ...prev, [dataSourceName]: value }))
              }}
            />
          </p>
        )
      }),
    [dataSourceConfig?.combine?.combineCfgDict, dataSourceConfig?.combine?.fieldsBinding, selectValues]
  )
  const onConfirm = () => {
    setPendingLuckysheetCfg(value =>
      produce(value, draft => {
        _.map(draft.data, i => {
          i.config!.sourceFields = selectValues
          i.config!.lastUpdatedAt = Date.now() // 强制重新加载数据
        })
      })
    )
  }
  const okText = '确认'
  const cancelText = '取消'

  return {
    title: (
      <div>
        <p>
          <Space>
            选择关联字段(针对分页)
            <Tooltip
              title='多数据源时可以通过关联相关字段进行合并填充 如数据源1的a字段和数据源2的b字段关联后 如果列存在空值的情况下会相互填充空白数据'
            >
              <QuestionCircleOutlined />
            </Tooltip>
          </Space>
        </p>
        {title}
      </div>
    ),
    onConfirm,
    okText,
    cancelText
  }
}

/** 生成公式插入菜单 */
export function useFormulaInsertMenu(
  pendingLuckysheetCfg: Partial<LuckysheetConfig>,
  setPendingLuckysheetCfg: (
    value: ((prevState: Partial<LuckysheetConfig>) => Partial<LuckysheetConfig>) | Partial<LuckysheetConfig>
  ) => void,
  allFieldDict: Record<string, ColumnInfo | null>,
  numFieldDict: Record<string, ColumnInfo | null>,
  chartData: any[]
) {
  // 设置openKeys
  const [selectedKeys, setSelectedKeys] = useState<string[]>([])
  const insertFormulaForCurrRange = useMemoizedFn(async e => {
    if (!e.key) {
      return
    }
    setSelectedKeys([e.key.split(':')?.[0]])
    try {
      const nextCfg = (await insertFormulaForCurrSelectRange(
        pendingLuckysheetCfg,
        e.key,
        allFieldDict,
        chartData
      )) as Partial<LuckysheetConfig>
      setPendingLuckysheetCfg(nextCfg)
    } catch (e) {
      console.error(e)
    }
  })
  // 拖拽字段到lucksheet
  const onDragStart = async (e, key) => {
    try {
      const formula = await insertFormulaForCurrSelectRange(pendingLuckysheetCfg, key, allFieldDict, chartData, true)
      e.dataTransfer.setData('text/plain', formula)
    } catch (e) {
      console.error(e)
    }
  }

  const onTitleClick = item => {
    setSelectedKeys(selectedKeys[0] === item.key ? [] : [item.key])
  }

  return useMemo(
    () => ({
      onClick: insertFormulaForCurrRange,
      openKeys: selectedKeys,
      items: _.map(
        [
          {
            label: '自增序号',
            key: 'idxGlobal',
            draggable: true,
            icon: <DragOutlined title='拖拽' />,
            onDragStart: e => onDragStart?.(e, 'idxGlobal')
          },
          {
            label: '一级序号',
            key: 'idxLv0',
            draggable: true,
            icon: <DragOutlined title='拖拽' />,
            onDragStart: e => onDragStart?.(e, 'idxLv0')
          },
          {
            label: '二级序号',
            key: 'idxLv1',
            draggable: true,
            icon: <DragOutlined title='拖拽' />,
            onDragStart: e => onDragStart?.(e, 'idxLv1')
          },
          {
            label: '全部序号',
            key: 'idxLvAll',
            draggable: true,
            icon: <DragOutlined title='拖拽' />,
            onDragStart: e => onDragStart?.(e, 'idxLvAll')
          },
          {
            label: '读取字段明细值',
            key: 'readField',
            children: genFieldItemMenu(allFieldDict, 'readField:', onDragStart)
          },
          { label: '求和', key: 'sumByField', children: genFieldItemMenu(numFieldDict, 'sumByField:') },
          { label: '均值', key: 'meanByField', children: genFieldItemMenu(numFieldDict, 'meanByField:') },
          { label: '最大值', key: 'maxFieldVal', children: genFieldItemMenu(numFieldDict, 'maxFieldVal:') },
          { label: '最小值', key: 'minFieldVal', children: genFieldItemMenu(numFieldDict, 'minFieldVal:') },
          { label: '首个值', key: 'firstFieldVal', children: genFieldItemMenu(allFieldDict, 'firstFieldVal:') },
          { label: '末尾值', key: 'lastFieldVal', children: genFieldItemMenu(allFieldDict, 'lastFieldVal:') },
          {
            label: '全局首个非空值',
            key: 'globalReadField',
            children: genFieldItemMenu(allFieldDict, 'globalReadField:', onDragStart)
          },
          { label: '全局变量-时间范围', key: 'globalVarTimeRange', children: genGlobalVarMenu('globalVarTimeRange:') },
          {
            label: '全局变量-基期时间范围',
            key: 'globalVarBaseTimeRange',
            children: genGlobalVarMenu('globalVarBaseTimeRange:')
          },
          { label: '全局变量-起始时间', key: 'globalVarStartTime', children: genGlobalVarMenu('globalVarStartTime:') },
          { label: '全局变量-结束时间', key: 'globalVarEndTime', children: genGlobalVarMenu('globalVarEndTime:') },
          {
            label: '全局变量-起始时间当月天数',
            key: 'globalVarStartTimeDaysInMonth',
            children: genGlobalVarMenu('globalVarStartTimeDaysInMonth:')
          },
          { label: '全局变量-不格式化', key: 'globalVar', children: genGlobalVarMenu('globalVar:') }
        ],
        obj => ({ ...obj, popupClassName: 'insert-formula-menu', onTitleClick })
      )
    }),
    [insertFormulaForCurrRange, allFieldDict, numFieldDict, selectedKeys]
  )
}
