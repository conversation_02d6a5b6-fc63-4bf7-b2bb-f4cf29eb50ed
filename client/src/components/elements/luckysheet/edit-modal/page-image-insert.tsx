import { MinusCircleOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons'
import { useMemoizedFn } from 'ahooks'
import { Button, Dropdown, Form, Input, InputProps, MenuProps, message, Modal, Space } from 'antd'
import { produce } from 'immer'
import _ from 'lodash'
import * as React from 'react'

import { getActiveSheet, getLuckysheetSelectionRange } from '@/components/elements/luckysheet/utils'
import { useFormFieldsAdapter } from '@/hooks/use-form-fields-adapter'
import { LuckysheetConfig, LuckysheetImageInfo } from '@/types/editor-core/config'
import { ColumnInfo, DataSourceConfig } from '@/types/editor-core/data-source'

import { genGlobalVarMenu } from './page-link-insert'
import { getSelectionRangeOfSheet } from './template-zone-config'


/** 拦截 luckysheet 的添加链接按钮 */
export function interceptImageBtn(appWin: any, showEditLinkModal: (appWindow: any) => void) {
  const $ = appWin?.$
  const appDoc = appWin?.document
  $(appDoc).on('mousedown', '#luckysheet-insertImg-btn-title', ev => {
    ev.preventDefault()
    ev.stopPropagation()
    showEditLinkModal(appWin)
  })
  $(appDoc).on('mousedown', '#luckysheetInsertImage', ev => {
    ev.preventDefault()
    ev.stopPropagation()
    $(appDoc).find('#luckysheet-rightclick-menu').hide()
    showEditLinkModal(appWin)
  })
}

/** 额外参数 key 输入框，支持选择全局变量 */
function LinkQueryKeyPicker(props: InputProps) {
  const items: MenuProps['items'] = genGlobalVarMenu('')

  const handleMenuClick: MenuProps['onClick'] = e => {
    const varName = e.key
    props.onChange?.({ target: { value: varName.replace(/^gv_/, '') } } as any)
  }

  return (
    <Dropdown.Button
      menu={{ items, onClick: handleMenuClick }}
      buttonsRender={([_leftButton, rightButton]) => [
        <Input {...props} />,
        rightButton
      ]}
    />
  )
}

/** 额外参数 value 输入框，支持插入字段公式 */
function LinkQueryValPicker(props: { dataSourceConfig: DataSourceConfig } & InputProps) {
  const { dataSourceConfig, ...rest } = props
  const { fieldsBinding, sortedFieldNames } = dataSourceConfig[dataSourceConfig.dataSourceType] || {}
  // 如果没有用户设置对顺序，默认字段顺序：日期，字符，数字
  const orderDict = { date: 0, string: 1, number: 2 }
  const orderedFieldNames = _.isEmpty(sortedFieldNames)
    ? _.orderBy(_.keys(fieldsBinding), k => orderDict[fieldsBinding?.[k]?.dataType || 'string'])
    : (sortedFieldNames as string[])
  const fieldsDict = _.pick(fieldsBinding, orderedFieldNames) as Record<string, ColumnInfo | null>
  const strFieldDict = _.pickBy(fieldsDict, f => f?.dataType !== 'number')

  const items: MenuProps['items'] = _.map(strFieldDict, (c, k) => c && ({ label: c.title, key: k }))

  const handleMenuClick: MenuProps['onClick'] = e => {
    const fieldName = e.key
    const field = strFieldDict[fieldName]
    const fieldTitle = field?.title
    const formula = fieldTitle ? `\${obj[t('${fieldTitle}')]}` : `\${${fieldName}}`
    rest.onChange?.({ target: { value: formula } } as any)
  }

  return (
    <Dropdown.Button
      menu={{ items, onClick: handleMenuClick }}
      buttonsRender={([_leftButton, rightButton]) => [
        <Input {...rest} />,
        rightButton
      ]}
    />
  )
}

export interface EditImageLinkModalProps {
  luckysheetCfg: Partial<LuckysheetConfig>
  dataSourceConfig: DataSourceConfig
  value: Partial<LuckysheetImageInfo> | undefined | null
  onChange: (v: Partial<LuckysheetImageInfo>) => any
  onUploadImgBtnClick?: () => any
}

/** 插入超链接对话框 */
export function EditImageLinkModal(props: EditImageLinkModalProps) {
  const {
    luckysheetCfg, dataSourceConfig,
    value, onChange, onUploadImgBtnClick
  } = props
  const [form] = Form.useForm()
  const { fields, onFieldsChange } = useFormFieldsAdapter(value, onChange)

  const selRange = getLuckysheetSelectionRange(luckysheetCfg)
  if (!selRange) {
    return <div className='p-4 text-center text-xl'>请先选择单元格</div>
  }

  const formItemLayout = {
    labelCol: { xs: { span: 24 }, sm: { span: 4 } },
    wrapperCol: { xs: { span: 24 }, sm: { span: 20 } }
  }

  const formItemLayoutWithOutLabel = {
    wrapperCol: { xs: { span: 24, offset: 0 }, sm: { span: 20, offset: 4 } }
  }

  return (
    <Form
      labelCol={{ span: 4 }}
      wrapperCol={{ span: 18 }}
      layout='horizontal'
      form={form}
      style={{ width: '100%' }}
      fields={fields}
      onFieldsChange={onFieldsChange}
    >
      <Form.Item label='链接地址' name='src' tooltip='设置为空则删除超链接'>
        <Input
          placeholder='图片 url 地址'
          addonAfter={(
            <UploadOutlined onClick={onUploadImgBtnClick} />
          )}
        />
      </Form.Item>
      <Form.List name='_extraQueries'>
        {(extraQueryFields, { add, remove }, { errors }) => (
          <>
            {extraQueryFields.map(({ key, name, ...restField }, index) => (
              <Form.Item
                {...(index === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
                label={index === 0 ? '额外参数' : ''}
                required={false}
                key={key}
              >
                <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align='baseline'>
                  <Form.Item
                    {...restField}
                    name={[name, 'key']}
                    rules={[{ required: true, message: 'url 传参 key 未填' }]}
                    className='!mb-0'
                  >
                    <LinkQueryKeyPicker placeholder='自定义参数名' />
                  </Form.Item>
                  <span>=</span>
                  <Form.Item
                    {...restField}
                    name={[name, 'val']}
                    rules={[{ required: true, message: 'url 传参 val 未填' }]}
                    className='!mb-0'
                  >
                    <LinkQueryValPicker placeholder='自定义参数值' dataSourceConfig={dataSourceConfig} />
                  </Form.Item>
                  <MinusCircleOutlined onClick={() => remove(name)} />
                </Space>
              </Form.Item>
            ))}
            <Form.Item {...formItemLayoutWithOutLabel}>
              <Button type='dashed' onClick={() => add()} style={{ width: '60%' }} icon={<PlusOutlined />}>
                添加参数
              </Button>
              <Form.ErrorList errors={errors} />
            </Form.Item>
          </>
        )}
      </Form.List>
    </Form>
  )
}

/** 封装编辑超链接对话框 */
export function useEditImageModal(
  dataSourceConfig: DataSourceConfig,
  pendingLuckysheetCfg: Partial<LuckysheetConfig>,
  setPendingLuckysheetCfg: (
    value: ((prevState: Partial<LuckysheetConfig>) => Partial<LuckysheetConfig>) | Partial<LuckysheetConfig>
  ) => void
) {
  return useMemoizedFn((appWin: any) => {
    const selRange = getSelectionRangeOfSheet(getActiveSheet(pendingLuckysheetCfg))
    if (!selRange) {
      message.error('请先选中单元格')
      return
    }
    const { column, row } = selRange
    let pendingImgLinkConfig: Partial<LuckysheetImageInfo> | undefined = {
      _baseCell: { r: row[0], c: column[0] },
      src: '',
      _extraQueries: []
    }
    const inst = Modal.confirm({
      title: '插入图片',
      width: 700,
      content: (
        <EditImageLinkModal
          luckysheetCfg={pendingLuckysheetCfg}
          dataSourceConfig={dataSourceConfig}
          value={pendingImgLinkConfig}
          onChange={v => (pendingImgLinkConfig = v.src ? v : undefined)}
          onUploadImgBtnClick={() => {
            inst.destroy()
            appWin.$('#luckysheet-insertImg-btn-title').click()
          }}
        />
      ),
      onOk: () => {
        if (!pendingImgLinkConfig || !pendingImgLinkConfig.src) return

        const onInsertImgSuccess = () => {
          const latestDoc = appWin.luckysheet.toJson()
          const sheet = getActiveSheet(latestDoc)
          const imgKey = _.maxBy(_.keys(sheet?.images), k => Number(_.last(k.split('_'))))
          const imgInfo = imgKey ? sheet!.images![imgKey] : null

          setPendingLuckysheetCfg(prev => produce(prev, draft => {
            const actSheet = getActiveSheet(draft)
            if (!actSheet || !imgKey || !imgInfo) return
            let cell = _.find(actSheet.celldata, c => c.r === row[0] && c.c === column[0])
            if (!cell) {
              cell = { r: row[0], c: column[0], v: { v: '' } }
              actSheet.celldata = actSheet.celldata || []
              actSheet.celldata.push(cell)
            }

            actSheet.images = actSheet.images || {}
            // 补充 _baseCell、_extraQueries 信息
            actSheet.images[imgKey] = {
              ...imgInfo,
              _baseCell: { r: row[0], c: column[0] },
              _extraQueries: pendingImgLinkConfig!._extraQueries
            }

            // 修改更新时间，触发 luckysheet 重新渲染
            actSheet.config = actSheet.config || {}
            actSheet.config.lastUpdatedAt = Date.now()
          }))
        }
        appWin.luckysheet.insertImage(pendingImgLinkConfig.src, {
          rowIndex: row[0],
          colIndex: column[0],
          success: onInsertImgSuccess
        })
      }
    })
  })
}
