import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons'
import { useMemoizedFn } from 'ahooks'
import { Button, Dropdown, Form, Input, InputProps, MenuProps, message, Modal, Radio, Space } from 'antd'
import { produce } from 'immer'
import _ from 'lodash'
import qs from 'querystring'
import * as React from 'react'

import {
  getActiveSheet,
  getLocalStorageKeys,
  getLuckysheetSelectionRange
} from '@/components/elements/luckysheet/utils'
import { useFormFieldsAdapter } from '@/hooks/use-form-fields-adapter'
import { LuckysheetConfig, LuckysheetLinkConfig } from '@/types/editor-core/config'
import { ColumnInfo, DataSourceConfig } from '@/types/editor-core/data-source'
import { ProjectType } from '@/types/entitys'

import { getSelectionRangeOfSheet } from './template-zone-config'

/** 拦截 luckysheet 的添加链接按钮 */
export function interceptLinkBtn(appWin: any, showEditLinkModal: (appWindow: any) => void) {
  const $ = appWin?.$
  const appDoc = appWin?.document
  $(appDoc).on('mousedown', '#luckysheet-insertLink-btn-title', ev => {
    ev.preventDefault()
    ev.stopPropagation()
    showEditLinkModal(appWin)
  })
  $(appDoc).on('mousedown', '#luckysheetInsertLink', ev => {
    ev.preventDefault()
    ev.stopPropagation()
    $(appDoc).find('#luckysheet-rightclick-menu').hide()
    showEditLinkModal(appWin)
  })
}

/** 编辑链接，支持选择页面 */
function LinkUrlPicker(props: { pages: ProjectType['directory']['nodes']; previewUrl: string } & InputProps) {
  const { pages, previewUrl, ...rest } = props
  const items: MenuProps['items'] = _.map(pages, p => ({ label: p.title, key: p.screenId || '' })).filter(p => p.key)

  const handleMenuClick: MenuProps['onClick'] = e => {
    // const query = qs.parse(window.location.search.replace(/^\?/, ''))
    const s = `${previewUrl}?${qs.stringify({ screenId: e.key })}`

    // 创建一个URL对象，获取路径和查询参数
    const urlObj = new URL(s)
    const pathAndQuery = urlObj.pathname + urlObj.search
    rest.onChange?.({ target: { value: pathAndQuery } } as any)
  }

  return (
    <Dropdown.Button
      menu={{ items, onClick: handleMenuClick }}
      buttonsRender={([_leftButton, rightButton]) => [
        <Input {...rest} />,
        rightButton
      ]}
    />
  )
}

/** 生成全局变量菜单 */
export function genGlobalVarMenu(keyPrefix: string) {
  return getLocalStorageKeys('gv_').map(varName => ({
    label: `${varName.replace(/^gv_/, '')} = ${localStorage.getItem(varName)}`,
    key: `${keyPrefix}${varName}`
  }))
}

/** 额外参数 key 输入框，支持选择全局变量 */
function LinkQueryKeyPicker(props: InputProps) {
  const items: MenuProps['items'] = genGlobalVarMenu('')

  const handleMenuClick: MenuProps['onClick'] = e => {
    const varName = e.key
    props.onChange?.({ target: { value: varName.replace(/^gv_/, '') } } as any)
  }

  return (
    <Dropdown.Button
      menu={{ items, onClick: handleMenuClick }}
      buttonsRender={([_leftButton, rightButton]) => [
        <Input {...props} />,
        rightButton
      ]}
    />
  )
}

/** 额外参数 value 输入框，支持插入字段公式 */
function LinkQueryValPicker(props: { dataSourceConfig: DataSourceConfig } & InputProps) {
  const { dataSourceConfig, ...rest } = props
  const { fieldsBinding, sortedFieldNames } = dataSourceConfig[dataSourceConfig.dataSourceType] || {}
  // 如果没有用户设置对顺序，默认字段顺序：日期，字符，数字
  const orderDict = { date: 0, string: 1, number: 2 }
  const orderedFieldNames = _.isEmpty(sortedFieldNames)
    ? _.orderBy(_.keys(fieldsBinding), k => orderDict[fieldsBinding?.[k]?.dataType || 'string'])
    : (sortedFieldNames as string[])
  const fieldsDict = _.pick(fieldsBinding, orderedFieldNames) as Record<string, ColumnInfo | null>
  const strFieldDict = _.pickBy(fieldsDict, f => f?.dataType !== 'number')

  const items: MenuProps['items'] = _.map(strFieldDict, (c, k) => c && ({ label: c.title, key: k }))

  const handleMenuClick: MenuProps['onClick'] = e => {
    const fieldName = e.key
    const field = strFieldDict[fieldName]
    const fieldTitle = field?.title
    const formula = fieldTitle ? `\${obj[t('${fieldTitle}')]}` : `\${${fieldName}}`
    rest.onChange?.({ target: { value: formula } } as any)
  }

  return (
    <Dropdown.Button
      menu={{ items, onClick: handleMenuClick }}
      buttonsRender={([_leftButton, rightButton]) => [
        <Input {...rest} />,
        rightButton
      ]}
    />
  )
}

export interface EditLinkModalProps {
  luckysheetCfg: Partial<LuckysheetConfig>
  previewUrl: string
  pages: ProjectType['directory']['nodes']
  dataSourceConfig: DataSourceConfig
  value: LuckysheetLinkConfig | undefined | null
  onChange: (v: LuckysheetLinkConfig) => any
  // value 例子：
  // linkAddress : "http://*************%3A8000/abi/preview/hgjDev?screenId=UxfN6ki0aJ&zy=__cellVal__"
  // linkTooltip : ""
  // linkType : "external"
}

/** 插入超链接对话框 */
export function EditLinkModal(props: EditLinkModalProps) {
  const { luckysheetCfg, previewUrl, pages, dataSourceConfig, value, onChange } = props
  const [form] = Form.useForm()
  const { fields, onFieldsChange } = useFormFieldsAdapter(value, onChange)

  const selRange = getLuckysheetSelectionRange(luckysheetCfg)
  if (!selRange) {
    return <div className='p-4 text-center text-xl'>请先选择单元格</div>
  }

  const { column, row } = selRange
  const cell = _.find(getActiveSheet(luckysheetCfg)?.celldata, c => c.r === row[0] && c.c === column[0])
  const cellContent = _.isObject(cell?.v) ? cell!.v.v : cell?.v

  const formItemLayout = {
    labelCol: { xs: { span: 24 }, sm: { span: 4 } },
    wrapperCol: { xs: { span: 24 }, sm: { span: 20 } }
  }

  const formItemLayoutWithOutLabel = {
    wrapperCol: { xs: { span: 24, offset: 0 }, sm: { span: 20, offset: 4 } }
  }

  return (
    <Form
      labelCol={{ span: 4 }}
      wrapperCol={{ span: 18 }}
      layout='horizontal'
      form={form}
      style={{ width: '100%' }}
      fields={fields}
      onFieldsChange={onFieldsChange}
    >
      <Form.Item label='文本'>
        <Input disabled value={cellContent} />
      </Form.Item>
      <Form.Item label='链接地址' name='linkAddress' tooltip='设置为空则删除超链接'>
        <LinkUrlPicker placeholder='url 地址，可选择当前项目页面' pages={pages} previewUrl={previewUrl} />
      </Form.Item>
      <Form.Item label='打开方式' name='_target'>
        <Radio.Group
          options={[
            { label: '当前窗口', value: '_self' },
            { label: '新窗口', value: '_blank' }
          ]}
        />
      </Form.Item>
      <Form.List name='_extraQueries'>
        {(extraQueryFields, { add, remove }, { errors }) => (
          <>
            {extraQueryFields.map(({ key, name, ...restField }, index) => (
              <Form.Item
                {...(index === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
                label={index === 0 ? '额外参数' : ''}
                required={false}
                key={key}
              >
                <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align='baseline'>
                  <Form.Item
                    {...restField}
                    name={[name, 'key']}
                    rules={[{ required: true, message: 'url 传参 key 未填' }]}
                    className='!mb-0'
                  >
                    <LinkQueryKeyPicker placeholder='自定义参数名' />
                  </Form.Item>
                  <span>=</span>
                  <Form.Item
                    {...restField}
                    name={[name, 'val']}
                    rules={[{ required: true, message: 'url 传参 val 未填' }]}
                    className='!mb-0'
                  >
                    <LinkQueryValPicker placeholder='自定义参数值' dataSourceConfig={dataSourceConfig} />
                  </Form.Item>
                  <MinusCircleOutlined onClick={() => remove(name)} />
                </Space>
              </Form.Item>
            ))}
            <Form.Item {...formItemLayoutWithOutLabel}>
              <Button type='dashed' onClick={() => add()} style={{ width: '60%' }} icon={<PlusOutlined />}>
                添加参数
              </Button>
              <Form.ErrorList errors={errors} />
            </Form.Item>
          </>
        )}
      </Form.List>
    </Form>
  )
}

/** 封装编辑超链接对话框 */
export function useEditLinkModal(
  dataSourceConfig: DataSourceConfig,
  pages: ProjectType['directory']['nodes'],
  previewUrl: string,
  pendingLuckysheetCfg: Partial<LuckysheetConfig>,
  setPendingLuckysheetCfg: (
    value: ((prevState: Partial<LuckysheetConfig>) => Partial<LuckysheetConfig>) | Partial<LuckysheetConfig>
  ) => void
) {
  return useMemoizedFn((_appWin: any) => {
    const selRange = getSelectionRangeOfSheet(getActiveSheet(pendingLuckysheetCfg))
    if (!selRange) {
      message.error('请先选中单元格')
      return
    }
    const { column, row } = selRange
    const currSettings = getActiveSheet(pendingLuckysheetCfg)?.hyperlink?.[`${row[0]}_${column[0]}`]
    let pendingLinkConfig: LuckysheetLinkConfig | undefined = currSettings
      || { linkType: 'external', linkAddress: '', linkTooltip: '', _target: 'blank' }
    Modal.confirm({
      title: '插入链接',
      width: 700,
      content: (
        <EditLinkModal
          luckysheetCfg={pendingLuckysheetCfg}
          previewUrl={previewUrl}
          pages={pages}
          dataSourceConfig={dataSourceConfig}
          value={pendingLinkConfig}
          onChange={v => (pendingLinkConfig = v.linkAddress ? v : undefined)}
        />
      ),
      onOk: () => {
        setPendingLuckysheetCfg(prev => produce(prev, draft => {
          const sheet = getActiveSheet(draft)
          if (!sheet) return
          let cell = _.find(sheet.celldata, c => c.r === row[0] && c.c === column[0])
          if (!cell) {
            cell = { r: row[0], c: column[0], v: { v: '' } }
            sheet.celldata = sheet.celldata || []
            sheet.celldata.push(cell)
          }

          sheet.hyperlink = sheet.hyperlink || {}
          if (pendingLinkConfig) {
            // 修改单元格样式
            // fc : "rgb(0, 0, 255)"
            // un : 1
            if (!_.isObject(cell.v)) {
              cell.v = { v: cell.v, fc: 'rgb(0, 0, 255)', un: 1 }
            } else {
              cell.v.fc = 'rgb(0, 0, 255)'
              cell.v.un = 1
            }

            // 修改超链接配置
            sheet.hyperlink[`${row[0]}_${column[0]}`] = pendingLinkConfig
          } else {
            // 清除单元格样式
            if (_.isObject(cell.v)) {
              delete cell.v.fc
              delete cell.v.un
            }

            delete sheet.hyperlink[`${row[0]}_${column[0]}`]
          }

          // 修改更新时间，触发 luckysheet 重新渲染
          sheet.config = sheet.config || {}
          sheet.config.lastUpdatedAt = Date.now()
        }))
      }
    })
  })
}
