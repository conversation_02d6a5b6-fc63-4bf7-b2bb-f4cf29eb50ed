import { CaretLeftOutlined, CaretRightOutlined, QuestionCircleFilled, QuestionOutlined } from '@ant-design/icons'
import { useDeepCompareEffect, useMemoizedFn } from 'ahooks'
import { Button, Checkbox, Input, InputNumber, message, Popconfirm, Space, Switch, Tooltip } from 'antd'
import classNames from 'classnames'
import { produce } from 'immer'
import _ from 'lodash'
import React, { useMemo, useState } from 'react'

import { useTempFilterSettingPanel } from '@/components/data-filter-config/temp-filter-config-panel'
import { getActiveSheet, getActiveSheetIndex } from '@/components/elements/luckysheet/utils'
import { LuckysheetCellData, LuckysheetConfig, LuckysheetTemplateZone } from '@/types/editor-core/config'
import { ColumnInfo, DataSourceConfig } from '@/types/editor-core/data-source'

import {
  computeNewZoneGroup,
  getCellValFieldName,
  getInFieldZonePredicate,
  getSelectionRangeOfSheet
} from './template-zone-config'

interface TemplateZoneConfigPanelProps {
  value: LuckysheetTemplateZone
  onChange: (next: LuckysheetTemplateZone) => any
  query: DataSourceConfig
  chartData?: any // 图表数据，用于静态数据源的 eq 值选择
  onClose: () => any
  inflateMode: 'basic' | 'pivot'
  luckysheetCfg: Partial<LuckysheetConfig>
  allFieldDict: Record<string, ColumnInfo | null>
  extraBtns?: React.ReactNode
}

/** 循环体配置面板 */
export function TemplateZoneConfigPanel(props: TemplateZoneConfigPanelProps) {
  const {
    query, value, onChange, chartData, onClose, inflateMode,
    luckysheetCfg, allFieldDict, extraBtns
  } = props
  const [pendingState, setPendingState] = useState(value)
  const [customDimensionValue, setCustomDimensionValue] = useState(value.customDimensionValue)

  useDeepCompareEffect(() => {
    setPendingState(value)
  }, [value])

  const qCfgWithFilters = useMemo(
    () =>
      produce(query, draft => {
        const qCfg = draft[draft.dataSourceType]!
        qCfg.filters = pendingState.filters || []
      }),
    [pendingState.filters, query]
  )
  const onDsCfgChange = useMemoizedFn(nextQuery => {
    const q = nextQuery[nextQuery.dataSourceType]
    setPendingState({ ...pendingState, filters: q.filters })
  })
  const filterCfgDom = useTempFilterSettingPanel({
    chartData,
    value: qCfgWithFilters,
    onChange: onDsCfgChange,
    dataSourcePickerInfo: null,
    loadMoreDataSourceInfo: async () => null,
    className: 'border-none'
  })
  const onOk = () => {
    onChange(pendingState)
  }
  const onUpdateRangeClick = () => {
    const sheetIdx = getActiveSheetIndex(luckysheetCfg)
    const selRange = getSelectionRangeOfSheet(luckysheetCfg.data?.[sheetIdx])
    if (!selRange) {
      return
    }
    const zones = getActiveSheet(luckysheetCfg)?.config?.templateZones || []

    const { column: c, row: r } = selRange
    const myId = pendingState.id
    const nextGroup = computeNewZoneGroup(_.filter(zones, z => z.id !== myId), r, c)
    setPendingState({ ...pendingState, r, c, group: nextGroup })
    message.info('确认后生效')
  }
  const onChangePendingState = (obj = {}) => {
    setPendingState({
      ...pendingState,
      ...obj
    })
  }
  return (
    <div className='w-[300px]'>
      {filterCfgDom}

      <div className='flex p-2'>
        <div className=' mr-auto'>手动调整范围</div>
        <Button
          className='cursor-pointer'
          size='small'
          onClick={onUpdateRangeClick}
        >更新为当前选区</Button>
      </div>
      <div className='flex p-2 '>
        <div className='mr-auto'>
          页头/页尾行数
          <Tooltip title='循环体上面和下面附属的行数，-1表示自动计算，有多个循环体时如果表头表尾之间没有空行间隔，则无法自动计算，需要手动设置'>
            <QuestionCircleFilled className='ml-2' />
          </Tooltip>
        </div>
        <Space.Compact>
          <InputNumber
            className='w-[60px]'
            min={-1}
            step='1'
            defaultValue={-1}
            precision={0}
            value={pendingState.headerRowCnt}
            onChange={n => onChangePendingState({ headerRowCnt: n })}
          />
          <InputNumber
            className='w-[60px]'
            min={-1}
            step='1'
            defaultValue={-1}
            precision={0}
            value={pendingState.footerRowCnt}
            onChange={n => onChangePendingState({ footerRowCnt: n })}
          />
        </Space.Compact>
      </div>
      {// 如果value.field 是 __开头__结尾那么就不显示
        pendingState.field.startsWith('__') && pendingState.field.endsWith('__') ? null : (
          <div className='flex p-2'>
            <div className=' mr-auto'>循环维度值</div>
            <Popconfirm
              placement='topLeft'
              okText='确定'
              onConfirm={() => {
                onChangePendingState({ customDimensionValue })
              }}
              title={<>
                自定义循环维度值
                <Input.TextArea
                  value={customDimensionValue}
                  onChange={s => setCustomDimensionValue(s.target.value)}
                  rows={4}
                  placeholder='输入自定义循环维度的结果，通过换行来分隔结果'
                />
              </>}
              cancelText='取消'
            >
              <Button
                className='cursor-pointer'
                size='small'
                type={!customDimensionValue ? 'default' : 'primary'}
              >
                自定义
              </Button>
            </Popconfirm>
          </div>
        )
      }

      <div className='flex p-2 '>
        <div className='mr-auto'>禁用筛选</div>
        <Checkbox.Group
          options={[
            { label: '头部', value: '0' },
            { label: '内部', value: '1' },
            { label: '尾部', value: '2' }
          ]}
          value={_.compact(_.range(3).map(i => pendingState.filtersDisabled?.[i] ? `${i}` : null))}
          onChange={vals => {
            onChangePendingState({ filtersDisabled: _.range(3).map(i => vals.includes(`${i}`)) })
          }}
        />
      </div>

      <div className='pt-2 border-gray-100 border-solid border-0 border-t'>
        {extraBtns}
        <span className='float-right'>
        <Button onClick={onClose} size='small'>取消</Button>
        <Button className='ml-4' type='primary' onClick={onOk} size='small'>确认</Button>
        </span>
      </div>
    </div>
  )
}
