import { ArrowDownOutlined, <PERSON>RightOutlined, CaretLeftOutlined, CaretRightOutlined, DeleteOutlined, MergeC<PERSON>sOutlined, PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import { useReactive } from 'ahooks'
import { Button, Dropdown, message, Popover, Tag, Tooltip } from 'antd'
import classNames from 'classnames'
import { produce } from 'immer'
import _ from 'lodash'
import { nanoid } from 'nanoid'
import * as React from 'react'
import { useMemo } from 'react'

import { encodeCell, getActiveSheet, getActiveSheetIndex, getTemplateZonesIntersectRange } from '@/components/elements/luckysheet/utils'
import { LuckysheetCellData, LuckysheetCellVal, LuckysheetConfig, LuckysheetTemplateZone } from '@/types/editor-core/config'
import { ColumnInfo, DataSourceConfig } from '@/types/editor-core/data-source'

import { TemplateZoneConfigPanel } from './template-zone-config-panel'


// 颜色参考 https://flatuicolors.com/palette/defo
const loopZoneBgColor = '#2ECC71' // 统一用一个颜色

/** 根据 zoneIdDict 重新修正循环体的区域 */
function refreshZonesRange(sheet: LuckysheetConfig['data'][number], zones: LuckysheetTemplateZone[]) {
  return _(sheet.celldata)
    .filter(c => !!(c.v as LuckysheetCellVal)?.zoneIdDict)
    .flatMap(c => _.map((c.v as LuckysheetCellVal).zoneIdDict, (_g, zId) => ({ ...c, zoneId: zId })))
    .groupBy(c => c.zoneId)
    .mapValues((cells, zId) => {
      // 取每组最左上、左下、右上和右下的单元格
      const minR = _.minBy(cells, 'r')!.r
      const minC = _.minBy(cells, 'c')!.c
      const maxR = _.maxBy(cells, 'r')!.r
      const maxC = _.maxBy(cells, 'c')!.c
      const originZone = _.find(zones, z => z.id === zId)!
      return {
        ...originZone,
        r: [minR, maxR],
        c: [minC, maxC]
      } as LuckysheetTemplateZone
    })
    .thru(dict => _.map(sheet!.config?.templateZones, z => dict[z.id!]))
    .value()
}

/** 为循环体标记一个背景颜色 */
function indicateTemplateZoneBg(luckysheetCfg: Partial<LuckysheetConfig>, showTemplateZoneBg: number) {
  const templateZones = getActiveSheet(luckysheetCfg)?.config?.templateZones || []
  const fieldColorDict = _.zipObject(
    templateZones.map(z => z.id!),
    templateZones.map((_z, i) => (i === showTemplateZoneBg ? loopZoneBgColor : ''))
  )
  return produce(luckysheetCfg, draft => {
    // 显示循环体背景色，如果区域变了，也要更新

    _.each(draft.data, sheet => {
      _.each(sheet?.celldata, cell => {
        const { r, c, v } = cell
        const zoneColor = _.find(
          fieldColorDict,
          (color, id) =>
            !!color &&
            _.isObject((cell.v as LuckysheetCellVal).zoneIdDict) &&
            id in (cell.v as LuckysheetCellVal).zoneIdDict!
        )

        // 优先显示循环体颜色（大写字符）
        if (zoneColor || _.has(v, 'bgBak')) {
          // 不能用 zoneColor（大写字符）写入 bgBak
          const vAny: any = v
          cell.v = _.isObject(v)
            ? { ...v, bg: zoneColor, bgBak: vAny.bgBak || (/[A-Z]/.test(vAny.bg) ? null : vAny.bg) || null }
            : { bg: zoneColor, v, m: `${v}`, bgBak: null }
        }
      })
    })
  })
}

/** 取消循环体的标记背景颜色 */
export function cancelIndicateTemplateZoneBg(luckysheetCfg: Partial<LuckysheetConfig>) {
  return produce(luckysheetCfg, draft => {
    // 还原原背景色
    _.each(draft.data, sheet => {
      _.each(sheet?.celldata, cell => {
        const { v } = cell
        if (_.isString(v) || _.isNumber(v) || _.isNil(v) || !('bgBak' in v)) {
          return
        }
        v.bg = v.bgBak
        delete v.bgBak
      })
    })
  })
}

/** 获取选区 */
export function getSelectionRangeOfSheet(sheet: LuckysheetConfig['data'][number] | null | undefined) {
  return sheet?.luckysheet_select_save?.[0]
}

export function getInFieldZonePredicate(fieldZones: LuckysheetTemplateZone[]) {
  return (r: number, c: number) =>
    _.some(fieldZones, z => {
      const [minR, maxR] = z.r
      const [minC, maxC] = z.c
      return minR <= r && r <= maxR && minC <= c && c <= maxC
    })
}

export function getCellValFieldName(v: string | number | LuckysheetCellVal) {
  const cellContent = _.isObject(v) ? (v as LuckysheetCellVal).v : v
  return /['"`](.*?)['"`]/.exec(cellContent)?.[1]
}

export function computeNewZoneGroup(zones: LuckysheetTemplateZone[] | undefined, r: number[], c: number[]) {
  const [minColIdx, maxColIdx] = c
  const [minRowIdx, maxRowIdx] = r
  const intersectZones = getTemplateZonesIntersectRange(zones || [], [minColIdx, maxColIdx], [minRowIdx, maxRowIdx])
  const group = intersectZones[0]?.group
  const selectionIntersectZones = _.filter(zones, z => z.group === group || _.some(intersectZones, i => i === z))

  return _.isEmpty(selectionIntersectZones)
    ? encodeCell({ r: minRowIdx, c: minColIdx })
    : selectionIntersectZones![0].group
}

/**
 * 根据字段位置，添加横向循环体
 * 算法：找到字段所在的单元格，然后找到所有的指标单元格，取最小和最大的行列索引，作为循环体的范围
 */
function addTemplateZoneByFieldPos(
  pendingLuckysheetCfg: Partial<LuckysheetConfig>,
  fieldName: string,
  allFieldDict: Record<string, ColumnInfo | null>
) {
  const titleDict = _.keyBy(_.values(allFieldDict), 'title')
  const fieldNameBak = fieldName
  return produce(pendingLuckysheetCfg, draft => {
    _.merge(draft, { data: [{ config: { templateZones: [] } }] }) // 保证templateZone存在
    const sheet = getActiveSheet(draft)!
    const configDraft = sheet.config!
    configDraft.lastUpdatedAt = Date.now() // 强制重新加载数据
    // 区域背景色，只会存在于临时状态，不需要修改
    if (fieldName === '__clear__') {
      configDraft.templateZones = []
      return
    }
    const zones = configDraft.templateZones
    if (fieldName === '__restDim__') {
      const usedDimsSet = new Set(_.map(zones, z => z.field))
      fieldName = _.findKey(allFieldDict, (c, k) => c?.dataType !== 'number' && !usedDimsSet.has(k)) || fieldName
    }
    if (fieldName === '__value__' || fieldName === '__auto__') {
      fieldName = _.findKey(allFieldDict, d => d?.dataType === 'number') || fieldName
    }
    const fieldZones = _.filter(configDraft.templateZones, z => z.field === fieldName)
    const inFieldZonePredicate = getInFieldZonePredicate(fieldZones)
    const fieldTitle = allFieldDict[fieldName]?.title || fieldName
    const targetCell = _.find(sheet.celldata, cell => {
      const { r, c, v } = cell
      if (inFieldZonePredicate(r, c)) {
        return false
      }
      const matched = getCellValFieldName(v)
      return matched === fieldTitle || matched === fieldName
    }) || _.first(sheet.celldata)!

    const templateCells = _.filter(sheet?.celldata, d => getCellValFieldName(d.v)) as LuckysheetCellData[]
    const [metricCells, dimCells] = _.partition(templateCells, cell => {
      const { v } = cell
      const field = getCellValFieldName(v)
      return field && (titleDict[field] || allFieldDict[field])?.dataType === 'number'
    })
    // 判断是否横向循环
    const becomeColumnLoop = !/^__(?:auto|value)__$/.test(fieldNameBak)
      && _.some(metricCells, cell => cell.c === targetCell.c)
    // 算出一个范围，可以容纳所有的指标列和目标维度
    const considerCells = [...metricCells, targetCell]
    const minRowIdx = _.minBy(considerCells, 'r')?.r ?? targetCell.r
    const maxRowIdx = _.maxBy(considerCells, 'r')?.r ?? targetCell.r
    const minColIdx = becomeColumnLoop
      ? _.minBy(considerCells, 'c')?.c ?? targetCell.c
      : _.minBy(sheet?.celldata, 'c')?.c ?? targetCell.c
    const maxColIdx = _.maxBy(considerCells, 'c')?.c ?? targetCell.c

    // 如果选区位于循环体内，那么就将其归到一组
    configDraft.templateZones!.push({
      id: nanoid(5),
      field: fieldNameBak,
      direction: becomeColumnLoop ? 'column' : 'row',
      r: [minRowIdx, maxRowIdx],
      c: [minColIdx, maxColIdx],
      group: computeNewZoneGroup(zones, [minRowIdx, maxRowIdx], [minColIdx, maxColIdx])
    })
  })
}

/** 标记当前选区为循环体 */
function markCurrRangeAsTemplateZone(
  pendingLuckysheetCfg: Partial<LuckysheetConfig>,
  fieldName: string,
  selectionIntersectZones: null | LuckysheetTemplateZone[]
) {
  return produce(pendingLuckysheetCfg, draft => {
    _.merge(draft, { data: [{ config: { templateZones: [] } }] }) // 保证templateZone存在
    const sheet = getActiveSheet(draft)!
    const configDraft = sheet.config!
    configDraft.lastUpdatedAt = Date.now() // 强制重新加载数据
    // 区域背景色，只会存在于临时状态，不需要修改
    if (fieldName === '__clear__') {
      configDraft.templateZones = []
      return
    }
    const selRange = getSelectionRangeOfSheet(sheet)
    if (!selRange) {
      return
    }
    const { column: c, row: r } = selRange
    const group = _.isEmpty(selectionIntersectZones)
      ? encodeCell({ r: r[0], c: c[0] })
      : selectionIntersectZones![0].group
    configDraft.templateZones!.push({ id: nanoid(5), field: fieldName, c, r, group })
  })
}

interface UseTemplateZoneCfgParams {
  pendingLuckysheetCfg: Partial<LuckysheetConfig>
  setPendingLuckysheetCfg: (
    value: ((prevState: Partial<LuckysheetConfig>) => Partial<LuckysheetConfig>) | Partial<LuckysheetConfig>
  ) => void
  dataSourceConfig: DataSourceConfig
  chartData: any[]
  allFieldDict: Record<string, ColumnInfo | null>
}

/** 给单元格添加循环体标记（不能只标记四个角落，删掉最左/右列就会出问题），zone 变动后都应该调用 */
export function assignZoneIdDict(pendingLuckysheetCfg: Partial<LuckysheetConfig>) {
  return produce(pendingLuckysheetCfg, draft => {
    _.each(draft.data, sheet => {
      const zones = _.compact(sheet?.config?.templateZones || [])

      // 循环体内，如果有单元格缺失，则需要补全，不然无法标记颜色
      const cellsDict = _.keyBy(sheet.celldata, encodeCell)
      const allPrePatchCells = _.flatMap(zones, zone => {
        const { r, c } = zone
        const prePatchCells: LuckysheetCellData[] = []
        for (let i = r[0]; i <= r[1]; i++) {
          for (let j = c[0]; j <= c[1]; j++) {
            if (!cellsDict[encodeCell({ r: i, c: j })]) {
              prePatchCells.push({ r: i, c: j, v: '' })
            }
          }
        }
        return prePatchCells
      })
      if (allPrePatchCells.length) {
        sheet.celldata = [...(sheet.celldata || []), ...allPrePatchCells]
      }

      // 需要根据 zone 的范围重新计算 zoneIdDict（不能只标记四个角落）
      let hasChanged = false
      _.each(sheet?.celldata, cell => {
        const { r, c } = cell
        if (!_.isObject(cell.v)) {
          cell.v = { v: cell.v }
        }
        const nextZoneIdDict = _.filter(
          zones,
          ({ r: zr, c: zc }) => _.inRange(r, zr[0], zr[1] + 1) && _.inRange(c, zc[0], zc[1] + 1)
        ).reduce((acc, cur) => {
          acc[cur.id!] = cur.group || 'default'
          return acc
        }, {})
        if (!_.isEqual({ ...cell.v?.zoneIdDict }, nextZoneIdDict)) {
          hasChanged = true
          cell.v.zoneIdDict = nextZoneIdDict
        }
      })
      if (hasChanged) {
        sheet!.config!.lastUpdatedAt = Date.now() // 强制重新加载数据
      }
    })
  })
}

/** 根据 zoneIdDict 更新循环体，并且根据循环体区域，补全内部区域的信息（单元格 + zoneIdDict）; cell 位置变动后都应该调用 */
export function refreshLuckysheetCfgZoneByCellPos(pendingLuckysheetCfg: Partial<LuckysheetConfig>) {
  return produce(pendingLuckysheetCfg, draft => {
    _.each(draft.data, sheet => {
      const zones = sheet?.config?.templateZones || []
      // 如果有 zoneIdDict 则根据它重新修正循环体的区域
      if (!_.isEmpty(zones) && _.some(sheet?.celldata, cell => (cell.v as LuckysheetCellVal)?.zoneIdDict)) {
        const refreshedZones = refreshZonesRange(sheet, zones)
        if (!_.isEqual(_.cloneDeep(zones), refreshedZones)) {
          sheet.config = sheet.config || {}
          sheet!.config.templateZones = refreshedZones
          sheet!.config.lastUpdatedAt = Date.now() // 强制重新加载数据
        }
      }
    })
  })
}

/** 循环体配置 */
export function useTemplateZoneCfg({
  pendingLuckysheetCfg,
  setPendingLuckysheetCfg,
  dataSourceConfig,
  chartData,
  allFieldDict
}: UseTemplateZoneCfgParams) {
  const reState = useReactive({ showTemplateZoneBg: -1 })
  const templateZones = useMemo(
    () => getActiveSheet(pendingLuckysheetCfg)?.config?.templateZones || [],
    [pendingLuckysheetCfg]
  )

  const showTemplateZoneBg = reState.showTemplateZoneBg

  const strFieldsBinding = useMemo(() => {
    const { fieldsBinding, sortedFieldNames } = dataSourceConfig[dataSourceConfig.dataSourceType] || {}

    // 如果没有用户设置对顺序，默认字段顺序：日期，字符，数字
    const orderDict = { date: 0, string: 1, number: 2 }
    const orderedFieldNames = _.isEmpty(sortedFieldNames)
      ? _.orderBy(_.keys(fieldsBinding), k => orderDict[fieldsBinding?.[k]?.dataType || 'string'])
      : (sortedFieldNames as string[])

    const nonNumFields = _.filter(orderedFieldNames, f => /date|string/.test(fieldsBinding?.[f]?.dataType || ''))
    return _.pick(fieldsBinding, nonNumFields)
  }, [dataSourceConfig])

  const cleanTemplateZone = idx => {
    const nextCfg = produce(pendingLuckysheetCfg, draft => {
      _.merge(draft, { data: [{ config: { templateZones: [] } }] }) // 保证templateZone存在
      const sheet = getActiveSheet(draft)!
      const configDraft = sheet.config!
      configDraft.templateZones = _.filter(configDraft.templateZones, (_z, i) => i !== idx)
      configDraft.lastUpdatedAt = Date.now() // 强制重新加载数据
    })
    reState.showTemplateZoneBg = -1
    setPendingLuckysheetCfg(nextCfg)
  }

  const templateZonesFieldTranslate = useMemo(
    () => ({
      ..._.mapValues(strFieldsBinding, (c, fieldName) => c?.title || c?.name || fieldName),
      __value__: '明细值',
      __auto__: '(剩余维度+明细值)',
      __restDim__: '(剩余维度)'
    }),
    [strFieldsBinding]
  )

  // 显示循环体背景色，如果区域变了，也要更新
  const pendingLuckysheetCfgWithBg = useMemo(() => {
    const pendingLuckysheetCfgWithZoneUpdated = refreshLuckysheetCfgZoneByCellPos(pendingLuckysheetCfg)
    const pendingLuckysheetCfgWithZoneIdDict = assignZoneIdDict(pendingLuckysheetCfgWithZoneUpdated)

    return showTemplateZoneBg !== -1
      ? indicateTemplateZoneBg(pendingLuckysheetCfgWithZoneIdDict, showTemplateZoneBg)
      : cancelIndicateTemplateZoneBg(pendingLuckysheetCfgWithZoneIdDict)
  }, [pendingLuckysheetCfg, showTemplateZoneBg])

  const currInflateMode = useMemo(
    () => getActiveSheet(pendingLuckysheetCfg)?.config?.inflateMode,
    [pendingLuckysheetCfgWithBg]
  )
  // 添加循环体规则：
  // 明细表：只能添加明细值循环体，如果添加多个，应该加筛选条件
  // 透视表： 选中区域如果处于循环体内部，则可以添加最近一个分组的下层分组，否则只能从首个分组维度开始（应该加筛选条件）

  // 没有添加过循环体的字段，可以添加循环体
  const unusedZoneFieldDict = useMemo(() => {
    const autoZones = templateZones.filter(z => /^__(value|auto|restDim)__$/.test(z.field))
    const inAutoZonePredicate = getInFieldZonePredicate(autoZones)

    const fieldZoneDict = _.keyBy(templateZones, 'field')
    const chartFields = _.keys(allFieldDict)
    const titleToKeyDict = _.zipObject(chartFields.map(k => allFieldDict[k]?.title || k), chartFields)
    const nameToKeyDict = _.zipObject(chartFields.map(k => allFieldDict[k]?.name || k), chartFields)

    const sheet = getActiveSheet(pendingLuckysheetCfgWithBg)!
    const templateCellUnused = (sheet?.celldata || []).filter(cell => {
      const { r, c, v } = cell
      const fieldTitle = getCellValFieldName(v)
      if (!fieldTitle) {
        return false
      }
      // 还需要判断是否位于 __value__ 等自动的循环体内部，是的话也不能添加
      const fieldName = titleToKeyDict[fieldTitle] || nameToKeyDict[fieldTitle] || fieldTitle
      return !fieldZoneDict[fieldName] && !inAutoZonePredicate(r, c)
    })
    const unusedFieldSet = new Set(templateCellUnused.map(d => getCellValFieldName(d.v)))

    // 先取得对应的字段名称，再判断这些名称是否已经创建了循环体
    return _.pickBy(
      allFieldDict,
      (c, field) => unusedFieldSet.has(field) || unusedFieldSet.has(c?.title || field)
    )
  }, [templateZones, allFieldDict, pendingLuckysheetCfgWithBg])

  const addTemplateZone = fieldName => {
    if (!fieldName) {
      return
    }
    let nextCfg = addTemplateZoneByFieldPos(pendingLuckysheetCfg, fieldName, allFieldDict)
    nextCfg = assignZoneIdDict(nextCfg)

    reState.showTemplateZoneBg = (getActiveSheet(nextCfg)?.config?.templateZones?.length || 0) - 1
    setPendingLuckysheetCfg(nextCfg)
  }

  const inflateMode = useMemo(
    () => getActiveSheet(pendingLuckysheetCfg)?.config?.inflateMode || 'basic',
    [pendingLuckysheetCfg]
  )

  const isAllDimSelected = useMemo(
    () => _.every(unusedZoneFieldDict, c => c?.dataType === 'number'),
    [unusedZoneFieldDict]
  )

  function forceRefreshLuckysheet() {
    setPendingLuckysheetCfg(
      produce(pendingLuckysheetCfg, draft => {
        _.merge(draft, { data: [{ config: {} }] })
        const sheet = getActiveSheet(draft)!
        sheet.config!.lastUpdatedAt = Date.now() // 强制重新加载数据
      })
    )
  }

  const zoneOpts = (currInflateMode === 'pivot'
      ? [
        ..._.map(strFieldsBinding, (col, fieldName) => ({
          label: `${col?.title || fieldName}`,
          key: fieldName,
          disabled: isAllDimSelected || !unusedZoneFieldDict[fieldName]
        })),
        { label: '(剩余维度)', key: '__restDim__', disabled: isAllDimSelected },
        // 不选完全部维度，不能选明细值，因为不能保证是单条数据
        { label: '明细值（只支持纵向）', key: '__value__', disabled: isAllDimSelected },
        { label: '(剩余维度+明细值)（只支持纵向）', key: '__auto__', disabled: isAllDimSelected }
      ]
      : [{ label: '明细值', key: '__value__', disabled: isAllDimSelected }]
  ).map(item => ({
    ...item,
    disabled: false,
    className: item.disabled ? 'text-gray-300' : undefined
  }))

  const getZoneConfigPopoverContent = (z: LuckysheetTemplateZone, i: number) => {
    const moveZone = (from: number, to: number) => {
      const nextCfg = produce(pendingLuckysheetCfgWithBg, draft => {
        const sheet = getActiveSheet(draft)
        const zones = sheet?.config?.templateZones || []
        const zone = zones[from]
        zones[from] = zones[to]
        zones[to] = zone
        sheet!.config!.lastUpdatedAt = Date.now() // 强制重新加载数据
      })
      reState.showTemplateZoneBg = to
      setPendingLuckysheetCfg(nextCfg)
    }

    const toggleDirection = () => {
      const nextCfg = produce(pendingLuckysheetCfgWithBg, draft => {
        const sheet = getActiveSheet(draft)
        const zones = sheet?.config?.templateZones || []
        const zone = zones[i]
        zone.direction = zone.direction === 'column' ? 'row' : 'column'
        sheet!.config!.lastUpdatedAt = Date.now() // 强制重新加载数据
      })
      setPendingLuckysheetCfg(nextCfg)
    }

    const title = (
      <>
        <Dropdown
          menu={{
            items: zoneOpts,
            onClick: e => {
              const nextCfg = produce(pendingLuckysheetCfgWithBg, draft => {
                const sheet = getActiveSheet(draft)
                const zones = sheet?.config?.templateZones || []
                const zone = zones[i]
                zone.field = e.key
                sheet!.config!.lastUpdatedAt = Date.now() // 强制重新加载数据
              })
              setPendingLuckysheetCfg(nextCfg)
            }
          }}
          trigger={['click']}
        >
          <span
            className='mr-1 text-primary-500 cursor-pointer'
            title='点击修改字段'
          >{templateZonesFieldTranslate[z.field]}</span>
        </Dropdown>
        循环体配置（{z.group || '默认'}组）

        {inflateMode === 'basic' ? null : (z.direction === 'column'
          ? (
            <ArrowRightOutlined
              className='float-right text-primary-500 leading-6'
              onClick={toggleDirection}
              title='切换为横向循环'
            />
          )
          : (
            <ArrowDownOutlined
              className='float-right text-primary-500 leading-6'
              onClick={toggleDirection}
              title='切换为纵向循环'
            />
          ))}
      </>
    )
    const contentGen = function () {
      return (
        <TemplateZoneConfigPanel
          key={z.id}
          chartData={chartData}
          value={z}
          onChange={next => {
            reState.showTemplateZoneBg = -1
            const sheetIdx = getActiveSheetIndex(pendingLuckysheetCfgWithBg)
            const nextCfg = produce(pendingLuckysheetCfgWithBg, draft => {
              const sheet = draft.data![sheetIdx]!
              sheet.config = sheet.config || {}
              sheet.config.templateZones![i] = next
              sheet.config.lastUpdatedAt = Date.now() // 强制重新加载数据
            })
            setPendingLuckysheetCfg(assignZoneIdDict(nextCfg))
          }}
          onClose={() => {
            reState.showTemplateZoneBg = -1
            forceRefreshLuckysheet()
          }}
          query={dataSourceConfig}
          inflateMode={inflateMode}
          luckysheetCfg={pendingLuckysheetCfgWithBg}
          allFieldDict={allFieldDict}
          extraBtns={
            <span>
              <CaretLeftOutlined
                className={classNames('mr-4', {
                  'cursor-not-allowed text-gray-300': i === 0,
                  'text-primary-500': i !== 0
                })}
                onClick={i === 0 ? undefined : () => moveZone(i, i - 1)}
              />
              <CaretRightOutlined
                className={classNames('mr-4', {
                  'cursor-not-allowed text-gray-300': i === _.size(templateZones) - 1,
                  'text-primary-500': i !== _.size(templateZones) - 1
                })}
                onClick={i === _.size(templateZones) - 1 ? undefined : () => moveZone(i, i + 1)}
              />
              <MergeCellsOutlined
                className={classNames('mr-4 cursor-pointer', {
                  'text-primary-500': z.mergeConstCells
                })}
                title='将长宽为 1 的常量单元格标记为合并'
                onClick={() => {
                  const temp = _.cloneDeep(pendingLuckysheetCfgWithBg)
                  const nextCfg = produce(temp, draft => {
                    const sheet = getActiveSheet(draft)!
                    const zones = sheet.config?.templateZones || []
                    const zone = zones[i]
                    const { r, c, direction } = zone
                    const [rs, re] = r
                    const [cs, ce] = c
                    // 只对单行或单列的循环体进行合并
                    if (direction === 'column' && ce - cs > 0) {
                      message.error('横向循环体只能合并单列')
                      return
                    }
                    if (direction === 'row' && re - rs > 0) {
                      message.error('纵向循环体只能合并单行')
                      return
                    }
                    zone.mergeConstCells = !zone.mergeConstCells
                    // 强制重新加载数据
                    sheet!.config!.lastUpdatedAt = Date.now()
                  })
                  setPendingLuckysheetCfg(nextCfg)
                }}
              />
            </span>
          }
        />
      )
    }
    return { title, contentGen }
  }

  const dom = (
    <span>
      循环体：
      <Tooltip
        key='hint'
        className='leading-8 mr-2.5'
        title={
          <div>
            1. 明细表只需要标注【明细值】循环体，用于读取字段值公式
            <br />
            2. 循环体内部可以包裹其他分组维度的循环体，纵向和横向循环体可以有重叠部分
            <br />
            3. 透视表需要按分组维度顺序标注循环体，明细值循环体 对应 读取字段值，维度循环体 对应 汇总公式
            <br />
            4. 标注循环体后不应再增减行列，如需修改请先删除全部循环体
          </div>
        }
      >
        <QuestionCircleOutlined />
      </Tooltip>
      {_.map(templateZones, (z, i) => {
        const c = getZoneConfigPopoverContent(z, i)
        return (
          <Popover
            key={i}
            placement='top'
            overlayClassName='[&_.ant-popover-inner-content]:pt-0'
            title={c.title}
            content={c.contentGen}
            open={showTemplateZoneBg === i}
            onOpenChange={open => {
              if (!open) {
                return
              }
              reState.showTemplateZoneBg = i
              forceRefreshLuckysheet()
            }}
            trigger={['click']}
            mouseLeaveDelay={1}
          >
            <Tag
              color={showTemplateZoneBg !== i ? undefined : loopZoneBgColor}
              className='cursor-pointer'
              closable
              onClose={e => {
                e.preventDefault()
                e.stopPropagation()
                cleanTemplateZone(i)
              }}
              onClick={() => {
                reState.showTemplateZoneBg = showTemplateZoneBg === i ? -1 : i
                forceRefreshLuckysheet()
              }}
            >
              {z.direction === 'column' ? (
                <ArrowRightOutlined className='mr-1' />
              ) : (
                <ArrowDownOutlined className='mr-1' />
              )}
              {templateZonesFieldTranslate[z.field]}
              {_.some(templateZones, (oz, j) => i !== j && oz.field === z.field) && `(${z.group || '默认组'})`}
            </Tag>
          </Popover>
        )
      })}
      <Dropdown placement='top' menu={{ items: zoneOpts, onClick: e => addTemplateZone(e.key) }}>
        <Tag key='__new__' className={classNames('bg-white border-dashed')} onClick={_.noop}>
          <PlusOutlined /> 循环体
        </Tag>
      </Dropdown>
      {_.isEmpty(templateZones) ? null : (
        <Button size='small' danger ghost icon={<DeleteOutlined />} onClick={() => addTemplateZone('__clear__')}>
          清除所有循环体（{_.size(templateZones)} 个）
        </Button>
      )}
    </span>
  )
  return {
    reState,
    dom,
    pendingLuckysheetCfgWithBg
  }
}
