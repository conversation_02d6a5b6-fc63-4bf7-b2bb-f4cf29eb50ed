import _ from 'lodash'

// 常用 excel 函数

/** SUM函数：用于求和 =SUM (A2:A6) */
export function sumFn(vals: number[][]) {
  return _.flatten(vals).reduce((a, b) => a + (+b), 0)
}

// IF函数：用于条件判断  =IF (B2>=60,“合格”,“不合格”)
export function ifFn(condition: boolean, trueValue: any, falseValue: any) {
  return condition ? trueValue : falseValue
}

// COUNTIF函数：用于条件计数
export function countifFn(
  vals: number[][],
  criteria: string | number | boolean
) {
  return _.flatten(vals).filter(v => v === criteria).length
}

// SUMIF函数：用于条件求和
export function sumifFn(
  checkVals: number[][],
  criteria: string | number | boolean,
  sumVals: any[]
) {
  return _.flatten(checkVals)
    .map((v, i) => (v === criteria ? sumVals[i] : 0))
    .reduce((a, b) => a + (+b), 0)
}

// AVERAGE函数：用于求平均值
export function averageFn(vals: number[][]) {
  const numbers = _.flatten(vals)
  return numbers.reduce((a, b) => a + (+b), 0) / numbers.length
}

// iferror函数：用于判断是否有错误，如果有错误则返回指定值
export function iferrorFn(value: any, valueIfError: any) {
  return Number.isFinite(value) ? value : valueIfError
}
