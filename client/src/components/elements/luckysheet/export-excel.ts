import { require as d3Require } from 'd3-require'
import _ from 'lodash'

import { formatVal } from '@/components/elements/luckysheet/ce-renderer'
import { toArgbHex } from '@/components/elements/luckysheet/utils'
import { LuckysheetConfig, LuckysheetDataConfig } from '@/types/editor-core/config'

/** luckysheet 导出 excel  定义异步函数 exportExcel，接收 luckysheet 配置和标题作为参数 */
export async function exportExcel(luckysheetCfg: LuckysheetConfig, title: string | null = null) {
  // 使用 d3Require 异步加载 exceljs 库
  const ExcelJS = await d3Require(
    `${window.location.origin}/custom-charts/_deps/cdn.jsdelivr.net/npm/exceljs@4.3.0/dist/exceljs.min.js`
  )
  // 定义 transToData 函数，将 celldata 转换为二维数组
  const transToData = celldata => {
    const row = Math.max(...celldata.map(item => item.r))
    const column = Math.max(...celldata.map(item => item.c))
    const data = Array.from({ length: row + 1 }, () => Array.from({ length: column + 1 }, () => null))
    for (let i = 0; i < celldata.length; i++) {
      const { r, c, v } = celldata[i]
      data[r][c] = v
    }
    return data
  }

  // 定义 setMerge 函数，设置合并单元格
  function setMerge(luckyMerge: LuckysheetDataConfig['merge'], worksheet) {
    if (!luckyMerge) return
    const mergearr = Object.values(luckyMerge)
    mergearr.forEach(elem => {
      // elem格式：{r: 0, c: 0, rs: 1, cs: 2}
      // 按开始行，开始列，结束行，结束列合并（相当于 K10:M12）
      worksheet.mergeCells(elem.r + 1, elem.c + 1, elem.r + elem.rs, elem.c + elem.cs)
    })
  }

  // 定义 fillConvert 函数，转换背景颜色
  function fillConvert(bg) {
    if (!bg) return {}
    return { type: 'pattern', pattern: 'solid', fgColor: { argb: toArgbHex(bg) } }
  }

  // luckysheet：ff(样式), fc(颜色), bl(粗体), it(斜体), fs(大小), cl(删除线), ul(下划线)
  function fontConvert(ff = 0, fc = '#000000', bl = 0, it = 0, fs = 10, cl = 0, ul = 0) {
    const luckyToExcel = {
      0: '微软雅黑',
      1: '宋体（Song）',
      2: '黑体（ST Heiti）',
      3: '楷体（ST Kaiti）',
      4: '仿宋（ST FangSong）',
      5: '新宋体（ST Song）',
      6: '华文新魏',
      7: '华文行楷',
      8: '华文隶书',
      9: 'Arial',
      10: 'Times New Roman ',
      11: 'Tahoma ',
      12: 'Verdana',
      num2bl(num) {
        return num !== 0
      }
    }

    const font = {
      name: luckyToExcel[ff],
      family: 1,
      size: fs,
      color: { argb: toArgbHex(fc) },
      bold: luckyToExcel.num2bl(bl),
      italic: luckyToExcel.num2bl(it),
      underline: luckyToExcel.num2bl(ul),
      strike: luckyToExcel.num2bl(cl)
    }

    return font
  }

  // luckysheet:vt(垂直), ht(水平), tb(换行), tr(旋转)
  function alignmentConvert(vt = 'default', ht = 'default', tb = 'default', tr = 'default') {
    const luckyToExcel = {
      vertical: {
        0: 'middle',
        1: 'top',
        2: 'bottom',
        default: 'middle'
      },
      horizontal: {
        0: 'center',
        1: 'left',
        2: 'right',
        default: 'left'
      },
      wrapText: {
        0: false,
        1: false,
        2: true,
        default: false
      },
      textRotation: {
        0: 0,
        1: 45,
        2: -45,
        3: 'vertical',
        4: 90,
        5: -90,
        default: 0
      }
    }

    const alignment = {
      vertical: luckyToExcel.vertical[vt],
      horizontal: luckyToExcel.horizontal[ht],
      wrapText: luckyToExcel.wrapText[tb],
      textRotation: luckyToExcel.textRotation[tr]
    }
    return alignment
  }

  // 定义 setStyleAndValue 函数，设置单元格样式和值
  function setStyleAndValue(cellArr, worksheet) {
    if (!Array.isArray(cellArr)) return
    cellArr.forEach((row, rowid) => {
      row.every((cell, columnid) => {
        if (!cell) return true
        const cellVal = formatVal(cell)
        const fill = fillConvert(cell.bg)
        const font = fontConvert(cell.ff, cell.fc, cell.bl, cell.it, cell.fs, cell.cl, cell.ul)
        const alignment = alignmentConvert(cell.vt, cell.ht, cell.tb, cell.tr)
        const value = cell.f ? { formula: cell.f, result: cellVal } : cellVal
        const target = worksheet.getCell(rowid + 1, columnid + 1)
        target.fill = fill
        target.font = font
        target.alignment = alignment
        target.value = value
        return true
      })
    })
  }

  // 对应luckysheet的config中borderinfo的的参数
  function borderConvert(borderType, style = 1, color = '#000') {
    if (!borderType) return {}
    const luckyToExcel = {
      type: {
        'border-all': 'all',
        'border-top': 'top',
        'border-right': 'right',
        'border-bottom': 'bottom',
        'border-left': 'left'
      },
      style: {
        0: 'none',
        1: 'thin',
        2: 'hair',
        3: 'dotted',
        4: 'dashDot', // 'Dashed',
        5: 'dashDot',
        6: 'dashDotDot',
        7: 'double',
        8: 'medium',
        9: 'mediumDashed',
        10: 'mediumDashDot',
        11: 'mediumDashDotDot',
        12: 'slantDashDot',
        13: 'thick'
      }
    }
    const template = { style: luckyToExcel.style[style], color: { argb: toArgbHex(color) } }
    const border: any = {}
    if (luckyToExcel.type[borderType] === 'all') {
      border.top = template
      border.right = template
      border.bottom = template
      border.left = template
    } else {
      border[luckyToExcel.type[borderType]] = template
    }
    return border
  }

  // 定义 setBorder 函数，设置边框
  function setBorder(luckyBorderInfo, worksheet) {
    if (!Array.isArray(luckyBorderInfo)) return
    luckyBorderInfo.forEach(elem => {
      const { borderType, style, color, range } = elem
      const border = borderConvert(borderType, style, color)

      range.forEach(({ row, column }) => {
        for (let i = row[0]; i <= row[1]; i++) {
          for (let j = column[0]; j <= column[1]; j++) {
            const cell = worksheet.getCell(i + 1, j + 1)
            cell.border = border
          }
        }
      })
    })
  }


  // 设置行高列宽
  function setWorksheetDimensions(luckyConfig: LuckysheetDataConfig, worksheet: any) {
    const { columnlen, rowlen } = luckyConfig
    _.keys(columnlen!).forEach(key => {
      const columnIndex = _.parseInt(key, 10)
      const width = columnlen![key] / 6 // 算出来的列宽可能不准确
      worksheet.columns[columnIndex].width = width
    })

    _.keys(rowlen!).forEach(key => {
      const rowIndex = _.parseInt(key, 10)
      const height = rowlen![key] / 1.5 // 算出来的行高可能不准确
      worksheet.getRow(rowIndex + 1).height = height
    })
  }


  // 参数为luckysheet.getluckysheetfile()获取的对象
  const toBuffer = async () => {
    // 1.创建工作簿，可以为工作簿添加属性
    const workbook = new ExcelJS.Workbook()
    // 2.创建表格，第二个参数可以配置创建什么样的工作表
    luckysheetCfg.data.forEach(table => {
      try {
        const worksheet = workbook.addWorksheet(table.name)
        // 3.设置单元格合并,设置单元格边框,设置单元格样式,设置值
        const gridData = transToData(table.celldata)
        if (gridData.length === 0) return
        setStyleAndValue(gridData, worksheet)
        if (table.config?.merge) setMerge(table.config!.merge, worksheet)
        setBorder(table.config!.borderInfo, worksheet)
        // 设置行高列宽
        setWorksheetDimensions(table.config!, worksheet)
        return
      } catch (err) {
        console.error('export error:', table)
        console.error(err)
      }
    })
    // 4.写入 buffer
    // const buffer = await workbook.xlsx.writeBuffer()
    return workbook.xlsx.writeBuffer() // promise
  }

  // const luckysheet1 = window.luckysheet
  const data = await toBuffer()
  const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
  const blob = new Blob([data], { type: EXCEL_TYPE })
  const filename = `${title || '导出'}.xlsx`

  if ((navigator as any).msSaveBlob) {
    // IE 10+
    (navigator as any).msSaveBlob(blob, filename)
  } else {
    const link = document.createElement('a')
    if (link.download !== undefined) {
      // feature detection
      // Browsers that support HTML5 download attribute
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', filename)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }
}
