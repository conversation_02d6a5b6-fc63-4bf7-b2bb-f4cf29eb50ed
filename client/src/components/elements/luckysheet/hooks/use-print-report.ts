import { useMemoizedFn } from 'ahooks'
import { message } from 'antd'
import _ from 'lodash'
import { useState } from 'react'

import { request } from '@/utils/request'



// 处理dom 元素
const handleDom = (dom: HTMLElement, style) => {
  const html = document.querySelector('html')!.cloneNode(true) as HTMLElement
  const head = html.querySelector('head') as HTMLElement
  head.appendChild(style)
  const body = html.querySelector('body') as HTMLElement
  body.innerHTML = dom.outerHTML
  return html
}

// 创建iframe组件打印,将传入的 dom 打印
const iframePrint = async (dom: HTMLElement, style: HTMLStyleElement) => {
  const iframe = document.createElement('iframe')
  const html = handleDom(dom, style)
  // 将打印内容添加到iframe中
  iframe.srcdoc = html.outerHTML
  iframe.style.display = 'none'
  document.body.appendChild(iframe)
  iframe.contentWindow?.print()
  // 打印完成后，删除iframe
  iframe.onload = () => {
    requestAnimationFrame(() => {
      document.body.removeChild(iframe)
    })
  }
}

// 导出pdf
const pdfDomPrint = async (dom: HTMLElement, style: HTMLStyleElement, title: string, printType) => {
  const html = handleDom(dom, style)
  // 获取当前页面的根url
  const originUrl = window.location.origin

  try {
    const blob = await request('/abi/api/export-file', {
      method: 'POST',
      responseType: 'blob',
      contentType: 'text/html',
      credentials: 'include',
      data: {
        dom: _.toString(html.outerHTML),
        name: `${title}.${printType}`,
        url: originUrl,
        type: printType
      }
    })

    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${title}.pdf`
    a.click()
    window.URL.revokeObjectURL(url)
  } catch (err: any) {
    if (err.name === 'AbortError') {
      console.log('export page aborted')
    } else {
      console.error('export page error:', err)
    }
  }
}


/**
 * 打印id为domId的报表
 * @param domId 打印的元素id
 * @return { onPrintReport, printLoading }
 * onPrintReport 调用打印
 * PrintLoading 是否正在生成打印元素
 * */
export const usePrintReport = (domId: string, screenTitle = '报表') => {
  const [printLoading, setPrintLoading] = useState<boolean>(false)
  const handlePrintFn = _.debounce(async (printType, cancel) => {
    const container = document?.getElementById(`dev-${domId}`) || document?.getElementById(`${domId}`)
    if (!container) return message.error('报表组件出现问题')
    // 创建一个新的<div>元素作为打印内容容器
    const printableElement = document.createElement('div')
    printableElement.className = 'printable-element'
    // document.body.appendChild(printableElement)
    let containers =
      container?.querySelectorAll('.relative')
    if (containers?.length === 0) {
      containers = container?.querySelectorAll('.print-luckysheet-table')
    }

    // 处理打印样式
    const style = document.createElement('style')
    // 设置打印样式
    style.innerHTML = `
    table thead {
      display: none !important;
    }

    .print-luckysheet-table{
      display: block !important;
    }

    .jexcel_row {
      display: none !important;
    }

    .jexcel {
      border-collapse: collapse !important;
      table-layout: fixed !important;
      white-space: nowrap !important;
      empty-cells: show !important;
    }

    .jexcel > tbody > tr > td {
      border: none;
    }
  `

    containers!.forEach(element => {
      const div = element.cloneNode(true) as HTMLElement
      div.style.display = 'block'
      div.style.pageBreakAfter = 'always'
      printableElement.appendChild(div)
    })

    try {
      if (printType === 'print') {
        await iframePrint(printableElement, style)
      }
      if (printType === 'pdf') {
        await pdfDomPrint(printableElement, style, screenTitle, printType)
      }
    } catch (error: any) {
      console.error(error.message)
      message.error('打印错误')
    } finally {
      setPrintLoading(false)
      cancel()
    }
  }, 1000)

  const onPrintReport = useMemoizedFn(() => {
    // 当触发打印时，将状态设置为true
    setPrintLoading(true)
    const cancel = message.loading('正在打印中，请稍后...')
    setTimeout(() => handlePrintFn('print', cancel), 1000)
  })

  const onPrintPdf = useMemoizedFn(() => {
    // 当触发打印时，将状态设置为true
    setPrintLoading(true)
    const cancel = message.loading('正在打印中，请稍后...')
    setTimeout(() => handlePrintFn('pdf', cancel), 1000)
  })

  return {
    onPrintPdf,
    onPrintReport,
    printLoading
  }
}
