import './index.less'

import { EditOutlined, RollbackOutlined } from '@ant-design/icons'
import { useDebounceFn, useDeepCompareEffect, useMemoizedFn, useSize } from 'ahooks'
import { Button, Empty, message } from 'antd'
import classNames from 'classnames'
import dayjs from 'dayjs'
import _ from 'lodash'
import * as React from 'react'
import { useEffect, useMemo, useRef, useState } from 'react'

import { bindDataToLuckySheet } from '@/components/elements/luckysheet/bind-data'
import { CERenderer } from '@/components/elements/luckysheet/ce-renderer'
import { useLuckysheetConfigModal } from '@/components/elements/luckysheet/edit-modal/luckysheet-config-modal'
import { exportExcel } from '@/components/elements/luckysheet/export-excel'
import { usePrintReport } from '@/components/elements/luckysheet/hooks/use-print-report'
import { LuckysheetRenderer } from '@/components/elements/luckysheet/luckysheet-renderer'
import { LuckysheetCompConfig, LuckySheetProps } from '@/components/elements/luckysheet/types'
import { computeLuckysheetTotalHeight, genLuckysheetInitConfig, readOnly } from '@/components/elements/luckysheet/utils'
import { LUCKYSHEET_URL } from '@/consts/screen'
import type { LuckysheetConfig } from '@/types/editor-core/config'
import { useComponentPosFromCanvas } from '@/utils/dom'
import { isComponentHasDataSource } from '@/utils/editor-core/element'
import { PubSubVerbose as PubSub } from '@/utils/pubsub'


/** luckysheet 组件，config 里面保存的是模板，最终效果需要结合实时查得的数据生成 */
export default function LuckysheetComponent(props: LuckySheetProps) {
  const { config, idPrefix, meta, dataSource, data, onEvents, runtimeAPI, isPreview, style, define } = props
  const screenTitle = runtimeAPI?.getScreenInfo()?.title
  const rootRef = useRef<HTMLDivElement | null>(null)
  const size = useSize(rootRef)
  // transform 作用于上层，这里被清掉了，需要重新获取位置
  const componentKey = `${idPrefix}${meta.key}`
  const compPos = useComponentPosFromCanvas(componentKey)
  const moduleAddress = define.extraConfig?.moduleAddress || LUCKYSHEET_URL
  const compVersion = define.version
  const { onPrintReport, onPrintPdf, printLoading } = usePrintReport(meta.key, screenTitle)
  // 编辑模式时，根据组件高度限制渲染的条数，优化渲染性能
  const configAdaptViewMode = useMemo(
    () =>
      !isPreview
        ? {
          ...config,
          pageHeight: Math.max(config.pageHeight || 100, style.height + compPos.top + 20),
          printMode: false
        }
        : {
          ...config,
          printMode: printLoading ? 'reportPrint' : config.printMode
        },
    [config, isPreview, style.height, compPos.top, printLoading]
  )
  const { luckysheetConfig, ...restCfg } = configAdaptViewMode

  const injectHooks = useMemo(
    () => ({
      workbookCreateAfter: () => {
        // 目前没有需要注入的方法
      }
    }),
    []
  )
  const [pendingState, setPendingState] = useState(() => {
    if (config.luckysheetConfigModified) {
      return config.luckysheetConfigModified
    }
    return bindDataToLuckySheet(
      readOnly(luckysheetConfig || genLuckysheetInitConfig(dataSource, 'basic')),
      dataSource,
      data,
      restCfg,
      injectHooks,
      compPos
    )
  })

  // 如果没有配置数据，这个函数是一个空函数
  const exportExcelFunc = useMemoizedFn(() => {
    if (!isComponentHasDataSource({ dataSource })) {
      message.warn('请先绑定数据源，再进行导出 Excel')
    }
    const restCfgPage = { ...restCfg, pageSize: 0 }
    const inflatedLuckySheet = bindDataToLuckySheet(
      readOnly(luckysheetConfig || genLuckysheetInitConfig(dataSource, 'basic')),
      dataSource,
      data,
      restCfgPage,
      injectHooks,
      compPos
    )
    exportExcel(inflatedLuckySheet as LuckysheetConfig, `${screenTitle}_${dayjs().format('YYYY-MM-DD')}`)
  })

  const getContentHeight = useMemoizedFn(newPageHeight =>
    computeLuckysheetTotalHeight(pendingState, configAdaptViewMode, newPageHeight, compPos)
  )


  useEffect(() => {
    // 触发初始化事件
    onEvents?.didMounted?.()

    let exportExcelSubToken: string | null = null
    let printReportSubToken: string | null = null
    let printPdfSubToken: string | null = null
    let getContentHeightSubToken: string | null = null
    // 只在点击打印前订阅
    if (runtimeAPI && !runtimeAPI.isPrintPreview()) {
      exportExcelSubToken = PubSub.subscribe(`notify:${meta.key}/exportExcel`, exportExcelFunc)
      printReportSubToken = PubSub.subscribe(`notify:${meta.key}/printReport`, onPrintReport)
      printPdfSubToken = PubSub.subscribe(`notify:${meta.key}/printPdf`, onPrintPdf)
      // 注入获取内容高度的方法，以便打印使用 client/src/cores/evaction/config-resolver/print-page.tsx
      getContentHeightSubToken = PubSub.subscribe(
        `notify:${meta.key}/getContentHeightCalcFn`,
        (_msg, cb) => cb(getContentHeight)
      )
    }

    return () => {
      PubSub.unsubscribe(exportExcelSubToken as string)
      PubSub.unsubscribe(printReportSubToken as string)
      PubSub.unsubscribe(getContentHeightSubToken as string)
      PubSub.unsubscribe(printPdfSubToken as string)
    }
  }, [])

  // 数据绑定
  const { run: inflateDebounced } = useDebounceFn(
    () => {
      const inflatedLuckySheet = config.luckysheetConfigModified
        ? config.luckysheetConfigModified
        : bindDataToLuckySheet(
          readOnly(luckysheetConfig || genLuckysheetInitConfig(dataSource, 'basic')),
          dataSource,
          data,
          restCfg,
          injectHooks,
          compPos
        )
      setPendingState(inflatedLuckySheet)
      // 绑定分页器时需要
      onEvents?.onContentUpdated?.(inflatedLuckySheet)
    },
    { wait: 1000, leading: true }
  )

  useDeepCompareEffect(inflateDebounced, [
    !onEvents?.onContentUpdated,
    configAdaptViewMode,
    data,
    size,
    compPos
    // dataSource 不能加入依赖，某些字段删掉后，会导致循环体逻辑不对
  ])

  const RenderComp: (p) => any = configAdaptViewMode.renderer === 'ce' ? CERenderer : LuckysheetRenderer
  const { reactiveState: state, modal } = useLuckysheetConfigModal({
    dataSourceConfig: dataSource,
    chartData: data,
    // 暂不支持配置超链接
    pages: [],
    previewUrl: '',
    value: { ...config, luckysheetConfig: pendingState } as LuckysheetCompConfig,
    onChange: nextCfg => {
      const comp = runtimeAPI?.getComponentByKey(meta.key)
      comp?.setConfig(prev => ({ ...prev, luckysheetConfigModified: nextCfg }))

      // 通知报表，配置已经修改
      const ev: any = new Event('reportComponentSave')
      ev.key = componentKey
      window.dispatchEvent(ev)
    },
    moduleAddress,
    compVersion
  })

  const isNoData = _.isEmpty(data) || data[0].resultSet?.length === 0
  // 预览时需要遮盖 45.5 * 19.5 区域，是列头和序号列；还有 12 px 的滚动条；但是底部的滚动条保留
  // 打印时展开所有行，不需要遮盖
  return (
    <div
      className={classNames('local-element-luckysheet group h-full [.print-views-list-box_&]:!h-auto')}
      style={style}
      ref={rootRef}
    >
      {isNoData && !config.hideNoData
        ? (
          <Empty className='center-of-relative' />
        )
        : (
          <RenderComp
            componentKey={componentKey}
            luckysheetCompConfig={configAdaptViewMode}
            inflatedLuckysheetConfig={pendingState}
            moduleAddress={moduleAddress}
            compVersion={compVersion}
          />
        )}
      {!config.allowPreviewEdit || isNoData ? null : (
        <div
          className={classNames(
            'absolute right-2 bottom-2 [.fix-allow-static-component_&]:collapse',
            'opacity-0 group-hover:opacity-100 transition-opacity duration-300 ease-in-out'
          )}
        >
          <Button
            onClick={() => {
              state.modalVisible = true
            }}
          >
            <EditOutlined />
          </Button>
          {!config.luckysheetConfigModified ? null : (
            <Button
              className='ml-2'
              onClick={() => {
                const comp = runtimeAPI?.getComponentByKey(meta.key)
                comp?.setConfig(() => ({}))
              }}
            >
              <RollbackOutlined />
            </Button>
          )}
          {modal}
        </div>
      )}
    </div>
  )
}
