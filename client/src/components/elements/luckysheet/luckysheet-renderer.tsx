import { LoadingOutlined } from '@ant-design/icons'
import type { Sheet } from '@fortune-sheet/core/dist/types'
import type { WorkbookInstance } from '@fortune-sheet/react'
import { useDebounce, useDebounceEffect } from 'ahooks'
import _ from 'lodash'
import * as React from 'react'
import { Suspense, useRef } from 'react'

import { LuckysheetCompConfig } from '@/components/elements/luckysheet/types'
import { ComponentKey } from '@/types/editor-core/component'
import { LuckysheetConfig, LuckySheetSettings } from '@/types/editor-core/config'


export const WorkbookLazy = React.lazy(async () => {
  await import('@fortune-sheet/react/dist/index.css')
  const lib = await import('@fortune-sheet/react')
  return { default: lib.Workbook }
})

interface LuckysheetRendererProps {
  componentKey: ComponentKey
  luckysheetCompConfig: LuckysheetCompConfig
  inflatedLuckysheetConfig: Partial<LuckysheetConfig>
  moduleAddress: string
  compVersion: string
}

export function updateSheets(sheets: Sheet[], nextSheets: LuckySheetSettings[], isPreview: boolean) {
  // 解除挟持锁定
  const cloneDeepSafe = obj => _.cloneDeepWith(obj, o => {
    if (_.isArrayLikeObject(o)) {
      return _.map(o, cloneDeepSafe)
    }
    return _.isObject(o) ? _.mapValues(o, cloneDeepSafe) : o
  })
  const newSheets: Sheet[] = _.range(nextSheets.length).map(i => ({
    ...cloneDeepSafe(sheets[i]),
    name: `Sheet${i + 1}`
  }))

  _.forEach(nextSheets, (s, i) => {
    if (!newSheets[i]) {
      newSheets[i] = { name: `Sheet${i + 1}` }
    }
    // 不能覆盖原选区
    Object.assign(
      newSheets[i],
      _.cloneDeep(_.omit(s, 'celldata', 'data', 'luckysheet_selection_range', 'luckysheet_select_save'))
    )
    newSheets[i].images = []
    newSheets[i].celldata = cloneDeepSafe(s.celldata)
    let datum = newSheets[i].data || []
    if (_.isEmpty(datum)) {
      const emptyRow = Array.from({ length: 120 }, () => null)
      datum = Array.from({ length: 84 }, () => [...emptyRow])
      newSheets[i].data = datum
    }

    // 初始化数据
    const cellDataDict = _.keyBy(s.celldata, c => `${c.r}_${c.c}`)
    for (let r = 0; r < datum.length; r++) {
      for (let c = 0; c < datum[r].length; c++) {
        const cellData = cellDataDict[`${r}_${c}`]
        const cellVal = cellData && cellData.v
        const cv = cellVal && Object.isFrozen(cellVal) ? cloneDeepSafe(cellVal) : cellVal || null
        // if (isPreview) {
        //   // 需要更新显示值 m
        //   window.luckysheet.setcellvalue(r, c, datum, cv)
        // } else {
        //   // 不能调用 setcellvalue，否则会导致数值格式被覆盖
        //   datum[r][c] = cv
        // }
        datum[r][c] = cv
      }
    }
  })

  return newSheets
}

/** fortune-sheet 渲染器，不适合打印 */
export function LuckysheetRenderer(props: LuckysheetRendererProps) {
  const { luckysheetCompConfig: config, inflatedLuckysheetConfig } = props
  const ref = useRef<WorkbookInstance>(null)

  useDebounceEffect(() => {
    const inst: WorkbookInstance | null = ref.current
    if (!inst) return
    const currSheets = inst.getAllSheets()
    const next = updateSheets(currSheets, inflatedLuckysheetConfig.data || [], false)
    inst.updateSheet(next)
  }, [inflatedLuckysheetConfig, config], { wait: 600 })

  // images 改为数组，可能是对象，fortune-sheet 暂未支持图片
  const sheetsWithImagesFix = _.map(inflatedLuckysheetConfig.data as Sheet[], sheet =>
    ({ ...sheet, images: [] })
  )

  const sheets = useDebounce(sheetsWithImagesFix, { wait: 1000 })

  return (
    <div
      style={{
        position: 'absolute',
        left: '-46px',
        top: '-20px',
        width: 'calc(100% + 46px + 12.5px)',
        height: config.showSheetBar ? 'calc(100% + 20px)' : 'calc(100% + 20px + 12.5px)'
      }}
    >
      <Suspense fallback={<LoadingOutlined />}>
        <WorkbookLazy
          ref={ref}
          data={sheets?.length ? sheets : [{ name: 'Sheet1' }]}
          showToolbar={false}
          showSheetTabs={false}
          showFormulaBar={false}
          sheetTabContextMenu={[]}
          cellContextMenu={[]}
        />
      </Suspense>
    </div>
  )
}
