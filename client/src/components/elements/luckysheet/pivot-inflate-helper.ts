import _ from 'lodash'

import { InflatedResult, LoopState, LuckysheetCompConfig } from '@/components/elements/luckysheet/types'
import {
  calcFooterRowCnt,
  calcHeaderRowCnt,
  decodeColMemo,
  encodeCol,
  extractBorderInfo,
  extractCellSize,
  extractCondFormatInfo,
  extractHyperLinkInfo,
  extractImageInfo,
  generateCalcChain,
  genMergeConfig, getEdge,
  getRowHeight,
  getTemplateZonesIntersectRange,
  inflatedTemplateEachRow,
  mergeConstCellsByZones, patchOutsideCellData,
  resolveCellTemplate,
  rowCollByCellIndex,
  takeGenWhile,
  templateCelldataCombineBorderInfo,
  templateCelldataCombineCellSize,
  templateCellMarkCondFormat,
  templateCellMarkHyperLink,
  templateCellMarkImageInfo,
  templateCellMarkMergedCell
} from '@/components/elements/luckysheet/utils'
import {
  LuckysheetCellData,
  LuckysheetConfig,
  LuckysheetImageInfo,
  LuckysheetTemplateZone
} from '@/types/editor-core/config'
import { DataFilterCondition } from '@/types/editor-core/data-source'


/** 递归转字典，叶子为数组 */
const recurGroupByDict = (data: any[], fields: string[]) => {
  if (_.isEmpty(fields)) {
    return data
  }
  const [field, ...rest] = fields
  return _(data)
    .groupBy(field)
    .mapValues(arr => recurGroupByDict(arr, rest))
    .value()
}

/** 生成带缓存功能的函数：获取单元格预备聚合的数据 */
const getSeqMemo = (arr: any[]) => {
  const groupByDict = {}
  return function (this: Record<string, any>) {
    const filterObj = this.seqFilter
    if (_.isEmpty(filterObj)) {
      return _.chain(arr)
    }
    let cache = _.get(groupByDict, _.keys(filterObj))
    if (!cache) {
      cache = recurGroupByDict(arr, _.keys(filterObj))
      _.set(groupByDict, _.keys(filterObj), cache)
    }
    const res = _.get(cache, _.values(filterObj))
    return _.chain(res)
  }
}

/** 每列处理一次模板，生成用于行循环的模板 */
function* inflatedTemplateEachColumn(
  chartData: any[],
  templateCellData: LuckysheetCellData[],
  templateZone: LuckysheetTemplateZone,
  loopState: LoopState
): Generator<LuckysheetCellData[], void, number | undefined> {
  const [cs, ce] = templateZone.c
  const [rs, re] = templateZone.r
  const colCnt = ce - cs + 1
  if (_.isEmpty(templateCellData) || colCnt <= 0) {
    return
  }
  // 基本上是处理以下这几种情况，处理办法：正常逻辑渲染一遍，在 cell 中加入一个路径和原模板，最终按行渲染时根据这些信息重新渲染
  // ${dim}
  // ${seq().sumBy('')}
  // ${obj[t('')]}
  const titleFieldDict = loopState.titleFieldDict || {}
  const translateTitle = title => titleFieldDict[title] || title
  const prevLayerIdxArr = loopState.i || []
  const getRowCollectFn = rowCollByCellIndex()
  for (let di = 0; di < chartData.length; di++) {
    const d = chartData[di]
    const columnOffset = loopState.columnOffset
    loopState.columnOffset += colCnt
    const exData = {
      t: translateTitle,
      i: [...prevLayerIdxArr, di],
      lv: templateZone.depth
    }
    const forceOffset = yield templateCellData.map((templateCell, cI) => {
      let cellVal = templateCell.v
      // 合并单元格的列偏移
      if (_.isObject(cellVal) && _.isObject(cellVal.mc) && columnOffset > 0) {
        cellVal = { ...cellVal, mc: { ...cellVal.mc, c: cellVal.mc.c - cs + columnOffset } }
      }
      // 公式变量的列偏移
      if (_.isObject(cellVal) && _.startsWith(cellVal.f, '=')) {
        const fOffset = cellVal.f!.replace(/(\$?[A-Z]+)(\$?\d+)/g, (m, c, r) => {
          if (c[0] === '$') {
            return m
          }
          const ci = decodeColMemo(c)
          const ri = r - 1
          const inZone = cs <= ci && ci <= ce && rs <= ri && ri <= re
          return inZone ? `${encodeCol(ci - cs + columnOffset)}${r}` : m
        })
        cellVal = { ...cellVal, f: fOffset }
      }
      return {
        ...templateCell,
        c: templateCell.c - cs + columnOffset,
        v: resolveCellTemplate(cellVal, { ...d, ...exData, rowCollect: getRowCollectFn(cI) }),
        tmpV: cellVal, // 如果 row 循环时，发现这个，则优先使用这个作为模板
        seqFilter: loopState.columnLoop && _.pick(d, loopState.columnLoop)
      } as LuckysheetCellData
    })
    if (!_.isNil(forceOffset)) {
      di += forceOffset
      loopState.columnOffset += colCnt * forceOffset
      yield [] // 抵消传递回退命令这次的 next
    }
  }
}

/** 递归 groupBy，构造树形结构，子级为 children */
const recursiveGroupBy = (data: any[], fields: string[], routeDict: Record<string, any> = {}) => {
  if (_.isEmpty(fields)) {
    return data
  }
  const [field, ...rest] = fields
  return _(data)
    .groupBy(field)
    .thru(dict =>
      _.keys(dict).map(fieldVal => {
        const flatChildren = dict[fieldVal]
        return {
          ...routeDict,
          [field]: fieldVal,
          children: recursiveGroupBy(flatChildren, rest, { ...routeDict, [field]: fieldVal }),
          seq: getSeqMemo(flatChildren)
        }
      })
    )
    .value()
}

/** 渲染模板（适配循环方向） */
export function* inflatedTemplateWithData(
  chartData: any[],
  templateCellData: LuckysheetCellData[],
  templateZone: LuckysheetTemplateZone,
  loopState: LoopState
): Generator<LuckysheetCellData[], void, number | undefined> {
  if (templateZone.direction === 'column' && loopState.columnLoop) {
    yield* inflatedTemplateEachColumn(chartData, templateCellData, templateZone, loopState)
  } else {
    yield* inflatedTemplateEachRow(chartData, templateCellData, templateZone, loopState)
  }
}

/** 根据 restDim | auto 设置补全模板区域 */
function patchTemplateZones(templateZones: LuckysheetTemplateZone[], groupByFields: string[]) {
  const lastTemplateZone = templateZones[templateZones.length - 1]

  // restDim = 剩下的维度，auto = 剩下的维度 + 明细值
  const getRestDimZones = () => {
    const dimZones = _.filter(templateZones, z => !_.startsWith(z.field, '__') || !_.endsWith(z.field, '__'))
    return _.difference(
      groupByFields,
      dimZones.map(z => z.field)
    ).map(f => ({ ...lastTemplateZone, field: f }))
  }

  return _(templateZones)
    .flatMap(zone => {
      const { field } = zone
      if (field === '__restDim__') {
        return getRestDimZones()
      }
      if (field === '__auto__') {
        return [...getRestDimZones(), { ...lastTemplateZone, field: '__value__' }]
      }
      return zone
    })
    .uniqBy('field')
    .value()
}

/** 取得表头模板 */
function getHeaderTemplateByZone(templateCelldataArr: LuckysheetCellData[], zone: LuckysheetTemplateZone) {
  const {
    r: [rs, re],
    c: [cs, ce],
    direction,
    headerRowCnt
  } = zone
  if (direction === 'column') {
    // TODO 考虑多个循环体的情况
    return _.filter(templateCelldataArr, ({ r, c }) => c < cs && rs <= r && r <= re)
  }
  const minRow =
    rs - (_.isNil(headerRowCnt) || headerRowCnt === -1 ? calcHeaderRowCnt(templateCelldataArr, rs) : headerRowCnt)
  return _.filter(templateCelldataArr, ({ r, c }) => minRow <= r && r < rs && cs <= c && c <= ce)
}

/** 取得表尾模板 */
function getFooterTemplateByZone(templateCelldataArr: LuckysheetCellData[], zone: LuckysheetTemplateZone) {
  const {
    r: [rs, re],
    c: [cs, ce],
    direction,
    footerRowCnt
  } = zone
  if (direction === 'column') {
    // TODO 考虑多个循环体的情况
    return _.filter(templateCelldataArr, ({ r, c }) => ce < c && rs <= r && r <= re)
  }
  const maxRow =
    re + (_.isNil(footerRowCnt) || footerRowCnt === -1 ? calcFooterRowCnt(templateCelldataArr, re) : footerRowCnt)
  return _.filter(templateCelldataArr, ({ r, c }) => re < r && r <= maxRow && cs <= c && c <= ce)
}

/** 用数据填充模板 */
function* recurInflateCelldata(
  data: any[],
  templateZones: LuckysheetTemplateZone[],
  templateCelldataArr: LuckysheetCellData[],
  loopState: LoopState
): Generator<LuckysheetCellData[], void, undefined> {
  if (_.isEmpty(data) || _.isEmpty(templateZones)) {
    return
  }
  const [zone, ...nextZones] = templateZones
  // 列无需考虑，因为遍历 children 时，父级的值一样
  const [rs, re] = zone.r
  const [cs, ce] = zone.c
  const direction = zone.direction
  const nextZone = nextZones[0]

  if (!nextZone) {
    // 无 children
    // 要比 groupBy 多一个 zone，才能直接输出叶子节点（指标值）
    const bodyTemplate = _.filter(templateCelldataArr, ({ r, c }) => rs <= r && r <= re && cs <= c && c <= ce)
    yield* inflatedTemplateWithData(data, bodyTemplate, zone, loopState)
    return
  }

  const [crs, cre] = nextZone.r || []
  const [ccs, cce] = nextZone.c || []
  const headerTemplate =
    direction === 'column'
      ? _.filter(templateCelldataArr, ({ r, c }) => rs <= r && r <= re && cs <= c && c < ccs)
      : _.filter(templateCelldataArr, ({ r, c }) => rs <= r && r < crs && cs <= c && c <= ce)
  const childrenBodyTemplate = _.filter(templateCelldataArr, ({ r, c }) => crs <= r && r <= cre && ccs <= c && c <= cce)
  const footerTemplate =
    direction === 'column'
      ? _.filter(templateCelldataArr, ({ r, c }) => rs <= r && r <= re && cce < c && c <= ce)
      : _.filter(templateCelldataArr, ({ r, c }) => cre < r && r <= re && cs <= c && c <= ce)

  const prevLayerIdxArr = loopState.i || []
  for (let i = 0; i < data.length; i++) {
    const d = data[i]
    loopState.i = [...prevLayerIdxArr, i]
    let igBak = loopState.ig
    yield* inflatedTemplateWithData(
      [d],
      headerTemplate,
      direction === 'column'
        ? { r: zone.r, c: [cs, ccs - 1], field: '__header__', direction }
        : { r: [rs, crs - 1], c: zone.c, field: '__header__' },
      loopState
    )

    // 头部的渲染不算 ig
    loopState.ig = igBak
    yield* recurInflateCelldata(d.children, nextZones, childrenBodyTemplate, loopState)
    igBak = loopState.ig

    yield* inflatedTemplateWithData(
      [d],
      footerTemplate,
      direction === 'column'
        ? { r: zone.r, c: [cce + 1, ce], field: '__footer__', direction }
        : { r: [cre + 1, re], c: zone.c, field: '__footer__' },
      loopState
    )
    // 尾部的渲染不算 ig
    loopState.ig = igBak
  }
}

/** 透视表的形式填充表格数据 */
function* groupInflatePerPage(
  chartData: any[],
  groupByFields: string[],
  templateZones: LuckysheetTemplateZone[],
  templateCelldataArr: LuckysheetCellData[],
  extraConfig: Partial<LuckysheetCompConfig> & {
    pageSize: number
    rootLoopState: LoopState
    getFilterPredicate: (flts: DataFilterCondition[] | undefined) => (data: any[]) => boolean
  }
): Generator<LuckysheetCellData[], void, undefined> {
  const {
    pageSize,
    pageHeight = 0,
    pagePadding = 0,
    showFooterOnEveryPage,
    printMode,
    renderer,
    rootLoopState: loopState,
    getFilterPredicate
  } = extraConfig
  const pageLimitHeight = pageHeight > 0 ? pageHeight - pagePadding * 2 : Number.MAX_SAFE_INTEGER
  const joinToFirstSheet = printMode === 'printing' && renderer !== 'ce'

  // 要比 groupBy 多一个 zone，才能直接输出叶子节点（指标值）
  // 设置循环体的深度
  let templateZonePatched = patchTemplateZones(templateZones, groupByFields).map((z, lv) => ({ ...z, depth: lv }))

  // 如果是带有横向的循环体，必须预先取得横向循环体表头维度值（不然有的组缺失，就会导致内容对不齐）
  // 这里的做法是，先横向循环，生成一份新的 templateCells，再拿去做纵向循环
  const [horZones, vertZones] = _.partition(templateZonePatched, z => z.direction === 'column')
  if (horZones.length !== 0 && !loopState.columnLoop) {
    const horFields = _.map(horZones, z => z.field).filter(f => _.includes(groupByFields, f))
    loopState.columnLoop = horFields
    const gen = groupInflatePerPage(chartData, horFields, horZones, templateCelldataArr, extraConfig)
    templateCelldataArr = _.flatten([...gen])
    loopState.columnLoop = undefined
    // 构造完列头后，内容按行循环走，扩充纵向循环的范围
    const colOffset = loopState.columnOffset
    templateZonePatched = vertZones.map(z => ({ ...z, c: [z.c[0], colOffset] }))
  }
  if (_.isEmpty(templateZonePatched)) {
    yield templateCelldataArr
    return
  }
  // 如果纵向循环体的区域没有接触，就分别循环
  if (
    templateZonePatched.length > 1 &&
    _.isEmpty(getTemplateZonesIntersectRange([templateZonePatched[1]], templateZonePatched[0].c, templateZonePatched[0].r))
  ) {
    const [firstZone, ...restZones] = templateZonePatched
    const gen = groupInflatePerPage(chartData, groupByFields, [firstZone], templateCelldataArr, extraConfig)
    const genArr = _.flatten([...gen])
    const gen2 = groupInflatePerPage(chartData, groupByFields, restZones, templateCelldataArr, extraConfig)
    const genArr2 = _.flatten([...gen2])
    yield [...genArr, ...genArr2]
    return
  }

  // 2. 遍历分组结果，根据 templateZones cellData 的表达式，生成最终的 cellData
  const {
    r: [rs, re],
    c: [cs, ce],
    customDimensionValue,
    field: zoneField,
    direction,
    filters,
    filtersDisabled
  } = templateZonePatched[0]

  const filterDataMemo = _.memoize((disabledFilters: boolean) =>
    disabledFilters ? chartData : _.filter(chartData, getFilterPredicate(filters))
  )
  const [headData, bodyData, footerData] = _.range(3).map(i => filterDataMemo(filtersDisabled?.[i] || false))

  const groupedArr = recursiveGroupBy(
    bodyData,
    templateZonePatched.map(z => z.field).filter(f => _.includes(groupByFields, f))
  )
  let groupedData = groupedArr
  if (customDimensionValue) {
    const keyZoneField = _.keyBy(groupedArr, zoneField)
    // 如果设置了自定义的维度值 就把groupedData的数据转换成自定义的维度值 抛弃掉多余的维度
    const customDimensionArr = _.split(customDimensionValue, '\n')
    groupedData = _.map(customDimensionArr, customDimension => ({
      ...(keyZoneField[customDimension] || {
        children: [],
        seq: () => _.chain([])
      }),
      [zoneField]: customDimension
    }))
  }

  const headerTemplate = getHeaderTemplateByZone(templateCelldataArr, templateZonePatched[0])
  const footerTemplate = getFooterTemplateByZone(templateCelldataArr, templateZonePatched[0])
  // if (!(_.isNil(headerRowCnt) || headerRowCnt === -1)) {
  //   loopState.rowOffset = loopState.rowOffset - rs + (headerRowCnt || 0)
  // }

  const { minRow, minCol, maxRow, maxCol } = getEdge([...headerTemplate, ...footerTemplate])

  const footerRowCnt = Math.max(0, maxRow - re)
  const footerHeight = getRowHeight(footerTemplate, re + 1, maxRow)

  let bodyGen = recurInflateCelldata(groupedData, templateZonePatched, templateCelldataArr || [], loopState)

  let done = false
  while (!done) {
    const headerGen = inflatedTemplateWithData(
      [{ children: chartData, seq: getSeqMemo(headData) }],
      headerTemplate,
      direction === 'column'
        ? { r: [rs, re], c: [minCol, cs - 1], field: '__header__', direction }
        : { r: [minRow, rs - 1], c: [cs, ce], field: '__header__' },
      loopState
    )
    const headerRows = [...headerGen]
    // 页头的渲染不算 ig
    loopState.ig = 0

    const {
      values: bodyRows,
      restGen,
      done: allContentDone
    } = takeGenWhile(
      bodyGen,
      (_row, i) =>
        // 无论 pageHeight 多么小，每页至少要输出一行，不然会死循环
        // 横向循环时，每页输出的行数不受限制
        i === 0 || ((direction === 'column' || i < pageSize) && loopState.rowY + footerHeight < pageLimitHeight)
    )
    bodyGen = restGen
    done = allContentDone
    loopState.totalContentRowCnt = bodyRows.length

    let footerRows: LuckysheetCellData[][] = []
    if (showFooterOnEveryPage || done || loopState.columnLoop) {
      const igBak = loopState.ig
      const footerGen = inflatedTemplateWithData(
        [{ children: chartData, seq: getSeqMemo(footerData) }],
        footerTemplate,
        direction === 'column'
          ? { r: [rs, re], c: [ce + 1, maxCol], field: '__footer__', direction }
          : { r: [re + 1, maxRow], c: [cs, ce], field: '__footer__' },
        loopState
      )
      footerRows = [...footerGen]
      // 页脚的渲染不算 ig
      loopState.ig = igBak
    } else if (joinToFirstSheet) {
      // 页脚不显示，但是要保留页脚的高度
      loopState.rowOffset += footerRowCnt
      loopState.rowY += footerHeight
    }

    yield _.flatten([...headerRows, ...bodyRows, ...footerRows])
  }
}

/** 透视表的形式填充单页表格数据 */
export function* groupInflate(
  sheetCfg: LuckysheetConfig['data'][number],
  templateZones: LuckysheetTemplateZone[],
  chartData: any[],
  groupByFields: string[],
  extraConfig: Partial<LuckysheetCompConfig> & {
    pageSize: number
    rootLoopState: LoopState
    containerWidth: number
    getFilterPredicate: (flts: DataFilterCondition[] | undefined) => (data: any[]) => boolean
  }
): Generator<InflatedResult> {
  const { celldata, config } = sheetCfg
  const cellDataWithMergeConfig = mergeConstCellsByZones(celldata, templateZones)

  const templateCellWithRowLen = templateCelldataCombineCellSize({
    celldata: cellDataWithMergeConfig || [],
    rowHeightDict: config?.rowlen || {},
    colWidthDict: config?.columnlen || {},
    rowHidden: config?.rowhidden || {},
    colHidden: config?.colhidden || {}
  })
  const templateCellWithBorder = templateCelldataCombineBorderInfo(templateCellWithRowLen, config?.borderInfo || [])

  // 为合并单元格分配 id，数据复制填充后，相同的 id 会被合并
  const templateCellWithMergedCellInfo = templateCellMarkMergedCell(templateCellWithBorder)

  const condFormats = sheetCfg.luckysheet_conditionformat_save
  const templateCellWithCondFormat = templateCellMarkCondFormat(templateCellWithMergedCellInfo, condFormats)
  const templateCellWithHyperlink = templateCellMarkHyperLink(templateCellWithCondFormat, sheetCfg.hyperlink)
  const templateCellWithImgs = templateCellMarkImageInfo(templateCellWithHyperlink, sheetCfg.images as LuckysheetImageInfo[])
  const inflatedCelldataCombinedGen = groupInflatePerPage(
    chartData,
    groupByFields,
    templateZones,
    templateCellWithImgs,
    extraConfig
  )

  let sheetIdx = 0
  for (const inflatedCelldataCombined of inflatedCelldataCombinedGen) {
    const { rowlen, columnlen, rowhidden, colhidden } = extractCellSize(
      inflatedCelldataCombined,
      extraConfig.widthAdaptive ? extraConfig.containerWidth : undefined
    )
    const { inflatedCelldata, borderInfo } = extractBorderInfo(inflatedCelldataCombined, config?.borderInfo)
    const { mergeConfig, inflatedCelldata: inflatedCelldata2 } = genMergeConfig(inflatedCelldata, config?.merge, templateZones)
    const { inflatedCelldata: cellsWithoutCondFormat, condFormatInfos } = extractCondFormatInfo(
      inflatedCelldata2,
      condFormats
    )
    const { inflatedCelldata: cellsWithoutLink, hyperlink } = extractHyperLinkInfo(cellsWithoutCondFormat)
    const { inflatedCelldata: cellsWithoutImgs, images } = extractImageInfo(cellsWithoutLink, sheetCfg.images as LuckysheetImageInfo[])

    const { calcChain, computedCells } = generateCalcChain(cellsWithoutImgs, sheetIdx)
    const patchedInflatedCells = patchOutsideCellData(computedCells, celldata)
    const computedCellsIds = computedCells.map(cell => `${cell.r}_${cell.c}`)

    sheetIdx += 1
    yield {
      templateSheet: sheetCfg,
      inflatedCelldata: patchedInflatedCells,
      borderInfo,
      merge: { ..._.omit(sheetCfg.config?.merge || {}, computedCellsIds), ...mergeConfig },
      rowlen,
      columnlen,
      rowhidden,
      colhidden,
      totalContentRowCnt: extraConfig.rootLoopState.totalContentRowCnt,
      condFormatInfos,
      calcChain,
      hyperlink,
      images
    }
  }
}
