import { BaseProps } from '@/components/elements/type';
import { Config, LuckysheetBorderInfo, LuckysheetCalcChainNode, LuckysheetCellData, LuckysheetConditionFormat, LuckysheetConfig } from '@/types/editor-core/config';


/** luckysheet 组件的数据绑定配置 */
export interface LuckysheetCompConfig extends Config {
  // luckysheet 数据绑定配置
  luckysheetConfig?: LuckysheetConfig

  // 分页配置，0 表示不分页；设置后数据将通过 sheet 分页；默认为 0
  pageSize?: number

  // 表示按高度分页，例如 A4: 794px（横屏）/1123px（竖屏），0 表示不分页
  pageHeight?: number

  // 分页时需要跳过的页面底部的高度，因为包含页头和页尾，所以实际计算时会按两倍算
  pagePadding?: number

  // 强制切换页面，从 1 开始算，0 表示不控制；默认为 1
  page?: number

  // 是否显示网格线；默认为 true
  showGridLines?: boolean

  // 每页都显示表尾：总是显示/仅最后一页，默认 false
  showFooterOnEveryPage?: boolean

  // 显示SheetBar，一般作为内置分页器使用，使用外部分页器时应该隐藏；默认为 false
  showSheetBar?: boolean

  // 打印模式，全部页面都会依次平铺在第一页输出，跟下一页的间隔为 footer 的高度（如果有 footer 则无间隔）；默认为 false
  printMode?: boolean | 'printing' | 'reportPrint'

  // 渲染器，luckysheet 功能完整，但是性能不高，高性能要求场景可以使用 ce 渲染器；默认为 luckysheet
  renderer?: 'luckysheet' | 'ce'

  // 是否自适应宽度，如果为 true，则会根据容器宽度自动调整表格宽度；默认为 false
  widthAdaptive?: boolean

  // 预览时是否允许修改，如果为 true，则会在预览时显示修改按钮；默认为 false
  allowPreviewEdit?: boolean

  // 预览时修改后的渲染结果，避免 modifyFn merge 影响，分开存储
  luckysheetConfigModified?: LuckysheetConfig

  // 预览时是否允许编辑，如果为 true，则会在预览时显示编辑按钮；默认为 false
  enableAndDisableEditing?: boolean

  // 查看报表的时候是否自动换行
  autoWhiteSpace?: boolean

  // 是否隐藏无数据提示
  hideNoData?: boolean
}

export type LuckySheetProps = BaseProps<{
  config: LuckysheetCompConfig
}>

/** 循环状态，主要用于记录当前正在输出的行与位置 */
export interface LoopState {
  // 当前行数，循环时会累加循环体行数，包括表头、表尾
  rowOffset: number
  columnOffset: number
  // 循环体行数，循环时会累加循环体行数，不包括表头、表尾，主要用于展示总行数
  totalContentRowCnt: number
  // 循环行的屏幕 Y 坐标，主要用于分页
  rowY: number
  // 标题转字段名字典
  titleFieldDict: Record<string, string>
  // 循环索引，临时记录当前循环的层级索引
  i: number[]
  ig: number // 全局循环索引
  getGlobalFNEData: Function // 全局首个非空值
  columnLoop?: string[] // 水平循环字段，水平循环时会用到
  mockRender?: boolean // 是否是模拟渲染，模拟渲染时不会真正渲染，只是计算行数行高
}

/** 填充结果 */
export interface InflatedResult {
  templateSheet: LuckysheetConfig['data'][number]
  inflatedCelldata: LuckysheetCellData[]
  borderInfo: LuckysheetBorderInfo[]
  merge: Record<string, { r: number; c: number; rs?: number; cs?: number }>
  rowlen: Record<string, number>
  columnlen: Record<string, number>
  rowhidden: Record<string, number>
  colhidden: Record<string, number>
  totalContentRowCnt: number
  condFormatInfos: LuckysheetConditionFormat[]
  calcChain: LuckysheetCalcChainNode[]
  hyperlink: LuckysheetConfig['data'][number]['hyperlink']
  images: LuckysheetConfig['data'][number]['images']
}
