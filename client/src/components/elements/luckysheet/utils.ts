import { format } from 'd3-format'
import dayjs, { ManipulateType } from 'dayjs'
import { produce } from 'immer'
import _ from 'lodash'
import qs from 'querystring'

import { InflatedResult, LoopState, LuckysheetCompConfig } from '@/components/elements/luckysheet/types'
import { LuckysheetBorderInfo, LuckysheetCalcChainNode, LuckysheetCellData, LuckysheetCellVal, LuckysheetConditionFormat, LuckysheetConfig, LuckysheetImageInfo, LuckysheetLinkConfig, LuckysheetTemplateZone } from '@/types/editor-core/config'
import { DataSourceConfig } from '@/types/editor-core/data-source'
import { withExtraQuery } from '@/utils/query'

import * as excelFns from './excel-fns'


/** 索引转 excel 列头，截取自 xlsxjs */
export function encodeCol(col: number) {
  let s = ''
  // eslint-disable-next-line no-plusplus
  for (++col; col; col = Math.floor((col - 1) / 26)) s = String.fromCharCode(((col - 1) % 26) + 65) + s
  return s
}

export const encodeColMemo = _.memoize(encodeCol)

/** 整数转 excel 行头，截取自 xlsxjs */
export function encodeRow(row) {
  return `${row + 1}`
}

/** 单元格位置转 excel 单元格位置编码 */
export function encodeCell(cell) {
  return encodeColMemo(cell.c) + encodeRow(cell.r)
}

function unfixCol(cstr) {
  return cstr.replace(/^\$([A-Z])/, '$1')
}

function unfixRow(cstr) {
  return cstr.replace(/\$(\d+)$/, '$1')
}

export function decodeCol(colstr) {
  const c = unfixCol(colstr)
  let d = 0
  let i = 0
  for (; i !== c.length; ++i) d = 26 * d + c.charCodeAt(i) - 64
  return d - 1
}

export const decodeColMemo = _.memoize(decodeCol)

function decodeRow(rowstr) {
  return parseInt(unfixRow(rowstr), 10) - 1
}

function splitCell(cstr) {
  return cstr.replace(/(\$?[A-Z]*)(\$?\d*)/, '$1,$2').split(',')
}

export function decodeCell(cstr) {
  const splt = splitCell(cstr)
  return { c: decodeColMemo(splt[0]), r: decodeRow(splt[1]) }
}

/** 获取当前选中的工作簿 */
export function getActiveSheet(luckysheetCfg: Partial<LuckysheetConfig>): LuckysheetConfig['data'][number] | undefined {
  return _.find(luckysheetCfg.data, d => (d.status || 0) > 0) || luckysheetCfg.data?.[0]
}

/** 获取当前选中的工作簿索引 */
export function getActiveSheetIndex(luckysheetCfg: Partial<LuckysheetConfig>): number {
  return Math.max(
    0,
    _.findIndex(luckysheetCfg.data, d => (d.status || 0) > 0)
  )
}

/** 取得与选区有交集的循环体 */
export function getTemplateZonesIntersectRange(
  allTemplateZones: LuckysheetTemplateZone[],
  colRange: number[],
  rowRange: number[]
) {
  const [zcs, zce] = colRange
  const [zrs, zre] = rowRange
  return _.filter(allTemplateZones, ({ c, r }) => {
    const [cs, ce] = c
    const [rs, re] = r
    return cs <= zce && zcs <= ce && rs <= zre && zrs <= re
  })
}

const templateMemo = _.memoize(_.template)
const nilToEmpty = (v: any) => (_.isNil(v) ? '' : v)

/** 解析单元格中的模板表达式，替换为表达式运算后的结果 */
export function resolveCellTemplate(cellVal: LuckysheetCellData['v'], rowData: any): LuckysheetCellData['v'] {
  try {
    const cellValObj: LuckysheetCellVal = _.isObject(cellVal) ? cellVal : { v: cellVal }

    const expr = nilToEmpty(cellValObj.v)
    // 注意不能传入 opts，不支持缓存
    const v = templateMemo(expr)(rowData)

    // 填充超链接插值表达式
    let hyperlink = cellValObj.hyperlink
    if (hyperlink?._href) {
      hyperlink = { ...hyperlink, linkAddress: templateMemo(hyperlink._href || '')(rowData) }
    }
    // 填充图片链接插值表达式
    let image = cellValObj.image
    if (image?._href) {
      image = { ...image, src: templateMemo(image._href || '')(rowData) }
    }

    // 不设置 m，避免影响用户设置的格式化
    return { ..._.omit(cellValObj, 'm'), v: v === 'undefined' ? '' : v, hyperlink, image }
  } catch (e) {
    if (e instanceof ReferenceError) {
      // 变量未定义，返回空字符串
      return _.isObject(cellVal) ? { ...cellVal, v: null, m: '' } : ''
    }
    if (!(e instanceof Error)) {
      throw e
    }
    return _.isObject(cellVal) ? { ...cellVal, m: e.message } : e.message
  }
}

/** 获取模板区域的行高（px) */
export function getRowHeight(templateCellData: LuckysheetCellData[], minRow = 0, maxRow = 0) {
  if (_.isEmpty(templateCellData) || maxRow < minRow) {
    return 0
  }
  const minR = _.isNil(minRow) ? _.minBy(templateCellData, 'r')!.r : minRow
  const maxR = _.isNil(maxRow) ? _.maxBy(templateCellData, 'r')!.r : maxRow
  const rowHeightDict = _(templateCellData)
    .filter(c => (c?.rh ?? 0) !== 0)
    .uniqBy(c => c.r)
    .keyBy(c => c.r)
    .value()
  return _.range(minR, maxR + 1).reduce((acc, r) => acc + Math.max(0, rowHeightDict[r]?.rh ?? 20), 0)
}

/**
 * 格式化时间范围，年份相同则省略，月份或者天数，如果为 1 则省略
 *
 * 输入：["2023-01-01", "2023-05-01"]
 * 输出：2023年1-5月
 *
 * 输入：["2022-01-01", "2023-05-01"]
 * 输出：2022年1月-2023年5月
 *
 * 输入：["2023-01-20", "2023-05-01"]
 * 输出：2023年1月20日-5月1日
 * @param inputs
 * @param pattern
 */
function fmtDateRange(inputs: (string | null)[], pattern = 'YYYY年M月D日') {
  if (!inputs[0] && !inputs[1]) {
    return ''
  }
  if (!inputs[0]) {
    return `${dayjs(inputs[1]).format(pattern)}之前`
  }
  if (!inputs[1]) {
    return `${dayjs(inputs[0]).format(pattern)}至今`
  }
  const [start, end] = _.map(inputs, input => dayjs(input))
  const startYear = start.year()
  const endYear = end.year()
  const startMonth = start.month() + 1
  const endMonth = end.month() + 1
  const startDay = start.date()
  const endDay = end.date()
  const allDayOne = startDay === endDay && startDay === 1
  const allMonthOne = startMonth === endMonth && startMonth === 1
  const sameYear = startYear === endYear
  if (start.isSame(end, 'day')) {
    return `${start.format(pattern)}`
  }
  return [
    `${startYear}年`,
    allMonthOne && allDayOne ? '' : `${startMonth}${sameYear && allDayOne ? '' : '月'}`,
    allDayOne ? '' : `${startDay}日`,
    '-',
    sameYear ? '' : `${endYear}年`,
    allMonthOne && allDayOne ? '' : `${endMonth}月`,
    allDayOne ? '' : `${endDay}日`
  ].join('')
}

/** 时间范围减法 */
const timeSub = (range: string[], n: number, unit: ManipulateType) =>
  _.map(range, input => dayjs(input).subtract(n, unit).format('YYYY-MM-DD'))

const formatMemo = _.memoize(format)

/** 数值格式化工具函数 */
function applyFormatter(val: any, formatter?: string) {
  return _.isNil(val) ? '--' : formatMemo(formatter || '.2~f')(val)
}

export function rowCollByCellIndex() {
  const rowCollected: Record<string, (number | string | null)[]> = {} // 行内累计
  return r => (val?: string | number | null) => {
    if (_.isNil(val)) {
      return _.chain(rowCollected[r] || []) // 使用数据
    }
    if (rowCollected[r] === undefined) {
      rowCollected[r] = [val]
    } else {
      rowCollected[r].push(val)
    }
    return val
  }
}

/** 每行数据填充一次模板 */
export function* inflatedTemplateEachRow(
  chartData: any[],
  templateCellData: LuckysheetCellData[],
  templateZone: LuckysheetTemplateZone,
  loopState: LoopState
): Generator<LuckysheetCellData[], void, number | undefined> {
  const [rs, re] = templateZone.r
  const [cs, ce] = templateZone.c
  const rowCnt = re - rs + 1
  if (_.isEmpty(templateCellData) || rowCnt <= 0) {
    return
  }
  // 如果正在渲染表头，则保留横向循环的渲染值
  const keepColumnLoopResult = templateZone.field === '__header__'
  const rowHeight = getRowHeight(templateCellData, rs, re)
  const titleFieldDict = loopState.titleFieldDict || {}
  const translateTitle = title => titleFieldDict[title] || title
  const prevLayerIdxArr = loopState.i || []

  const getRowCollectFn = rowCollByCellIndex()
  for (let di = 0; di < chartData.length; di++) {
    const d = chartData[di]
    const rowOffset = loopState.rowOffset
    const exData = {
      t: translateTitle,
      i: [...prevLayerIdxArr, di],
      lv: templateZone.depth,
      ig: loopState.ig,
      fmt: applyFormatter,
      fmtDateRange,
      timeSub,
      dayjs
    }
    loopState.rowOffset += rowCnt
    loopState.rowY += rowHeight
    loopState.ig += 1
    const forceOffset = yield loopState.mockRender
      ? []
      : templateCellData.map(templateCell => {
        let cellVal = keepColumnLoopResult && templateCell.tmpV
          ? templateCell.v
          : templateCell.tmpV || templateCell.v
        // 合并单元格的行偏移，这里的 r，c 指向的是指向的是合并单元格的左上角
        if (_.isObject(cellVal) && _.isObject(cellVal.mc) && rowOffset > 0) {
          cellVal = { ...cellVal, mc: { ...cellVal.mc, r: cellVal.mc.r - rs + rowOffset } }
        }
        // 公式变量的行偏移
        if (_.isObject(cellVal) && _.startsWith(cellVal.f, '=')) {
          const fOffset = cellVal.f!.replace(/(\$?[A-Z]+)(\$?\d+)/g, (m, c, r) => {
            // m: C3, c: C, r: 3
            if (r[0] === '$') {
              return m
            }
            const ci = decodeColMemo(c)
            const ri = r - 1
            const inZone = cs <= ci && ci <= ce && rs <= ri && ri <= re
            // 单元格偏移参考下方的 r
            if (inZone) {
              // 在循环体内，需要偏移
              return c + (+r - rs + rowOffset)
            }
            if (re < ri) {
              // 在循环体下方，需要偏移 rowCnt * (chartData.length - 1)
              return c + (+r + rowCnt * (chartData.length - 1))
            }
            // 其他情况不偏移
            return m
          })
          cellVal = { ...cellVal, f: fOffset }
        }
        const nextR = templateCell.r - rs + rowOffset
        const nextVal =
          keepColumnLoopResult && templateCell.tmpV
            ? cellVal
            : resolveCellTemplate(cellVal, {
              // 有 seq 表示带有 children，过滤逻辑在 seq() 里，无 seq 表示叶子节点，直接取值
              // 如果是叶子节点，需要使用 seqFilter 过滤一下自己，避免横向循环出现错误的值
              ...(d.seq || _.every(templateCell.seqFilter, (v, k) => d[k] === v) ? d : {}),
              ...exData,
              // seqFilter：根据 path 读取值
              seqFilter: templateCell.seqFilter,
              getGlobalFNEData: (loopState.getGlobalFNEData || _.noop),
              rowCollect: getRowCollectFn(nextR)
            })
        return { ...templateCell, r: nextR, v: nextVal } as LuckysheetCellData
      })
    if (!_.isNil(forceOffset)) {
      di += forceOffset
      loopState.rowOffset += rowCnt * forceOffset
      loopState.rowY += rowHeight * forceOffset
      loopState.ig += forceOffset
      yield [] // 抵消传递回退命令这次的 next
    }
  }
}

/** 判断合并区域是否重叠，只判断列 */
function isColumnMCCollapsed(mc: LuckysheetCellVal['mc'], mc2: LuckysheetCellVal['mc']) {
  if (!mc || !mc2 || _.isNil(mc.cs) || _.isNil(mc2.cs)) {
    return false
  }
  const { c: c1, cs: cs1, r: r1 } = mc
  const { c: c2, cs: cs2, r: r2 } = mc2
  return r1 === r2 && (_.inRange(c2, c1, c1 + cs1) || _.inRange(c1, c2, c2 + cs2))
}

/** 判断合并区域是否重叠，只判断行 */
function isRowMCCollapsed(mc: LuckysheetCellVal['mc'], mc2: LuckysheetCellVal['mc']) {
  if (!mc || !mc2 || _.isNil(mc.rs) || _.isNil(mc2.rs)) {
    return false
  }
  const { r: r1, rs: rs1, c: c1 } = mc
  const { r: r2, rs: rs2, c: c2 } = mc2
  return c1 === c2 && (_.inRange(r2, r1, r1 + rs1) || _.inRange(r1, r2, r2 + rs2))
}

/** 将不相邻的区域拆分，只按列 */
function groupColumnMergeCellsByMergeInfo(mergeInfos: LuckysheetCellVal['mc'][]) {
  if (_.isEmpty(mergeInfos)) {
    return []
  }
  const nextNotAdjacentCellIdx = _.findIndex(mergeInfos, (c, i) => {
    const nextCell = mergeInfos[i + 1]
    return !nextCell || !isColumnMCCollapsed(c, nextCell)
  })
  return nextNotAdjacentCellIdx === -1
    ? [mergeInfos]
    : [
      mergeInfos.slice(0, nextNotAdjacentCellIdx + 1),
      ...groupColumnMergeCellsByMergeInfo(mergeInfos.slice(nextNotAdjacentCellIdx + 1))
    ]
}

/** 将不相邻的区域拆分，只按行 */
function groupRowMergeCellsByMergeInfo(mergeInfos: LuckysheetCellVal['mc'][]) {
  if (_.isEmpty(mergeInfos)) {
    return []
  }
  const nextNotAdjacentCellIdx = _.findIndex(mergeInfos, (c, i) => {
    const nextCell = mergeInfos[i + 1]
    return !nextCell || !isRowMCCollapsed(c, nextCell)
  })
  return nextNotAdjacentCellIdx === -1
    ? [mergeInfos]
    : [
      mergeInfos.slice(0, nextNotAdjacentCellIdx + 1),
      ...groupRowMergeCellsByMergeInfo(mergeInfos.slice(nextNotAdjacentCellIdx + 1))
    ]
}

/** 为合并单元格分配 id，数据复制填充后，相同的 id 会被合并 */
export function templateCellMarkMergedCell(templateCells: LuckysheetCellData[]) {
  return produce(templateCells, drafts => {
    _.forEach(drafts, ({ v }) => {
      if (_.isObject(v) && !_.isNil(v.mc?.r)) {
        // id 感觉没啥影响，后续也不清除了
        const { r, c } = v.mc!
        v.mc!.id = `${r}_${c}`
      }
    })
  })
}

export function getEdge(arr: { r: number, c: number }[] | undefined | null) {
  let minCol = 99999
  let minRow = 99999
  let maxCol = -1
  let maxRow = -1

  if (!arr) {
    return { minCol, minRow, maxCol, maxRow }
  }

  for (let i = 0; i < arr.length; i++) {
    const { r, c } = arr[i]
    if (c < minCol) minCol = c
    if (r < minRow) minRow = r
    if (c > maxCol) maxCol = c
    if (r > maxRow) maxRow = r
  }

  return { minCol, minRow, maxCol, maxRow }
}

/** 提取合并单元格信息 */
export function genMergeConfig(
  inflatedCelldata: LuckysheetCellData[],
  mergeCfg: Record<string, { r: number; c: number; rs: number; cs: number }> | null | undefined,
  templateZones: LuckysheetTemplateZone[]
) {
  if (
    _.isEmpty(mergeCfg) && (
      !_.some(templateZones, z => z.mergeConstCells) ||
      !_.some(inflatedCelldata, c => _.isObject(c.v) && c.v.mc?.rs)
    )
  ) {
    return { mergeConfig: {}, inflatedCelldata }
  }
  // 填充时，mc 的 r 会增加，合并 id 相同且值相同的 mergeInfo，并且需要处理中间的 mc，标记为 {r: 起始r，c: 起始c}
  const mergeInfo = inflatedCelldata
    .filter(({ v }) => _.isObject(v) && v.mc?.id)
    .map(({ v }) => (v as LuckysheetCellVal).mc!)
  const mcIdDict = _.groupBy(mergeInfo, ({ id }) => id)

  const nextMcs = _.flatMap(mcIdDict, mergeInfos => {
    // 丢弃掉因分页截断的 mc 信息
    const clearedMergeInfos = _.dropWhile(mergeInfos, ({ rs, cs }) => !(rs || cs))
    const topLeftMc: LuckysheetCellVal['mc'] = _.first(clearedMergeInfos) ?? clearedMergeInfos[0]
    const mergeInfosWithCompleteInfo = _.map(clearedMergeInfos, mc => ({ ...topLeftMc, ...mc }))
    // // 只会有两种情况，一种是上下相连，一种是上下不相连
    const notAdjacentCellArr =
      (topLeftMc?.rs ?? 0) > 1
        ? groupRowMergeCellsByMergeInfo(mergeInfosWithCompleteInfo)
        : groupColumnMergeCellsByMergeInfo(mergeInfosWithCompleteInfo)

    return _(notAdjacentCellArr)
      .map(mcs => {
        // 合并成一个
        const originalRs = mcs[0]?.rs ?? 1
        const originalCs = mcs[0]?.cs ?? 1
        const { minCol, minRow, maxCol, maxRow } = getEdge(mcs)
        const rs = maxRow + originalRs - minRow
        const cs = maxCol + originalCs - minCol
        return { r: minRow, rs, c: minCol, cs }
      })
      .compact()
      .value()
  })

  const mergeConfig = _.zipObject(
    _.map(nextMcs, ({ r, c }) => `${r}_${c}`),
    nextMcs
  )

  // cellData 全部 mc 清掉，然后根据 mergeConfig 重新设置
  const cellDataPosDict = _.keyBy(inflatedCelldata, ({ r, c }) => `${r}_${c}`)
  const nextCelldata = produce(inflatedCelldata, draft => {
    draft.forEach(cell => {
      const { r, c, v } = cell
      if (!_.isObject(v)) {
        return
      }
      const nextMc = mergeConfig[`${r}_${c}`]
      const mcId = v.mc?.id
      if (nextMc && mcId) {
        // 还要清除实际不应该合并的单元格，比如配置的上下合并，但因为分页原因被拆开了
        const { r: rowIdx, c: colIdx, rs } = nextMc
        const allBelowCells = _.range(0, rs).map(i => cellDataPosDict[`${rowIdx + i}_${colIdx}`])
        const validMCRowSpan = _.takeWhile(allBelowCells, belowCell => _.isObject(belowCell?.v) && belowCell.v.mc?.id === mcId).length
        v.mc = {
          ...nextMc,
          rs: validMCRowSpan
        }
      } else {
        delete v.mc
      }
    })
  })
  // 取清理后的 mc 信息
  const nextMcs2 = nextCelldata
    .filter(({ v }) => _.isObject(v) && v.mc?.rs)
    .map(({ v }) => (v as LuckysheetCellVal).mc!)
  const mergeConfig2 = _.zipObject(
    _.map(nextMcs2, ({ r, c }) => `${r}_${c}`),
    nextMcs2
  )
  return {
    mergeConfig: mergeConfig2,
    inflatedCelldata: nextCelldata
  }
}

/** 从 generator 取 cnt 个元素 */
export function takeGenWhile<T>(
  g: Generator<T, void, number | undefined | null>,
  predicate: (v: T, idx: number) => boolean = _.constant(true)
) {
  const ret: T[] = []
  let idx = 0
  while (g) {
    const { value, done } = g.next()
    if (done) {
      return { values: ret, restGen: g, ret: value, done: true }
    }
    if (!predicate(value as any, idx)) {
      g.next(-1) // 通知 generator 回退
      break
    }
    ret.push(value as any)
    idx += 1
  }
  return { values: ret, restGen: g, ret: undefined, done: false }
}

/** 将行高信息合并到行内的某个单元格 */
export function templateCelldataCombineCellSize(
  { celldata, rowHeightDict, colWidthDict, rowHidden, colHidden }: {
    celldata: LuckysheetCellData[],
    rowHeightDict: Record<number, number>,
    colWidthDict: Record<number, number>,
    rowHidden?: Record<number, boolean>,
    colHidden?: Record<number, boolean>
  }
) {
  // "defaultRowHeight": 19, //自定义行高
  // "defaultColWidth": 73, //自定义列宽
  // 如果有隐藏的行，那么行高为 负的原行高
  rowHeightDict = {
    ...rowHeightDict,
    ..._(rowHidden)
      .pickBy(v => +v === 0)
      .mapValues((_v, r) => rowHeightDict[r] ? -rowHeightDict[r] : -19)
      .value()
  }
  // 如果有隐藏的列，那么列宽为 负的原列宽
  colWidthDict = {
    ...colWidthDict,
    ..._(colHidden)
      .pickBy(v => +v === 0)
      .mapValues((_v, c) => colWidthDict[c] ? -colWidthDict[c] : -73)
      .value()
  }

  // 行高、列宽只需要写入第一个有数据的 cell
  const rowMemo: Record<number, number> = {}
  const columnMemo: Record<number, number> = {}
  return _.map(celldata, cell => {
    const { r, c } = cell
    if (rowHeightDict[r] && !rowMemo[r]) {
      rowMemo[r] = rowHeightDict[r]
      cell = { ...cell, rh: rowHeightDict[r] }
    }
    if (colWidthDict[c] && !columnMemo[c]) {
      columnMemo[c] = colWidthDict[c]
      cell = { ...cell, cw: colWidthDict[c] }
    }
    return cell
  })
}

/** 提取行高/行列隐藏信息 */
export function extractCellSize(inflatedCellData: LuckysheetCellData[], adaptToWidth?: number) {
  const celldataWithRowHeight = _.filter(inflatedCellData, 'rh')
  const rowlen = _.mapValues(_.keyBy(celldataWithRowHeight, 'r'), c => c.rh)
  const celldataWithColWidth = _.filter(inflatedCellData, 'cw')
  let columnlen = _.mapValues(_.keyBy(celldataWithColWidth, 'c'), c => c.cw)

  // 如果需要适应宽度，那么先计算出每列的宽度，注意：如果有空的列，可能导致宽度无法设置正常
  if (adaptToWidth) {
    const colWidthDict = _(inflatedCellData)
      .groupBy('c')
      .mapValues(arr => _.maxBy(arr, c => c.cw ?? 0)?.cw ?? 73)
      .value()
    const totalCols = _.max(_.keys(colWidthDict).map(k => +k)) ?? 1
    const colNums = _.range(0, totalCols + 1)
    // 如果列隐藏了，那么宽度为 0
    const currContentWidth = colNums.reduce((acc, colIdx) => acc + Math.max(0, colWidthDict[colIdx] ?? 73), 0)
    adaptToWidth -= totalCols // 避免显示滚动条
    const ratio = adaptToWidth / currContentWidth
    columnlen = _.zipObject(
      colNums,
      colNums.map(colIdx => _.round((colWidthDict[colIdx] ?? 73) * ratio))
    )
  }

  return {
    rowlen: _.pickBy(rowlen, v => Math.max(0, v ?? 0)) as Record<string, number>,
    columnlen: _.mapValues(columnlen, v => Math.max(0, v ?? 0)) as Record<string, number>,
    rowhidden: _(rowlen).pickBy(v => (v ?? 0) < 0).mapValues(() => 0).value(),
    colhidden: _(columnlen).pickBy(v => (v ?? 0) < 0).mapValues(() => 0).value()
  }
}

/** 取得合并了边框信息的单元格模板 */
export function templateCelldataCombineBorderInfo(celldata: LuckysheetCellData[], borderInfo: LuckysheetBorderInfo[]) {
  const borderInfoWithId = _.map(borderInfo, (b, i) => ({ ...b, id: `${i}` }))

  return produce(celldata || [], drafts => {
    _.forEach(drafts, cell => {
      const { r, c } = cell
      // 将边框信息写入其内部的全部 celldata，复制后，提取边框信息，取最左上、左下、右上和右下的，相同 id 的边框
      const borderInfos = _.filter(borderInfoWithId, b => {
        if (b.rangeType === 'range') {
          const { row, column } = b.range[0]
          return row[0] <= r && r <= row[1] && column[0] <= c && c <= column[1]
        }
        const { row_index: ri, col_index: ci } = b.value
        return ri === r && ci === c
      })
      if (_.isEmpty(borderInfos)) {
        return
      }
      cell.borders = borderInfos
    })
  })
}

/** 分离边框信息和填充后的单元格数据 */
export function extractBorderInfo(
  inflatedCelldata: LuckysheetCellData[],
  borderInfos: LuckysheetBorderInfo[] | null | undefined
) {
  if (_.isEmpty(borderInfos)) {
    return { borderInfo: [], inflatedCelldata }
  }
  const EMPTY_ARR = []
  const borderInfoWithCellPos = _.flatMap(inflatedCelldata, ({ r, c, borders }) =>
    !_.isArray(borders) ? EMPTY_ARR : _.map(borders, b => ({ border: b, bId: b.id, r, c }))
  )
  // 对于 cell type 的边框，直接套用，不需要去重
  const cellBorders = _(borderInfoWithCellPos)
    .filter(({ border }) => border.rangeType === 'cell')
    .map(({ r, c, border }) =>
      produce(border as Extract<LuckysheetBorderInfo, { rangeType: 'cell' }>, draft => {
        draft.value.row_index = r
        draft.value.col_index = c
      })
    )
    .value()

  // 对于 range type 的边框，取最左上、左下、右上和右下的，相同 id 的边框
  const rangeBorders = _(borderInfoWithCellPos)
    .filter(({ border }) => border.rangeType === 'range')
    .groupBy(b => b.bId)
    .map(arr => {
      const minR = _.minBy(arr, 'r')!.r
      const minC = _.minBy(arr, 'c')!.c
      const maxR = _.maxBy(arr, 'r')!.r
      const maxC = _.maxBy(arr, 'c')!.c
      return produce(arr[0].border as Extract<LuckysheetBorderInfo, { rangeType: 'range' }>, draft => {
        draft.range = [{ row: [minR, maxR], column: [minC, maxC] }]
      })
    })
    .value()

  // 排序有意义，后面设置的边框优先级更高，需要恢复这个信息
  const borderInfo = _.orderBy([...rangeBorders, ...cellBorders], b => +(b.id || 0))

  // 删除 celldata 里的 borders
  const cellDataDroppedBorderInfo = _.map(inflatedCelldata, cell =>
    _.isArray(cell.borders) ? _.omit(cell, 'borders') : cell
  )

  return { borderInfo, inflatedCelldata: cellDataDroppedBorderInfo }
}

/** 将条件格式信息写入单元格模板 */
export function templateCellMarkCondFormat(
  templateCells: LuckysheetCellData[],
  condFormats: LuckysheetConditionFormat[] | null | undefined
) {
  if (_.isEmpty(condFormats)) {
    return templateCells
  }
  const condFormatsWithId = _.map(condFormats, (c, i) => ({ ...c, id: `${i}` }))
  return produce(templateCells, drafts => {
    _.forEach(drafts, cell => {
      const { r, c } = cell
      const condFormatIds = _.filter(condFormatsWithId, cf => {
        const { row, column } = cf.cellrange[0]
        return row[0] <= r && r <= row[1] && column[0] <= c && c <= column[1]
      }).map(cf => cf.id)
      if (_.isEmpty(condFormatIds)) {
        return
      }
      cell.condFormatIds = condFormatIds
    })
  })
}

/** 提取并合并条件格式信息 */
export function extractCondFormatInfo(
  inflatedCelldata: LuckysheetCellData[],
  condFormats: LuckysheetConditionFormat[] | null | undefined
) {
  if (_.isEmpty(condFormats)) {
    return { condFormatInfos: [], inflatedCelldata }
  }
  // {cfId, r, c}
  const condFormatsWithId = _.map(condFormats, (c, i) => ({ ...c, id: `${i}` }))
  const cfIndexDict = _.keyBy(condFormatsWithId, 'id')
  const EMPTY_ARR = []
  const condFormatRanges = _.flatMap(inflatedCelldata, ({ c, r, condFormatIds }) => {
    if (_.isEmpty(condFormatIds)) {
      return EMPTY_ARR
    }
    return _.map(condFormatIds, cfId => ({ cfId, r, c }))
  })
  const cfInfos = _(condFormatRanges)
    .groupBy('cfId')
    .mapValues((arr, cfId) => {
      // 合并相同的区域，扩展范围
      const rs = _.minBy(arr, 'r')!.r
      const re = _.maxBy(arr, 'r')!.r
      const cs = _.minBy(arr, 'c')!.c
      const ce = _.maxBy(arr, 'c')!.c
      return produce(cfIndexDict[cfId], draft => {
        draft.cellrange[0] = { row: [rs, re], column: [cs, ce] }
      })
    })
    .values()
    .orderBy('id')
    .map(cf => _.omit(cf, 'id'))
    .value()
  const inflatedCelldataDroppedCondFormatIds = _.map(inflatedCelldata, cell =>
    cell.condFormatIds ? _.omit(cell, 'condFormatIds') : cell
  )
  return {
    condFormatInfos: cfInfos,
    inflatedCelldata: inflatedCelldataDroppedCondFormatIds
  }
}

/** 将超链接信息写入单元格模板 */
export function templateCellMarkHyperLink(
  templateCells: LuckysheetCellData[],
  hyperlinks: LuckysheetConfig['data'][number]['hyperlink'] | null | undefined
) {
  if (_.isEmpty(hyperlinks)) {
    return templateCells
  }
  return produce(templateCells, drafts => {
    _.forEach(drafts, cell => {
      const { r, c } = cell
      const hyperlink = hyperlinks![`${r}_${c}`]
      if (_.isNil(hyperlink)) {
        return
      }
      if (!_.isObject(cell.v)) {
        cell.v = { v: cell.v }
      }
      cell.v.hyperlink = {
        ...hyperlink,
        _href: withExtraQuery(
          hyperlink.linkAddress,
          _.map(hyperlink._extraQueries, ({ key, val }) => `${key}=${val}`).join('&')
        )
      }
    })
  })
}

/** 提取并合并超链接信息 */
export function extractHyperLinkInfo(inflatedCelldata: LuckysheetCellData[]) {
  const pageQuery = qs.parse(window.location.search.replace(/^\?/, ''))
  const hyperLinkPosDict: LuckysheetConfig['data'][number]['hyperlink'] = _(inflatedCelldata)
    .filter(c => _.isObject(c.v) && !!c.v.hyperlink)
    .keyBy(({ r, c }) => `${r}_${c}`)
    .mapValues(c => {
      // decodeUri，补充内容到参数 args
      const cv = c.v as LuckysheetCellVal
      const withCellVal = `${decodeURIComponent(cv.hyperlink?.linkAddress || '')}`
      const [linkAddress, extraQuery] = withCellVal.split('?')
      const latestQuery = qs.parse(extraQuery) || {}
      const finalQuery = qs.stringify({ ...pageQuery, ...latestQuery })
      return {
        ...(cv.hyperlink || {}),
        linkAddress: withExtraQuery(linkAddress, finalQuery)
      } as LuckysheetLinkConfig
    })
    .value()
  if (_.isEmpty(hyperLinkPosDict)) {
    return { hyperlink: {}, inflatedCelldata }
  }
  const inflatedCelldataDroppedHyperlink = _.map(inflatedCelldata, cell =>
    _.isObject(cell.v) && 'hyperlink' in cell.v ? _.omit(cell, 'v.hyperlink') : cell
  )
  return { hyperlink: hyperLinkPosDict, inflatedCelldata: inflatedCelldataDroppedHyperlink }
}

/** 将图片信息写入单元格模板 */
export function templateCellMarkImageInfo(
  templateCells: LuckysheetCellData[],
  images: LuckysheetImageInfo[] | null | undefined
) {
  if (_.isEmpty(images)) {
    return templateCells
  }
  const imgPosDict = _(images)
    .pickBy(img => !!img._baseCell)
    .mapValues((img, _id) => ({ ...img, _id }))
    .mapKeys(({ _baseCell: p }) => `${p!.r}_${p!.c}`)
    .value()
  return produce(templateCells, drafts => {
    _.forEach(drafts, cell => {
      const { r, c } = cell
      const imgInfo = imgPosDict![`${r}_${c}`]
      if (_.isNil(imgInfo)) {
        return
      }
      if (!_.isObject(cell.v)) {
        cell.v = { v: cell.v }
      }
      cell.v.image = {
        ...imgInfo,
        _href: withExtraQuery(imgInfo.src, _.map(imgInfo._extraQueries, ({ key, val }) => `${key}=${val}`).join('&'))
      }
    })
  })
}

/** 提取并合并图片信息 */
export function extractImageInfo(
  inflatedCelldata: LuckysheetCellData[],
  images: LuckysheetImageInfo[] | undefined
) {
  const pageQuery = qs.parse(window.location.search.replace(/^\?/, ''))
  const imgs: LuckysheetImageInfo[] = _(inflatedCelldata)
    .filter(c => _.isObject(c.v) && !!c.v.image)
    .keyBy(({ r, c, v }) => `${(v as LuckysheetCellVal).image!._id || (v as LuckysheetCellVal).image!.id}_${r}_${c}`)
    .mapValues(c => {
      // decodeUri，补充内容到参数 args
      const cv = c.v as LuckysheetCellVal
      const withCellVal = `${decodeURIComponent(cv.image?.src || '')}`
      const [linkAddress, extraQuery] = withCellVal.split('?')
      const latestQuery = qs.parse(extraQuery) || {}
      const finalQuery = qs.stringify({ ...pageQuery, ...latestQuery })
      return {
        ...(cv.image || {}),
        src: withExtraQuery(linkAddress, finalQuery)
      } as LuckysheetImageInfo
    })
    .values()
    .value()
  if (_.isEmpty(imgs)) {
    return { images, inflatedCelldata }
  }
  const inflatedCelldataDroppedImgs = _.map(inflatedCelldata, cell =>
    _.isObject(cell.v) && 'image' in cell.v ? _.omit(cell, 'v.image') : cell
  )
  return { images: _.uniqBy([...(images || []), ...imgs], 'id'), inflatedCelldata: inflatedCelldataDroppedImgs }
}

/** 计算 excel 公式，只支持四则运算 */
function computeFormula(formula: string, cellPosDict: Record<string, LuckysheetCellData>) {
  // A1:B2 => range(A,1,B,2)
  const rangeToJs = formula.replace(
    /(\$?[A-Z]+)(\$?[0-9]+):(\$?[A-Z]+)(\$?[0-9]+)/g,
    (_m, c1, r1, c2, r2) => `range('${c1}',${r1},'${c2}',${r2})`
  )
  // SUM(...) => utils.sumFn(...) // 加上 fn 是因为 if 是 js 的保留字
  const fnCallWithNs = rangeToJs.replace(/(\w+)\(/gi, (_m, fnName) => `utils.${fnName.toLowerCase()}Fn(`)
  // =utils.ifFn(E2<=0,0,G2/E2)  ->  =utils.ifFn(348<=0,0,1704/348)
  const js = fnCallWithNs.replace(/(\$?[A-Z]+)(\$?[0-9]+)/g, (_m, col, row) => {
    const cell = cellPosDict[`${unfixCol(col)}${unfixRow(row)}`]
    if (!cell) {
      return '0'
    }
    const v = (_.isObject(cell.v) ? cell.v.v : cell.v) || 0
    return Number.isNaN(+v) ? `"${v}"` : v
  })
  const utils = {
    ...excelFns,
    rangeFn: (c1: string, r1: string, c2: string, r2: string) => {
      const cs = decodeColMemo(c1)
      const rs = +r1
      const ce = decodeColMemo(c2)
      const re = +r2
      const range: any[][] = []
      for (let r = rs; r <= re; r++) {
        const inner: any[] = []
        for (let c = cs; c <= ce; c++) {
          const cell = cellPosDict[encodeCell({ c, r: r - 1 })]
          if (!cell) {
            inner.push(null)
            continue
          }
          inner.push(_.isObject(cell.v) ? cell.v.v : cell.v)
        }
        range.push(inner)
      }
      return range
    }
  }
  // =utils.ifFn(348=0,0,1704/348)  ->  =utils.ifFn(348==0,0,1704/348)
  const excelOpToJs = js.replace(/([\d.]+)=([\d.]+)/g, '$1==$2')
  const wrappedJs = `(function(utils){return ${excelOpToJs.substring(1)}})`
  try {
    // eslint-disable-next-line no-eval
    return eval(wrappedJs)(utils)
  } catch (e) {
    console.error(e)
  }
}

/** 生成公式链，进行初步运算（只支持四则运算） */
export function generateCalcChain(
  inflatedCells: LuckysheetCellData[],
  sheetIdx: number
): { calcChain: LuckysheetCalcChainNode[]; computedCells: LuckysheetCellData[] } {
  const calcChain = _(inflatedCells)
    .filter(({ v }) => _.isObject(v) && _.startsWith(v.f, '='))
    .map(({ r, c }) => ({ r, c, index: sheetIdx }))
    .value()
  if (_.isEmpty(calcChain)) {
    return { computedCells: inflatedCells, calcChain: [] }
  }
  return {
    calcChain,
    computedCells: produce(inflatedCells, drafts => {
      const cellPosDict = _.keyBy(drafts, c => encodeCell(c))
      _.forEach(calcChain, ({ r, c }) => {
        const cell = cellPosDict[encodeCell({ r, c })]
        if (_.isObject(cell.v) && _.startsWith(cell.v.f, '=')) {
          const { f, v } = cell.v
          const computedValue = computeFormula(f!, cellPosDict) ?? v
          cell.v = { ...cell.v, v: computedValue, m: `${computedValue}` }
        }
      })
    })
  }
}

export function patchOutsideCellData(
  inflatedCells: LuckysheetCellData[],
  templateCells: LuckysheetCellData[] | undefined
) {
  const insideCellDataDict = _.keyBy(inflatedCells, encodeCell)
  const preAppend = _.map(templateCells, cell => {
    const { r, c } = cell
    const insideCell = insideCellDataDict[encodeCell({ r, c })]
    return !insideCell ? cell : null
  })
  return [...inflatedCells, ..._.compact(preAppend)]
}

/** 将多个页面合并成一个页面 */
export function combinePages(pagesInfo: InflatedResult[], opts?: { isMergingZonesInSinglePage?: boolean }): InflatedResult {
  const { isMergingZonesInSinglePage = false } = opts || {}
  if (pagesInfo.length === 1) {
    return pagesInfo[0]
  }
  return {
    templateSheet: pagesInfo[0].templateSheet,
    inflatedCelldata: _.flatMap(pagesInfo, p => p.inflatedCelldata),
    borderInfo: _.flatMap(pagesInfo, p => p.borderInfo),
    merge: _.assign({}, ..._.map(pagesInfo, p => p.merge)),
    rowlen: _.assign({}, ..._.map(pagesInfo, p => p.rowlen)),
    columnlen: _.assign({}, ..._.map(pagesInfo, p => p.columnlen)),
    rowhidden: _.assign({}, ..._.map(pagesInfo, p => p.rowhidden)),
    colhidden: _.assign({}, ..._.map(pagesInfo, p => p.colhidden)),
    // 如果正在合并单个页面的 zones，那么 totalContentRowCnt 只取第一个页面的 totalContentRowCnt
    totalContentRowCnt: isMergingZonesInSinglePage
      ? pagesInfo[0].totalContentRowCnt
      : _.sum(_.map(pagesInfo, p => p.totalContentRowCnt)),
    condFormatInfos: _.flatMap(pagesInfo, p => p.condFormatInfos),
    calcChain: _.flatMap(pagesInfo, p => p.calcChain).map(c => ({ ...c, index: 0 })),
    hyperlink: _.assign({}, ..._.map(pagesInfo, p => p.hyperlink)),
    images: _.compact(_.flatMap(pagesInfo, p => p.images))
  }
}

/** 禁止用户编辑单元格 */
export function readOnly(luckysheetCfg: Partial<LuckysheetConfig> | undefined) {
  return produce(luckysheetCfg || ({} as LuckysheetConfig), draft => {
    draft.allowEdit = false
    // 清除选择区域
    const sel = {
      row: [83, 83],
      column: [0, 0]
    }
    draft.data?.forEach(sheet => {
      sheet.luckysheet_select_save = [sel]
      sheet.luckysheet_selection_range = []
    })
  })
}

/** 生成重置后的 luckysheet 配置 */
export function genLuckysheetInitConfig(
  dataSourceConfig: DataSourceConfig,
  mode: 'basic' | 'pivot'
): Partial<LuckysheetConfig> {
  const { fieldsBinding, sortedFieldNames } = dataSourceConfig[dataSourceConfig.dataSourceType] || {}
  if (!fieldsBinding) {
    return {}
  }
  // 如果没有用户设置对顺序，默认字段顺序：日期，字符，数字
  let orderedFieldNames = _.isEmpty(sortedFieldNames)
    ? _.orderBy(_.keys(fieldsBinding), k => {
      const col = fieldsBinding?.[k]
      if (col?.dataType === 'date') {
        return 0
      }
      return col?.dataType === 'string' ? 1 : 2
    })
    : (sortedFieldNames as string[])
  orderedFieldNames = _.intersection(orderedFieldNames, _.keys(fieldsBinding))
  const totalColCnt = orderedFieldNames.length + 1 // 包含序号列
  const templateFields = _.filter(orderedFieldNames, f => /date|string/.test(fieldsBinding[f]?.dataType || ''))
  // 透视表也是只取一个就行，剩下的分组维度默认跟最后一个标记区域相同
  const templateFieldsByMode = mode === 'basic' ? ['__value__'] : [..._.take(templateFields, 1), '__restDim__']

  const startRow = 1
  const startCol = 1
  const templateZones = _.map(templateFieldsByMode, fieldName => ({
    field: fieldName,
    c: [startCol, startCol + totalColCnt - 1],
    r: [startRow + 1, startRow + 1]
  }))
  const basicConfig: Partial<LuckysheetConfig> = {
    lang: 'zh',
    plugins: [],
    data: [
      {
        name: 'Sheet1',
        color: '',
        status: 1,
        order: 0,
        celldata: [
          { r: startRow, c: startCol, v: '序号' },
          ..._.map(orderedFieldNames, (fieldName, i) => ({
            r: startRow,
            c: startCol + i + 1,
            v: fieldsBinding[fieldName]?.title || fieldName
          })),
          // eslint-disable-next-line no-template-curly-in-string
          { r: startRow + 1, c: startCol, v: '${ig+1}' },
          ..._.map(orderedFieldNames, (fieldName, i) => ({
            r: startRow + 1,
            c: startCol + i + 1,
            v:
              mode === 'basic' || fieldsBinding[fieldName]?.dataType !== 'number'
                ? `\${obj[t('${fieldsBinding[fieldName]!.title}')]}`
                : `\${seq().sumBy(t('${fieldsBinding[fieldName]!.title}'))}`
          }))
        ],
        data: [],
        chart: [],
        column: 120,
        config: {
          templateZones,
          inflateMode: mode,
          lastUpdatedAt: Date.now()
        },
        hyperlink: {},
        index: 0
      }
    ]
  }

  return basicConfig
}

/** 计算 luckysheet 总高度 */
export function computeLuckysheetTotalHeight(
  inflatedSheet: Partial<LuckysheetConfig>,
  config: LuckysheetCompConfig,
  newPageHeight: number,
  containerPos: { top: number; left: number; width: number; height: number }
) {
  // 总渲染行数 = 内容行数 + 页头行数 * 页数 + 页尾行数 * 页数
  const sheets = inflatedSheet.data!
  const firstVertZone = _.find(sheets[0].config?.templateZones, z => z.direction !== 'column')
  const [rs, re] = firstVertZone?.r || [0, 0]
  const templateSheet = config.luckysheetConfig?.data[0]
  const templateCelldata = templateCelldataCombineCellSize(
    {
      celldata: templateSheet?.celldata || [],
      rowHeightDict: templateSheet?.config?.rowlen || {},
      colWidthDict: templateSheet?.config?.columnlen || {},
      rowHidden: templateSheet?.config?.rowhidden || {},
      colHidden: templateSheet?.config?.colhidden || {}
    }
  )

  const headerTemplate = _.filter(templateCelldata, ({ r }) => r < rs)
  const bodyTemplate = _.filter(templateCelldata, ({ r }) => rs <= r && r <= re)
  const footerTemplate = _.filter(templateCelldata, ({ r }) => re < r)

  const maxRowIdx = _.maxBy(templateCelldata, 'r')?.r || 0

  const headerHeight = getRowHeight(headerTemplate, 0, rs - 1)
  const bodyRowAvgHeight = getRowHeight(bodyTemplate, rs, re) / (re - rs + 1)
  const footerHeight = getRowHeight(footerTemplate, (firstVertZone?.r[1] || 0) + 1, maxRowIdx)
  const currPageHeight = config.pageHeight
  const printMode = config.printMode
  const pageRowLimit = printMode ? Number.MAX_SAFE_INTEGER : config.pageSize || Number.MAX_SAFE_INTEGER
  const finalPageHeight = newPageHeight || currPageHeight || Number.MAX_SAFE_INTEGER

  const footerOnlyInLastPage = !config.showFooterOnEveryPage
  // 总内容行数
  const totalContentRowCnt = _.sumBy(sheets, s => s.config?.totalContentRowCnt || 0)

  // 每页能容纳的内容行数
  const contentRowCntByPageHeight = _.min([
    pageRowLimit, // 打印模式开启后，pageSize 会设为 0，只按页高来计算
    totalContentRowCnt,
    footerOnlyInLastPage
      ? Math.floor((finalPageHeight - headerHeight) / bodyRowAvgHeight)
      : Math.floor((finalPageHeight - headerHeight - footerHeight) / bodyRowAvgHeight)
  ])!

  // 页数 = 内容行数 / 每页内容行数，不需要取整，获得更精确的高度
  const pageCnt = totalContentRowCnt / contentRowCntByPageHeight


  // 总内容高度 = 页高 * 页数
  const pageContentHeight = footerOnlyInLastPage
    ? (bodyRowAvgHeight * contentRowCntByPageHeight + headerHeight + footerHeight)
    : (bodyRowAvgHeight * contentRowCntByPageHeight + headerHeight) + footerHeight

  if (!printMode) {
    // 打印模式关闭，相当于每页纸只渲染一个分页，页高就是纸高
    return Math.floor(finalPageHeight * (pageCnt - 1) + containerPos.top + pageContentHeight)
  }
  return Math.floor(pageCnt * pageContentHeight)
}

/** 计算页头行数 */
export function calcHeaderRowCnt(templateCelldata: LuckysheetCellData[], rs: number) {
  // 从 rs 往上找，找到第一个空行，空行与 rs 之间的行数就是页头行数
  const rowGroup = _.groupBy(templateCelldata, 'r')
  for (let i = rs - 1; i >= 0; i--) {
    const rowCells = rowGroup[i]
    if (_.every(rowCells, ({ v }) => _.isNil(_.isObject(v) ? v.v : v))) {
      return rs - i - 1
    }
  }
  return rs
}

/** 计算页脚行数 */
export function calcFooterRowCnt(templateCelldata: LuckysheetCellData[], re: number) {
  // 从 re 往下找，找到第一个空行，空行与 re 之间的行数就是页脚行数
  const rowGroup = _.groupBy(templateCelldata, 'r')
  const maxRow = _.maxBy(templateCelldata, 'r')?.r || 0
  for (let i = re + 1; i <= maxRow; i++) {
    const rowCells = rowGroup[i]
    if (_.every(rowCells, ({ v }) => _.isNil(_.isObject(v) ? v.v : v))) {
      return i - re - 1
    }
  }
  return maxRow - re
}

/** 获取 luckysheet 的当前选中区域 */
export function getLuckysheetSelectionRange(luckysheetCfg: Partial<LuckysheetConfig>) {
  return getActiveSheet(luckysheetCfg)?.luckysheet_select_save?.[0]
}

/** 获取 localStorage 中以 prefix 开头的所有 key */
export function getLocalStorageKeys(prefix: string) {
  const keys: string[] = []
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i)
    if (key?.indexOf(prefix) === 0) {
      keys.push(key)
    }
  }
  return keys
}

export function toArgbHex(cssColor: string) {
  if (!cssColor) {
    return undefined
  }

  if (cssColor.startsWith('#')) {
    if (cssColor.length === 7) {
      // rgb 转 argb
      const [r, g, b] = cssColor.match(/\w{2}/g) || []
      return `FF${r}${g}${b}`
    }
    if (cssColor.length === 9) {
      // rgba 转 argb
      const [r, g, b, a] = cssColor.match(/\w{2}/g) || []
      return `${a}${r}${g}${b}`
    }
    return undefined
  }

  if (cssColor.startsWith('rgb')) {
    // rgb(255, 255, 255) 转 argb
    const rgb = cssColor.match(/\d+/g) || []
    const [r, g, b] = rgb.map(c => _.padStart(Number(c).toString(16), 2, '0'))
    return `FF${r}${g}${b}`
  }

  if (cssColor.startsWith('rgba')) {
    // rgba(255, 255, 255, 1) 转 argb
    const [r, g, b, a] = cssColor.match(/\d+/g) || []
    const rh = (+r).toString(16).padStart(2, '0')
    const gh = (+g).toString(16).padStart(2, '0')
    const bh = (+b).toString(16).padStart(2, '0')
    const ah = (+a * 255).toString(16).padStart(2, '0')
    return `${ah}${rh}${gh}${bh}`
  }

  return undefined
}

/** 合并循环体内的常量（通常是重复的）单元格 */
export function mergeConstCellsByZones(
  templateCelldata: LuckysheetCellData[] | undefined,
  zones: LuckysheetTemplateZone[]
) {
  const zonesFiltered = zones.filter(z => z.mergeConstCells && (
    z.direction === 'column' && (z.c[1] - z.c[0] === 0) || z.direction === 'row' && (z.r[1] - z.r[0] === 0)
  ))
  if (_.isEmpty(zonesFiltered)) {
    return templateCelldata
  }
  return _.map(templateCelldata, cell => {
    const { r, c, v: cv } = cell
    if (!_.isObject(cv)) {
      return cell
    }
    const isFormulaCell = _.includes(cv.v, '${') || _.includes(cv.v, '<%')
    if (isFormulaCell || _.isNil(cv.v) || cv.v === '') {
      return cell
    }
    const zone = _.find(zonesFiltered, z => {
      const [rs, re] = z.r
      const [cs, ce] = z.c
      return rs <= r && r <= re && cs <= c && c <= ce
    })
    if (!zone) {
      return cell
    }
    return {
      ...cell,
      v: {
        ...cv,
        mc: {
          r,
          c,
          rs: zone.direction === 'row' ? 2 : 1,
          cs: zone.direction === 'column' ? 2 : 1,
          id: `${r}_${c}`
        }
      }
    }
  })
}
