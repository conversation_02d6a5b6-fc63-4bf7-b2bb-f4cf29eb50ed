import './index.less'

import _ from 'lodash'
import React, { useEffect, useMemo } from 'react'

import type { BaseProps } from '@/components/elements/type'
import QrcodeView from '@/components/qrcode-view'

export type QrcodeElementProps = BaseProps<{
  config: {
    color: string
    text: string
    mode: 'static' | 'dynamic' | (string & {})
  }
}>

/**
 * 二维码
 * @name element-qrcode
 */
export default function QrcodeElement(props: QrcodeElementProps) {
  const { style, config, data } = props
  const { color, mode, text } = config
  // 最多 512 字符
  const code = useMemo(() => {
    try {
      return mode === 'static' ? String(text).slice(0, 512) : String(_.get(data, '[0].dim', '')).slice(0, 512)
    } catch (err) {
      return 'error'
    }
  }, [mode, data, text])

  const w = Math.min(style.width, style.height)

  if (!code) return null

  return (
    <QrcodeView
      width={w}
      height={w}
      data={code}
      color={color}
      quality={2}
      className='local-element-qrcode' // ..
    />
  )
}
