import './index.less'

import _ from 'lodash'
import React from 'react'

import type { BaseProps } from '@/components/elements/type'

import { EditTextElement } from '../text'

export type RectangleProps = BaseProps<{
  config: {}
}>

/**
 * 矩形，圆形
 * @name element-rectangle
 */
export default function RectangleElement(props) {
  const { style, config } = props

  // return <div className='local-element-rectangle' style={_.omit(style, ['width', 'height'])} />

  return (
    <EditTextElement
      {...props}
      className='local-element-rectangle'
      style={{
        ..._.omit(style, ['width', 'height']),
        alignItems: config?.isCard ? 'flex-start' : 'center'
      }}
    />
  )
}
