import './index.less'

import { Tabs } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React, { useEffect } from 'react'

import type { BaseProps } from '@/components/elements/type'
import { EditorCorePreviewRuntimeAPI } from '@/cores/evaction/rumtime-api/preview'
import { ComponentDefine } from '@/services/type'
import { DataSourceConfig } from '@/types/editor-core/data-source'
import { EventHandleActionMap } from '@/types/editor-core/events'

export type TabsIndicesElementProps = BaseProps<{
  config: {
    index: number
  }
}>

type fieldsValue = {
  title: string
  id: string
  name: string
}

/** 获取展示的字段 */
const getFieldsValue = (dataSource: DataSourceConfig, define: ComponentDefine) => {
  const dataSourceType = _.get(dataSource, 'dataSourceType')
  const dataSourceData = _.get(dataSource, dataSourceType)?.fieldsBinding
  const dataSourceIndicesConfig = _.map(dataSourceData, i => ({
    title: i?.title,
    id: i?.id,
    name: i?.name
  }))
  const dataSourceDefine = _.get(define, 'dataSourceDefine')
  const demoFields = dataSourceDefine?.fields
  const dimension = _.map(_.filter(demoFields, { type: 'number' }), i => ({
    title: i?.title,
    id: i?.title,
    name: i?.name
  }))

  const fields = _.isEmpty(dataSourceData) ? dimension : dataSourceIndicesConfig
  return fields as fieldsValue[]
}

/** 改变组件数据源hook */
export const useChangeDataConfig = (
  eventAction: EventHandleActionMap,
  runtimeAPI: EditorCorePreviewRuntimeAPI<any> | undefined
) => {
  const changeTarget = _.get(eventAction, 'entities.change')
  return {
    changeDataConfig: (value: string, title: string | undefined, name: string | undefined) => {
      // 拿到要改变数据源的组件
      const changeTargetComp = _.flatMap(changeTarget, action => action.params?.changeTargetComp)
      // 去改变多个数据源
      _.forEach(changeTargetComp, compKey => {
        const targetConfig = runtimeAPI?.getComponentByKey(compKey)
        targetConfig?.updateDataSourceQueryConfig(queryCfg => {
          const queryConfig = _.cloneDeep(queryCfg)
          if (queryConfig?.fieldsBinding) {
            // 修改指标id 和 title
            queryConfig.fieldsBinding = _.mapValues(queryConfig?.fieldsBinding, i =>
              i?.type === 'indicesSpec' ? { ...i, id: value, title: title || i.title, name: name || i.name } : i
            )
          }
          return queryConfig
        })
        targetConfig?.cleanChartData()
      })
    }
  }
}

/**
 * 指标Tabs 组件
 * @name element-indices-tabs
 */
export default function TabsIndicesElement(props: TabsIndicesElementProps) {
  const { config, style, meta, dataSource, define, runtimeAPI, onEvents, onConfigChange, eventAction } = props
  const fields = getFieldsValue(dataSource, define)
  const index = (config?.index <= fields.length || config?.index < 0 ? config?.index || 1 : 1) - 1
  const defaultValue = config?.value || fields[index]?.id
  const activeColor = _.get(config, 'activeColor') || '#1677ff'
  const zoom = _.get(config, 'zoom') || 1
  // const themeColor = _.get(config, 'themeColor') || '#1677ff'
  // 值改变触发
  const onChange = onEvents?.onChange || onEvents?.change || _.noop

  const onUpdateConfigChange = onConfigChange || _.noop // 为了保存值到 linkage，避免初始化的时候触发事件

  const classKey = `indices-tabs-${meta.key}`.replace('@', '-')

  const { changeDataConfig } = useChangeDataConfig(eventAction, runtimeAPI)
  const queryCfg = _.find(fields, i => i.id === defaultValue)

  useEffect(() => {
    changeDataConfig(defaultValue, queryCfg?.title, queryCfg?.name)
  }, [changeDataConfig, defaultValue, fields, queryCfg?.name, queryCfg?.title])

  const tabStyle = `
  .ant-tabs-tab-active .ant-tabs-tab-btn {
    color: ${activeColor} !important;
  }
  .ant-tabs-ink-bar {
    background: ${activeColor} !important;
  }
  `

  /** 渲染Tab子组件 */
  // const renderTab = (_fields: fieldsValue[]) => _.map(_fields, field => <Tabs.Tab title={field.title} key={field.id} />)

  return (
    <div
      className={cn({
        'local-element-indices-tabs': true,
        [classKey]: true
      })}
      style={{ ...style, zoom }}
    >
      <style>{tabStyle}</style>
      <Tabs
        activeKey={config?.value || defaultValue}
        onChange={v => {
          const fieldValues = _.find(fields, i => i.id === v)
          changeDataConfig(v, fieldValues?.title, fieldValues?.name)
          onChange(v)
          onUpdateConfigChange({ ...config, value: v })
        }}
        defaultActiveKey={defaultValue}
        items={fields.map(i => ({ key: i.id, tabKey: i.id, label: i.title || i.name }))}
      >
        {/* {renderTab(fields)} */}
      </Tabs>
    </div>
  )
}
