.local-element-tabs-layout {
  overflow: hidden;
}

.local-element-tabs-layout-mc-tabs {
  height: 100%;
  width: 100%;

  &.fix-tabs-layout-bottom {
    flex-direction: column-reverse;
  }

  .mc-tabs-header {
    height: 35px;
    padding: 1px 2px;
    display: flex;
    align-items: center;
    overflow: hidden;

    .mc-tabs-item-wrap {
      overflow: auto hidden;
      white-space: nowrap;

      &::-webkit-scrollbar {
        width: 3px;
        height: 3px;
        border: none;
      }
    }

    &-item {
      font-size: 15px;
      padding: 2px 6px;
      margin: 0 4px;
      text-align: center;
      min-width: 50px;
      cursor: pointer;
      display: inline-block;
      // border: 1px solid transparent;
    }

    .mc-tabs-header-tab-circle,
    .mc-tabs-header-tab-linear {
      width: 8px;
      height: 8px;
      border-radius: 100%;
      min-width: auto;
      // border: 1px solid;
      padding: 0;
      // box-shadow: 0 0 2px rgba(1, 1, 1, 0.1);
    }
    .mc-tabs-header-tab-linear {
      width: 32px;
      border-radius: 4px;
      height: 5px;
    }
  }

  .mc-tabs-content {
    position: relative;
  }

  .ant-tabs-tab {
    margin: 0 6px;
  }
  .ant-tabs-content {
    width: 100%;
    height: 100%;
  }

  .ant-tabs-tabpane {
    position: relative;
  }
}
