import './index.less'

import { useDeepCompareEffect } from 'ahooks'
import _ from 'lodash'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { useSwipeable } from 'react-swipeable'

import McTabs from './mc-tabs'
import { TriangleProps } from './type'

/**
 * tabs layout 布局组件（比较复杂）
 * @name layout-tabs
 */
export default function TabsLayout(props: TriangleProps) {
  const { config, renderTabContent, style, meta } = props

  const [activeIndex, setActiveIndex] = useState(0)
  const timerRef = useRef<any>(0)

  const tabs = useMemo(() => config.contents || [], [config.contents])
  const layouts = useMemo(() => config.layouts || [], [config.layouts])

  const innerStyle = _.omitBy(style, i => i === undefined)
  const headerStyle = _.omitBy(config.headerStyle, i => i === undefined)
  const activeStyle = _.omitBy(config.activeStyle, i => i === undefined)
  const itemStyle = _.omitBy(config.itemStyle, i => i === undefined)
  const tabPosition = config.tabPosition || 'top'
  const tabIndicate = config.tabIndicate || 'text'
  const hidden = config.hidden || true
  const swiperConfig = config.swiper
  const maxIndex = tabs.length

  const interval = _.get(swiperConfig, 'interval', 0)
  const display = _.get(swiperConfig, 'display', false)

  const handlers = useSwipeable({
    onSwipedLeft: () => setActiveIndex(prevKey => {
      const nextKey = _.toNumber(prevKey) + 1
      // 如果 nextKey 大于 tabs 的数量，就返回最后一个 tab 的 key
      return nextKey >= layouts.length ? layouts.length - 1 : nextKey
    }),
    onSwipedRight: () => setActiveIndex(prevKey => {
      const nextKey = prevKey - 1
      // 如果 nextKey 小于 1，就返回第一个 tab 的 key
      return nextKey < 1 ? 0 : nextKey
    }),
    trackMouse: true
  })

  const run = () => {
    if (!(display && interval > 0)) return
    timerRef.current = setInterval(() => {
      setActiveIndex(t => {
        // 如果是不循环则停止进行
        if (t === maxIndex - 1 && !swiperConfig?.loop) {
          clearInterval(timerRef.current)
          return t
        }
        if (t >= maxIndex - 1) return 0
        return t + 1
      })
    }, interval * 1000)
  }

  const stop = () => {
    clearInterval(timerRef.current)
  }

  const events = display
    ? {
      onMouseEnter: () => stop(),
      onMouseLeave: () => run()
    }
    : {}

  useEffect(() => {
    setActiveIndex(config.activeIndex || 0)
  }, [config.activeIndex])

  useDeepCompareEffect(() => {
    stop()
    setActiveIndex(config.activeIndex)
    run()
    return () => {
      stop()
    }
  }, [swiperConfig])

  return (
    <div className='local-element-tabs-layout' style={innerStyle} {...events} {...handlers}>
      <McTabs
        swiperConfig={config.swiper}
        componentKey={meta.key}
        options={tabs}
        layouts={layouts}
        activeIndex={activeIndex}
        headerStyle={headerStyle}
        activeStyle={activeStyle}
        tabPosition={tabPosition}
        tabIndicate={tabIndicate}
        itemStyle={itemStyle}
        tabsHidden={hidden}
        onChange={index => setActiveIndex(index)}
        renderTabContent={renderTabContent}
      />
    </div>
  )
}
