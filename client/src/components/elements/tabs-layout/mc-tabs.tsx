import './mc-tabs.less'

import { Tabs } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React from 'react'

import { TabsProps } from './type'

const { TabPane } = Tabs

/**
 * 自定义的 tabs
 * @param props
 */
export default function McTabs(props: TabsProps) {
  const { activeIndex, options = [], renderTabContent, layouts, onChange, componentKey, tabsHidden } = props
  const { headerStyle, activeStyle, itemStyle, tabPosition, tabIndicate } = props

  const onSelect = (index: any) => e => {
    e.stopPropagation()
    onChange(index)
    document['activeElement' as any]?.blur()
  }

  const renderTabBar = () => (
    <header className='mc-tabs-header' style={headerStyle}>
      <div className='mc-tabs-item-wrap'>
        {_.map(options, (item, index) => (
          <span
            key={item.key}
            className={cn({
              'mc-tabs-header-item': true,
              'mc-tabs-header-active': index === activeIndex,
              'mc-tabs-header-tab-circle': tabIndicate === 'circle',
              'mc-tabs-header-tab-linear': tabIndicate === 'linear'
            })}
            style={index === activeIndex ? { ...itemStyle, ...activeStyle } : itemStyle}
            onClick={onSelect(index)}
          >
            {tabIndicate === 'text' && item.title}
          </span>
        ))}
      </div>
    </header>
  )

  return (
    <Tabs
      id={`mc-tabs-${componentKey}`}
      className={cn({
        'local-element-tabs-layout-mc-tabs': true,
        'fix-tabs-layout-bottom': tabPosition === 'bottom',
        // echarts图表对display: none处理有优化 这里使用opacity做处理 保证图表正常显示
        'mc-tabs-hidden-opacity': tabsHidden,
        'mc-tabs-hidden': tabsHidden
      })}
      activeKey={activeIndex.toString()}
      onChange={index => onSelect(index)}
      // tabPosition={tabPosition}
      tabBarStyle={{ height: 35, ...headerStyle }}
      renderTabBar={renderTabBar}
      animated={!false}
    >
      {_.map(options, (item, index) => (
        <TabPane key={index} tab={item.title}>
          {renderTabContent({ data: layouts[index], index })}
        </TabPane>
      ))}
    </Tabs>
  )
}
