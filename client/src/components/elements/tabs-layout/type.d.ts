import { CSSProperties } from 'react'
import type { BaseProps } from '../type'

export interface TabsLayoutConfig {
  config: {
    headerStyle: CSSProperties
    itemStyle: CSSProperties
    activeStyle: CSSProperties
    activeIndex: number
    swiper?: {
      dispaly: boolean
      loop: boolean
      interval?: number
    }
    // 只存这么多信息了
    contents: {
      title: string
      key: string // 每次创建 tab 时要自动生成一个
      [key: string]: any
    }[]
    // tabs 里的布局结构
    layouts: {
      // component-key（子组件）: info
      [key: string]: {
        key: string // component key（子组件）
        name: string
        defineKey: string
        containerKey: string // 父级的 tabs layout key
        tabIndex: number
      }
    }[]
  }
}

export interface TriangleProps extends BaseProps<TabsLayoutConfig> {
  renderTabContent: (props: {
    data: TabsLayoutConfig['config']['layouts'][number]
    index: number
  }) => JSX.Element | null
}

export interface TabsProps {
  componentKey: string
  activeIndex: number
  options: {
    title: string
    key: string
  }[]
  tabsHidden: boolean
  layouts: TabsLayoutConfig['config']['layouts']
  headerStyle: CSSProperties
  itemStyle: CSSProperties
  activeStyle: CSSProperties
  tabPosition?: 'top' | 'bottom'
  // 指示灯样式
  tabIndicate?: 'text' | 'circle' | 'linear'

  swiperConfig: TabsLayoutConfig['config']['swiper']

  onChange: (index: number) => any
  renderTabContent: (props: {
    data: TabsLayoutConfig['config']['layouts'][number]
    index: number
  }) => JSX.Element | null
}
