import { format } from 'd3-format'
import _ from 'lodash'
import { CSSProperties, useMemo } from 'react'

import { Component } from '@/types/editor-core/component'

import { FONT_FIELD } from '../const'


/**
 * 获取 font style
 * @param style
 * @returns
 */
export const useFontStyle = (style: CSSProperties | any, config: any = {}) => {

  const lineHeight = Number.parseInt(style.lineHeight as string, 10)
  const fontSize = Number.parseInt(style.fontSize as string, 10)

  const textStyle = useMemo(() => {
    const obj = {
      ..._.pick(style, FONT_FIELD),
      textAlign: style.textAlign || 'center',
      // 字体小于解决 12px 的问题
      transform: fontSize < 12 ? `scale(${fontSize / 12})` : 'none',
      lineHeight: `${lineHeight}px`,
      backgroundClip: 'text'
    }
    // 这两个特殊处理
    if (config.fontBorder) {
      _.forIn(config.fontBorder, (val, key) => {
        if (val === undefined) return
        _.set(obj, key, val)
      })
    }
    if (config.fontPadding) {
      _.forIn(config.fontPadding, (val, key) => {
        if (val === undefined) return
        _.set(obj, key, val)
      })
    }
    return obj
  }, [style, fontSize, lineHeight, config.fontBorder, config.fontPadding])

  const omitField = Object.keys(textStyle)
  const wrapStyle = _.omit(style, omitField)

  return {
    textStyle,
    wrapStyle
  }
}

export const getTotalNumber = (value: string, dataSource: Component['dataSource']): string => {
  const formatStr = _.get(dataSource, `${dataSource.dataSourceType}.fieldsBinding.total.columnFormatter`)
  if (!_.isNumber(value) || !formatStr) return value

  try {
    return format(formatStr)(value)
  } catch (err) {
    console.error(err)
  }
  return value
}
