import './index.less'

import { useReactive } from 'ahooks'
import dayjs from 'dayjs'
import _ from 'lodash'
import React, { useEffect, useMemo, useRef } from 'react'

import type { BaseProps } from '@/components/elements/type'

import { getTotalNumber, useFontStyle } from './hooks'

export type TextProps = BaseProps<{
  config: {
    text: string // 文字
    mode: 'static' | 'dynamic' | (string & {})
  }
}> & {
  className: string
}

/**
 * 文本组件
 * @name element-text
 */
export function EditTextElement(props: TextProps) {
  const { onConfigChange, isPreview, style, className, config, data, dataSource } = props
  const preRef = useRef<HTMLPreElement | null>(null)

  const isText = className === 'local-element-text'
  const mode = config.mode

  const { text } = useMemo(() => {
    // 判断是否有 ${value}
    const hasBindValue = /dim|total|value|_|dayjs/.test(config?.text)

    // console.log('hasBindValue', hasBindValue)

    let _text = isText ? config?.text || 'label' : config?.text
    let rv // 渲染的值
    // 读取指标值
    if (mode === 'dynamic') {
      rv = _.first(_.values(_.get(data, '[0]', {}))) || 0

      if (hasBindValue) {
        try {
          const temp = _.template(_text)
          const obj = _.chain(data)
            .flatMap(o => _.entries(o))
            .groupBy(0)
            .mapValues((values: any[]) => _.map(values, value => {
              if (values[0] === 'total') return getTotalNumber(value[1], dataSource)
              return value[1]
            }))
            .value()
          if (obj.total === undefined) obj.total = [0]
          if (obj.dim0 === undefined) obj.dim0 = ['']

          // 如：{ "dim0":["0000|自助", "001|001"],"total":[41117.0999250412, 1669.9999725818634] }
          // console.log('obj', obj)
          _text = temp({ ...obj, _, dayjs }) // 通过模板变量设置
          // console.log(_text)
        } catch (err) {
          console.error(err)
          _text = getTotalNumber(rv, dataSource)
        }
      } else {
        _text = getTotalNumber(rv, dataSource)
      }
    }

    return { text: _text, rawValue: rv }
  }, [config.text, mode, data, dataSource, isText])

  const tip = isText ? '请输入文字' : ''
  const state = useReactive({ isEdit: false, text })
  const { textStyle, wrapStyle } = useFontStyle(style, config)

  const onDoubleClick = e => {
    e.stopPropagation()
    if (!state.isEdit) state.isEdit = true
  }
  const onChange = e => (state.text = e.target.value)
  const onBlur = e => {
    e.stopPropagation()
    state.isEdit = false
    onConfigChange(cig => {
      cig.text = state.text
      return cig
    })
  }
  const onKeyPress = e => e.key === 'Enter' && onBlur(e)

  useEffect(() => {
    state.text = text
  }, [text])

  if (mode === 'dynamic') {
    return (
      <div className={className} style={wrapStyle}>
        <pre className='text' style={textStyle} ref={preRef}>
          {text}
        </pre>
      </div>
    )
  }

  return (
    <div className={className} onDoubleClick={isPreview ? undefined : onDoubleClick} style={wrapStyle}>
      {state.isEdit && !isPreview ? (
        <textarea
          value={state.text}
          placeholder='请输入文字'
          onChange={onChange}
          onBlur={onBlur}
          onKeyPress={onKeyPress}
          onFocus={e => e.target.select()}
          // eslint-disable-next-line jsx-a11y/no-autofocus
          autoFocus
        />
      ) : (
        <pre className='text' style={textStyle} ref={preRef}>
          {text || (isPreview ? '' : tip)}
        </pre>
      )}
    </div>
  )
}

export default function TextElement(props) {
  return <EditTextElement {...props} className='local-element-text' />
}
