import { TimePicker } from '@sugo/design'
import { useMemoizedFn } from 'ahooks'
import { Modal } from 'antd'
import _ from 'lodash'
import PubSub from 'pubsub-js'
import React, { useEffect } from 'react'

import type { BaseProps } from '@/components/elements/type'
import { GRANULARITY_TO_PICKER_DICT } from '@/consts/data-source'


export type TimePickerElementProps = BaseProps<{
  config: {
    timeBucket?: 'DAY' | 'MONTH' | 'WEEK' | 'YEAR'
    value: string | string[]
    pattern?: string // 时间格式
    globalVarName?: string // 全局变量名
  }
}> & {
  className: string
}

/** 通知相同页面的其他组件（全局变量一样的），全局变量发生了变化 */
function notifySamePage(globalVarName: string) {
  const ev: any = new Event('storage')
  ev.key = globalVarName
  ev.newValue = window.localStorage[globalVarName]
  window.dispatchEvent(ev)
}

/** 通过弹窗配置默认值 */
function useDefaultValConfigModal(
  compKey: string,
  value: string | string[],
  onChange: (next: string | string[]) => any
) {
  const valRef = React.useRef(value)
  valRef.current = value
  const onChangeMemo = useMemoizedFn(onChange)

  useEffect(() => {
    const token = PubSub.subscribe(`notify:${compKey}/edit`, (_msg, _data) => {
      let pendingVal = valRef.current

      Modal.confirm({
        title: '修改默认值',
        content: (
          <TimePicker
            value={pendingVal}
            onChange={(_val, formatStr) => {
              pendingVal = formatStr || '-1 day'
            }}
          />
        ),
        bodyStyle: { padding: '0px' },
        onOk: () => {
          // data.onChange(pendingVal)
          onChangeMemo(pendingVal)
        },
        okText: '确定',
        cancelText: '取消'
      })
    })

    return () => {
      PubSub.unsubscribe(token)
    }
  }, [compKey])
}

/** 内置组件：相对时间选择器 */
export default function TimePickerElement(props: TimePickerElementProps) {
  const { config, className, style, idPrefix, meta, onConfigChange } = props
  const { timeBucket, value, pattern, globalVarName } = config
  const componentKey = `${idPrefix}${meta.key}`

  // 获取全局变量
  const finalGlobalVarName = globalVarName && `gv_${globalVarName}`
  const storedStr = finalGlobalVarName && window.localStorage[finalGlobalVarName] || null
  const storedVal = storedStr && JSON.parse(storedStr) || null

  // 初始化全局变量、或根据全局变量初始化 value
  useEffect(() => {
    if (storedVal && !_.isEqual(storedVal, value)) {
      onConfigChange(cig => {
        cig.value = storedVal
        return cig
      })
    }
    // 如果有初始值，就初始化全局变量
    if (!_.isEmpty(value) && finalGlobalVarName) {
      window.localStorage[finalGlobalVarName] = JSON.stringify(value)
    }
  }, [])

  // 监听全局变量变化
  useEffect(() => {
    const isSnapshotPage = _.includes(window.location.search, 'arhType')
    if (isSnapshotPage) {
      return
    }
    const listener = ev => {
      if (ev.key !== finalGlobalVarName) {
        return
      }
      const newValue = ev.newValue
      const newStoredVal = newValue && JSON.parse(newValue) || []
      if (newValue !== storedStr) {
        onConfigChange(cig => {
          cig.value = newStoredVal
          return cig
        })
      }
    }
    window.addEventListener('storage', listener)
    return () => {
      window.removeEventListener('storage', listener)
    }
  }, [])

  const saveVal = (formatStr: string | string[]) => {
    onConfigChange(cig => {
      cig.value = formatStr
      return cig
    })
    // 保存全局变量
    if (finalGlobalVarName) {
      window.localStorage[finalGlobalVarName] = JSON.stringify(formatStr)
      notifySamePage(finalGlobalVarName)
    }
  }

  // 支持默认值配置
  useDefaultValConfigModal(componentKey, value, saveVal)

  return (
    <div
      className={className}
      style={style}
    >
      <TimePicker
        rangeProps={{ picker: GRANULARITY_TO_PICKER_DICT[timeBucket || 'DAY'] }}
        value={value}
        onChange={(_next, formatStr) => saveVal(formatStr || '-1 day')}
        showFormat={pattern}
      />
    </div>
  )
}
