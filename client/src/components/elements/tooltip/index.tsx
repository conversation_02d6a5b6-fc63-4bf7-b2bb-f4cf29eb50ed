import './index.less'

import { QuestionCircleOutlined } from '@ant-design/icons'
import { Tooltip } from 'antd'
import React from 'react'

import type { BaseProps } from '@/components/elements/type'

export type TooltipElementProps = BaseProps<{
  config: {
    text: string
    trigger: string[]
    placement: string
    color: string
  }
}>

/**
 * 提示语
 * @name element-tooltip
 */
export default function ButtonElement(props: TooltipElementProps) {
  const { config, style } = props
  const { text = '', trigger = ['hover'], placement = 'top' } = config

  const w = style.width || 30
  const h = style.height || 30
  const size = Math.max(Math.min(w, h) - 20, 12)
  const color = style.color || '#222'

  return (
    <div className='local-element-tooltip'>
      <Tooltip title={text} trigger={trigger} placement={placement as any} color={config.color}>
        <QuestionCircleOutlined style={{ color, fontSize: size }} />
      </Tooltip>
    </div>
  )
}
