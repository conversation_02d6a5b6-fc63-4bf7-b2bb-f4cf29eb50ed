import './index.less'

import React from 'react'

import type { BaseProps } from '@/components/elements/type'

export type TriangleProps = BaseProps<{
  config: {}
}>

/**
 * 三角形
 * @name element-triangle
 */
export default function TriangleElement(props: TriangleProps) {
  const { style } = props
  const w = (style.width || 60) - 2 // 宽
  const h = (style.height || 60) - 2 // 高
  const m = Math.floor((style.width || 60) / 2) // 中位线
  const fill = style.backgroundColor || '#f89'

  return (
    <div className='local-element-triangle'>
      <svg width='100%' height='100%' version='1.1' xmlns='https://www.w3.org/2000/svg' fill={fill}>
        <path d={`M 0 0 L ${w} 0 L${m} ${h} Z`} />
      </svg>
    </div>
  )
}
