import { Component, ComponentDefine } from '@/types/editor-core/component'
import { ChartDataMap } from '@/types/editor-core/component'
import { EditorCorePreviewRuntimeAPI } from '@/cores/evaction/rumtime-api/preview'

type Style = Component['style']
type Config = Component['config']

/**
 * 内置组件的基础 props
 */
export type BaseProps<T extends { config?: any }> = T & {
  /** 基础信息 */
  meta: Pick<Component, 'key' | 'title' | 'alias' | 'description' | 'name' | 'version'>
  /** 事件动作配置 */
  eventAction: Component['eventAction']
  /** 定义表内容 */
  define: ComponentDefine
  /** 数据 */
  data?: ChartDataMap['entities'][string]
  /** 数据源配置 */
  dataSource: Component['dataSource']

  /** 环境信息 */
  isPreview: boolean
  /** id 前缀 */
  idPrefix?: string
  /** 是否是移动端环境 */
  isMobile: boolean
  /** 此组件的设备类型 */
  deviceType: string

  /** 触发配置改变 */
  onConfigChange: (value: BaseProps<T>['config'] | ((value: BaseProps<T>['config']) => BaseProps<T>['config'])) => any
  /** 触发样式改变 */
  onStyleChange: (value: Style | ((value: Style) => Style)) => any
  /** 配置 */
  config: Config & T['config']
  /** 样式 */
  style: Style

  /** 数据筛选器，查询按钮使用 */
  runtimeAPI?: EditorCorePreviewRuntimeAPI
  /** 事件响应回调 */
  onEvents?: Record<string, Function>
}
