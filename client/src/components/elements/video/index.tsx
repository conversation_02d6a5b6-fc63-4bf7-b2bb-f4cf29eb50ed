import './index.less'

import React from 'react'

import type { BaseProps } from '@/components/elements/type'

export type VideoViewProps = BaseProps<{
  config: {
    objectFit: any
    controls: boolean
    autoPlay: boolean
    loop: boolean
    muted: boolean
    // 模式，原创 url 或本地上传
    mode: 'remote' | 'local'
    remoteUrl: string
    localUrl: string
  }
}>

/**
 * 视频
 * @name element-video
 */
export default function VideoView(props: VideoViewProps) {
  const { style, isPreview, config } = props
  const { width, height, borderRadius } = style
  const { objectFit = 'none', autoPlay, controls, muted, loop, remoteUrl, mode, localUrl } = config

  const url = mode === 'remote' ? remoteUrl : localUrl

  return (
    <div className='local-element-video'>
      <video
        src={url}
        width={width}
        height={height}
        style={{ objectFit, borderRadius }}
        controls={isPreview ? controls : false}
        autoPlay={isPreview ? autoPlay : false}
        muted={muted}
        loop={loop}
      >
        <track default kind='captions' />
      </video>
    </div>
  )
}
