import './index.less'

import { fastMemo } from '@sugo/design/functions'
import { Button, Empty } from 'antd'
import React from 'react'

export interface EmptyDataProps {
  title?: string
  buttonText?: string
  onCreate?: (e: any) => any
  onlyEmpty?: boolean
}

/**
 * 空数据
 * @returns
 */
function _EmptyData(props: EmptyDataProps) {
  const { title, buttonText, onCreate, onlyEmpty } = props

  return (
    <div className='app-empty-data'>
      <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      {!onlyEmpty && title &&
        <div className='tit'>{title}</div>
      }
      {!onlyEmpty && buttonText &&
        <Button type='primary' className='btn-text' onClick={onCreate}>{buttonText}</Button>
      }
    </div>
  )
}

export const EmptyData = fastMemo(_EmptyData)
