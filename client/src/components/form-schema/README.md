---
title:
nav:
  title: Abi design
  path: /abi-design
group:
  title: form-schema
  path: /form-schema
---

## 约定配置式表单模型

> 下面列出，私有的属性类型，公共类型请看后面的内容说明。

### 支持的**基础表单**类型：

- `text`：文本输入框，支持定制前后缀。
  ```ts
  type Text = {
    prefix?: string // 前缀
    suffix?: string // 后缀
    maxLength?: number
  }
  ```
- `text-area`：多行文本输入框。
  ```ts
  type TextArea = {
    maxLength?: number
  }
  ```
- `number`：数值输入框，支持范围，支持百分比输入，支持前缀，后缀。
  ```ts
  type Number = {
    max?: number
    min?: number
    step?: number // 微调间距
    prefix?: string
    suffix?: string
  }
  ```
- `percent`：百分比、绝对值输入框。
- `select`：选择器。支持开启过滤和自定义输入。当需要设置字体时，可开启渲染字体功能。
  ```ts
  type Select = {
    mode?: 'multiple' | undefined // 多选还是单选
    defaultActiveFirst?: boolean // 默认为 false，是否默认选择第一个
    options?: {
      label: string
      value: string
      [key: string]: any
    }[]
  }
  ```
- `color`：单颜色选择器。
  ```ts
  type Color = {
    mode?: 'rgb' | 'hex' | 'rgba' // 模式，默认 rgba
  }
  ```
- `radio`：单选框。
  ```ts
  type Radio = {
    algin?: 'left' | 'center' | 'right' // 对齐方式
    layout?: 'row' | 'column'
    hideTitle?: boolean // 隐藏 title，一般用于只显示 icon 的情况
    options?: {
      label: string
      value: string
      icon?: string
      [key: string]: any
    }[]
  }
  ```
- `checkbox`：复选框或可选框。
  ```ts
  type Checkbox = {
    options?: {
      label: string
      value: string
      [key: string]: any
    }[]
  }
  ```
- `switch`：开关，⽀持选择是否展示⽂字状态。
  ```ts
  type Switch = {
    openText?: string
    closeText?: string
    valueMap?: {
      // value 映射
      [key: string]: boolean
    }
  }
  ```
- `slider`：为滑动输入条，⽀持定制步⻓，支持范围、最大值和最小值。
  ```ts
  type Slider = {
    layout?: 'row' | 'column'
    max?: number
    min?: number
    step?: number
    prefix?: string
    suffix?: string
  }
  ```
- `date`：时间选择，支持范围选择，格式。支持时间基准的加减动态时间表达式默认值，支持模版语法，内部注入了 dayjs 和 value。例如 `'{{ dayjs().startOf('day').add(-20, 's').format() }}'`。
  ```ts
  type Date = {
    // 支持范围，为 range 时 defaultValue 为数组
    mode?: 'default' | 'range'
    allowClear?: boolean
    placeholder?: string | [string, string]
    format?: string // 格式化
    showTime?: boolean // 是否显示 time
  }
  ```

### 支持的样式**套件表单**类型：

- `distance`：方位套件。
  ```ts
  type T = {
    // 忽略字段，忽略的不显示出来
    ignore: ('absolute' | 'percent' | 'enum')[]
  }
  ```
- `font`：字体套件。
  ```ts
  type T = {
    // 忽略字段，忽略的不显示出来
    ignore?: ('fontSize' | 'fontFamily' | 'fontWeight' | 'fontStyle' | 'lineHeight' | 'color')[]
  }
  ```
- `margin/padding`：边距套件。
- `border`：边框套件。
- `background-image`：背景图片套件。
  ```ts
  type T = {
    /** 是否开启剪切功能 */
    enableCropper?: boolean
  }
  ```
- `background`：背景套件。
  ```ts
  type T = {
    // 忽略功能
    ignore?: ('linear-gradient' | 'background-color')[]
  }
  ```
- `box-shadow`：阴影套件。
  ```ts
  type T = {
    mode?: 'box-shadow' | 'text-shadow'
  }
  ```

> 这些默认值都为 style 对应的属性，如

      ```js
      defaultValue: {
        marginRight: 10
      }
      ```

> 注：font、margin/padding、border 如果要支持根对象，需要设置 defaultValue='' 默认值对应的宽度，大小等应该是 number 类型，而是不是 string，例如 marginRight: 10，错误的为 marginRight: '10px'

### 支持内置的图表**套件表单**类型：

- `echart-base-config`：echart 的基础设置封装的套件，是个对象。
  ```ts
  type T = {
    // 忽略字段，忽略的不显示出来
    ignore?: ('showTitle' | 'title' | 'titleAlign' | 'fontSize' | 'fontFamily' | 'color')[]
  }
  ```
- `echart-title`：echart 的 title 配置，自带默认值，是个对象。
  ```ts
  type T = {
    unfold?: boolean // 默认是否展开分组
  }
  ```
- `echart-titles`：echart 的 title 复数配置，自带默认值，是个数组，多标题是用到。
  ```ts
  type T = {
    unfold?: boolean // 默认是否展开分组
  }
  ```
- `echart-xAxis`：echart 的 x 坐标轴配置，自带默认值。
  ```ts
  type T = {
    axis?: 'x' | 'y' // 轴类型
    unfold?: boolean // 默认是否展开分组
  }
  ```
- `echart-xAxes`：echart 的 x 坐标轴复数配置，自带默认值，是个数组。
  ```ts
  type T = {
    axis?: 'x' | 'y' // 轴类型
    unfold?: boolean // 默认是否展开分组
  }
  ```
- `echart-yAxis`：echart 的 y 坐标轴配置，自带默认值。
  ```ts
  type T = {
    axis?: 'x' | 'y' // 轴类型
    unfold?: boolean // 默认是否展开分组
  }
  ```
- `echart-yAxes`：echart 的 y 坐标轴复数配置，自带默认值，是个数组。
  ```ts
  type T = {
    axis?: 'x' | 'y' // 轴类型
    unfold?: boolean // 默认是否展开分组
  }
  ```
- `echart-legend`：echart 的 图例，自带默认值。
- `echart-grid`：echart 的网格线，自带默认值。
- `echart-tooltip`：echart 的提示框，自带默认值。
- `echart-color`：echart 的调色板，自带默认值。
- `echart-series-label`：echart 的 series label，自带默认值。**（暂未支持）**
- `echart-series-itemStyle`：echart 的 series label，自带默认值。**（暂未支持）**

> 注意： echart 图表的配置等出了，echart 套件再配置。

### 支持的**容器表单**类型：

- `tabs`：标签组。支持嵌套 group，menu，list 等
  ```ts
  type Tabs = {
    direction?: 'vertical' | 'horizontal' // tab 位置方向
  }
  ```
- `group`：折叠面板，支持嵌套的层级。
  ```ts
  type Group = {
    layout?: 'row' | 'column'
    unfold?: boolean // 默认是否展开
  }
  ```
- `list`：列表表单。
  ```ts
  type List = {
    minItem?: number // 最小的子项目
    maxItem?: number // 最大的子项目
    mode?: 'list' | 'tabs' // 列表平铺还是 tabs 那种布局
    tabPrefix?: string // tab 上的前缀
  }
  ```
  1. list 需要带一个 valuePath，子元素的 valuePath 只作用于 list 的 valuePath。
  2. list 需要带一个 defaultValue，子元素的 defaultValue 也需要。
     > 容器支持 children 属性，可以无限嵌套，但是不建议超过 3 层，因为界面会挤在一起。 BUG：目前发现多个容器嵌套有问题，尽量别嵌套。
- `object`：object 类型的套件，目前界面上没有东西显示出来，（后面会显示一个变量编辑器），常用于设置默认值。

<!-- ### 待定支持功能

- 支持表单的验证表达式。**（暂未支持）**
- 支持表单的脏值检测。**（暂未支持）**
- 支持条件显示规则，联动选项。**（暂未支持）**
- 支持历史配置功能。**（暂未支持）**
- 支持数据映射设置。**（暂未支持）** -->

### 类型定义

schema 的结构只要以 key-value 结构为主，部分 group 组件需要 children 属性。

> 注意：key 要驼峰式，不要带横杆 `-` 的。不同嵌套对象里，key 不需要全局唯一。

```js
{
  key: {
    title: '大小',
    type: 'number',
    defaultValue: 20,
    valuePath: 'options.size',
    caption: 'xxx'
  }
}
```
