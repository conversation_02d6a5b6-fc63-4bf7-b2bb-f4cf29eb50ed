import './antv-custom-axis.less'

import { InputNumber, Popover, Radio, Switch, Tabs } from 'antd'
import { produce } from 'immer'
import _ from 'lodash'
import React from 'react'

import ColorSelect from '../../color-picker/color-select'
import ItemLayout from '../components/item-layout'
import FontView from '../font/font'
import type { FormSchemaBase } from '../type'


const { TabPane } = Tabs

export interface AntvCustomAxisViewProps extends FormSchemaBase {
  value?: {
    visible?: boolean // 是否显示
    position?: 'left' | 'right' | 'top' | 'bottom' | (string & {})
    grid?: 'arc' | 'line' | (string & {}) // 网格类型
    field?: string // 两个固定的值，X 和 Y
    tickCount?: number // 轴的划分个数默认是 5
    style?: {
      label?: any
    }
  }[] // 多个轴
  onChange: (val: any) => any

  /** 指定坐标轴的索引，例如指定第一个为 x，第二个为 y，['x', 'y']  */
  axisIndex?: ('x' | 'y' | (string & {}))[]
}

/**
 * antv 坐标轴
 * @param props
 * @returns
 */
export default function AntvCustomAxisView(props: AntvCustomAxisViewProps) {
  const { value, onChange, title, icon, caption, axisIndex = ['x', 'y'] } = props

  const xIndex = axisIndex.findIndex(i => i === 'x')
  const yIndex = axisIndex.findIndex(i => i === 'y')
  const xAxis = _.get(value, `[${xIndex}]`, {})
  const yAxis = _.get(value, `[${yIndex}]`, {})

  const tabs = [
    {
      key: 'x' as const,
      tab: 'x 轴',
      posOpt: [
        { label: '下', value: 'bottom' },
        { label: '上', value: 'top' }
      ],
      value: xAxis
    },
    {
      key: 'y' as const,
      tab: 'Y 轴',
      posOpt: [
        { label: '左', value: 'left' },
        { label: '右', value: 'right' }
      ],
      value: yAxis
    }
  ]

  const setFieldValue = (key: 'x' | 'y', valuePath: string, val: any) => {
    const newValue = produce(value || [], data => {
      const item = data[key === 'x' ? xIndex : yIndex]
      if (!item) return
      _.set(item, valuePath, val)
    })
    onChange(newValue)
  }

  const renderFontView = (key: 'x' | 'y', style?: any) => (
    <div className='rows-item'>
      <span>轴标签样式：</span>
      <ColorSelect
        mode='rgba'
        value={style?.fill || '#333'}
        showText={false}
        className='style-color'
        onChange={val => setFieldValue(key, 'style.label.fill', val)}
      />
      <Popover
        trigger={['click']}
        placement='bottom'
        overlayClassName='abi-form-schema-echart-custom-text-overlay'
        destroyTooltipOnHide={{ keepParent: true }}
        content={
          <FontView
            type='font'
            ignore={['textDecoration', 'letterSpacing']}
            value={{
              fontFamily: style?.fontFamily || 'sans-serif',
              color: style?.fill || '#333',
              fontSize: style?.fontSize || 12,
              fontStyle: style?.fontStyle || 'normal',
              fontWeight: style?.fontWeight || 'normal',
              textAlign: style?.textAlign,
              lineHeight: style?.lineHeight
            }}
            onChange={val => {
              const data = { ..._.omit(val, 'color'), fill: val?.color }
              setFieldValue(key, 'style.label', data)
            }}
          />
        }
      >
        <span>更多</span>
      </Popover>
    </div>
  )

  return (
    <ItemLayout
      className='abi-form-schema-antv-custom-axis'
      title={title}
      caption={caption}
      icon={icon}
      layout='column'
    >
      <Tabs
        title='坐标轴'
        className='abi-form-schema-tabs-view tabs-horizontal'
        tabPosition='top'
        animated={false} // ...
      >
        {tabs.map(i => (
          <TabPane key={i.key} tab={i.tab}>
            <div>
              <div className='rows-item'>
                <span>显示：</span>
                <Switch
                  size='small'
                  checked={i.value?.visible}
                  onChange={val => setFieldValue(i.key, 'visible', val)}
                />
              </div>
              <div className='rows-item'>
                <span>位置：</span>
                <Radio.Group
                  value={i.value?.position}
                  options={i.posOpt}
                  defaultValue={i.posOpt[0].value}
                  onChange={e => setFieldValue(i.key, 'position', e.target.value)}
                />
              </div>
              <div className='rows-item'>
                <span>刻度点数：</span>
                <InputNumber
                  step={1}
                  min={0}
                  max={20}
                  value={i.value?.tickCount}
                  placeholder='请输入'
                  size='small'
                  style={{ width: 135 }}
                  onChange={val => setFieldValue(i.key, 'tickCount', val)}
                />
              </div>
              {renderFontView(i.key, i.value?.style?.label)}
            </div>
          </TabPane>
        ))}
      </Tabs>
    </ItemLayout>
  )
}

// 第一个为 x 轴，第二个为 y 轴
AntvCustomAxisView.defaultValue = () => [
  {
    visible: true,
    position: 'bottom'
  },
  {
    visible: true,
    position: 'left'
  }
]
