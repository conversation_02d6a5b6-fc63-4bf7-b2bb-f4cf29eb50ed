import './antv-custom-legend.less'

import { Radio, Switch } from 'antd'
import React from 'react'

import ItemLayout from '../components/item-layout'
import type { FormSchemaBase } from '../type'

export interface AntvCustomLegendViewProps extends FormSchemaBase {
  value?: {
    position?: 'left' | 'right' | 'top' | 'bottom' | (string & {})
    marker?: 'circle' | 'square' | (string & {})
    nameStyle?: any
    valueStyle?: any
    [key: string]: any
  }
  onChange: (val: any) => any
}

/**
 * antv 图例
 * @param props
 * @returns
 */
export default function AntvCustomLegendView(props: AntvCustomLegendViewProps) {
  const { value, onChange, title, icon, caption } = props

  const positionOpts = [
    { label: '上', value: 'top' },
    { label: '下', value: 'bottom' },
    { label: '左', value: 'left' },
    { label: '右', value: 'right' }
  ]

  const markerOpts = [
    { label: '圆角', value: 'circle' },
    { label: '方角', value: 'square' }
  ]

  const update = (key: string, val: any) => onChange({ ...(value || {}), [key]: val })

  const onShow = (show: boolean) => {
    if (show) {
      onChange({ position: 'top', marker: 'circle' })
    } else {
      onChange(undefined)
    }
  }

  return (
    <ItemLayout
      className='abi-form-schema-antv-custom-legend'
      title={title}
      caption={caption}
      icon={icon}
      layout='column'
    >
      <div className='rows-item'>
        <span>显示：</span>
        <Switch checked={value !== undefined} size='small' onChange={onShow} />
      </div>
      <div className='rows-item'>
        <span>位置：</span>
        <Radio.Group
          value={value?.position}
          // optionType='button'
          size='small'
          options={positionOpts}
          onChange={e => update('position', e.target.value)}
        />
      </div>
      <div className='rows-item'>
        <span>标记：</span>
        <Radio.Group
          value={value?.marker}
          size='small'
          options={markerOpts}
          onChange={e => update('marker', e.target.value)}
        />
      </div>
    </ItemLayout>
  )
}

AntvCustomLegendView.defaultValue = () => ({
  position: 'top',
  marker: 'circle'
})
