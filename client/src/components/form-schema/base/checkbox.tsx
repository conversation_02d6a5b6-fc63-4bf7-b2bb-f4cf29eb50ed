import { Checkbox } from 'antd'
import React from 'react'

import ItemLayout from '../components/item-layout'
import type { FormSchemaBase } from '../type'

export interface CheckboxViewProps extends FormSchemaBase {
  value?: string[]
  onChange: (value: string[]) => any
  options?: {
    label: string
    value: string
    [key: string]: any
  }[]
}

/**
 * 多选
 * @param props
 */
export default function CheckboxView(props: CheckboxViewProps) {
  const { title, icon, caption, options = [], value = [], onChange } = props

  return (
    <ItemLayout title={title} icon={icon} caption={caption} className='abi-form-schema-item-checkbox' layout='column'>
      <div>
        {options.map(item => (
          <Checkbox
            key={item.value}
            checked={value?.includes(item.value)}
            onChange={() => {
              if (value.includes(item.value)) {
                onChange(value.filter(i => i !== item.value))
              } else {
                onChange([...value, item.value])
              }
            }}
          >
            {item.label}
          </Checkbox>
        ))}
      </div>
    </ItemLayout>
  )
}
