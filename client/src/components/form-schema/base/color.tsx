import './color.less'

import React from 'react'

import ColorSelect from '../../color-picker/color-select'
import ItemLayout from '../components/item-layout'
import type { FormSchemaBase } from '../type'

export interface ColorViewProps extends FormSchemaBase {
  value?: string
  mode?: 'rgb' | 'hex' | 'rgba'
  onChange: (value?: string) => any
}

/**
 * 样式选择组件
 * @param props
 */
export default function ColorView(props: ColorViewProps) {
  const { title, caption, icon, mode = 'rgba', value, onChange } = props

  return (
    <ItemLayout title={title} icon={icon} caption={caption} className='abi-form-schema-abi-color-select'>
      <ColorSelect value={value} onChange={onChange} mode={mode} />
    </ItemLayout>
  )
}

ColorView.defaultValue = 'transparent'
