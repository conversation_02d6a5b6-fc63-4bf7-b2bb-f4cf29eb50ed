import './date.less'

import dayjs from 'dayjs'
import _ from 'lodash'
import React, { useEffect, useState } from 'react'

import { DatePicker } from '@/components/customs/date-picker'

import ItemLayout from '../components/item-layout'
import { FormSchemaBase } from '../type'

export interface DateViewProps extends FormSchemaBase {
  value?: string | (string | undefined)[]
  onChange: (value: DateViewProps['value']) => any
  mode?: 'default' | 'range'
  placeholder?: string | [string, string]
  // 格式化
  format?: string
  // 是否显示 time
  showTime?: boolean
  allowClear?: boolean
}

/**
 * 时间日期选择
 * @param props
 */
export default function DateView(props: DateViewProps) {
  const { title, icon, caption, value, onChange } = props
  const { placeholder, showTime, format = 'YYYY-MM-DD', mode, allowClear } = props
  const [innerValue, setInnerValue] = useState<any>(null)

  // 检查是否包含 时间表达式
  const checkTimeExpress = val => {
    if (_.isArray(val)) return val.some(i => !dayjs(i).isValid())
    return !dayjs(val).isValid()
  }

  // 尝试解析 value 是否是时间表达式
  const getTimeValue = val => {
    val = val?.toString() || ''
    const time = dayjs(val)
    if (time.isValid()) return dayjs(val.toString())

    return dayjs(_.template(val, { interpolate: /{{([\s\S]+?)}}/g })({ dayjs }))
  }

  useEffect(() => {
    if (!value) return setInnerValue(null)
    let newValue: dayjs.Dayjs | dayjs.Dayjs[]

    if (mode === 'range' && _.isArray(value)) {
      newValue = value.map(v => getTimeValue(v))
      setInnerValue(newValue)
    } else {
      newValue = getTimeValue(value)
      setInnerValue(newValue)
    }

    // 重新触发一个 change
    requestAnimationFrame(() => {
      if (checkTimeExpress(value)) {
        if (mode === 'range' && _.isArray(newValue)) {
          onChange(newValue.map(i => i.format(format)))
        } else if (!_.isArray(newValue)) {
          onChange(newValue.format(format))
        }
      }
    })
  }, [value, format, mode])

  return (
    <ItemLayout
      title={title}
      icon={icon}
      caption={caption}
      layout={_.isArray(innerValue) ? 'column' : 'row'}
      className='abi-form-schema-date-view'
    >
      {_.isArray(innerValue) ? (
        <DatePicker.RangePicker
          placeholder={_.isArray(placeholder) ? placeholder : ['请选择开始时间', '请选择结束时间']}
          className='form-input'
          showTime={showTime}
          format={format}
          allowClear={allowClear}
          value={innerValue as any}
          onChange={val => {
            setInnerValue(val)
            onChange(val?.map(i => i?.format(format)))
          }}
          showNow
          showHour
          showMinute
          showSecond
        />
      ) : (
        <DatePicker
          placeholder={_.isArray(placeholder) ? placeholder[0] : placeholder || '请选择时间'}
          className='form-input'
          showTime={showTime}
          allowClear={allowClear}
          format={format}
          value={innerValue as any}
          onChange={(val, dateString) => {
            setInnerValue(val)
            onChange(val?.format(format) || dateString)
          }}
          showNow
          showHour
          showMinute
          showSecond
          showToday
        />
      )}
    </ItemLayout>
  )
}
