import { InputNumber } from 'antd'
import React from 'react'

import ItemLayout from '../components/item-layout'
import type { FormSchemaBase } from '../type'

export interface NumberViewProps extends FormSchemaBase {
  max?: number
  min?: number
  step?: number
  value?: number
  prefix?: string
  suffix?: string
  onChange: (val: number) => any
}

/**
 * 数组输入
 * @param props
 */
export default function NumberView(props: NumberViewProps) {
  const { title, max, min, step, icon, prefix, suffix, caption, value, onChange } = props
  // const [innerValue, setInnerValue] = useState(value)

  // useEffect(() => {
  //   if (innerValue !== value) setInnerValue(value)
  // }, [value])

  // const onEnter = e => onChange(innerValue as number)

  return (
    <ItemLayout title={title} icon={icon} caption={caption}>
      <InputNumber
        placeholder='请输入'
        className='form-input'
        value={value}
        onChange={val => onChange(val || 0)}
        max={max}
        min={min}
        step={step}
        addonBefore={prefix}
        addonAfter={suffix}
        // onKeyPress={e => e.code === 'Enter' && onEnter(e)}
        // onBlur={onEnter}
      />
    </ItemLayout>
  )
}
