import './percent.less'

import { useReactive } from 'ahooks'
import { InputNumber, Radio,Slider } from 'antd'
import _ from 'lodash'
import React, { useEffect } from 'react'

import ItemLayout from '../components/item-layout'
import { FormSchemaBase } from '../type'

export interface PercentViewProps extends FormSchemaBase {
  layout?: 'row' | 'column'
  value?: string | number
  onChange: (value: PercentViewProps['value']) => any
  ignore?: ('absolute' | 'percent')[]
}

/**
 * 百分比或绝对值组件
 * @param props
 */
export default function PercentView(props: PercentViewProps) {
  const { title, caption, icon, layout, value, onChange, ignore } = props
  const state = useReactive({
    value: undefined as number | undefined,
    type: 'absolute' as 'absolute' | 'percent' | (string & {})
  })

  const options = [
    { value: 'absolute', label: '绝对值' },
    { value: 'percent', label: '百分比' }
  ]

  const onEnter = _val => {
    const val = Number.parseInt(_val, 10)
    if (_.isNaN(val)) return onChange(undefined)
    if (state.type === 'percent') return onChange(`${val}%`)
    onChange(val)
  }

  const onTypeChange = e => {
    state.type = e.target.value
    if (state.type === 'percent') {
      onChange(`${state.value}%`)
    } else {
      onChange(state.value)
    }
  }

  useEffect(() => {
    if (_.isString(value) && value.indexOf('%') > -1) {
      state.type = 'percent'
    } else {
      state.type = 'absolute'
    }
    if (_.isNil(value)) state.value = undefined
    if (_.isString(value)) state.value = Number.parseFloat(value)
    if (_.isNumber(value)) state.value = value
  }, [value])

  return (
    <ItemLayout title={title} caption={caption} icon={icon} layout={layout} className='abi-form-schema-percent-view'>
      <div className='abi-form-schema-percent-view-content'>
        {/* <div className='left-panel'>
          {options.map(item => (
            <Button
              key={item.value}
              size='small'
              onClick={() => onTypeChange(item.value)}
              className={cn({ active: state.type === item.value })}
            >
              {item.label}
            </Button>
          ))}
        </div> */}
        <Radio.Group options={options} optionType='button' size='small' value={state.type} onChange={onTypeChange} />
        <div className='bottom-panel'>
          {state.type === 'absolute' && (
            <InputNumber placeholder='请输入' addonAfter='px' value={state.value} onChange={onEnter} />
          )}
          {state.type === 'percent' && (
            <>
              <Slider
                value={state.value}
                onChange={v => {
                  state.value = v
                }}
                min={0}
                max={100}
                onAfterChange={onEnter}
              />
              <span>{state.value || 0}%</span>
            </>
          )}
        </div>
      </div>
    </ItemLayout>
  )
}
