import './radio.less'

import { Radio, Tooltip } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React, { useMemo } from 'react'

import ItemLayout from '../components/item-layout'
// import Icon from '@/components/icons/iconfont-icon'
import type { FormSchemaBase } from '../type'

export interface RadioViewProps extends FormSchemaBase {
  value?: string
  algin?: 'left' | 'center' | 'right'
  layout?: 'row' | 'column'
  options?: {
    value: string
    label: string
    icon?: string
    [key: string]: any
  }[]
  onChange: (value: string, item: any) => any
}

/**
 * 单选
 * @param props
 */
export default function RadioView(props: RadioViewProps) {
  const { title, value, options, icon, caption, onChange } = props
  const { algin, layout = 'column' } = props
  const _algin = layout === 'row' ? 'right' : 'left' || algin
  const mode = options?.some(i => i.icon) ? 'icon' : 'default'

  const dict = useMemo(() => _.keyBy(options, 'value'), [options])

  const renderItem = item => (
    <div
      key={item.value}
      onClick={() => onChange(item.value, dict[item.value])}
      className={cn({
        'radio-list-item': true,
        active: item.value === value
      })}
    >
      {/* {item.icon && <Icon name={item.icon} />} */}
      {item.label && <span className='tit'>{item.label}</span>}
    </div>
  )

  return (
    <ItemLayout title={title} icon={icon} caption={caption} layout={layout} className='abi-form-schema-radio-view'>
      {mode === 'icon' && (
        <div className='abi-form-schema-radio-view-content'>
          {options?.map(item => {
            if (!item.label) return renderItem(item)
            return <Tooltip title={item.label}>{renderItem(item)}</Tooltip>
          })}
        </div>
      )}

      {mode === 'default' && (
        <Radio.Group
          value={value}
          options={_.toArray(options)}
          className='form-input'
          style={{ textAlign: _algin }}
          onChange={e => {
            const val = e.target.value
            onChange(val, dict[val])
          }}
        />
      )}
    </ItemLayout>
  )
}
