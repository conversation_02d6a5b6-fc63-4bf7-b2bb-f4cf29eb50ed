import { Select } from 'antd'
import _ from 'lodash'
import React, { useMemo } from 'react'

import ItemLayout from '../components/item-layout'
import type { FormSchemaBase } from '../type'

export interface SelectViewProps extends FormSchemaBase {
  // filterable?: boolean // 根据 label，大于 10 个时自动开启
  defaultActiveFirst?: boolean // 默认为 false
  options?: {
    label: string
    value: string
    [key: string]: any
  }[]
  mode?: 'multiple' | undefined
  value?: string
  onChange: (value: any | any[], item: any) => any
  allowClear?: boolean
  /** 是否绑定维度数据，是的话，就抛弃 options，获取组件的维度数据 */
  bindDimensionData?: number | false
}

/**
 * 选择框
 * @param props
 */
export default function SelectView(props: SelectViewProps) {
  const { title, icon, value, caption, allowClear, onChange, mode } = props
  const { bindDimensionData, getComponentData } = props

  const options = useMemo(() => {
    if (_.isNumber(bindDimensionData) && getComponentData) {
      const { data, fieldMap } = getComponentData() || {}

      const fields: any[] = []
      _.forEach(fieldMap, (v, k) => {
        if (v.dataType === 'date' || v.dataType === 'string' || v.type === 'date' || v.type === 'string') {
          fields.push({ ...v, key: k })
        }
      })
      const field = fields[bindDimensionData]?.key
      if (data && field) {
        return _.uniqBy(data.map(i => ({ label: i[field], value: i[field] })), 'value')
      }
      return []
    }
    return props.options || []
  }, [props.options, bindDimensionData, getComponentData])

  const dict = useMemo(() => _.keyBy(options, 'value'), [options])
  const filterable = options?.length > 10

  const filterOption = (keyword, option) => {
    const tit = option?.label?.toLocaleLowerCase()
    if (!tit) return false
    return tit.indexOf(keyword?.toLocaleLowerCase()) > -1
  }

  const _value = useMemo(() => {
    if (mode === 'multiple' && !Array.isArray(value) && value !== undefined) return [value]
    return value
  }, [value, mode])

  const _onChange = (val: any | any[]) => {
    if (mode === 'multiple' && Array.isArray(val)) {
      onChange(val, val.map(i => dict[i]))
    } else {
      onChange(val, dict[val])
    }
  }

  return (
    <ItemLayout title={title} icon={icon} caption={caption}>
      <Select
        mode={mode}
        placeholder='请选择'
        options={_.toArray(options)}
        showSearch={filterable}
        filterOption={filterOption}
        className='form-input'
        value={_value}
        onChange={_onChange}
        allowClear={allowClear}
      />
    </ItemLayout>
  )
}
