import './slider.less'

import { Slider } from 'antd'
import _ from 'lodash'
import React, { useEffect, useState } from 'react'

import ItemLayout from '../components/item-layout'
import { FormSchemaBase } from '../type'

export interface SliderViewProps extends FormSchemaBase {
  value?: number | [number, number]
  onChange: (value: number | number[]) => any
  layout?: 'row' | 'column'
  max?: number
  min?: number
  step?: number // 默认是 1
  prefix?: string
  suffix?: string
  showValue?: boolean
}

/**
 * 滑块
 * @param props
 */
export default function SliderView(props: SliderViewProps) {
  const { value, title, caption, icon, onChange, step = 1, layout = 'column', defaultValue } = props
  const { prefix = '', suffix = '', showValue = true, max = 100, min = 0 } = props

  const [innerValue, setInnerValue] = useState<number | [number, number] | undefined>(0)

  const precision = (() => {
    if (step >= 1) return 0
    if (step >= 0.1) return 1
    if (step >= 0.01) return 2
    return 0
  })()

  useEffect(() => {
    const _val = value === undefined ? defaultValue : value
    setInnerValue(_val)
  }, [value, defaultValue])

  const renderValue = val => {
    if (!_.isArray(val)) return _.round(val || 0, precision)
    if (_.isArray(val)) return val.map(i => _.round(i || 0, precision)).join('-')
    return ''
  }

  return (
    <ItemLayout title={title} caption={caption} icon={icon} layout={layout} className='abi-form-schema-slider-view'>
      <div className='abi-form-schema-slider-view-content' style={layout === 'row' ? { width: '60%' } : undefined}>
        <Slider
          value={innerValue as any}
          onChange={v => setInnerValue(v)}
          onAfterChange={v => onChange(v)}
          step={step}
          className='slider'
          min={min}
          max={max}
          range={_.isArray(innerValue)}
        />
        <span className='val'>
          {_.template(prefix, { interpolate: /{{([\s\S]+?)}}/g })({ value: innerValue, _ })}
          {showValue && renderValue(innerValue)}
          {_.template(suffix, { interpolate: /{{([\s\S]+?)}}/g })({ value: innerValue, _ })}
        </span>
      </div>
    </ItemLayout>
  )
}
