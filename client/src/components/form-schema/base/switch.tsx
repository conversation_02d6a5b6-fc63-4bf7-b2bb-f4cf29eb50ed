import { Switch } from 'antd'
import _ from 'lodash'
import React from 'react'

import ItemLayout from '../components/item-layout'
import type { FormSchemaBase } from '../type'

export interface SwitchViewProps extends FormSchemaBase {
  openText?: string
  closeText?: string
  value?: boolean | string
  onChange: (value: boolean | string) => any
  valueMap?: {
    [key: string]: boolean
  }
}

/**
 * 开关
 * @param props
 */
export default function SwitchView(props: SwitchViewProps) {
  const { title, icon, caption, openText = '开启', closeText = '关闭', valueMap } = props
  const { value, onChange, defaultValue } = props
  const mode = _.isEmpty(valueMap) ? 'default' : 'valueMap'

  // 反转对象
  const mapValue = _.invert(valueMap || {})
  const _value = mode === 'default' ? value : valueMap?.[value as string] || false
  const _onChange = val => {
    if (mode === 'valueMap') onChange(mapValue[val] as string)
    else onChange(val as string)
  }

  return (
    <ItemLayout title={title} icon={icon} caption={caption}>
      <Switch
        checkedChildren={openText}
        unCheckedChildren={closeText}
        defaultChecked={defaultValue}
        checked={_value as boolean}
        onChange={_onChange}
      />
    </ItemLayout>
  )
}
