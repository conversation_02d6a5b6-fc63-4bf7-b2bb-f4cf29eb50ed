import { Input } from 'antd'
import React from 'react'

import DebounceInput from '../components/debounce-input'
import ItemLayout from '../components/item-layout'
import type { FormSchemaBase } from '../type'

export interface TextAreaViewProps extends FormSchemaBase {
  value?: string
  maxLength?: number
  onChange: (val: string) => any
  allowClear?: boolean
}

/**
 * 多行文本
 * @param props
 */
export default function TextAreaView(props: TextAreaViewProps) {
  const { title, value, icon, caption, maxLength, allowClear, onChange } = props
  // const [innerValue, setInnerValue] = useState(value)

  // useEffect(() => {
  //   if (innerValue !== value) setInnerValue(value)
  // }, [value])

  // const onEnter = val => onChange(val || '')

  return (
    <ItemLayout title={title} icon={icon} caption={caption} layout='column'>
      <DebounceInput
        Component={Input.TextArea}
        value={value}
        onChange={e => {
          const val = e?.target?.value
          onChange(val)
          // setInnerValue(e.target.value)
          // if (!e.target.value) onEnter('') // 清空
        }}
        maxLength={maxLength}
        showCount
        allowClear={allowClear}
        placeholder='请输入'
        // onBlur={() => onEnter(innerValue)}
        autoSize={{ minRows: 4, maxRows: 16 }}
      />
    </ItemLayout>
  )
}
