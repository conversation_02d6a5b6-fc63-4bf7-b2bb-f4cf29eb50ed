import './text.less'

import { Input } from 'antd'
import React from 'react'

import DebounceInput from '../components/debounce-input'
import ItemLayout from '../components/item-layout'
import type { FormSchemaBase } from '../type'

export interface TextViewProps extends FormSchemaBase {
  prefix?: string
  suffix?: string
  value?: string
  maxLength?: number
  onChange: (val: string) => any
  allowClear?: boolean
}

/**
 * 文本输入
 * @param props
 */
export default function TextView(props: TextViewProps) {
  const { prefix, suffix, title, icon, caption, maxLength, allowClear, value, onChange } = props
  // const [innerValue, setInnerValue] = useState(value)

  // useEffect(() => {
  //   if (innerValue !== value && !_.isObject(value)) setInnerValue(value)
  // }, [value, title])

  // const onEnter = e => onChange(innerValue || '')

  return (
    <ItemLayout title={title} icon={icon} caption={caption} className='abi-form-schema-text'>
      <DebounceInput
        Component={Input}
        prefix={prefix}
        suffix={suffix}
        maxLength={maxLength}
        placeholder='请输入'
        className='form-input'
        value={value}
        onChange={val => onChange(val)}
        allowClear={allowClear}
        mode='enter'
        // onKeyPress={e => e.code === 'Enter' && onEnter(e)}
        // onBlur={onEnter}
      />
      {maxLength && maxLength > 0 && (
        <span className='count'>
          {value?.length}/{maxLength}
        </span>
      )}
    </ItemLayout>
  )
}
