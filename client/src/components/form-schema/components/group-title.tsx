import './group-title.less'

import { EyeInvisibleOutlined, EyeOutlined, RightOutlined } from '@ant-design/icons'
import { Tooltip } from 'antd'
import React from 'react'

// import Icon from '@/components/icons/iconfont-icon'
import Title from './title'

export interface GroupTitleProps {
  icon?: any
  title?: string
  unfold?: boolean
  caption?: string
  onUnfoldChange?: (val: boolean) => any
  extra?: any
  onClick?: (e: any) => any
  show?: boolean
  showTooltip?: string
  onShowChange?: (show: boolean) => any
}

/**
 * 用于组的 title
 * @param props
 */
export default function GroupTitle(props: GroupTitleProps) {
  const { icon, title, caption, unfold, onUnfoldChange, extra, onClick } = props
  const { show, onShowChange, showTooltip } = props
  const hasShow = !!onShowChange

  const renderExtra = () => (
    <div onClick={e => e.stopPropagation()}>
      {extra}
    </div>
  )

  return (
    <header
      className='abi-form-schema-group-view-header'
      onClick={e => {
        e.stopPropagation()
        onUnfoldChange?.(!unfold)
      }}
    >
      <Title title={title} caption={caption} onClick={onClick} />
      <div className='flex-1' />

      {hasShow && (
        <span onClick={e => e.stopPropagation()}>
          {show ?
            showTooltip ?
              <Tooltip title={showTooltip} placement='left'>
                <EyeOutlined onClick={() => onShowChange(false)} />
              </Tooltip> :
              <EyeOutlined onClick={() => onShowChange(false)} />
            :
            showTooltip ?
              <Tooltip title={showTooltip} placement='left'>
                <EyeInvisibleOutlined onClick={() => onShowChange(true)} />
              </Tooltip> :
              <EyeInvisibleOutlined onClick={() => onShowChange(true)} />
          }
        </span>
      )}
      {extra && renderExtra()}
      <RightOutlined className='ml-2' rotate={unfold ? 90 : 0} />
    </header>
  )
}
