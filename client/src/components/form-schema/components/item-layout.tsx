import React from 'react'

import Title from './title'

export interface FormItemLayoutProps {
  title?: string
  icon?: string
  caption?: string
  className?: string
  layout?: 'row' | 'column'
  children: any
  extra?: any
  onTitleClick?: (e: any) => any
}

/**
 * 表单的布局
 * 支持 row，column
 * @param props
 */
export default function FormItemLayout(props: FormItemLayoutProps) {
  const { children, className = '', layout = 'row' } = props
  const { title, extra, caption, icon, onTitleClick } = props

  return (
    <div className={`abi-form-schema-${layout} ${className}`}>
      <Title title={title} icon={icon} caption={caption} extra={extra} onClick={onTitleClick} />
      {children}
    </div>
  )
}
