import { useMemoizedFn } from 'ahooks'
import { Alert } from 'antd'
import React from 'react'

import type { InjectComponentMap } from '../type'
import { getInjectValue, schemaValueChange } from '../utils'

export interface ItemRendererProps {
  Component: any
  schema: any
  value?: any
  onChange: (val: any) => any
  injectComponentMap?: InjectComponentMap
  getComponentData?: any
}

/**
 * 表单组件渲染器
 * @param props
 */
export default function ItemRenderer(props: ItemRendererProps) {
  const { Component, schema, value, injectComponentMap, onChange, getComponentData } = props

  const innerOnChange = useMemoizedFn(schemaValueChange(value, schema, onChange))
  const innerValue = getInjectValue(value, schema)

  try {
    return (
      <Alert.ErrorBoundary>
        <Component
          {...schema}
          getComponentData={getComponentData}
          value={innerValue}
          schema={schema}
          onChange={innerOnChange}
          injectComponentMap={injectComponentMap}
        />
      </Alert.ErrorBoundary>
    )
  } catch (err) {
    console.error('form-schema render item error:', err)
    return null
  }
}
