import './title.less'

import { QuestionCircleOutlined } from '@ant-design/icons'
import { Popover } from 'antd'
import cn from 'classnames'
import React from 'react'

// import MarkDown from '@/components/markdown-view'
// import Icon from '@/components/icons/iconfont-icon'
import { useContextState } from '../utils/context'

export interface TitleProps {
  icon?: string
  title?: string
  caption?: string
  className?: string
  extra?: any
  onClick?: (e: any) => any
}

/**
 * 表单的标题组件
 */
export default function Title(props: TitleProps) {
  const { title, icon, caption, className = '', extra, onClick } = props
  const _className = cn('abi-form-schema-item-title', className)

  const { Icon = () => null } = useContextState()

  const renderExtra = () => (
    <>
      <div className='flex-1' />
      {extra}
    </>
  )

  if (!title) return null

  if (caption)
    return (
      <span className={_className} onClick={onClick}>
        {icon && <Icon name={icon} />}
        <span>{title}</span>
        {caption && (
          <Popover
            placement='bottom'
            trigger={['click']}
            content={<div>{caption}</div>}
            overlayClassName='abi-form-schema-caption-overlay'
          >
            <QuestionCircleOutlined onClick={e => e.stopPropagation()} />
          </Popover>
        )}
        {extra && renderExtra()}
      </span>
    )

  return (
    <span className={_className} onClick={onClick}>
      {icon && <Icon name={icon} />}
      {title}
      {extra && renderExtra()}
    </span>
  )
}
