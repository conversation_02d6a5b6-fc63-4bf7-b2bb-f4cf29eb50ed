import isEqual from 'fast-deep-equal'
import { memo } from 'react'

import NotifyBtnView from '@/components/form-schema/notify/notify-btn'

import AntvCustomAxis from './antv-custom/antv-custom-axis'
// import { DEFAULT_THEME } from './echart/echart-theme/theme-const'
import AntvCustomLegend from './antv-custom/antv-custom-legend'
import Checkbox from './base/checkbox'
import Color from './base/color'
import Date from './base/date'
import Number from './base/number'
import Percent from './base/percent'
import Radio from './base/radio'
import Select from './base/select'
import Slider from './base/slider'
import Switch from './base/switch'
import Text from './base/text'
import TextArea from './base/text-area'
// ...
import EchartAnimation from './echart/echart-animation'
import EchartAxis from './echart/echart-axis'
import EchartBaseConfig from './echart/echart-base-config'
import EchartGrid from './echart/echart-grid'
import EchartLegend from './echart/echart-legend'
import EchartTheme from './echart/echart-theme'
import EchartTitle from './echart/echart-title'
import EchartTooltip from './echart/echart-tooltip'
import EchartCustomAxis from './echart-custom/echart-custom-axis'
import EchartCustomBeautify from './echart-custom/echart-custom-beautify'
import EchartCustomLabel from './echart-custom/echart-custom-label'
import EchartCustomLegend from './echart-custom/echart-custom-legend'
import EchartCustomText from './echart-custom/echart-custom-text'
import EchartCustomTheme from './echart-custom/echart-custom-theme'
// ...
import Background from './font/background'
import BackgroundImage from './font/background-image'
import Beautify from './font/beautify'
import Border from './font/border'
import Distance from './font/distance'
import Font from './font/font'
import Margin from './font/margin'
import Shadow from './font/shadow'
import Group from './group/group'
// import Menu from './group/menu'
import List from './group/list'
import Tabs from './group/tabs'



// 请到后面提供默认值配置
export const FORM_MAP = {
  // 基础表单
  number: memo(Number, isEqual),
  text: memo(Text, isEqual),
  'text-area': memo(TextArea, isEqual),
  select: memo(Select, isEqual),
  switch: memo(Switch, isEqual),
  radio: memo(Radio, isEqual),
  color: memo(Color, isEqual),
  checkbox: memo(Checkbox, isEqual),
  slider: memo(Slider, isEqual),
  date: memo(Date, isEqual),
  percent: memo(Percent, isEqual),
  // 容器组
  group: memo(Group, isEqual),
  tabs: memo(Tabs, isEqual),
  list: memo(List, isEqual),
  object: () => null,
  // 'menu': memo(Menu, isEqual),
  // 样式套件
  font: memo(Font, isEqual),
  border: memo(Border, isEqual),
  margin: memo(Margin, isEqual),
  padding: memo(Margin, isEqual),
  background: memo(Background, isEqual),
  'background-image': memo(BackgroundImage, isEqual),
  distance: memo(Distance, isEqual),
  shadow: memo(Shadow, isEqual),
  beautify: memo(Beautify, isEqual),

  // 图表相关
  'echart-title': memo(EchartTitle, isEqual),
  'echart-titles': memo(EchartTitle, isEqual), // 复数标题
  'echart-xAxis': memo(EchartAxis, isEqual),
  'echart-xAxes': memo(EchartAxis, isEqual), // 复数对称轴
  'echart-yAxis': memo(EchartAxis, isEqual),
  'echart-yAxes': memo(EchartAxis, isEqual), // 复数对称轴
  'echart-legend': memo(EchartLegend, isEqual),
  'echart-tooltip': memo(EchartTooltip, isEqual),
  'echart-animation': memo(EchartAnimation, isEqual),
  'echart-theme': memo(EchartTheme, isEqual),
  'echart-grid': memo(EchartGrid, isEqual),
  // echart 的定制化基础配置套件
  'echart-base-config': memo(EchartBaseConfig, isEqual),
  'echart-custom-theme': memo(EchartCustomTheme, isEqual),
  'echart-custom-axis': memo(EchartCustomAxis, isEqual),
  'echart-custom-legend': memo(EchartCustomLegend, isEqual),
  'echart-custom-text': memo(EchartCustomText, isEqual),
  'echart-custom-label': memo(EchartCustomLabel, isEqual),
  'echart-custom-beautify': memo(EchartCustomBeautify, isEqual),

  'antv-custom-legend': memo(AntvCustomLegend, isEqual),
  'antv-custom-axis': memo(AntvCustomAxis, isEqual),

  // 通过 pubsubjs 通知画布上组件，一般用于弹出编辑对话框
  'notify-btn': memo(NotifyBtnView, isEqual)
}

export type FormKey = keyof typeof FORM_MAP

export const FORM_SET = new Set(Object.keys(FORM_MAP))

// 这些为组类型
export const IS_GROUP = new Set(['tabs', 'group', 'menu'])
// 嵌套列表的类型
export const IS_NESTED_LIST = new Set(['echart-xAxes', 'echart-yAxes', 'echart-titles'])

export const ECHART_TEXT_DEFAULT = {
  fontSize: 16,
  fontFamily: 'Microsoft Yahei',
  fontWeight: 'normal',
  fontStyle: 'normal',
  color: '#222',
  align: 'left'
}
export const FONT_DEFAULT = {
  fontSize: 16,
  fontFamily: 'Microsoft Yahei',
  fontWeight: 'normal',
  fontStyle: 'normal',
  color: '#222',
  textAlign: 'left',
  letterSpacing: 0,
  colorGradient: ['rgba(0,0,0,1)', 'rgba(0,0,0,1)'],
  direction: 'to right'
}

export const FONT_SIZE = [
  { label: '10', value: 10 },
  { label: '11', value: 11 },
  { label: '12', value: 12 },
  { label: '14', value: 14 },
  { label: '15', value: 15 },
  { label: '16', value: 16 },
  { label: '18', value: 18 },
  { label: '20', value: 20 },
  { label: '24', value: 24 },
  { label: '28', value: 28 },
  { label: '32', value: 32 },
  { label: '36', value: 36 }
]

// 文本套件，必须描述这个
export const FONT_FIELD = [
  'fontSize',
  'fontFamily',
  'lineHeight',
  'fontWeight',
  'fontStyle',
  'color',
  'colorGradient',
  'direction',
  'textAlign',
  'letterSpacing',
  'textDecoration'
]

// 边框的字段
export const BORDER_FIELD = [
  'borderStyle',
  'borderWidth',
  'borderColor',

  'borderLeftStyle',
  'borderLeftWidth',
  'borderLeftColor',

  'borderRightStyle',
  'borderRightWidth',
  'borderRightColor',

  'borderTopStyle',
  'borderTopWidth',
  'borderTopColor',

  'borderBottomStyle',
  'borderBottomWidth',
  'borderBottomColor'
]
// 边距的字段
export const MARGIN_FIELD = [
  'padding',
  'paddingLeft',
  'paddingRight',
  'paddingTop',
  'paddingBottom',

  'margin',
  'marginLeft',
  'marginRight',
  'marginTop',
  'marginBottom'
]

// 背景
export const BACKGROUND_FIELD = ['backgroundColor', 'backgroundImage']

// 距离
const DISTANCE_FIELD = ['left', 'right', 'top', 'bottom']

export const IMAGE_FIELD = [
  'backgroundImage',
  'backgroundRepeat',
  'backgroundSize',
  'backgroundPosition',
  'backgroundAttachment'
]

export const SHADOW_FIELE = ['boxShadow', 'textShadow']

// 必须设置了字段才会更新
export const BEAUTIFY_FIELE = [
  'opacity',
  'backgroundColor',
  'backgroundImage',
  'borderColor',
  'boxShadow',
  'borderStyle',
  'boxShadow',
  ...BORDER_FIELD
]

// 样式套件，这些支持零度赋值
export const FONT_COM_MAP = {
  font: FONT_FIELD,
  border: BORDER_FIELD,
  margin: MARGIN_FIELD,
  padding: MARGIN_FIELD,
  background: BACKGROUND_FIELD,
  beautify: BEAUTIFY_FIELE,
  distance: DISTANCE_FIELD,
  'background-image': IMAGE_FIELD,
  shadow: SHADOW_FIELE
}

export const FONT_COM_KEYS = new Set(Object.keys(FONT_COM_MAP))

/**
 * 字体
 */
export const FONT_FAMILY = [
  { label: '宋体', value: '宋体' },
  { label: '微软雅黑', value: 'Microsoft Yahei' },
  { label: '黑体', value: '黑体' },
  { label: '仿宋', value: '仿宋' },
  { label: '楷体', value: '楷体' },
  { label: '圆体', value: '圆体' },
  { label: 'Courier New', value: 'Courier New' },
  { label: 'Courier', value: 'Courier' },
  { label: 'Segoe UI', value: 'Segoe UI' },
  { label: 'monospace', value: 'monospace' },
  { label: 'sans-serif', value: 'sans-serif' }
]

/**
 * 粗体
 */
export const FONT_WEIGHT = [
  { label: '细体', value: 'lighter' },
  { label: '默认', value: 'normal' },
  { label: '粗体', value: 'bold' },
  { label: '超粗', value: 'bolder' }
]

/**
 * 字体样式
 */
export const FONT_STYLE = [
  { label: '默认', value: 'normal' },
  { label: '斜体', value: 'italic' },
  { label: '倾斜', value: 'oblique' }
]

/**
 * 方向
 */
export const DIRECT_MAP = {
  left: '左',
  right: '右',
  bottom: '下',
  top: '上'
}

/**
 * 渐变方向
 */
export const GRADIENT_DIRECTION = [
  { value: 'to top', label: '沿上', rotate: -90 },
  { value: 'to top left', label: '沿左上', rotate: -135 },
  { value: 'to top right', label: '沿右上', rotate: -45 },
  { value: 'to left', label: '沿左', rotate: -180 },
  { value: 'to right', label: '沿右', rotate: 0 },
  { value: 'to bottom', label: '沿下', rotate: 90 },
  { value: 'to bottom left', label: '沿左下', rotate: 135 },
  { value: 'to bottom right', label: '沿右下', rotate: 45 }
]

export const BORDER_STYLE = [
  { label: '直线', value: 'solid' },
  { label: '点线', value: 'dotted' },
  { label: '虚线', value: 'dashed' },
  { label: '双线', value: 'double' },
  { label: '凹槽', value: 'groove' }
]

export const LABEL_DIRECT = [
  { label: '上', value: 'top' },
  { label: '下', value: 'bottom' },
  { label: '左', value: 'left' },
  { label: '右', value: 'right' },
  { label: '内', value: 'inside' }
]
