import './index.less'

import React, { useState } from 'react'

import { FormSchema, getSchemaDefaultValue } from '../../index'
import { getDemoSchema } from '../utils/mock'

const SCHAME = getDemoSchema()

const injectComponentMap = {
  Icon: () => <div />,
  UploadImage: () => <div />,
  Markdown: () => <div />
}

export default function Demo(props: any) {
  const [state, setState] = useState({
    schema: props.schema || SCHAME,
    value: getSchemaDefaultValue(props.schema || SCHAME)
  })
  const update = newState => setState({ ...state, ...newState })

  return (
    <div className='abi-form-schame-demo'>
      <pre>{JSON.stringify(state.schema, null, 2)}</pre>
      <FormSchema
        schema={state.schema}
        value={state.value}
        className='abi-form-schame-demo-schema'
        onChange={val => update({ value: val })}
        injectComponentMap={injectComponentMap}
      />
      <pre>{JSON.stringify(state.value, null, 2)}</pre>
    </div>
  )
}
