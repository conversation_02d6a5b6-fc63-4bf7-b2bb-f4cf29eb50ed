import React from 'react'

import type { FormSchemaBase } from '../type'
import ItemLayout from './echart-custom-group'
import _ from 'lodash'

export interface EchartCustomAxisProps extends FormSchemaBase {
  value?: {
  }
  onChange: (value: EchartCustomAxisProps['value']) => any
}


/**
 * echart 的 axis 封装
 * 是一个自定义的 schema 组合
 * @param props
 * @see https://echarts.apache.org/zh/option.html#xAxis
 */
export default function EchartCustomAreaSetting(props: EchartCustomAxisProps) {
  const { value = {} } = props

  return (
    <div>xxx</div>
  )
}
