// @import '../../styles/variable.less';

.abi-form-schema-echart-custom-axis {
  margin-right: -10px;

  .ant-tabs-nav-list {
    .ant-tabs-tab:first-of-type {
      margin-left: 4px !important;
    }
  }

  .view-group {
    padding-top: 6px;

    .view-group-box {
      display: flex;
      align-items: center;
      user-select: none;

      .style-color {
        margin: 0 8px;
        width: 25px;
        height: 25px;

        & + span {
          cursor: pointer;
          color: #888;

          &:hover {
            color: @primary-color;
          }
        }
      }
    }
  }

  .mb-2 {
    margin-bottom: 8px;
  }
}

.abi-form-schema-echart-custom-axis-grid-overlay {
  .ant-popover-inner-content {
    padding: 12px;
    border-radius: 3px;
    overflow: hidden;
  }
  .grid-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    > span:first-of-type {
      width: 75px;
    }
  }
}
