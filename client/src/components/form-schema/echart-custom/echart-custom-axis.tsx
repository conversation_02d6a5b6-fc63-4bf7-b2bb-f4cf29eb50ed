import './echart-custom-axis.less'

import { RedoOutlined } from '@ant-design/icons'
import { Col, InputNumber, Select, Switch } from 'antd'
import { produce } from 'immer'
import _ from 'lodash'
import React, { CSSProperties } from 'react'

import ColorSelect from '../../color-picker/color-select'
import DebounceInput from '../components/debounce-input'
import FontView from '../font/font'
import type { FormSchemaBase } from '../type'
import ItemLayout from './echart-custom-group'


type Axis = {
  show: boolean
  name: string
  axisLabel?: {
    rotate?: number
    [key: string]: any
  }
  nameTextStyle?: CSSProperties
  // 坐标轴线
  axisLine?: {
    show?: boolean
    lineStyle?: CSSProperties
  }
  // 网格线
  splitLine?: {
    show?: boolean
    lineStyle?: CSSProperties
  }
  // 是否是反向坐标轴
  inverse?: boolean
}

export interface EchartCustomAxisProps extends FormSchemaBase {
  value?: {
    xAxis: Axis | Axis[]
    yAxis: Axis | Axis[]
  }
  onChange: (value: EchartCustomAxisProps['value']) => any
}

/**
 * echart 的 axis 封装
 * 是一个自定义的 schema 组合
 * @param props
 * @see https://echarts.apache.org/zh/option.html#xAxis
 */
export default function EchartCustomAxis(props: EchartCustomAxisProps) {
  const { value, onChange, title, icon, caption } = props

  const xAxis = _.isArray(value?.xAxis) ? value?.xAxis[0] : value?.xAxis
  const yAxis = _.isArray(value?.yAxis) ? value?.yAxis[0] : value?.yAxis

  const tabs = [
    { key: 'x' as const, tab: 'x 轴', value: xAxis },
    { key: 'y' as const, tab: 'y 轴', value: yAxis }
  ]

  const lineOpts = [
    { label: '实线', value: 'solid' },
    { label: '虚线', value: 'dashed' },
    { label: '点线', value: 'dotted' }
  ]

  // 根据 valuePath 设置一个值
  const setAxisValue = (axis, valuePath, val) =>
    produce(axis || { show: true, name: '' }, draft => {
      if (_.isArray(draft)) {
        draft.forEach(i => {
          _.set(i, valuePath, val)
        })
      } else {
        _.set(draft, valuePath, val)
      }
    }) as any

  const onCloneAxisChange = (type: 'x' | 'y' | (string & {}), valuePath: string, val: any, obj: any = undefined) => {
    const newAxis = setAxisValue(type === 'x' ? obj?.xAxis : obj?.yAxis, valuePath, val)
    if (type === 'x') obj.xAxis = newAxis
    if (type === 'y') obj.yAxis = newAxis
  }
  const onAxisChange = (type: 'x' | 'y' | (string & {}), valuePath: string, val: any) => {
    const newAxis = setAxisValue(type === 'x' ? value?.xAxis : value?.yAxis, valuePath, val)
    if (type === 'x') onChange({ ...value!, xAxis: newAxis })
    if (type === 'y') onChange({ ...value!, yAxis: newAxis })
  }
  const onAllTitleShowChange = (Bool: boolean) => {
    const clonedValue = _.cloneDeep(value)
    onCloneAxisChange('x', 'show', Bool, clonedValue)
    onCloneAxisChange('y', 'show', Bool, clonedValue)
    onChange(clonedValue)
  }

  const getInitFontValue = style => ({
    fontFamily: style?.fontFamily || 'sans-serif',
    color: style?.color || '#333',
    fontSize: style?.fontSize || 12,
    fontStyle: style?.fontStyle || 'normal',
    fontWeight: style?.fontWeight || 'normal',
    textAlign: style?.align || 'auto',
    lineHeight: style?.lineHeight
  })

  const renderAxisName = (style: any, type: 'x' | 'y') => (
    <div className='view-group-box'>
      {/* <span>轴名称</span> */}
      <div className='abi-form-schema-echart-custom-text-overlay'>
        <FontView
          ignore={['textDecoration', 'letterSpacing', 'lineHeight', 'textAlign']}
          type='font'
          value={getInitFontValue(style)}
          onChange={val => onAxisChange(type, 'nameTextStyle', val)}
        />
      </div>
    </div>
  )

  const renderAxisLineView = (style: any, type: 'x' | 'y') => (
    <div className='view-group-box mt-2 ml-3'>
      <span>轴刻度</span>
      <ColorSelect
        mode='rgba'
        value={style?.color || '#333'}
        showText={false}
        className='style-color'
        onChange={val => onAxisChange(type, 'axisLine.lineStyle.color', val)}
      />
    </div>
  )

  const renderAxisInverseView = (style: any, type: 'x' | 'y') => (
    <div className='view-group-box mt-2 ml-3'>
      <span className='mr-1'>反向</span>
      <Switch checked={style?.inverse} size='small' onChange={checked => onAxisChange(type, 'inverse', checked)} />
    </div>
  )

  const renderAxisLabelView = (style: any, type: 'x' | 'y') => (
    <FontView
      type='font'
      ignore={['textDecoration', 'letterSpacing', 'lineHeight', 'textAlign']}
      value={getInitFontValue(style)}
      onChange={val => onAxisChange(type, 'axisLabel', val)}
    />
  )

  const renderAxisGridView = (style: any, type: 'x' | 'y', splitLine) => (
    <div className='view-group-box'>
      <div className='abi-form-schema-echart-custom-axis-grid-overlay ml-3 mt-2'>
        <div className='grid-item'>
          <span>网格线</span>
          <ColorSelect
            value={style?.color || '#333'}
            showText={false}
            mode='rgba'
            className='style-color'
            onChange={val => onAxisChange(type, 'splitLine.lineStyle.color', val)}
          />
        </div>
        <div className='grid-item'>
          <span>是否隐藏</span>
          <Switch
            defaultChecked={type !== 'x'}
            checked={splitLine?.show}
            checkedChildren='显示'
            unCheckedChildren='隐藏'
            onChange={val => onAxisChange(type, 'splitLine.show', val)}
          />
        </div>
        <div className='grid-item'>
          <span>线宽度</span>
          <InputNumber
            value={style?.width === undefined ? 1 : style?.width || 0}
            placeholder='线宽度'
            size='small'
            style={{ width: 100 }}
            onChange={val => onAxisChange(type, 'splitLine.lineStyle.width', val)}
            step={1}
            formatter={val => `${val} px`}
            parser={val => val!.replace(/\s?px/, '') as any}
          />
        </div>
        <div className='grid-item'>
          <span>线类型</span>
          <Select
            options={lineOpts}
            defaultValue='solid'
            placeholder='线条'
            size='small'
            style={{ width: 100 }}
            value={style?.type}
            onChange={val => onAxisChange(type, 'splitLine.lineStyle.type', val)}
          />
        </div>
      </div>
    </div>
  )

  return (
    <ItemLayout
      className='abi-form-schema-echart-custom-axis'
      title={title}
      caption={caption}
      icon={icon}
      layout='column'
      show={tabs[0].value?.show && tabs[1].value?.show}
      onShowChange={onAllTitleShowChange}
      showTooltip='隐藏/显示坐标轴'
    >
      {tabs.map(i => (
        <ItemLayout
          key={i.key}
          className='abi-form-schema-echart-custom-axis'
          title={`${i.tab}坐标`}
          caption={caption}
          icon={icon}
          layout='column'
          show={i.value?.show}
          onShowChange={show => onAxisChange(i.key, 'show', show)}
        >
          <div className='view-group'>
            <ItemLayout
              className='abi-form-schema-echart-custom-axis'
              title='轴名称'
              caption={caption}
              icon={icon}
              layout='column'
            >
              {renderAxisName(i.value?.nameTextStyle, i.key)}
              <div className='view-group-box mb-2 ml-3'>
                <span>名称</span>
                <DebounceInput
                  value={i.value?.name}
                  onChange={val => onAxisChange(i.key, 'name', val)}
                  mode='enter'
                  placeholder='x 轴名称'
                  style={{ width: 120, marginLeft: 8 }}
                  size='small'
                  allowClear
                />
              </div>

              <div className='view-group-box mb-2 ml-3'>
                <span>坐标旋转角度</span>
                <InputNumber
                  addonBefore={<RedoOutlined />}
                  min={-360}
                  max={360}
                  step={5}
                  size='small'
                  precision={0}
                  value={i.value?.axisLabel?.rotate || 0}
                  formatter={val => `${val || 0}°`}
                  parser={val => val!.replace('°', '') as any}
                  onChange={val => onAxisChange(i.key, 'axisLabel.rotate', val)}
                  style={{ width: 90, marginLeft: 8 }}
                />
              </div>
            </ItemLayout>
            <ItemLayout
              className='abi-form-schema-echart-custom-axis'
              title='轴标签'
              caption={caption}
              icon={icon}
              layout='column'
            >
              <Col>{renderAxisLabelView(i.value?.axisLabel, i.key)}</Col>
            </ItemLayout>
            <ItemLayout
              className='abi-form-schema-echart-custom-axis'
              title='网格线'
              caption={caption}
              icon={icon}
              layout='column'
            >
              <Col>{renderAxisGridView(i.value?.splitLine?.lineStyle, i.key, i.value?.splitLine)}</Col>
            </ItemLayout>
            {renderAxisLineView(i.value?.axisLine?.lineStyle, i.key)}
            {renderAxisInverseView(i.value, i.key)}
          </div>
        </ItemLayout>
      ))}
    </ItemLayout>
  )
}

EchartCustomAxis.defaultValue = ({ defaultValue }) =>
  _.merge(
    {
      xAxis: {
        show: true,
        name: 'x 轴',
        type: 'category',
        axisLabel: { rotate: 0 },
        splitLine: { show: false }
      },
      yAxis: {
        show: true,
        name: 'y 轴',
        type: 'value',
        axisLabel: { rotate: 0 },
        splitLine: { show: true }
      }
    },
    defaultValue,
    {}
  )
