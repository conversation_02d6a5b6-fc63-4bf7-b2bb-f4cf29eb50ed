import './echart-custom-beautify.less'

import { SwapRightOutlined } from '@ant-design/icons'
import { InputNumber, Select, Space, Switch } from 'antd'
import { produce } from 'immer'
import _ from 'lodash'
import React from 'react'

import ColorSelect from '@/components/color-picker/color-select'
import { GRADIENT_DIRECTION } from '@/components/form-schema/const'

import Distance from '../font/distance'
import type { FormSchemaBase } from '../type'
import ItemLayout from './echart-custom-group'

export interface EchartCustomBeautifyProps extends FormSchemaBase {
  value?: {
    grid?: {
      left?: number | string
      right?: number | string
      top?: number | string
      bottom?: number | string
    },
    _state?: {
      series?: {
        barWidth?: number
        barGap?: string
        barColorType?: string
        barColor?: string[]
        smooth?: boolean
        borderRadius?: number
        barColorStart?: string | null
        colorDirection?: string | null
      }
    }
  }
  onChange: (value: EchartCustomBeautifyProps['value']) => any
  ignore: ('barColor' | 'barGap' | 'barWidth' | 'borderRadius')[]
}

/**
 * 定制化的一个样式设置
 * @param props
 */
export default function EchartCustomBeautify(props: EchartCustomBeautifyProps) {
  const { value = {}, onChange, title, icon, caption, ignore = ['barColorType', 'smooth', 'barColorStart'] } = props

  const grid = value.grid || {}
  const smooth = value._state?.series?.smooth
  const barWidth = value._state?.series?.barWidth
  const barGap = value._state?.series?.barGap
  const barColorType = value._state?.series?.barColorType || 'default'
  const barColor = value._state?.series?.barColor || ['blue', 'red']
  const borderRadius = value._state?.series?.borderRadius || 0
  const barColorStart = value._state?.series?.barColorStart || ['white', 'white']
  const colorDirection = value._state?.series?.colorDirection || 'to right'
  const opts = [
    { label: '默认', value: 'default' },
    // { label: '主题多色', value: 'themes' },
    { label: '连续多色', value: 'dispersed' }
  ]

  const onFieldChange = (key: string, val: any) => {
    const _state = produce(value._state || {}, draft => {
      _.set(draft, `series.${key}`, val)
    })
    onChange({ ...value, _state })
  }

  const onGridChange = val => {
    if (val?.all) delete val.all
    const data = _.cloneDeep({ ...value, grid: val })
    _.set(data, 'title.left', val.left || 'auto')
    onChange(data)
  }

  return (
    <ItemLayout
      className='abi-form-schema-echart-custom-beautify'
      title={title}
      icon={icon}
      caption={caption}
      layout='column'
    >
      {!_.includes(ignore, 'barWidth') && (
        <div className='view-box'>
          <span>柱宽</span>
          <InputNumber
            value={barWidth}
            step={5}
            placeholder='默认自适应'
            min={0}
            max={128}
            precision={0}
            onChange={val => onFieldChange('barWidth', val)}
            style={{ width: 120 }}
            formatter={val => `${val} px`}
            parser={val => val!.replace(/\s?px/, '') as any}
          />
        </div>
      )}
      {!_.includes(ignore, 'barGap') && (
        <div className='view-box'>
          <span>间隙</span>
          <InputNumber
            value={_.parseInt(barGap || '0')}
            step={5}
            placeholder='默认自适应'
            min={-100}
            max={100}
            precision={0}
            onChange={val => onFieldChange('barGap', `${val}%`)}
            style={{ width: 120 }}
            formatter={val => `${val} %`}
            parser={val => val!.replace(/\s?%/, '') as any}
          />
        </div>
      )}
      {!_.includes(ignore, 'smooth') && (
        <div className='view-box'>
          <span>平滑曲线</span>
          <Space direction='vertical'>
            <Switch
              onChange={val => onFieldChange('smooth', val)}
              checkedChildren='开启'
              unCheckedChildren='关闭'
              checked={smooth}
              defaultChecked={smooth}
            />
          </Space>
        </div>
      )}
      {!_.includes(ignore, 'borderRadius') && (
        <div className='view-box'>
          <span>圆角</span>
          <Space direction='vertical'>
            <Switch
              onChange={val => onFieldChange('borderRadius', val ? 15 : 0)}
              checkedChildren='开启'
              unCheckedChildren='关闭'
              checked={borderRadius !== 0}
              defaultChecked={borderRadius !== 0}
            />
          </Space>
        </div>
      )}
      {!_.includes(ignore, 'barColorStart') && (
        <div className='view-box'>
          <span>渐变</span>
          <ColorSelect
            value={barColorStart[0]}
            mode='rgba'
            showText={false}
            onChange={val => onFieldChange('barColorStart', [val, barColorStart[1]])}
          />
          <Select
            size='small'
            placeholder='方向'
            className='gradient-direction'
            showArrow={false}
            value={colorDirection}
            onChange={val => onFieldChange('colorDirection', val)}
            style={{ width: 60, margin: 6 }}
          >
            {GRADIENT_DIRECTION.map(item => (
              <Select.Option key={item.value} title={item.label}>
                <SwapRightOutlined rotate={item.rotate || 0} />
              </Select.Option>
            ))}
          </Select>
          <ColorSelect
            value={barColorStart[1]}
            mode='rgba'
            showText={false}
            onChange={val => onFieldChange('barColorStart', [barColorStart[0], val])}
          />
        </div>
      )}
      {!_.includes(ignore, 'barColorType') && (
        <div className='view-box'>
          <span>柱颜色</span>
          <Select
            defaultValue='default'
            options={opts}
            style={{ width: 120 }}
            placeholder='请选择'
            value={barColorType}
            onChange={val => onFieldChange('barColorType', val)}
          />
        </div>
      )}
      {!_.includes(ignore, 'barColorType') && barColorType === 'dispersed' && (
        <div className='dispersed-setting'>
          <div>
            最小色：
            <ColorSelect value={barColor[0]} onChange={val => onFieldChange('barColor', [val, barColor[1]])} />
          </div>
          <div>
            最大色：
            <ColorSelect value={barColor[1]} onChange={val => onFieldChange('barColor', [barColor[0], val])} />
          </div>
        </div>
      )}
      <div className='grid-setting'>
        <Distance value={grid} type='distance' title='距离' onChange={onGridChange} />
      </div>
    </ItemLayout>
  )
}

EchartCustomBeautify.defaultValue = () => ({
  _state: {
    series: {
      barWidth: 30, // 不设置时自适应
      smooth: false
    }
  }
})
