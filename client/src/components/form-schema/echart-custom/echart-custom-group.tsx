import cn from 'classnames'
import React, { useEffect, useState } from 'react'

import GroupTitle from '@/components/form-schema/components/group-title'

export interface FormItemLayoutProps {
  title?: string
  icon?: string
  caption?: string
  className?: string
  layout?: 'row' | 'column'
  children: any
  extra?: any
  unfold?: boolean
  onTitleClick?: (e: any) => any

  /** 是否启用 show 按钮 */
  show?: boolean
  showTooltip?: string
  onShowChange?: (show: boolean) => any
}

/**
 * 表单的布局
 * 支持 row，column
 * @param props
 */

export default function FormItemLayout(props: FormItemLayoutProps) {
  const { children, className = '', layout = 'row' } = props
  const { title, extra, caption, icon, onTitleClick, unfold = false } = props
  const { show, onShowChange, showTooltip } = props

  const [_unfold, setUnfold] = useState(unfold)

  useEffect(() => {
    setUnfold(unfold)
  }, [unfold])

  return (
    <div style={{ borderBottom: _unfold ? '1px solid #f1f1f1' : 'none' }}>
      {title && (
        <GroupTitle
          caption={caption}
          icon={icon}
          extra={extra}
          show={show}
          showTooltip={showTooltip}
          onShowChange={onShowChange}
          title={title}
          unfold={_unfold}
          onUnfoldChange={setUnfold}
          onClick={onTitleClick}
        />
      )}
      <div
        className={cn({
          [`abi-form-schema-${layout}`]: _unfold,
          [className]: _unfold,
          'layout-active': _unfold,
          'layout-active-none': _unfold
        })}
        style={{ display: !title || _unfold ? 'block' : 'none' }}
      >
        {children}
      </div>
    </div>
  )
}
