// @import '../../styles/variable.less';

.abi-form-schema-echart-custom-label {
  > .abi-form-schema-font-view {
    padding: 2px 0;
    border: none;
  }

  .tag-panel {
    .anticon {
      cursor: pointer;
      user-select: none;
      font-size: 18px;
      opacity: 0.9;
      &:hover {
        color: @primary-color;
      }
    }

    > header {
      padding: 12px 0;
      color: #777;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .tag-list-item {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
      padding-right: 5px;

      .tag-index {
        background-color: #eee;
        border-radius: 3px;
        width: 20px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        user-select: none;
        margin: 0 8px;
      }

      .tag-info {
        display: flex;
        align-items: center;
        flex: 1;
        padding: 0 8px;

        > span {
          cursor: pointer;
          margin: 0 3px;

          &:hover {
            color: @primary-color;
          }
        }
      }

      .abi-color-select {
        width: 26px;
        height: 26px;
        margin-right: 8px;
      }

      .close-icon {
        display: none;
        cursor: pointer;
        color: #f45;
      }

      &:hover {
        .close-icon {
          display: block;
        }
      }
    }
  }
}

.abi-form-schema-echart-custom-label-overlay {
  min-width: 80px;
  background-color: #fff;
  box-shadow: 0 0 2px rgba(1, 1, 1, 0.12);
  border-radius: 3px;
  .ant-popover-inner-content {
    padding: 0;
  }

  .direct-map {
    border: 1px solid @border-color-base;
    border-radius: 3px;
    padding: 0 3px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    user-select: none;

    > span {
      border-right: 1px solid #f1f1f1;
      padding: 0 2px;
      text-align: center;
      &:last-of-type {
        border: none;
      }
    }
    > span:hover {
      cursor: pointer;
      color: @primary-color;
    }
  }

  .ant-input-number-group-addon {
    background-color: #fff;
    padding: 0 4px;
  }
}
