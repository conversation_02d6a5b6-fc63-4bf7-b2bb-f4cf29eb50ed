import './echart-custom-label.less'

import { CloseCircleOutlined, PlusOutlined } from '@ant-design/icons'
import { Col, Dropdown, InputNumber, Row, Select, Tooltip } from 'antd'
import { produce } from 'immer'
import _ from 'lodash'
import React from 'react'

import ColorSelect from '../../color-picker/color-select'
import { LABEL_DIRECT } from '../const'
import FontView from '../font/font'
import type { FormSchemaBase } from '../type'
import ItemLayout from './echart-custom-group'


type TextStyle = {
  color: string
  fontStyle: string
  fontWeight: string
  fontFamily: string
  fontSize: number
  align: 'left' | 'center' | 'right' | (string & {})
  lineHeight: number
}

export interface EchartCustomLabelProps extends FormSchemaBase {
  value?: {
    _state?: {
      label?: {
        show: boolean
        position: string
        offset: number[]
      } & TextStyle
    }
    series: {
      label: { show: boolean; offset: number[]; position: string } & TextStyle
    }[]
  }
  onChange: (value: EchartCustomLabelProps['value']) => any
  maxLabelLength: number
}

/**
 * 自定义的 label 设置
 * @param props
 * @returns
 */
export default function EchartCustomLabel(props: EchartCustomLabelProps) {
  const { value, onChange, icon, title, caption, maxLabelLength = 10 } = props
  const label = value?._state?.label
  const labelList = (_.isArray(value?.series) ? value?.series : []) || []
  const directMap = { top: '上', bottom: '下', left: '左', right: '右' }
  const directOptions = LABEL_DIRECT
  const updateLabelList = (done: (list: any[]) => any) => {
    const list = produce(labelList, arr => done(arr) || arr)
    onChange({ ...value!, series: list })
  }

  const onShowChange = () => {
    const _state = produce(value?._state || {}, draft => {
      _.set(draft, 'label.show', !label?.show)
    })
    onChange({ ...value!, _state })
  }

  // 更新样式，设置 _state 里的缓存状态
  const onStyleChange = val => {
    const style = {
      ..._.omit(val, ['letterSpacing', 'textDecoration', 'textAlign', 'lineHeight']),
      align: val?.textAlign,
      lineHeight: _.parseInt(val?.lineHeight as string) || undefined
    }
    const _state = produce(value?._state || {}, draft => {
      _.keys(style).forEach(key => {
        _.set(draft, `label.${key}`, style[key])
      })
    })

    if (val.color !== label?.color) {
      const series = produce(labelList, draft => {
        draft.forEach(item => {
          _.set(item, 'label.color', val.color)
        })
      })
      onChange({ ...value!, _state, series })
      return
    }

    onChange({ ...value!, _state })
  }

  const onAddLabel = () => {
    const val: any = {
      label: {
        show: true,
        position: 'top',
        color: value?._state?.label?.color || '#333',
        offset: [0, 0]
      }
    }
    onChange({ ...value!, series: labelList.concat([val]) })
  }

  const onOffsetChange = (type: 'x' | 'y', val: number, index: number) => {
    updateLabelList(list => {
      _.set(list, `[${index}].label.offset[${type === 'x' ? 0 : 1}]`, val)
    })
  }

  const onDirectChange = (direct: string, index: number) => {
    updateLabelList(list => {
      _.set(list, `[${index}].label.position`, direct)
    })
  }

  const onColorChange = (color: string | undefined, index: number) => {
    updateLabelList(list => {
      _.set(list, `[${index}].label.color`, color)
    })
  }

  const onDelLabel = (index: number) => {
    updateLabelList(list => {
      list.splice(index, 1)
    })
  }

  const renderDirectPopoevr = (item, index) => (
    <Dropdown
      overlayClassName='abi-form-schema-echart-custom-label-overlay'
      trigger={['click']}
      placement='bottomLeft'
      overlay={
        <div className='direct-map'>
          {directOptions.map(val => (
            <span key={val.value} onClick={() => onDirectChange(val.value, index)}>
              {val.label}
            </span>
          ))}
        </div>
      }
    >
      <span title='方向'>{directMap[item.label?.position] || '内'}</span>
    </Dropdown>
  )

  const renderOffsetPopoevr = (type: 'x' | 'y', item, index) => {
    const val = item.label?.offset?.[type === 'x' ? 0 : 1] || 0
    return (
      <Dropdown
        overlayClassName='abi-form-schema-echart-custom-label-overlay'
        trigger={['click']}
        placement='bottomLeft'
        overlay={
          <InputNumber
            value={val}
            max={1000}
            min={-1000}
            size='small'
            placeholder={`${type} 偏移量`}
            addonAfter='px'
            onChange={num => onOffsetChange(type, num, index)}
            style={{ width: 85 }}
          />
        }
      >
        <span title='偏移量'>
          {type === 'x' ? 'X' : 'Y'}: {val}
        </span>
      </Dropdown>
    )
  }

  const updateLabel = (newLabel: Partial<typeof label>) => {
    const _state = produce(value?._state, draft => {
      _.set(draft || {}, 'label', { ...draft?.label, ...newLabel })
    })
    onChange({ ...value!, _state })
  }

  const onLabelPositionChange = (val: string) => updateLabel({ position: val })

  const onLabelOffsetChange = (type: 'x' | 'y', val: number) =>
    updateLabel({
      offset: type === 'x' ? [val, label?.offset?.[1] || 0] : [label?.offset?.[0] || 0, val]
    })

  const renderLabelPopover = () => (
    <div className='ml-2'>
      <Row align='middle'>
        <Col span={8}>显示位置</Col>
        <Col span={16}>
          <Select
            value={label?.position || 'inside'}
            options={LABEL_DIRECT}
            placeholder='位置'
            onChange={onLabelPositionChange}
          />
        </Col>
      </Row>
      <Row align='middle'>
        <Col span={8}>X 偏移量</Col>
        <Col span={16}>
          <InputNumber
            precision={0}
            placeholder='X 偏移量'
            value={label?.offset?.[0] || 0}
            onChange={val => onLabelOffsetChange('x', val || 0)}
          />
        </Col>
      </Row>
      <Row align='middle'>
        <Col span={8}>Y 偏移量</Col>
        <Col span={16}>
          <InputNumber
            precision={0}
            placeholder='Y 偏移量'
            value={label?.offset?.[1] || 0}
            onChange={val => onLabelOffsetChange('y', val || 0)}
          />
        </Col>
      </Row>
    </div>
  )

  return (
    <ItemLayout
      className='abi-form-schema-echart-custom-label'
      icon={icon}
      title={title}
      caption={caption}
      layout='column'
      show={label?.show !== false}
      onShowChange={onShowChange}
      showTooltip='隐藏/显示标签'
    >
      <FontView
        ignore={['lineHeight', 'letterSpacing', 'textDecoration']}
        type='font'
        value={{
          fontFamily: label?.fontFamily || 'Microsoft Yahei',
          color: label?.color || '#333',
          fontSize: label?.fontSize || 13,
          fontStyle: label?.fontStyle || 'normal',
          fontWeight: label?.fontWeight || 'normal',
          textAlign: label?.align || 'center',
          lineHeight: label?.lineHeight,
          letterSpacing: 0,
          textDecoration: 'none'
        }}
        onChange={onStyleChange}
      />
      {renderLabelPopover()}
      <div className='tag-panel'>
        <header>
          <span className='ml-2'>个性化标签</span>
          {labelList.length < maxLabelLength && <PlusOutlined onClick={onAddLabel} />}
        </header>
        {/* 设置 series 里的 */}

        {labelList?.map((item, index) => (
          <div key={index} className='tag-list-item'>
            <span className='tag-index'>{index + 1}</span>
            <div className='tag-info'>
              <ColorSelect
                mode='rgba'
                showText={false}
                value={item?.label?.color}
                onChange={val => onColorChange(val, index)}
              />
              {renderDirectPopoevr(item, index)}；{renderOffsetPopoevr('x', item, index)}，
              {renderOffsetPopoevr('y', item, index)}
            </div>

            <CloseCircleOutlined className='close-icon' onClick={() => onDelLabel(index)} />
          </div>
        ))}
      </div>
    </ItemLayout>
  )
}

EchartCustomLabel.defaultValue = () => ({
  _state: {
    label: {
      show: true,
      position: 'top',
      offset: [0, 0],
      fontFamily: 'Microsoft Yahei',
      color: '#333',
      fontSize: 13,
      fontStyle: 'normal',
      fontWeight: 'normal',
      align: 'center'
    }
  }
  // series: [{
  //   label: {
  //     show: true,
  //     position: 'top',
  //     offset: [0, 0]
  //   }
  // }]
})
