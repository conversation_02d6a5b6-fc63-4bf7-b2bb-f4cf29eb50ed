// @import '../../styles/variable.less';

.abi-form-schema-echart-custom-legend {

  & > .view-box {
    margin-top: 8px;
    display: flex;
    align-items: center;
    // justify-content: space-around;

    .ant-radio-group,
    .ant-input-number-group-wrapper {
      margin-left: 8px;
    }
    .ant-radio-button-wrapper {
      padding: 0 10px;
      height: 26px;
      line-height: 24px;
      &:first-of-type {
        border-radius: 12px 0 0 12px;
      }
      &:last-of-type {
        border-radius: 0 12px 12px 0;
      }
    }
  }

  .style-color {
    margin: 0 8px;
    width: 25px;
    height: 25px;

    & + span {
      cursor: pointer;
      color: #888;

      &:hover {
        color: @primary-color;
      }
    }
  }
}

.abi-form-schema-echart-custom-legend-text-overlay {
  width: 300px;

  .ant-popover-inner-content {
    border-radius: 3px;
    overflow: hidden;
    padding: 0;
  }
}
