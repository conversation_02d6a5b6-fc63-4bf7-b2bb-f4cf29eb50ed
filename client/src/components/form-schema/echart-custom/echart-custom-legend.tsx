import './echart-custom-legend.less'

import { InputNumber, Radio } from 'antd'
import React, { CSSProperties } from 'react'

import FontView from '../font/font'
import type { FormSchemaBase } from '../type'
import ItemLayout from './echart-custom-group'

const orientOpts = [
  { label: '水平', value: 'horizontal' },
  { label: '垂直', value: 'vertical' }
]

const directionOpts = [
  { label: '左边', value: 'left' },
  { label: '中间', value: 'center' },
  { label: '右边', value: 'right' }
]

const legendOpts = [
  { label: '平铺', value: 'plain' },
  { label: '滚动', value: 'scroll' }
]

export interface EchartCustomLegendProps extends FormSchemaBase {
  value?: {
    legend?: {
      width?: number | string | 'auto'
      height?: number | string | 'auto'
      orient: 'horizontal'
      show: true
      type: 'plain'
      textStyle?: CSSProperties,
      left: number | string | 'auto',
      right: number | string | 'auto'
    }
  }
  onChange: (value: EchartCustomLegendProps['value']) => any
}

/**
 * echart 的图例套件
 * @param props
 * @see https://echarts.apache.org/zh/option.html#legend
 */
export default function EchartCustomLegend(props: EchartCustomLegendProps) {
  const { value = {}, onChange, title, icon, caption } = props
  const legend = value.legend || ({} as any)

  const onShowChange = () => {
    onChange({ ...value, legend: { ...legend, show: !legend.show } })
  }

  const onTypeChange = (key: 'orient' | 'type', val) => {
    onChange({ ...value, legend: { ...legend, [key]: val } })
  }

  const onStyleChange = style => {
    onChange({ ...value, legend: { ...legend, textStyle: style } })
  }

  const onDirection = dire => {
    if (dire === 'left') {
      onChange({ ...value, legend: { ...legend, left: '2px' } })
    }
    if (dire === 'center') {
      onChange({ ...value, legend: { ...legend, left: undefined, right: undefined } })
    }
    if (dire === 'right') {
      onChange({ ...value, legend: { ...legend, left: undefined, right: '2px' } })
    }
  }

  const onWidthChange = w => {
    onChange({ ...value, legend: { ...legend, width: w } })
  }

  const onHeightChange = w => {
    onChange({ ...value, legend: { ...legend, height: w } })
  }

  return (
    <ItemLayout
      className='abi-form-schema-echart-custom-legend'
      title={title}
      icon={icon}
      caption={caption}
      layout='column'
      show={legend.show}
      onShowChange={onShowChange}
      showTooltip='隐藏/显示图例'
    >
      <FontView
        type='font'
        ignore={['textDecoration', 'letterSpacing', 'textAlign', 'lineHeight']}
        value={legend?.textStyle}
        onChange={val => onStyleChange(val)}
      />
      <div className='view-box'>
        <span>宽度</span>
        <InputNumber placeholder='请输入' addonAfter='px' value={legend.width} onChange={onWidthChange} />
      </div>
      <div className='view-box'>
        <span>高度</span>
        <InputNumber placeholder='请输入' addonAfter='px' value={legend.height} onChange={onHeightChange} />
      </div>
      <div className='view-box'>
        <span>方向</span>
        <Radio.Group
          value={legend.orient}
          onChange={e => onTypeChange('orient', e.target.value)}
          size='small'
          options={orientOpts}
          optionType='button'
        />
        <Radio.Group
          value={legend.type}
          onChange={e => onTypeChange('type', e.target.value)}
          size='small'
          options={legendOpts}
          optionType='button'
        />
      </div>
      <div className='view-box'>
        <span>位置</span>
        <Radio.Group
          value={(() => {
            if (legend.left === 'auto' || legend.right) return 'right'
            if (legend.left) return 'left'
            return 'center'
          })()}
          onChange={e => onDirection(e.target.value)}
          size='small'
          options={directionOpts}
          optionType='button'
        />
      </div>

    </ItemLayout>
  )
}

EchartCustomLegend.defaultValue = () => ({
  legend: {
    orient: 'horizontal',
    show: true,
    type: 'plain',
    align: 'auto',
    right: 5,
    icon: 'circle',
    itemWidth: 10,
    itemHeight: 10
  }
})
