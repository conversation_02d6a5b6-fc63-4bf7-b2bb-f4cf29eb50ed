// @import '../../styles/variable.less';

.abi-form-schema-echart-custom-text {
  margin-right: -10px;
  padding-bottom: 12px;

  & > .view-box {
    display: flex;
    align-items: center;
    margin: 5px 10px;

    > span:first-of-type {
      margin-right: 10px;
    }

    > .ant-input {
      width: 185px;
    }

    .anticon {
      user-select: none;
      cursor: pointer;
      margin-left: 10px;
      font-size: 18px;
      &:hover {
        color: @primary-color;
      }
    }
  }
}

.abi-form-schema-echart-custom-text-overlay {
  width: 300px;
  .ant-popover-inner-content {
    padding: 0;

    .header-tip {
      padding: 0 12px;
      height: 30px;
      line-height: 30px;
      font-weight: bold;
      border-bottom: 1px solid #f1f1f1;
    }
  }
}

.abi-form-schema-echart-custom-text-label-overlay {
  width: 240px;

  .ant-popover-inner-content {
    padding: 0;
    padding-bottom: 3px;
    border-radius: 3px;
    overflow: hidden;

    .header-tip {
      padding: 0 12px;
      height: 30px;
      line-height: 30px;
      font-weight: bold;
      border-bottom: 1px solid #f1f1f1;
    }

    .abi-color-select,
    .ant-select,
    .ant-input-number {
      width: 100%;
    }
    .ant-row {
      margin: 8px 12px;
    }
  }
}
