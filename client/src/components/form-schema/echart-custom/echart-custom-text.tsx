import './echart-custom-text.less'

import { produce } from 'immer'
import _ from 'lodash'
import React from 'react'

import DebounceInput from '../components/debounce-input'
import { ECHART_TEXT_DEFAULT } from '../const'
import FontView from '../font/font'
import type { FormSchemaBase } from '../type'
import ItemLayout from './echart-custom-group'


type TextStyle = {
  color: string
  fontStyle: string
  fontWeight: string
  fontFamily: string
  fontSize: number
  align: 'left' | 'center' | 'right' | (string & {})
  lineHeight: number
}

export interface EchartCustomTextProps extends FormSchemaBase {
  value?: {
    _state?: {
      label: {
        show: boolean
        color: string
        position: string
        offset: number[]
      }
    }
    title?: {
      text: string
      show?: boolean
      textStyle: TextStyle
      subtextStyle: TextStyle
      subtext: string
    }
  }
  onChange: (value: EchartCustomTextProps['value']) => any
}

/**
 * echart 的标题，副标题，标签文本设置
 * @param props
 */
export default function EchartCustomText(props: EchartCustomTextProps) {
  const { value, onChange, icon, title, caption } = props

  const text = value?.title
  const subText = value?.title?.subtext || ''
  // const options = [{ label: '显示', value: 1 }, { label: '隐藏', value: 0 }]
  const updateTitle = (newData: Partial<typeof text>) => {
    const newTitle = produce(value?.title, draft => {
      _.keys(newData).forEach(key => {
        _.set(draft || {}, key, newData?.[key])
      })
    })
    onChange({ ...value!, title: newTitle })
  }

  const onSubTitleShowChange = () => updateTitle({ subtext: subText ? '' : '子标题' })
  const onTitleShowChange = () => updateTitle({ show: !text?.show })
  const onAllTitleShowChange = () =>
    updateTitle({
      show: !(text?.show && subText),
      subtext: text?.show && subText ? '' : '子标题'
    })
  const onTitleChange = (val: string) => updateTitle({ text: val })
  const onSubTitleChange = (val: string) => updateTitle({ subtext: val })

  const renderFontPopoevr = (type: 'title' | 'subTitle', style?: TextStyle) => (
    <FontView
      type='font'
      ignore={['textAlign', 'letterSpacing', 'textDecoration']}
      value={{
        fontFamily: style?.fontFamily || 'sans-serif',
        color: style?.color || '#333',
        fontSize: style?.fontSize || (type === 'subTitle' ? 13 : 15),
        fontStyle: style?.fontStyle || 'normal',
        fontWeight: style?.fontWeight || 'normal',
        textAlign: style?.align || 'left',
        lineHeight: style?.lineHeight,
        letterSpacing: 0,
        textDecoration: 'none'
      }}
      onChange={val => {
        const field = type === 'title' ? 'textStyle' : 'subtextStyle'
        updateTitle({
          [field]: {
            ..._.omit(val, ['letterSpacing', 'textDecoration', 'textAlign', 'lineHeight']),
            align: val?.textAlign,
            lineHeight: _.parseInt(val?.lineHeight as string) || undefined
          }
        })
      }}
    />
  )


  return (
    <ItemLayout
      className='abi-form-schema-echart-custom-text'
      icon={icon}
      title={title}
      show={text?.show && !!subText}
      onShowChange={onAllTitleShowChange}
      caption={caption}
      showTooltip='隐藏/显示标题'
      layout='column'
    >
      <ItemLayout
        className='abi-form-schema-echart-custom-text'
        icon={icon}
        title='主标题'
        caption={caption}
        layout='column'
        show={text?.show}
        onShowChange={onTitleShowChange}
      >
        {renderFontPopoevr('title', value?.title?.textStyle)}
        <div className='view-box'>
          <span>主标题</span>
          <DebounceInput mode='enter' value={text?.text} placeholder='主标题' onChange={onTitleChange} />
          <div className='flex-1' />

        </div>
      </ItemLayout>
      <ItemLayout
        className='abi-form-schema-echart-custom-text'
        icon={icon}
        title='副标题'
        caption={caption}
        layout='column'
        show={text?.show}
        onShowChange={onSubTitleShowChange}
        showTooltip='隐藏/显示副标题'
      >
        {renderFontPopoevr('subTitle', value?.title?.subtextStyle)}
        <div className='view-box'>
          <span>副标题</span>
          <DebounceInput mode='enter' value={subText} placeholder='副标题' onChange={onSubTitleChange} />
          <div className='flex-1' />
        </div>
      </ItemLayout>
    </ItemLayout>
  )
}

EchartCustomText.defaultValue = ({ defaultValue }) =>
  _.merge(
    {
      title: {
        show: true,
        text: '标题',
        textStyle: { ...ECHART_TEXT_DEFAULT, fontSize: 15 },
        subtextStyle: { ...ECHART_TEXT_DEFAULT, fontSize: 13 }
      },
      _state: {
        label: {
          show: true,
          position: 'top',
          color: '#333'
        }
      }
    },
    defaultValue,
    {}
  )
