// @import '../../styles/variable.less';

.abi-form-schema-echart-custom-theme {
  padding-bottom: 12px;
  .theme-setting {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid rgba(@border-color-base, 0.8);
    border-radius: 3px;
    padding: 6px 8px 5px;
    width: 100%;
    margin: 6px auto 0;

    .color-list {
      &-item {
        display: inline-block;
        width: 22px;
        height: 22px;
        margin: 0 2px;
        border-radius: 2px;
      }
    }

    .theme-name {
      margin-right: -3px;
      padding-bottom: 2px;
      font-size: 14px;
      cursor: pointer;
      user-select: none;
      .anticon-caret-down {
        margin-left: 2px;
        color: #888;
      }
    }
  }
}

.abi-form-schema-echart-custom-theme-overlay {
  z-index: 1001;

  width: 365px;
  background-color: #fff;
  border-radius: 3px;
  box-shadow: 0 0 12px rgba(#111, 0.12);

  .color-system-panel {
    display: flex;

    > .left-panel {
      width: 80px;
      border-right: 1px solid #f1f1f1;

      > .group-type-item {
        padding: 6px 8px;
        text-align: center;
        border-bottom: 1px solid @border-color-base;
        &:hover {
          color: @primary-color;
          background-color: #fefefe;
          cursor: pointer;
        }
        &.active {
          color: @primary-color;
        }
      }
    }

    > .right-panel {
      flex: 1;
      min-height: 160px;
      max-height: 240px;
      overflow-y: auto;

      > .color-system-list {
        display: flex;
        margin: 3px;
        padding: 3px;
        border-radius: 3px;
        // flex-direction: column;
        cursor: pointer;

        > span:first-of-type {
          width: 60px;
          text-align: center;
        }

        &:hover {
          box-shadow: 0 0 3px rgba(#111, 0.12);
        }

        > .color-system-list-theme {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          border-radius: 3px;

          > div {
            width: 20px;
            height: 20px;
            margin: 0 3px;
            border-radius: 5px;
            &:first-of-type {
              margin: 0;
            }
          }
        }
      }
    }

    // 自定义的颜色
    .costom-color {
      min-height: 200px;
      padding: 8px;
      > .color-list-row {
        display: flex;
        align-items: center;
        justify-content: space-around;
        margin-bottom: 4px;
      }
    }
  }
}
