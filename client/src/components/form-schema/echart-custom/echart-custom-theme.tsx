import './echart-custom-theme.less'

import { CaretDownOutlined } from '@ant-design/icons'
import { Dropdown } from 'antd'
import React, { useEffect,useMemo, useState } from 'react'

import ItemLayout from '../components/item-layout'
import ThemeModal from '../echart/echart-theme/echart-theme-modal'
import { DEFAULT_THEME,THEME_MAP } from '../echart/echart-theme/theme-const'
import { getInjectTheme, initDefaultValue } from '../echart/echart-theme/utils'
import type { FormSchemaBase } from '../type'

export interface EchartCustomThemeProps extends FormSchemaBase {
  // 需要传整个 options 进来
  value?: {
    backgroundColor?: string
    color: string[]
    _state?: {
      theme?: {
        colors: string[]
        textColor: string
        borderColor: string
        titleColor: string
        subTitleColor: string
      }
    }
  }
  onChange: (value: EchartCustomThemeProps['value']) => any
  colorMaxCount?: number
}

/**
 * echart 的主题配置套件
 * @param props
 * @see https://echarts.apache.org/zh/theme-builder.html
 * @see https://github.com/apache/echarts-theme-builder/blob/master/app/scripts/main.js
 */
export default function EchartCustomTheme(props: EchartCustomThemeProps) {
  const { value, onChange, colorMaxCount, title, caption, icon } = props

  const [color, setColor] = useState(DEFAULT_THEME?.theme || [])

  const systemColorTitle = useMemo(() => {
    const item = THEME_MAP[color.toString()]
    return item?.title ? `${item?.title}` : '自定义'
  }, [color])

  const updateColorByDefault = item => {
    setColor(item.theme || [])
    const data = initDefaultValue(item)
    onChange(getInjectTheme(data, value))
  }

  // 只改 color
  const onColorChange = (colors: string[]) => {
    setColor(colors)
    onChange({ color: colors })
  }

  useEffect(() => {
    setColor(value?.color || [])
  }, [value])

  return (
    <ItemLayout
      title={title}
      caption={caption}
      icon={icon}
      className='abi-form-schema-echart-custom-theme'
      layout='column'
    >
      <div className='theme-setting'>
        <div className='color-list'>
          {color?.slice(0, 6)?.map(item => (
            <span style={{ backgroundColor: item }} key={item} className='color-list-item' />
          ))}
        </div>
        <Dropdown
          trigger={['click']}
          placement='bottomRight'
          overlayClassName='abi-form-schema-echart-custom-theme-overlay'
          overlay={
            <ThemeModal
              maxCount={colorMaxCount}
              useCustomSetting
              onSelect={updateColorByDefault}
              colors={color}
              onColorChange={onColorChange}
            />
          }
        >
          <div className='theme-name'>
            <span>{systemColorTitle}</span>
            <CaretDownOutlined />
          </div>
        </Dropdown>
      </div>
    </ItemLayout>
  )
}

EchartCustomTheme.defaultValue = ({ defaultValue }) => ({
  color: defaultValue?.color || DEFAULT_THEME.theme || []
})
