import './echart.less'

import React from 'react'

import { FormSchema } from '../index'
import type { FormSchemaBase, FormSchemaType } from '../type'

export interface EchartTooltipProps extends FormSchemaBase {
  unfold?: boolean // 是否展开
  value?: {}
  onChange: (value: EchartTooltipProps['value']) => any
  // schema?: Record<string, FormSchemaBase>
}

/**
 * echart 动画配置套件
 * @param props
 * @see https://echarts.apache.org/zh/option.html#animation
 */
export default function EchartAnimationView(props: EchartTooltipProps) {
  const { title, caption, icon, unfold = false, value, onChange } = props

  const easings = [
    { value: 'linear', label: 'linear' },
    { value: 'quadraticIn', label: 'quadraticIn' },
    { value: 'quadraticOut', label: 'quadraticOut' },
    { value: 'quadraticInOut', label: 'quadraticInOut' },
    { value: 'cubicIn', label: 'cubicIn' },
    { value: 'cubicOut', label: 'cubicOut' },
    { value: 'cubicInOut', label: 'cubicInOut' },
    { value: 'quarticIn', label: 'quarticIn' },
    { value: 'quarticOut', label: 'quarticOut' },
    { value: 'quarticInOut', label: 'quarticInOut' },
    { value: 'quinticIn', label: 'quinticIn' },
    { value: 'quinticOut', label: 'quinticOut' },
    { value: 'quinticInOut', label: 'quinticInOut' },
    { value: 'sinusoidalIn', label: 'sinusoidalIn' },
    { value: 'sinusoidalOut', label: 'sinusoidalOut' },
    { value: 'sinusoidalInOut', label: 'sinusoidalInOut' },
    { value: 'exponentialIn', label: 'exponentialIn' },
    { value: 'exponentialOut', label: 'exponentialOut' },
    { value: 'exponentialInOut', label: 'exponentialInOut' },
    { value: 'circularIn', label: 'circularIn' },
    { value: 'circularOut', label: 'circularOut' },
    { value: 'circularInOut', label: 'circularInOut' },
    { value: 'elasticIn', label: 'elasticIn' },
    { value: 'elasticOut', label: 'elasticOut' },
    { value: 'elasticInOut', label: 'elasticInOut' },
    { value: 'backIn', label: 'backIn' },
    { value: 'backOut', label: 'backOut' },
    { value: 'backInOut', label: 'backInOut' },
    { value: 'bounceIn', label: 'bounceIn' },
    { value: 'bounceOut', label: 'bounceOut' },
    { value: 'bounceInOut', label: 'bounceInOut' }
  ]

  const panel: FormSchemaType = {
    show: {
      title: '启用动画',
      type: 'switch',
      defaultValue: true,
      openText: '启用',
      closeText: '关闭',
      valuePath: 'animation'
    },
    animationThreshold: {
      title: '动画阈值',
      type: 'number',
      defaultValue: 2000,
      suffix: 'ms',
      caption: '动画的阈值，当单个系列显示的图形数量大于这个阈值时会关闭动画。',
      valuePath: 'animationThreshold'
    },
    animationDuration: {
      title: '动画时长',
      type: 'number',
      defaultValue: 1000,
      suffix: 'ms',
      caption: '初始动画的时长，默认 1000 毫秒。',
      valuePath: 'animationDuration'
    },
    animationDelay: {
      title: '动画时长',
      type: 'number',
      defaultValue: 0,
      suffix: 'ms',
      caption: '初始动画的延迟，默认 0 毫秒。',
      valuePath: 'animationDelay'
    },
    animationDurationUpdate: {
      title: '数据更新时长',
      type: 'number',
      defaultValue: 300,
      suffix: 'ms',
      caption: '初始动画的延迟，默认 300 毫秒。',
      valuePath: 'animationDurationUpdate'
    },
    stateAnimationDuration: {
      title: '状态切换时长',
      type: 'number',
      defaultValue: 300,
      suffix: 'ms',
      caption: '状态切换动画时长，默认 300 毫秒。',
      valuePath: 'stateAnimation.duration'
    },
    stateAnimationEasing: {
      title: '状态切换缓冲',
      type: 'select',
      caption: '初始动画的缓动效果，默认 cubicOut。',
      valuePath: 'stateAnimation.easing',
      defaultValue: 'cubicOut',
      options: easings
    },
    animationEasing: {
      title: '动画缓冲',
      type: 'select',
      caption: '初始动画的缓动效果，默认 cubicOut。',
      valuePath: 'animationEasing',
      defaultValue: 'cubicOut',
      options: easings
    }
  }

  const innerSchema = {
    group: {
      title,
      unfold,
      type: 'group',
      icon,
      caption: caption || '动画配置',
      valuePath: '',
      children: panel
    }
  }

  return (
    <FormSchema
      className='abi-form-schema-echart-tooltip-view abi-form-schema-echart-view'
      value={value}
      schema={innerSchema}
      onChange={onChange}
    />
  )
}

EchartAnimationView.defaultValue = () => ({
  animation: true,
  animationThreshold: 2000,
  animationDuration: 1000,
  animationDelay: 0,
  animationDurationUpdate: 300,
  animationEasing: 'cubicOut',
  stateAnimation: {
    duration: 300,
    easing: 'cubicOut'
  }
})
