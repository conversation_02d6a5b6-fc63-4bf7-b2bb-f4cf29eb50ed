import './echart.less'

import _ from 'lodash'
import React from 'react'

import { FormSchema } from '../index'
import type { FormSchemaBase, FormSchemaType } from '../type'

const AxisFormatterDoc = `
#### 刻度标签的内容

刻度标签的内容格式器，使用字符串模板，模板变量为刻度默认标签 \`{value}\`。支持时间的格式化模板，例如 \`{yyyy}\`，\`{eeee}\` 等。
`

const AxisTypeDoc = `
#### 轴类型

坐标轴类型，可选：
- \`value\` 数值轴，适用于连续数据。
- \`category\` 类目轴，适用于离散的类目数据。为该类型时类目数据可自动从 series.data 或 dataset.source 中取，或者可通过 xAxis.data 设置类目数据。
- \`time\` 时间轴，适用于连续的时序数据，与数值轴相比时间轴带有时间的格式化，在刻度计算上也有所不同，例如会根据跨度的范围来决定使用月，星期，日还是小时范围的刻度。
- \`log\` 对数轴。适用于对数数据。
`

export interface EchartTitleProps extends FormSchemaBase {
  unfold?: boolean
  // 映射到 options.xAxis，options.yAxis
  value?:
  | Partial<{
    show: boolean // 是否显示
    name: string
    nameLocation: string
    min: number
    max: number
    splitNumber: number
    position: string
    inverse: boolean
    offset: number
    axisLine: {
      show: boolean
      lineStyle: {
        color: string
        width: number
        type: string
      }
    }
    axisTick: {
      show: boolean
      inside: boolean
    }
    axisLabel: {
      show: boolean
      inside: boolean
      margin: number
      formatter: string
    }
  }>
  | any[]
  onChange: (value: EchartTitleProps['value']) => any
  maxItem?: number
  // schema?: Record<string, FormSchemaBase>
}

/**
 * echart 的 axis 封装
 * 是一个自定义的 schema 组合
 * @param props
 * @see https://echarts.apache.org/zh/option.html#xAxis
 */
export default function EchartAxis(props: EchartTitleProps) {
  const { title, caption, icon, unfold = false, type, value, onChange, valuePath, maxItem = 2 } = props
  const listValuePath = _.last(valuePath?.split('.')) || ''
  const axis = type.indexOf('xAx') > -1 ? 'x' : 'y'
  const isMultiple = type.indexOf('Axes') > -1

  // 坐标轴的配置
  const panel: FormSchemaType = {
    show: {
      title: '显示坐标轴',
      type: 'switch',
      defaultValue: true,
      openText: '显示',
      closeText: '隐藏',
      valuePath: 'show'
    },
    tabs: {
      type: 'tabs',
      valuePath: '',
      // direction: 'vertical',
      children: [
        {
          title: '轴名称',
          children: {
            name: {
              title: '坐标轴名称',
              type: 'text',
              defaultValue: `${axis.toLocaleUpperCase()} 轴`,
              valuePath: 'name'
            },
            nameLocation: {
              title: '名称显示位置',
              type: 'select',
              defaultValue: 'end',
              valuePath: 'nameLocation',
              options: [
                { value: 'start', label: '开始位置' },
                { value: axis === 'x' ? 'center' : 'middle', label: '中间' },
                { value: 'end', label: '结束位置' }
              ]
            },
            nameGap: {
              title: '轴名偏移量',
              caption: '坐标轴名称与轴线之间的距离',
              type: 'number',
              suffix: 'px',
              defaultValue: undefined,
              valuePath: 'nameGap'
            },
            nameRotate: {
              title: '轴名旋转角度',
              type: 'number',
              suffix: '度',
              defaultValue: undefined,
              valuePath: 'nameRotate'
            }
          }
        },
        {
          title: '轴数值',
          children: {
            min: {
              title: '轴刻度最小值',
              type: 'number',
              defaultValue: undefined,
              valuePath: 'min'
            },
            max: {
              title: '轴刻度最大值',
              type: 'number',
              defaultValue: undefined,
              valuePath: 'max'
            },
            splitNumber: {
              title: '轴的分割段数',
              type: 'number',
              defaultValue: 5,
              valuePath: 'splitNumber'
            }
          }
        },
        {
          title: '轴位置',
          children: {
            position: {
              title: `${axis} 轴的位置`,
              type: 'radio',
              layout: 'row',
              defaultValue: axis === 'y' ? 'left' : 'bottom',
              valuePath: 'position',
              options:
                axis === 'y'
                  ? [
                    { value: 'left', label: '左' },
                    { value: 'right', label: '右' }
                  ]
                  : [
                    { value: 'top', label: '上' },
                    { value: 'bottom', label: '下' }
                  ]
            },
            inverse: {
              title: '反向坐标轴',
              type: 'switch',
              defaultValue: false,
              valuePath: 'inverse'
            },
            offset: {
              title: '轴偏移量',
              caption: '坐标轴相对于默认位置的偏移，在相同的 `position` 上有多个坐标轴的时候有用',
              type: 'number',
              suffix: 'px',
              defaultValue: undefined,
              valuePath: 'offset'
            }
          }
        },
        {
          title: '轴类型',
          children: {
            type: {
              title: '轴类型',
              type: 'select',
              defaultValue: axis === 'y' ? 'value' : 'category',
              valuePath: 'type',
              caption: AxisTypeDoc,
              options: [
                { value: 'value', label: '数值轴（value）' },
                { value: 'category', label: '类目轴（category）' },
                { value: 'time', label: '时间轴（time）' },
                { value: 'log', label: '对数轴（log）' }
              ]
            }
          }
        }
      ]
    },
    tabs2: {
      type: 'tabs',
      valuePath: '',
      children: [
        {
          title: '轴线条',
          children: {
            show: {
              title: '坐标轴轴线',
              type: 'switch',
              defaultValue: true,
              openText: '显示',
              closeText: '隐藏',
              valuePath: 'axisLine.show'
            },
            onZeroAxisIndex: {
              title: '0 刻度轴',
              caption: '当有双轴时，可以用这个属性手动指定，在哪个轴的 0 刻度上。',
              type: 'number',
              suffix: 'index',
              defaultValue: undefined,
              valuePath: 'axisLine.onZeroAxisIndex'
            },
            lineStyleColor: {
              title: '线条颜色',
              type: 'color',
              defaultValue: '#333',
              valuePath: 'axisLine.lineStyle.color'
            },
            lineStyleWidth: {
              title: '线条宽度',
              type: 'slider',
              defaultValue: 0.5,
              max: 10,
              step: 0.5,
              layout: 'row',
              suffix: 'px',
              valuePath: 'axisLine.lineStyle.width'
            },
            lineStyleType: {
              title: '线条类型',
              type: 'select',
              defaultValue: 'solid',
              valuePath: 'axisLine.lineStyle.type',
              options: [
                { value: 'solid', label: '实线' },
                { value: 'dashed', label: '点线' },
                { value: 'dotted', label: '虚线' }
              ]
            }
          }
        },
        {
          title: '轴刻度',
          children: {
            show: {
              title: '坐标轴轴刻度',
              type: 'switch',
              defaultValue: true,
              openText: '显示',
              closeText: '隐藏',
              valuePath: 'axisTick.show'
            },
            inside: {
              title: '标签朝向',
              type: 'radio',
              layout: 'row',
              defaultValue: false,
              valuePath: 'axisTick.inside',
              options: [
                { value: false, label: '朝外' },
                { value: true, label: '朝向' }
              ]
            }
          }
        },
        {
          title: '轴标签',
          children: {
            show: {
              title: '坐标轴刻度标签',
              type: 'switch',
              defaultValue: true,
              openText: '显示',
              closeText: '隐藏',
              valuePath: 'axisLabel.show'
            },
            inside: {
              title: '标签朝向',
              type: 'radio',
              defaultValue: false,
              layout: 'row',
              valuePath: 'axisLabel.inside',
              options: [
                { value: false, label: '朝外' },
                { value: true, label: '朝向' }
              ]
            },
            rotate: {
              title: '标签旋转角度',
              type: 'number',
              defaultValue: undefined,
              suffix: '度',
              valuePath: 'axisLabel.rotate'
            },
            margin: {
              title: '标签与轴线距离',
              // type: 'number',
              defaultValue: 8,
              suffix: 'px',
              type: 'slider',
              max: 50,
              layout: 'row',
              valuePath: 'axisLabel.margin'
            },
            formatter: {
              title: '标签内容格式',
              type: 'text',
              caption: AxisFormatterDoc,
              // height: 200,
              // defaultValue: 'function formatter(value, index) {\n  return value\n}',
              defaultValue: '{value}',
              valuePath: 'axisLabel.formatter'
            }
          }
        },
        {
          title: '网格线',
          children: {
            show: {
              title: '网格内分割线',
              type: 'switch',
              defaultValue: false,
              openText: '显示',
              closeText: '隐藏',
              valuePath: 'splitLine.show'
            },
            lineStyleColor: {
              title: '线条颜色',
              type: 'color',
              defaultValue: '#333',
              valuePath: 'splitLine.lineStyle.color'
            },
            lineStyleWidth: {
              title: '线条宽度',
              type: 'slider',
              defaultValue: 0.5,
              max: 10,
              step: 0.5,
              layout: 'row',
              suffix: 'px',
              valuePath: 'splitLine.lineStyle.width'
            },
            lineStyleType: {
              title: '线条类型',
              type: 'select',
              defaultValue: 'solid',
              valuePath: 'splitLine.lineStyle.type',
              options: [
                { value: 'solid', label: '实线' },
                { value: 'dashed', label: '点线' },
                { value: 'dotted', label: '虚线' }
              ]
            },
            opacity: {
              title: '线条透明度',
              type: 'slider',
              defaultValue: 1,
              max: 1,
              min: 0,
              step: 0.01,
              suffix: '{{ _.round(value * 100) }}%',
              showValue: false,
              layout: 'row',
              valuePath: 'splitLine.lineStyle.opacity'
            }
          }
        }
      ]
    }
  }

  const innerSchema = {
    group: {
      title,
      type: 'group',
      icon,
      unfold,
      caption:
        caption ||
        '**直角坐标系** grid 中的 x 轴，一般情况下单个 grid 组件最多只能放上下两个 x 轴。多于两个 x 轴需要通过配置 offset 属性防止同个位置多个 x 轴的重叠。',
      valuePath: '',
      children: isMultiple
        ? {
          list: {
            title: '',
            valuePath: listValuePath,
            type: 'list',
            maxItem,
            tabPrefix: `${axis.toLocaleUpperCase()} 轴`,
            mode: 'tabs',
            defaultValue: [],
            // defaultValue: [getInitDefaultValue(panel)],
            children: panel
          }
        }
        : panel
    }
  }

  // 兼容旧数据
  const _value = (() => {
    // 嵌套 list 产生的问题，临时解决
    if (isMultiple) return { [listValuePath]: value }
    if (_.isArray(value)) return value[0]
    return value
  })()

  return (
    <FormSchema
      className='abi-form-schema-echart-axis-view abi-form-schema-echart-view'
      value={_value}
      schema={innerSchema}
      onChange={onChange}
    />
  )
}

EchartAxis.defaultValue = ({ type }) => {
  const value = {
    show: true,
    name: 'x 轴',
    type: 'category',
    // nameLocation: 'end',
    splitNumber: 5,
    position: 'bottom',
    inverse: false,
    splitLine: {
      show: false
    },
    axisLine: {
      show: true
      // lineStyle: {
      //   color: '#333',
      //   width: 1,
      //   type: 'solid'
      // }
    },
    axisTick: {
      show: true,
      inside: false
    },
    axisLabel: {
      show: true,
      inside: false,
      margin: 8,
      formatter: '{value}'
      // formatter: 'function formatter(value, index) {\n  return value\n}'
    }
  }
  if (type.indexOf('xes') > -1) return [value]
  return value
}
