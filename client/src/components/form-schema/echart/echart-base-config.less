// @import '../../styles/variable.less';

.from-schema-echart-base-config-view {
  border-bottom: 1px solid @border-color-base;

  & > header {
    padding: 6px 8px;
    padding-bottom: 2px;
    // border-bottom: 1px solid #f1f1f1;
  }

  &-content {
    // margin: 4px;
    // &:hover {
    //   border-radius: 3px;
    //   box-shadow: 0 0 4px rgba(#111, 0.24);
    // }
  }

  .title-algin {
    display: flex;
    align-items: center;
    width: 100%;

    > .anticon {
      margin-right: 12px;
      padding: 4px;
      font-size: 16px;
      border: 1px solid #f1f1f1;
      border-radius: 3px;
      cursor: pointer;

      &.active {
        color: @primary-color;
        border-color: tint(@primary-color, 15%);
      }
    }
  }

  .theme-setting {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 4px 0;

    .color-list {
      &-item {
        display: inline-block;
        width: 18px;
        height: 18px;
      }
    }

    .theme-name {
      margin-right: -3px;
      padding-bottom: 2px;
      font-size: 14px;
      cursor: pointer;
      user-select: none;
      .anticon-caret-down {
        margin-left: 2px;
        color: #888;
      }
    }
  }
}
