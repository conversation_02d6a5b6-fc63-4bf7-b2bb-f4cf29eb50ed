import './echart-base-config.less'

import { AlignCenterOutlined, Align<PERSON>eftOutlined, AlignRightOutlined, CaretDownOutlined } from '@ant-design/icons'
import { useReactive } from 'ahooks'
import { Dropdown, InputNumber, Select, Switch, Tooltip } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React, { useEffect,useMemo } from 'react'

import DebounceInput from '../components/debounce-input'
import ItemLayout from '../components/item-layout'
import { FONT_FAMILY } from '../const'
import type { FormSchemaBase } from '../type'
import ThemeModal from './echart-theme/echart-theme-modal'
import { DEFAULT_THEME,THEME_MAP } from './echart-theme/theme-const'
import { getInjectTheme, initDefaultValue } from './echart-theme/utils'

export interface EchartBaseConfigProps extends FormSchemaBase {
  value?: Partial<{
    title:
      | {
          show: boolean
          text: string
          textStyle: {
            fontSize: number
            fontFamily: string
          }
          left: 'left' | 'center' | 'right'
        }
      | any[]
    color: string[]
    _theme: {
      colors: string[]
      textColor: string
      borderColor: string
      titleColor: string
      subTitleColor: string
    }
  }>
  onChange: (value: EchartBaseConfigProps['value']) => any
  colorMaxCount?: number
  ignore?: ('showTitle' | 'title' | 'titleAlign' | 'fontSize' | 'fontFamily' | 'color')[]
}

/**
 * echart 的基础配置面板，这里是定制的要求
 * @param props
 */
export default function EchartBaseConfig(props: EchartBaseConfigProps) {
  const { value, onChange, ignore, colorMaxCount } = props
  const ignoreSet = new Set(ignore || [])

  const state = useReactive({
    titleAlign: 'left',
    showTitle: true,
    title: '标题',
    fontSize: 18,
    fontFamily: 'Microsoft Yahei',
    color: DEFAULT_THEME?.theme || []
    // ['#9ddeb0', '#74809f', '#e5bf2f', '#786ffb', '#a3d5f8', '#7997f8']
  })

  const systemColorTitle = useMemo(() => {
    const item = THEME_MAP[state.color.toString()]
    return item?.title ? `${item?.title}` : '自定义'
  }, [state.color])

  const leftOptions = [
    { label: '左对齐', value: 'left', icon: AlignLeftOutlined },
    { label: '居中对齐', value: 'center', icon: AlignCenterOutlined },
    { label: '右对齐', value: 'right', icon: AlignRightOutlined }
  ]

  const update = (key: keyof typeof state, val: any) => {
    state[key as any] = val
    let newValue = _.cloneDeep({ ...value }) // TODO: 不 clone 有问题
    // 字段与路径映射
    const pathMap = {
      titleAlign: 'title.left',
      showTitle: 'title.show',
      fontSize: 'title.textStyle.fontSize',
      title: 'title.text',
      fontFamily: 'title.textStyle.fontFamily'
    }

    // 不存在的不会更新
    if (!pathMap[key] || ignoreSet.has(key)) return
    if (ignore && ignore.length > 0) {
      newValue = _.omit(newValue, ignore)
    }

    if (_.isArray(newValue.title)) {
      newValue.title = newValue.title.map(item => {
        const newItem = { ...item }
        _.set(newItem, pathMap[key].replace(/^title\./, ''), val)
        return newItem
      })
    } else {
      _.set(newValue, pathMap[key], val)
    }

    onChange(newValue)
  }

  const updateColorByDefault = item => {
    state.color = item.theme
    const data = initDefaultValue(item)
    onChange(getInjectTheme(data, value))
  }

  // 只改 color
  const onColorChange = colors => {
    state.color = colors
    const obj: any = {
      color: colors,
      _theme: { ...value!._theme, colors }
    }
    onChange(obj)
  }

  useEffect(() => {
    const title = _.isArray(value?.title) ? value?.title[0] : value?.title
    state.color = value?.color || []
    state.fontFamily = _.get(title, 'textStyle.fontFamily', 'Microsoft Yahei')
    state.fontSize = _.get(title, 'textStyle.fontSize', 18)
    state.showTitle = _.get(title, 'show', true)
    state.titleAlign = _.get(title, 'left', 'left')
    state.title = _.get(title, 'text', '标题')
  }, [value])

  return (
    <div className='from-schema-echart-base-config-view'>
      <header>基础配置</header>

      <div className='from-schema-echart-base-config-view-content'>
        {!ignoreSet.has('title') && (
          <ItemLayout title='显示标题'>
            <Switch
              unCheckedChildren='关闭'
              checkedChildren='显示'
              checked={state.showTitle}
              onChange={val => update('showTitle', val)}
            />
          </ItemLayout>
        )}

        {!ignoreSet.has('title') && (
          <ItemLayout title='标题'>
            <DebounceInput
              className='form-input'
              placeholder='请输入'
              value={state.title}
              mode='enter'
              onChange={val => update('title', val)}
            />
          </ItemLayout>
        )}

        {!ignoreSet.has('fontFamily') && (
          <ItemLayout title='字体'>
            <Select
              className='form-input'
              placeholder='请选择'
              value={state.fontFamily}
              options={FONT_FAMILY}
              onChange={val => update('fontFamily', val)}
            />
          </ItemLayout>
        )}

        {!ignoreSet.has('fontSize') && (
          <ItemLayout title='字号'>
            <InputNumber
              className='form-input'
              placeholder='请输入'
              addonAfter='px'
              value={state.fontSize}
              onChange={val => update('fontSize', val)}
            />
          </ItemLayout>
        )}

        {!ignoreSet.has('titleAlign') && (
          <ItemLayout title='标题位置'>
            <div className='title-algin'>
              {leftOptions.map(item => (
                <Tooltip title={item.label} key={item.value}>
                  <item.icon
                    className={cn({ active: item.value === state.titleAlign })}
                    onClick={() => update('titleAlign', item.value)}
                  />
                </Tooltip>
              ))}
            </div>
          </ItemLayout>
        )}

        {!ignoreSet.has('color') && (
          <ItemLayout title='主题' className='fix-theme-title'>
            <div className='theme-setting'>
              <div className='color-list'>
                {state.color?.slice(0, 6)?.map(item => (
                  <span style={{ backgroundColor: item }} key={item} className='color-list-item' />
                ))}
              </div>
              <Dropdown
                trigger={['click']}
                placement='bottomRight'
                overlayClassName='abi-form-schema-echart-theme-view-color-overlay'
                overlay={
                  <ThemeModal
                    maxCount={colorMaxCount}
                    useCustomSetting
                    onSelect={updateColorByDefault}
                    colors={state.color}
                    onColorChange={onColorChange}
                  />
                }
              >
                <div className='theme-name'>
                  <span>{systemColorTitle}</span>
                  <CaretDownOutlined />
                </div>
              </Dropdown>
            </div>
          </ItemLayout>
        )}
      </div>
    </div>
  )
}

EchartBaseConfig.defaultValue = () => ({
  color: DEFAULT_THEME.theme || []
})
