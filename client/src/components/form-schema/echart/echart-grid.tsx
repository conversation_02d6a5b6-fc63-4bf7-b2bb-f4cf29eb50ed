import './echart.less'

import React from 'react'

import { FormSchema } from '../index'
import type { FormSchemaBase, FormSchemaType } from '../type'

export interface EchartGridProps extends FormSchemaBase {
  unfold?: boolean
  value?: any
  onChange: (value: EchartGridProps['value']) => any
}

/**
 * echart 的 grid 套件
 * @param props
 * @see https://echarts.apache.org/zh/option.html#grid
 */
export default function EchartGrid(props: EchartGridProps) {
  const { title, icon, caption, unfold = false, value, onChange } = props

  const panel: FormSchemaType = {
    containLabel: {
      title: '区域是否包坐标轴',
      type: 'switch',
      defaultValue: true,
      openText: '包含',
      closeText: '不包含',
      valuePath: 'containLabel'
    },
    distance: {
      title: '网格距离',
      type: 'distance',
      caption: '组件离容器四侧的距离',
      defaultValue: {},
      valuePath: ''
    }
  }

  const innerSchema = {
    group: {
      title,
      type: 'group',
      unfold,
      icon,
      caption:
        caption ||
        '直角坐标系内绘图网格，单个 grid 内最多可以放置上下两个 X 轴，左右两个 Y 轴。可以在网格上绘制折线图，柱状图，散点图（气泡图）。',
      valuePath: '',
      children: panel
    }
  }

  return (
    <FormSchema
      key={`${unfold}`}
      schema={innerSchema}
      value={value}
      onChange={onChange}
      className='abi-form-schema-echart-grid-view abi-form-schema-echart-view'
    />
  )
}

EchartGrid.defaultValue = () => ({
  top: 70,
  left: 15,
  right: 50,
  bottom: 15,
  containLabel: true
})
