import './echart.less'

import React from 'react'

import { FormSchema } from '../index'
import type { FormSchemaBase, FormSchemaType } from '../type'

const LegendTypeDoc = `
#### 图例的类型

可选值：
- \`plain\`：普通图例。缺省就是普通图例。
- \`scroll\`：可滚动翻页的图例。当图例数量较多时可以使用。
`

type Algin = 'center' | 'left' | 'right' | 'auto'

export interface EchartLegendProps extends FormSchemaBase {
  unfold?: boolean
  value?: {
    type: string
    show: boolean // 是否显示
    left: Algin
    top: Algin
    right: Algin
    bottom: Algin
  }
  onChange: (value: EchartLegendProps['value']) => any
}

/**
 * echart 的图例套件
 * @param props
 * @see https://echarts.apache.org/zh/option.html#legend
 */
export default function EchartLegend(props: EchartLegendProps) {
  const { title, caption, icon, unfold = false, value, defaultValue, onChange } = props

  const panel: FormSchemaType = {
    show: {
      title: '显示图例',
      type: 'switch',
      defaultValue: true,
      openText: '显示',
      closeText: '隐藏',
      valuePath: 'show'
    },
    tabs: {
      title: '',
      type: 'tabs',
      children: [
        {
          title: '布局',
          children: {
            distance: {
              title: '图例距离',
              caption: '图例组件离容器四方的距离。',
              type: 'distance',
              defaultValue: {},
              valuePath: ''
            },
            orient: {
              title: '布局方向',
              type: 'radio',
              defaultValue: 'horizontal',
              valuePath: 'orient',
              caption: '图例列表的布局朝向',
              layout: 'row',
              options: [
                { value: 'horizontal', label: '水向' },
                { value: 'vertical', label: '垂直' }
              ]
            },
            align: {
              title: '文本对齐',
              type: 'radio',
              defaultValue: 'auto',
              valuePath: 'align',
              caption: `图例标记和文本的对齐。默认自动，根据组件的位置和 orient 决定，
              当组件的 left 值为 'right' 以及纵向布局（orient 为 'vertical'）的时候为右对齐，即为 'right'。`,
              layout: 'row',
              options: [
                { value: 'auto', label: '自动' },
                { value: 'left', label: '左' },
                { value: 'right', label: '右' }
              ]
            }
          }
        },
        {
          title: '样式',
          children: {
            font: {
              title: '文本样式',
              type: 'font',
              defaultValue: defaultValue?.textStyle || {
                fontSize: 12,
                fontWeight: 'normal',
                fontStyle: 'normal',
                color: '#333'
              },
              valuePath: 'textStyle'
            },
            width: {
              title: '图例宽度',
              type: 'percent',
              caption: '图例组件的宽度，支持百分比',
              defaultValue: undefined,
              valuePath: 'width'
            },
            height: {
              title: '图例高度',
              type: 'percent',
              caption: '图例组件的高度，支持百分比',
              defaultValue: undefined,
              valuePath: 'height'
            },
            backgroundColor: {
              title: '图例背景',
              type: 'color',
              defaultValue: 'transparent',
              valuePath: 'backgroundColor'
            },
            borderColor: {
              title: '图例边框颜色',
              type: 'color',
              defaultValue: 'transparent',
              valuePath: 'borderColor'
            },
            borderWidth: {
              title: '图例边框线宽',
              type: 'slider',
              min: 0,
              max: 20,
              suffix: 'px',
              defaultValue: 0,
              layout: 'row',
              valuePath: 'borderWidth'
            },
            item: {
              title: '子项',
              type: 'group',
              unfold: false,
              caption: '每个图例的项单独设置',
              icon: '宫格',
              children: {
                itemWidth: {
                  title: '子项宽度',
                  type: 'number',
                  defaultValue: 25,
                  valuePath: 'itemWidth'
                },
                itemHeight: {
                  title: '子项高度',
                  type: 'number',
                  defaultValue: 14,
                  valuePath: 'itemHeight'
                },
                color: {
                  title: '子项颜色',
                  type: 'color',
                  defaultValue: undefined,
                  valuePath: 'itemStyle.color'
                }
              }
            },
            line: {
              title: '线条',
              type: 'group',
              caption:
                '图例图形中线的样式，用于诸如折线图图例横线的样式设置。其属性的取值为 `inherit` 时，表示继承系列中的属性值',
              unfold: false,
              icon: '线条粗细',
              children: {
                width: {
                  title: '线条宽度',
                  valuePath: 'lineStyle.width',
                  type: 'slider',
                  min: 0,
                  max: 20,
                  suffix: 'px',
                  defaultValue: 0,
                  layout: 'row'
                },
                color: {
                  title: '线条颜色',
                  type: 'color',
                  defaultValue: undefined,
                  valuePath: 'lineStyle.color'
                },
                type: {
                  title: '线条类型',
                  type: 'select',
                  defaultValue: 'solid',
                  valuePath: 'lineStyle.type',
                  options: [
                    { value: 'solid', label: '实线' },
                    { value: 'dashed', label: '点线' },
                    { value: 'dotted', label: '虚线' }
                  ]
                }
              }
            }
          }
        },
        {
          title: '类型',
          children: {
            type: {
              title: '图例类型',
              type: 'radio',
              layout: 'row',
              caption: LegendTypeDoc,
              valuePath: 'type',
              defaultValue: 'plain',
              options: [
                { value: 'plain', label: '平铺' },
                { value: 'scroll', label: '滚动' }
              ]
            }
          }
        }
      ]
    }
  }

  const innerSchema = {
    group: {
      title,
      type: 'group',
      unfold,
      icon,
      caption:
        caption || '**图例组件** 展现了不同系列的标记（symbol），颜色和名字。可以通过点击图例控制哪些系列不显示。',
      valuePath: '',
      children: panel
    }
  }

  return (
    <FormSchema
      className='abi-form-schema-echart-legend-view abi-form-schema-echart-view'
      schema={innerSchema}
      value={value}
      onChange={onChange}
    />
  )
}

EchartLegend.defaultValue = () => ({
  type: 'plain',
  show: true,
  orient: 'horizontal',
  align: 'auto',
  textStyle: {
    fontSize: 12,
    fontWeight: 'normal',
    fontStyle: 'normal',
    color: '#333'
  },
  itemStyle: {},
  lineStyle: {
    type: 'solid'
  }
})
