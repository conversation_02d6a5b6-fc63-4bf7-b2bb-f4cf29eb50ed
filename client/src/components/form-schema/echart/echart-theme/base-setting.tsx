import React from 'react'

import ColorSelect from '../../../color-picker/color-select'
import Title from '../../components/title'

export interface EchartThemeBaseProps {
  onColorSelect: (val: string | undefined, item: any) => any
  data: any
}

/**
 * 基础的设置
 * @param props
 */
export default function EchartThemeBase(props: EchartThemeBaseProps) {
  const { data, onColorSelect } = props

  const baseList = [
    { title: '背景', field: 'backgroundColor' },
    { title: '标题', field: 'titleColor' },
    { title: '副标题', field: 'subTitleColor' },
    { title: '标签文字', field: 'textColor' },
    { title: '描边', field: 'borderColor' }
  ]

  return (
    <div className='color-panel'>
      <Title title='基础' />
      {baseList.map(item => (
        <div className='color-panel-item' key={item.field}>
          <span>{item.title}</span>
          <ColorSelect mode='rgba' value={data[item.field]} onChange={val => onColorSelect(val, item)} />
        </div>
      ))}
    </div>
  )
}
