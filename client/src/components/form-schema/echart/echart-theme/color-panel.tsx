import _ from 'lodash'
import React, { Fragment } from 'react'

import ColorSelect from '../../../color-picker/color-select'

export interface EchartThemeColorProps {
  colors: string[]
  maxLength?: number
  isCustomColor?: boolean
  showIndex?: boolean
  onColorChange: (val: string, index: number) => any
}

/**
 * 颜色列表
 * @param props
 */
export default function EchartThemeColor(props: EchartThemeColorProps) {
  const { colors = [], maxLength = 8, isCustomColor, onColorChange, showIndex } = props

  let _colors: any[] = colors.slice(0, maxLength)
  if (isCustomColor) {
    // 循环色
    _colors = [...new Array(maxLength)].map((_v, index) => colors[index] || '#3399ff')
  }
  _colors = _colors.map((v, i) => ({ value: v, index: i }))

  return (
    <>
      {_.chunk<any>(_colors, 2).map((list, idx) => (
        <div key={idx} className='color-list-row'>
          {list.map(({ value, index }) => {
            if (showIndex)
              return (
                <Fragment key={index + value}>
                  <span>{index + 1}</span>
                  <ColorSelect value={value} onChange={val => onColorChange(val!, index)} />
                </Fragment>
              )
            return <ColorSelect key={value + index} value={value} onChange={val => onColorChange(val!, index)} />
          })}
        </div>
      ))}
    </>
  )
}
