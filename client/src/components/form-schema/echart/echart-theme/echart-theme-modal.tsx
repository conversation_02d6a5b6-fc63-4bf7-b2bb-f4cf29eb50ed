import cn from 'classnames'
import React, { useState } from 'react'

import ColorPanel from './color-panel'
import { PRE_DEFINED_THEMES,THEME_GROUP_MAP, THEME_KEYS } from './theme-const'

export interface EchartThemeModalProps {
  onSelect?: (data: any) => any
  maxCount?: number
  /** 是否使用自定义的颜色设置 */
  useCustomSetting?: boolean
  themes?: Record<string, any>
  colors?: string[]
  onColorChange?: (colors: string[]) => any
}

export default function EchartThemeModal(props: EchartThemeModalProps) {
  const { onSelect, themes = PRE_DEFINED_THEMES, maxCount = 8 } = props
  const { useCustomSetting, onColorChange, colors = [] } = props

  const [group, setGroup] = useState(THEME_KEYS[0])
  const CUSTOM_TYPE = '自定义色'
  const menus = useCustomSetting ? [...THEME_KEYS, CUSTOM_TYPE] : THEME_KEYS

  // 自定义颜色
  const colorChange = (color: string, index: number) => {
    const newColor = [...new Array(maxCount)].map((_v, idx) => {
      if (idx === index) return color
      return colors[idx] || '#3399ff'
    })
    onColorChange?.(newColor)
  }

  const renderItem = (item: any, index: number) => {
    const _colors = item.theme.slice(0, maxCount)
    return (
      <div
        key={index}
        className='color-system-list'
        style={{ backgroundColor: item.backgroundColor }}
        onClick={() => onSelect?.(item)}
      >
        <span style={{ color: ['dark', 'chalk', 'purple-passion'].includes(item.name) ? '#eee' : '#555' }}>
          {item.title}
        </span>
        <div className='color-system-list-theme'>
          {_colors.map(color => (
            <div key={color} style={{ backgroundColor: color }} />
          ))}
        </div>
      </div>
    )
  }

  const renderCostom = () => (
    <div className='costom-color'>
      <ColorPanel colors={colors} isCustomColor onColorChange={colorChange} maxLength={maxCount} showIndex />
    </div>
  )

  return (
    <div className='color-system-panel'>
      <div className='left-panel'>
        {menus.map(type => (
          <div
            key={type}
            onClick={() => setGroup(type)}
            className={cn({
              'group-type-item': true,
              active: type === group
            })}
          >
            {THEME_GROUP_MAP[type] || type || '自定义色'}
          </div>
        ))}
      </div>
      <div className='right-panel'>
        {group !== CUSTOM_TYPE && themes[group]?.map(renderItem)}
        {group === CUSTOM_TYPE && renderCostom()}
      </div>
    </div>
  )
}
