// @import '../../../styles/variable.less';

.abi-form-schema-echart-theme-view {
  padding: 0 !important;

  & > .abi-form-schema-item-title {
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    height: 33px;
    max-height: 33px;
    margin: 0 !important;
    padding: 12px;
    line-height: 1;
    background-color: #fff;
    border-bottom: 1px solid @border-color-base;
    box-shadow: 1px 2px 3px rgba(#111, 0.045);
    cursor: pointer;
    user-select: none;
  }

  &-content {
    display: block;
    padding: 8px;

    .abi-color-select {
      width: 200px;
      border-color: transparent;
      &:hover {
        border: 1px solid @border-color-base;
      }
    }

    .color-panel {
      margin-bottom: 5px;
      border: 1px solid #f1f1f1;
      border-radius: 3px;
      .color-panel-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-left: 6px;
      }
    }

    .color-list,
    .color-panel {
      & > .abi-form-schema-item-title {
        margin-right: 0;
        padding: 5px 8px;
        border-bottom: 1px solid @border-color-base;
      }
    }

    .color-list {
      &:hover {
        border-radius: 3px;
        box-shadow: 0 0 4px rgba(#111, 0.24);
      }

      .color-list-row {
        display: flex;
        padding: 1px;
        > .abi-color-select {
          flex: 1;
        }
      }

      & > .color-list-item {
        display: flex;
        align-items: center;
        justify-content: space-between;

        > span:first-of-type {
          padding-left: 6px;
          color: #777;
          font-size: 14px;
        }
      }

      .color-sys-text {
        color: #579;
        font-size: 14px;
        cursor: pointer;
      }
    }
  }

  // 收起
  &.unfold {
    > .abi-form-schema-item-title {
      border-bottom: none;
      box-shadow: none;
    }
    > .abi-form-schema-echart-theme-view-content {
      display: none;
    }
  }
}

.abi-form-schema-echart-theme-view-color-overlay {
  z-index: 1001;

  width: 360px;
  background-color: #fff;
  border-radius: 3px;
  box-shadow: 0 0 12px rgba(#111, 0.12);

  .color-system-panel {
    display: flex;

    > .left-panel {
      width: 80px;
      border-right: 1px solid #f1f1f1;

      > .group-type-item {
        padding: 6px 8px;
        text-align: center;
        border-bottom: 1px solid @border-color-base;
        &:hover {
          color: @primary-color;
          background-color: #fefefe;
          cursor: pointer;
        }
        &.active {
          color: @primary-color;
        }
      }
    }

    > .right-panel {
      flex: 1;
      min-height: 160px;
      max-height: 240px;
      overflow-y: auto;

      > .color-system-list {
        display: flex;
        margin: 3px;
        padding: 3px;
        border-radius: 3px;
        // flex-direction: column;
        cursor: pointer;

        > span:first-of-type {
          width: 60px;
          text-align: center;
        }

        &:hover {
          box-shadow: 0 0 3px rgba(#111, 0.12);
        }

        > .color-system-list-theme {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          border-radius: 3px;

          > div {
            width: 20px;
            height: 20px;
            margin: 0 3px;
            border-radius: 5px;
            &:first-of-type {
              margin: 0;
            }
          }
        }
      }
    }

    // 自定义的颜色
    .costom-color {
      min-height: 200px;
      padding: 8px;
      > .color-list-row {
        display: flex;
        align-items: center;
        justify-content: space-around;
        margin-bottom: 4px;
      }
    }
  }
}
