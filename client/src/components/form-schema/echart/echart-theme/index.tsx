import './index.less'
import '../echart.less'

import { RightOutlined } from '@ant-design/icons'
import { useReactive } from 'ahooks'
import { Dropdown } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React, { useEffect, useMemo,useState } from 'react'

import ItemLayout from '../../components/item-layout'
import Title from '../../components/title'
import type { FormSchemaBase } from '../../type'
import BaseSetting from './base-setting'
import ColorPanel from './color-panel'
import ThemeModal from './echart-theme-modal'
import { DEFAULT_THEME, THEME_MAP } from './theme-const'
import { getInjectTheme, initDefaultValue } from './utils'

type BaseLineStyle<T = unknown> = {
  axisLine: { lineStyle: { color: string } } // 轴线条颜色
  axisTick: { lineStyle: { color: string } } // 轴刻度颜色
  splitLine: { lineStyle: { color: string } } // 分隔线颜色
} & T

export interface EchartThemeProps extends FormSchemaBase {
  unfold?: boolean
  value?: Partial<{
    backgroundColor: string // 背景色
    color: string[] // 调色盘颜色列表
    textStyle: { color: string } // 全局的字体色
    title:
      | {
          textStyle: { color: string } // 标题颜色
          subtextStyle: { color: string }
        }[]
      | Record<string, any>
    legend: {
      itemStyle: { color: string } // 图例每项的样式（优先取 color）
      lineStyle: { color: string } // 图例线条的样式（优先取 color，饼图会有）
      textStyle: { color: string } // 图例文字颜色
    }
    xAxis: BaseLineStyle<{ nameTextStyle: { color: string } }>[] | Record<string, any>
    yAxis: BaseLineStyle<{ nameTextStyle: { color: string } }>[] | Record<string, any>
    // 这个可以先不要管
    radar: BaseLineStyle<{ axisName: { color: string } }>[] | Record<string, any>
    series: {
      label: { color: string }
    }[]
  }>
  onChange: (value: EchartThemeProps['value']) => any
  colorMaxCount?: number // 最大颜色个数
}

/**
 * echart 的主题配置套件
 * @param props
 * @see https://echarts.apache.org/zh/theme-builder.html
 * @see https://github.com/apache/echarts-theme-builder/blob/master/app/scripts/main.js
 */
export default function EchartTheme(props: EchartThemeProps) {
  const { title, caption, icon, value, onChange, colorMaxCount = 8 } = props
  const wonderland = DEFAULT_THEME

  const [unfold, setUnfold] = useState(props.unfold)
  const state = useReactive({
    ...initDefaultValue(wonderland)
    // 默认色系
    // colors: wonderland?.theme || [],
    // backgroundColor: wonderland?.backgroundColor || 'transparent',
    // titleColor: wonderland?.textColor || '#222',
    // subTitleColor: wonderland?.subTitleColor || '#888',
    // textColor: wonderland?.textColor || '#444',
    // borderColor: wonderland?.borderColor || '#eee' // 描边颜色
  })

  // 是否是自定义颜色
  const systemColor = useMemo(() => {
    const item = THEME_MAP[state.colors.toString()]
    return item
  }, [state.colors])

  const isCustomColor = !systemColor?.title
  const systemColorTitle = isCustomColor ? '自定义 - ' : `${systemColor?.title} - `

  // 同步数据上去
  const updateNewValue = () => {
    onChange(getInjectTheme(state, value))
  }

  // 更新色系
  const updateColorSystem = (color: string, index: number) => {
    const colors = [...state.colors]
    colors[index] = color
    state.colors = colors
    updateNewValue()
  }

  // 选中系统颜色
  const updateColorByDefault = item => {
    const data = initDefaultValue(item)
    _.forEach(data, (val, key) => {
      state[key] = val
    })
    updateNewValue()
  }

  const onColorSelect = (val, item) => {
    state[item.field] = val
    updateNewValue()
  }

  useEffect(() => {
    state.colors = _.get(value, 'color', [])
    state.backgroundColor = value?.backgroundColor || 'transparent'
    state.textColor = _.get(value, '_theme.textColor', '#444')
    state.borderColor = _.get(value, '_theme.borderColor', '#eee')
    state.titleColor = _.get(value, '_theme.titleColor', '#333')
    state.subTitleColor = _.get(value, '_theme.subTitleColor', '#888')
  }, [value])

  return (
    <ItemLayout
      title={title}
      caption={caption || '图表的主题配置'}
      icon={icon}
      layout='column'
      className={cn({
        'abi-form-schema-echart-theme-view': true,
        unfold: !unfold
      })}
      onTitleClick={e => {
        e.stopPropagation()
        setUnfold(!unfold)
      }}
      extra={<RightOutlined rotate={unfold ? 90 : 0} />}
    >
      <div className='abi-form-schema-echart-theme-view-content'>
        <BaseSetting data={state} onColorSelect={onColorSelect} />
        <div className='color-list'>
          <Title
            title='色系'
            caption='调色盘颜色列表，如果系列没有设置颜色，则会依次循环从该列表中取颜色作为系列颜色'
            extra={
              <Dropdown
                trigger={['click']}
                placement='bottomRight'
                overlayClassName='abi-form-schema-echart-theme-view-color-overlay'
                overlay={<ThemeModal maxCount={colorMaxCount} onSelect={updateColorByDefault} />}
              >
                <span className='color-sys-text'>
                  {systemColorTitle}
                  色系方案
                </span>
              </Dropdown>
            }
          />
          <ColorPanel colors={state.colors} isCustomColor={isCustomColor} onColorChange={updateColorSystem} />
        </div>
      </div>
    </ItemLayout>
  )
}

EchartTheme.defaultValue = () => ({
  color: DEFAULT_THEME.theme || []
})
