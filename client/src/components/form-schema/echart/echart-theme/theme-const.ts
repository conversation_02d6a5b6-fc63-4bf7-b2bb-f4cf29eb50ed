import _ from 'lodash'

/**
 * echart 的色系
 */
export const PRE_DEFINED_THEMES = {
  sugo: [
    {
      name: 'westeros',
      title: '商务蓝',
      backgroundColor: 'transparent',
      titleColor: '#333',
      subTitleColor: '#aaa',
      textColor: '#555',
      borderColor: '#bbb',
      theme: ['#193093', '#05a8e9', '#47dfdf', '#7680f1', '#6582a5', '#a5e1ef']
    },
    {
      name: 'js',
      title: '简约',
      backgroundColor: 'transparent',
      titleColor: '#333',
      subTitleColor: '#aaa',
      textColor: '#555',
      borderColor: '#bbb',
      theme: ['#5790f5', '#59d9a8', '#5c7091', '#f7bc33', '#e9644e', '#ff97c2']
    },
    {
      name: 'ez',
      title: '稳重',
      backgroundColor: 'transparent',
      titleColor: '#333',
      subTitleColor: '#aaa',
      textColor: '#555',
      borderColor: '#bbb',
      theme: ['#6979f7', '#056de9', '#119bea', '#565f69', '#5752f6', '#995efb']
    },
    {
      name: 'shis',
      title: '时尚',
      backgroundColor: 'transparent',
      titleColor: '#333',
      subTitleColor: '#aaa',
      textColor: '#555',
      borderColor: '#bbb',
      theme: ['#005ff0', '#db69cc', '#1b99cf', '#babde4', '#3d46af', '#995efb']
    },
    {
      name: 'qings',
      title: '清新',
      backgroundColor: 'transparent',
      titleColor: '#333',
      subTitleColor: '#aaa',
      textColor: '#555',
      borderColor: '#bbb',
      theme: ['#125a10', '#178e3e', '#647e1c', '#7ac77a', '#047288', '#0086de']
    },
    {
      name: 'haiyang',
      title: '海洋',
      backgroundColor: 'transparent',
      titleColor: '#333',
      subTitleColor: '#aaa',
      textColor: '#555',
      borderColor: '#bbb',
      theme: ['#004b85', '#bc9855', '#0076ee', '#7f92ad', '#5c35d8', '#42b782']
    }
  ],
  // * @see https://github.com/apache/echarts-theme-builder/blob/master/app/scripts/main.js
  echart: [
    // echart 的配色
    {
      name: 'vintage',
      title: '复古',
      backgroundColor: '#fef8ef',
      titleColor: '#333',
      subTitleColor: '#aaa',
      textColor: '#555',
      borderColor: '#ccc',
      theme: [
        '#d87c7c',
        '#919e8b',
        '#d7ab82',
        '#6e7074',
        '#61a0a8',
        '#efa18d',
        '#787464',
        '#cc7e63'
        // '#724e58',
        // '#4b565b'
      ]
    },
    {
      name: 'dark',
      title: '黑暗',
      backgroundColor: '#333',
      titleColor: '#eee',
      subTitleColor: '#ccc',
      textColor: '#eee',
      theme: [
        '#dd6b66',
        '#759aa0',
        '#e69d87',
        '#8dc1a9',

        '#ea7e53',
        '#eedd78',
        '#73a373',
        '#73b9bc'

        // '#7289ab',
        // '#91ca8c',
        // '#f49f42'
      ]
    },
    {
      name: 'westeros',
      title: '年轻',
      backgroundColor: 'transparent',
      titleColor: '#516b91',
      subTitleColor: '#93b7e3',
      textColor: '#555',
      theme: ['#516b91', '#59c4e6', '#edafda', '#93b7e3', '#a5e7f0', '#cbb0e3']
    },
    {
      name: 'essos',
      title: '日落',
      backgroundColor: 'rgba(242,234,191,0.15)',
      titleColor: '#893448',
      subTitleColor: '#d95850',
      textColor: '#555',
      theme: ['#893448', '#d95850', '#eb8146', '#ffb248', '#f2d643', '#ebdba4']
    },
    {
      name: 'wonderland',
      title: '仙境',
      backgroundColor: 'transparent',
      titleColor: '#666',
      subTitleColor: '#999',
      textColor: '#555',
      theme: ['#4ea397', '#22c3aa', '#7bd9a5', '#d0648a', '#f58db2', '#f2b3c9']
    },
    {
      name: 'walden',
      title: '异乡',
      backgroundColor: 'rgba(252,252,252,0)',
      titleColor: '#666',
      subTitleColor: '#999',
      textColor: '#555',
      theme: ['#3fb1e3', '#6be6c1', '#626c91', '#a0a7e6', '#c4ebad', '#96dee8']
    },
    {
      name: 'chalk',
      title: '深渊',
      backgroundColor: '#293441',
      titleColor: '#fff',
      subTitleColor: '#ddd',
      textColor: '#eee',
      theme: ['#fc97af', '#87f7cf', '#f7f494', '#72ccff', '#f7c5a0', '#d4a4eb', '#d2f5a6', '#76f2f2']
    },
    {
      name: 'infographic',
      title: '低调',
      backgroundColor: 'transparent',
      titleColor: '#27727b',
      subTitleColor: '#aaa',
      textColor: '#555',
      theme: [
        '#C1232B',
        '#27727B',
        '#FCCE10',
        '#E87C25',
        '#B5C334',

        '#FE8463',
        '#9BCA63',
        '#FAD860',
        '#F3A43B',
        '#60C0DD'

        // '#D7504B',
        // '#C6E579',
        // '#F4E001',
        // '#F0805A',
        // '#26C0C0'
      ]
    },
    {
      name: 'macarons',
      title: '魔幻',
      backgroundColor: 'transparent',
      titleColor: '#008acd',
      subTitleColor: '#aaa',
      textColor: '#555',
      theme: [
        '#2ec7c9',
        '#b6a2de',
        '#5ab1ef',
        '#ffb980',
        '#d87a80',

        '#8d98b3',
        '#e5cf0d',
        '#97b552',
        '#95706d',
        '#dc69aa'

        // '#07a2a4',
        // '#9a7fd1',
        // '#588dd5',
        // '#f5994e',
        // '#c05050',
        // '#59678c',
        // '#c9ab00',
        // '#7eb00a',
        // '#6f5553',
        // '#c14089'
      ]
    },
    {
      name: 'roma',
      title: '浅淡',
      backgroundColor: 'transparent',
      titleColor: '#333',
      subTitleColor: '#aaa',
      textColor: '#555',
      theme: [
        // '#E01F54', '#001852', '#f5e8c8', '#b8d2c7', '#c6b38e',
        '#a4d8c2',
        '#f3d999',
        '#d3758f',
        '#dcc392',
        '#2e4783',

        '#82b6e9',
        '#ff6347',
        '#a092f1',
        '#0a915d',
        '#eaf889'

        // '#6699FF',
        // '#ff6666',
        // '#3cb371',
        // '#d5b158',
        // '#38b6b6'
      ]
    },
    {
      name: 'shine',
      title: '闪耀',
      backgroundColor: 'transparent',
      titleColor: '#333',
      subTitleColor: '#aaa',
      textColor: '#555',
      theme: ['#c12e34', '#e6b600', '#0098d9', '#2b821d', '#005eaa', '#339ca8', '#cda819', '#32a487']
    },
    {
      name: 'purple-passion',
      title: '失语',
      backgroundColor: 'rgba(91,92,110,1)',
      titleColor: '#fff',
      subTitleColor: '#ccc',
      textColor: '#eee',
      borderColor: '#fff',
      theme: ['#8a7ca8', '#e098c7', '#8fd3e8', '#71669e', '#cc70af', '#7cb4cc']
    }
  ],
  design: [
    {
      name: 'Dust Red',
      title: '薄暮',
      backgroundColor: 'transparent',
      titleColor: '#333',
      subTitleColor: '#aaa',
      textColor: '#555',
      borderColor: '#bbb',
      theme: ['#ffccc7', '#ffa39e', '#ff7875', '#ff4d4f', '#f5222d', '#cf1322', '#a8071a', '#820014']
    },
    {
      name: 'Volcano',
      title: '火山',
      backgroundColor: 'transparent',
      titleColor: '#333',
      subTitleColor: '#aaa',
      textColor: '#555',
      borderColor: '#bbb',
      theme: ['#ffd8bf', '#ffbb96', '#ff9c6e', '#ff7a45', '#fa541c', '#d4380d', '#ad2102', '#871400']
    },
    {
      name: 'Sunset Orange',
      title: '日暮',
      backgroundColor: 'transparent',
      titleColor: '#333',
      subTitleColor: '#aaa',
      textColor: '#555',
      borderColor: '#bbb',
      theme: ['#ffe7ba', '#ffd591', '#ffc069', '#ffa940', '#fa8c16', '#d46b08', '#ad4e00', '#873800']
    },
    {
      name: 'Polar Green',
      title: '极光绿',
      backgroundColor: 'transparent',
      titleColor: '#333',
      subTitleColor: '#aaa',
      textColor: '#555',
      borderColor: '#bbb',
      theme: ['#d9f7be', '#b7eb8f', '#95de64', '#73d13d', '#52c41a', '#389e0d', '#237804', '#135200']
    },
    {
      name: 'Cyan',
      title: '明青',
      backgroundColor: 'transparent',
      titleColor: '#333',
      subTitleColor: '#aaa',
      textColor: '#555',
      borderColor: '#bbb',
      theme: ['#b5f5ec', '#87e8de', '#5cdbd3', '#36cfc9', '#13c2c2', '#08979c', '#006d75', '#00474f']
    },
    {
      name: 'Daybreak Blue',
      title: '拂晓蓝',
      backgroundColor: 'transparent',
      titleColor: '#333',
      subTitleColor: '#aaa',
      textColor: '#555',
      borderColor: '#bbb',
      theme: ['#bae7ff', '#91d5ff', '#69c0ff', '#40a9ff', '#1890ff', '#096dd9', '#0050b3', '#003a8c']
    },
    {
      name: 'Geek Blue',
      title: '极客蓝',
      backgroundColor: 'transparent',
      titleColor: '#333',
      subTitleColor: '#aaa',
      textColor: '#555',
      borderColor: '#bbb',
      theme: ['#d6e4ff', '#adc6ff', '#85a5ff', '#597ef7', '#2f54eb', '#1d39c4', '#10239e', '#061178']
    },
    {
      name: 'Golden Purple',
      title: '酱紫',
      backgroundColor: 'transparent',
      titleColor: '#333',
      subTitleColor: '#aaa',
      textColor: '#555',
      borderColor: '#bbb',
      theme: ['#efdbff', '#d3adf7', '#b37feb', '#9254de', '#722ed1', '#531dab', '#391085', '#22075e']
    },
    {
      name: 'Magenta',
      title: '法洋红',
      backgroundColor: 'transparent',
      titleColor: '#333',
      subTitleColor: '#aaa',
      textColor: '#555',
      borderColor: '#bbb',
      theme: ['#ffd6e7', '#ffadd2', '#ff85c0', '#f759ab', '#eb2f96', '#c41d7f', '#9e1068', '#780650']
    },
    {
      name: 'Bown',
      title: '抱熊棕',
      backgroundColor: 'transparent',
      titleColor: '#333',
      subTitleColor: '#aaa',
      textColor: '#555',
      borderColor: '#bbb',
      theme: ['#ffefd9', '#f2d0a7', '#e6b17a', '#d99250', '#cd732b', '#a6551b', '#803a0e', '#592305']
    }
  ]
}

export const DEFAULT_THEME = PRE_DEFINED_THEMES.sugo[1]

export const THEME_GROUP_MAP = {
  sugo: '内置配色',
  design: '纯色系列',
  echart: '图表官方'
}

// 用于渲染时排序
export const THEME_KEYS = ['sugo', 'design', 'echart']

export const THEME_MAP = (() => {
  const arr = _.flatMap(_.values(PRE_DEFINED_THEMES))
  return arr.reduce(
    (obj, val) => ({
      ...obj,
      [val.theme.slice(0, 8).toString()]: val
    }),
    {}
  )
})()
