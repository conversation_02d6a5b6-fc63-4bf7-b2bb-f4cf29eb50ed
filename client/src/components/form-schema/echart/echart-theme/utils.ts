import Color from 'color'
import _ from 'lodash'

export const initDefaultValue = data => ({
  colors: data?.theme || [],
  backgroundColor: data?.backgroundColor || 'transparent',
  titleColor: data?.textColor || '#222',
  subTitleColor: data?.subTitleColor || '#888',
  textColor: data?.textColor || '#444',
  borderColor: data?.borderColor || '#ccc' // 描边颜色
})

export const getInjectTheme = (data, value) => {
  const newValue = _.cloneDeep({ ...value }) // 不 copy 有问题，未解

  newValue.backgroundColor = data.backgroundColor
  newValue.color = data.colors.slice()

  const lineColor = Color(data.borderColor).alpha(0.3).toString()

  _.set(newValue, 'textStyle.color', data.textColor)
  _.set(newValue, 'legend.textStyle.color', data.textColor)

  // 缓存字段内容
  _.set(newValue, '_state.theme.colors', newValue.color)
  _.set(newValue, '_state.theme.textColor', data.textColor)
  _.set(newValue, '_state.theme.borderColor', data.borderColor)
  _.set(newValue, '_state.theme.titleColor', data.titleColor)
  _.set(newValue, '_state.theme.subTitleColor', data.subTitleColor)

  // 标题
  if (value?.title) {
    if (_.isArray(value?.title)) {
      newValue.title = newValue.title.map(item => {
        const newItem = { ...item }
        _.set(newItem, 'textStyle.color', data.titleColor)
        _.set(newItem, 'subtextStyle.color', data.subTitleColor)
        return newItem
      })
    } else {
      _.set(newValue.title, 'textStyle.color', data.titleColor)
      _.set(newValue.title, 'subtextStyle.color', data.subTitleColor)
    }
  }

  // 坐标轴
  // eslint-disable-next-line @typescript-eslint/no-extra-semi
  ;['xAxis', 'yAxis'].forEach(key => {
    if (!value?.[key]) return

    if (_.isArray(value[key])) {
      newValue[key] = newValue[key].map(item => {
        const newItem = { ...item }
        _.set(newItem, 'nameTextStyle.color', data.textColor)
        _.set(newItem, 'axisLine.lineStyle.color', data.borderColor)
        _.set(newItem, 'axisTick.lineStyle.color', lineColor)
        _.set(newItem, 'splitLine.lineStyle.color', lineColor)
        return newItem
      })
    } else {
      _.set(newValue[key], 'nameTextStyle.color', data.textColor)
      _.set(newValue[key], 'axisLine.lineStyle.color', data.borderColor)
      _.set(newValue[key], 'axisTick.lineStyle.color', lineColor)
      _.set(newValue[key], 'splitLine.lineStyle.color', lineColor)
    }
  })

  // label 上的文字
  if (_.isArray(value?.series)) {
    newValue.series = newValue.series?.map(item => {
      const newItem = { ...item }
      _.set(newItem, 'label.color', data.textColor)
      return newItem
    })
  } else {
    _.set(newValue, 'series.label.color', data.textColor)
  }

  return newValue
}
