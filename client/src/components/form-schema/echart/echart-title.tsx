import './echart.less'

import _ from 'lodash'
import React from 'react'

import { FONT_DEFAULT } from '../const'
import { FormSchema } from '../index'
import type { FormSchemaBase, FormSchemaType } from '../type'

type Algin = 'center' | 'left' | 'right' | 'auto'

export interface EchartTitleProps extends FormSchemaBase {
  // 映射到 options.title
  unfold?: boolean // 是否展开
  // 支持多个 title 的情况
  // mode?: 'default' | 'multiple', // 单个或复数 title
  value?:
    | {
        show: boolean // 是否显示
        text: string // 支持 \n
        subtext: string
        left: Algin
        top: Algin
        right: Algin
        bottom: Algin
        backgroundColor: string
        textAlign: Algin
        textStyle: {
          color: string
          fontWeight: string
          fontFamily: string
          fontSize: number
          // 目前这两个 font 组件未支持
          lineHeight: number
          fontStyle: string
        }
        subtextStyle: {
          color: string
          fontWeight: string
          fontFamily: string
          fontSize: number
          // 目前这两个 font 组件未支持
          lineHeight: number
          fontStyle: string
        }
      }
    | any[]
  onChange: (value: EchartTitleProps['value']) => any
  // schema?: Record<string, FormSchemaBase>
}

/**
 * echart 的 title 封装
 * 是一个自定义的 schema 组合
 * @param props
 * @see https://echarts.apache.org/zh/option.html#title
 */
export default function EchartTitle(props: EchartTitleProps) {
  const { title, caption, icon, unfold = false, value, onChange, valuePath, type } = props
  const listValuePath = _.last(valuePath?.split('.')) || ''
  const isMultiple = type === 'echart-titles'

  const panel: FormSchemaType = {
    show: {
      title: '显示标题',
      type: 'switch',
      defaultValue: true,
      openText: '显示',
      closeText: '隐藏',
      valuePath: 'show'
    },
    textAlign: {
      title: '整体对齐方式',
      caption: '整体（包括 text 和 subtext）的水平对齐。',
      type: 'select',
      defaultValue: 'auto',
      valuePath: 'textAlign',
      options: [
        { value: 'auto', label: '自动' },
        { value: 'left', label: '左' },
        { value: 'center', label: '居中' },
        { value: 'right', label: '右' }
      ]
    },
    backgroundColor: {
      title: '标题背景色',
      caption: '标题背景色，默认透明',
      type: 'color',
      defaultValue: 'transparent',
      valuePath: 'backgroundColor'
    },
    distance: {
      title: '文本距离',
      caption: '标题组件离容器四方的距离。',
      type: 'distance',
      defaultValue: {},
      valuePath: ''
    },
    tabs: {
      title: '',
      type: 'tabs',
      algin: 'center',
      valuePath: '',
      children: [
        {
          title: '标题',
          children: {
            text: {
              title: '主标题文本',
              type: 'text-area',
              defaultValue: undefined,
              maxLength: 300,
              valuePath: 'text'
            },
            textStyle: {
              title: '标题',
              type: 'font',
              defaultValue: FONT_DEFAULT,
              valuePath: 'textStyle'
            }
          }
        },
        {
          title: '子标题',
          children: {
            subtext: {
              title: '子标题文本',
              type: 'text-area',
              defaultValue: undefined,
              maxLength: 300,
              valuePath: 'subtext'
            },
            subtextStyle: {
              title: '子标题',
              type: 'font',
              defaultValue: FONT_DEFAULT,
              valuePath: 'subtextStyle'
            }
          }
        }
      ]
    }
  }

  const innerSchema = {
    group: {
      title,
      unfold,
      type: 'group',
      icon,
      caption: caption || '标题组件，包含主标题和副标题',
      valuePath: '',
      children: isMultiple
        ? {
            list: {
              title: '',
              valuePath: listValuePath,
              type: 'list',
              maxItem: 3,
              tabPrefix: '标题',
              mode: 'tabs',
              defaultValue: [],
              // defaultValue: [getInitDefaultValue(panel)],
              children: panel
            }
          }
        : panel
    }
  }

  // 兼容旧数据
  const _value = (() => {
    if (isMultiple) return { [listValuePath]: value }
    if (_.isArray(value)) return value[0]
    return value
  })()

  return (
    <FormSchema
      className='abi-form-schema-echart-title-view abi-form-schema-echart-view'
      value={_value}
      schema={innerSchema}
      onChange={onChange}
    />
  )
}

EchartTitle.defaultValue = ({ type }) => {
  const value = {
    show: true,
    textAlign: 'auto',
    backgroundColor: 'transparent',
    text: '标题',
    textStyle: {
      fontSize: 18,
      // lineHeight: 18,
      fontFamily: 'Microsoft Yahei',
      fontWeight: 'normal',
      fontStyle: 'normal',
      color: '#222'
    },
    subtext: '',
    subtextStyle: {
      fontSize: 13,
      // lineHeight: 3,
      fontFamily: 'Microsoft Yahei',
      fontWeight: 'normal',
      fontStyle: 'normal',
      color: '#555'
    }
  }
  if (type === 'echart-titles') return [value]
  return value
}
