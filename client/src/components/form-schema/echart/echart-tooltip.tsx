import './echart.less'

import React from 'react'

import { FormSchema } from '../index'
import type { FormSchemaBase, FormSchemaType } from '../type'

export interface EchartTooltipProps extends FormSchemaBase {
  unfold?: boolean // 是否展开
  value?: {}
  onChange: (value: EchartTooltipProps['value']) => any
  // schema?: Record<string, FormSchemaBase>
}

/**
 * echart 图例配置套件
 * @param props
 * @see https://echarts.apache.org/zh/option.html#tooltip
 */
export default function EchartTooltip(props: EchartTooltipProps) {
  const { title, caption, icon, unfold = false, value, onChange } = props

  const panel: FormSchemaType = {
    show: {
      title: '显示提示框',
      type: 'switch',
      defaultValue: true,
      openText: '显示',
      closeText: '隐藏',
      valuePath: 'show'
    },
    tabs: {
      title: '',
      type: 'tabs',
      children: [
        {
          title: '触发',
          children: {
            trigger: {
              title: '触发类型',
              type: 'select',
              defaultValue: 'item',
              valuePath: 'trigger',
              options: [
                { value: 'item', label: '数据项' },
                { value: 'axis', label: '坐标轴' },
                { value: 'none', label: '无' }
              ]
            },
            triggerOn: {
              title: '触发条件',
              type: 'select',
              defaultValue: 'mousemove|click',
              valuePath: 'triggerOn',
              options: [
                { value: 'mousemove', label: '鼠标移动' },
                { value: 'click', label: '鼠标点击' },
                { value: 'mousemove|click', label: '同时鼠标移动和点击' },
                { value: 'none', label: '无' }
              ]
            }
          }
        },
        {
          title: '指示器',
          children: {
            type: {
              title: '指示器类型',
              type: 'select',
              defaultValue: 'line',
              valuePath: 'axisPointer.type',
              options: [
                { value: 'line', label: '直线' },
                { value: 'shadow', label: '阴影' },
                { value: 'cross', label: '十字准星' },
                { value: 'none', label: '无' }
              ]
            },
            axis: {
              title: '指示器坐标轴',
              type: 'select',
              defaultValue: 'line',
              valuePath: 'axisPointer.axis',
              options: [
                { value: 'auto', label: 'auto' },
                { value: 'x', label: 'x' },
                { value: 'y', label: 'y' },
                { value: 'radius', label: 'radius' },
                { value: 'angle', label: 'angle' }
              ]
            },
            groups: {
              type: 'group',
              title: '指示器文本',
              valuePath: '',
              children: {
                show: {
                  title: '显示文本标签',
                  type: 'switch',
                  defaultValue: true,
                  openText: '显示',
                  closeText: '隐藏',
                  valuePath: 'axisPointer.label.show'
                },
                precision: {
                  title: '小数点精度',
                  type: 'number',
                  caption: '文本标签中数值的小数点精度，默认根据当前轴的值自动判断。也可以指定如 2 表示保留两位小数。',
                  defaultValue: undefined,
                  valuePath: 'axisPointer.label.precision'
                },
                formatter: {
                  title: '文本格式化器',
                  type: 'code',
                  defaultValue: 'function formatter (params) {\n  return params.value \n}',
                  valuePath: 'axisPointer.label.formatter'
                }
              }
            }
          }
        },
        {
          title: '样式',
          children: {
            font: {
              title: '文本',
              type: 'font',
              defaultValue: {},
              valuePath: 'itemStyle'
            },
            backgroundColor: {
              title: '提示框背景',
              type: 'color',
              defaultValue: 'transparent',
              valuePath: 'backgroundColor'
            },
            borderColor: {
              title: '提示框边框颜色',
              type: 'color',
              defaultValue: 'transparent',
              valuePath: 'borderColor'
            },
            borderWidth: {
              title: '提示框边框线宽',
              type: 'slider',
              min: 0,
              max: 20,
              suffix: 'px',
              defaultValue: 0,
              layout: 'row',
              valuePath: 'borderWidth'
            }
          }
        }
      ]
    }
  }

  const innerSchema = {
    group: {
      title,
      unfold,
      type: 'group',
      icon,
      caption: caption || '提示框组件',
      valuePath: '',
      children: panel
    }
  }

  return (
    <FormSchema
      className='abi-form-schema-echart-tooltip-view abi-form-schema-echart-view'
      value={value}
      schema={innerSchema}
      onChange={onChange}
    />
  )
}

EchartTooltip.defaultValue = () => ({
  show: true,
  trigger: 'item',
  triggerOn: 'mousemove|click',
  itemStyle: {},
  backgroundColor: '#fff',
  axisPointer: {
    type: 'line',
    axis: 'auto'
  }
})
