### 支持的**基础表单**类型：

- `text`：文本输入框，支持定制前后缀。
  ```ts
  type Text = {
    prefix?: string // 前缀
    suffix?: string // 后缀
    maxLength?: number
  }
  ```
- `text-area`：多行文本输入框。
  ```ts
  type TextArea = {
    maxLength?: number
  }
  ```
- `number`：数值输入框，支持范围，支持百分比输入，支持前缀，后缀。
  ```ts
  type Number = {
    max?: number
    min?: number
    step?: number // 微调间距
    prefix?: string
    suffix?: string
  }
  ```
- `percent`：百分比、绝对值输入框。
- `select`：选择器。支持开启过滤和自定义输入。当需要设置字体时，可开启渲染字体功能。
  ```ts
  type Select = {
    mode?: 'multiple' | undefined // 多选还是单选
    defaultActiveFirst?: boolean // 默认为 false，是否默认选择第一个
    options?: {
      label: string
      value: string
      [key: string]: any
    }[]
  }
  ```
- `color`：单颜色选择器。
  ```ts
  type Color = {
    mode?: 'rgb' | 'hex' | 'rgba' // 模式，默认 rgba
  }
  ```
- `radio`：单选框。
  ```ts
  type Radio = {
    algin?: 'left' | 'center' | 'right' // 对齐方式
    layout?: 'row' | 'column'
    options?: {
      value: string
      label: string // 使用图标时，可以不写 label
      icon?: string // 支持图标
      [key: string]: any
    }[]
  }
  ```
- `checkbox`：复选框或可选框。
  ```ts
  type Checkbox = {
    options?: {
      label: string
      value: string
      [key: string]: any
    }[]
  }
  ```
- `switch`：开关，⽀持选择是否展示⽂字状态。
  ```ts
  type Switch = {
    openText?: string
    closeText?: string
    valueMap?: {
      // value 映射
      [key: string]: boolean
    }
  }
  ```
- `slider`：为滑动输入条，⽀持定制步⻓，支持范围、最大值和最小值。
  ```ts
  type Slider = {
    layout?: 'row' | 'column'
    max?: number
    min?: number
    step?: number
    showValue?: boolean // 用 suffix 可以将这个设置为 false
    // prefix 和 suffix 都支持模版语法，有两个变量 value 和 _ 可用
    // suffix: '{{ _.round(value * 100) }}%'
    prefix?: string
    suffix?: string
  }
  ```
- `date`：时间选择，支持范围选择，格式。支持时间基准的加减动态时间表达式默认值，支持模版语法，内部注入了 dayjs 和 value。例如 `'{{ dayjs().startOf('day').add(-20, 's').format() }}'`。
  ```ts
  type Date = {
    // 支持范围，为 range 时 defaultValue 为数组
    mode?: 'default' | 'range'
    allowClear?: boolean
    placeholder?: string | [string, string]
    format?: string // 格式化
    showTime?: boolean // 是否显示 time
  }
  ```
- `notify-btn`：通知组件按钮，一般用于控制自定义组件弹出编辑界面。  
  PubSub msg 格式为 notify:$compKey/$eventKey  
  payload 为 { onChange: (nextVal: any) => any}。
  ```ts
  type NotifyBtn = {
    btnTitle?: string // 默认为 '编辑'
    eventKey?: string // 默认为 'edit'
  }
  ```
