### 支持内置的图表**套件表单**类型：

- `echart-base-config`：echart 的基础设置封装的套件，是个对象。
  ```ts
  type T = {
    colorMaxCount?: number // 最大的颜色个数
    // 忽略字段，忽略的不显示出来
    ignore?: ('showTitle' | 'title' | 'titleAlign' | 'fontSize' | 'fontFamily' | 'color')[]
  }
  ```
- `echart-title`：echart 的 title 配置，自带默认值，是个对象。
  ```ts
  type T = {
    unfold?: boolean // 默认是否展开分组
  }
  ```
- `echart-titles`：echart 的 title 复数配置，自带默认值，是个数组，多标题是用到。
  ```ts
  type T = {
    unfold?: boolean // 默认是否展开分组
  }
  ```
- `echart-xAxis`：echart 的 x 坐标轴配置，自带默认值。
  ```ts
  type T = {
    axis?: 'x' | 'y' // 轴类型
    unfold?: boolean // 默认是否展开分组
  }
  ```
- `echart-xAxes`：echart 的 x 坐标轴复数配置，自带默认值，是个数组。
  ```ts
  type T = {
    axis?: 'x' | 'y' // 轴类型
    maxItem?: number // 最大的轴个数
    unfold?: boolean // 默认是否展开分组
  }
  ```
- `echart-yAxis`：echart 的 y 坐标轴配置，自带默认值。
  ```ts
  type T = {
    axis?: 'x' | 'y' // 轴类型
    unfold?: boolean // 默认是否展开分组
  }
  ```
- `echart-yAxes`：echart 的 y 坐标轴复数配置，自带默认值，是个数组。
  ```ts
  type T = {
    axis?: 'x' | 'y' // 轴类型
    maxItem?: number // 最大的轴个数
    unfold?: boolean // 默认是否展开分组
  }
  ```
- `echart-legend`：echart 的 图例，自带默认值。
- `echart-grid`：echart 的网格线，自带默认值。
- `echart-tooltip`：echart 的提示框，自带默认值。
- `echart-theme`：echart 的主题板，自带默认值。
  ```ts
  type T = {
    colorMaxCount?: number // 最大颜色个数，默认 8
  }
  ```
- `echart-animation`：echart 的主题板，自带默认值。
