### 支持的样式**套件表单**类型：

- `distance`：方位套件。
  ```ts
  type T = {
    // 忽略字段，忽略的不显示出来
    ignore: ('absolute' | 'percent' | 'enum')[]
  }
  ```
- `font`：字体套件。
  ```ts
  type T = {
    // 忽略字段，忽略的不显示出来
    ignore?: ('fontSize' | 'fontFamily' | 'fontWeight' | 'fontStyle' | 'lineHeight' | 'color')[]
  }
  ```
- `margin/padding`：边距套件。
- `border`：边框套件。
- `background-image`：背景图片套件。
  ```ts
  type T = {
    /** 是否开启剪切功能 */
    enableCropper?: boolean
  }
  ```
- `background`：背景套件。
  ```ts
  type T = {
    // 忽略功能
    ignore?: ('linear-gradient' | 'background-color')[]
  }
  ```
- `box-shadow`：阴影套件。
  ```ts
  type T = {
    mode?: 'box-shadow' | 'text-shadow'
  }
  ```

> 这些默认值都为 style 对应的属性，如

      ```js
      defaultValue: {
        marginRight: 10
      }
      ```

> 注：font、margin/padding、border 如果要支持根对象，需要设置 defaultValue='' 默认值对应的宽度，大小等应该是 number 类型，而是不是 string，例如 marginRight: 10，错误的为 marginRight: '10px'
