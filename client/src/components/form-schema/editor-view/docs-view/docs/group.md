### 支持的**容器表单**类型：

- `tabs`：标签组。支持嵌套 group，menu，list 等
  ```ts
  type Tabs = {
    direction?: 'vertical' | 'horizontal' // tab 位置方向
  }
  ```
- `group`：折叠面板，支持嵌套的层级。
  ```ts
  type Group = {
    layout?: 'row' | 'column'
    unfold?: boolean // 默认是否展开
  }
  ```
- `list`：列表表单。
  ```ts
  type List = {
    minItem?: number // 最小的子项目
    maxItem?: number // 最大的子项目
    mode?: 'list' | 'tabs' // 列表平铺还是 tabs 那种布局
    tabPrefix?: string // tab 上的前缀
  }
  ```
  1. list 需要带一个 valuePath，子元素的 valuePath 只作用于 list 的 valuePath。
  2. list 需要带一个 defaultValue，子元素的 defaultValue 也需要。
     > 容器支持 children 属性，可以无限嵌套，但是不建议超过 3 层，因为界面会挤在一起。 BUG：目前发现多个容器嵌套有问题，尽量别嵌套。
- `object`：object 类型的套件，目前界面上没有东西显示出来，（后面会显示一个变量编辑器），常用于设置默认值。
