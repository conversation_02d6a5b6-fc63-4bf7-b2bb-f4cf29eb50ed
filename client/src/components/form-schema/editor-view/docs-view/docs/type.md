### 基础类型

每个表单组件的基础类型：

```ts
export interface FormSchemaBase {
  title?: string // 字段显示名称
  icon?: string // iconfont 图表库的图标，不是 antd 图表，会显示在 title 前
  type: FormKey | (string & {}) // 类型（必填）
  name?: string // 字段名称（英文）
  defaultValue?: string | number | any | object | any[] // 默认值
  description?: string // 配置描述
  valuePath?: string // 映射到字段的 value path
  array?: {
    valuePath?: string // 如果有 array.valuePath 会作为父级，与 valuePath 拼接成一个遍历现象
    iterate?: number // 数组模拟迭代次数
  }
  caption?: string | any // 配置项标注（注释），支持 markdown
  nodes?: string // 注释内容，不会显示在界面上
  // TODO: 目前未实现，显示规则，关联联动时用，对标的是 showInPanel
  displayRules?: {
    // 定义通过逻辑时是显示还是隐藏
    visible?: boolean
    // 定义条件的与或关系
    logicalType: 'or' | 'and'
    // 定义条件的逻辑关系
    conditions: {
      path: string
      op: 'eq' | 'ne' | 'gt' | 'lt' | 'gte' | 'lte' | 'in' | 'nin'
      value: string
    }[]
  }
  order?: number // 位置排序用
  ignore?: string[] // 部分套件有，用于忽略某些配置项
  hidden?: boolean // 隐藏掉，界面不渲染出来，常用于设置默认值
}
```

### 备注

- 私有类型，请看各自的定义。
- 默认的表单排序是按照 object-key 的排序规则，有些浏览器显示可能不一致，这时你要加上 order 字段。
