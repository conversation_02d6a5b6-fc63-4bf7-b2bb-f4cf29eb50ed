import React, { useState } from 'react'

import CustomTabs from '@/components/customs/custom-tabs'
import { FormSchema, getInitDefaultValue } from '@/components/form-schema'
import Markdown from '@/components/markdown-view'

import BASE_DOCS from './docs/base.md'
import COMPLEX_DOCS from './docs/complex.md'
import ECHART_DOCS from './docs/echart.md'
import FONT_DOCS from './docs/font.md'
import GROUP_DOCS from './docs/group.md'


/**
 * 示例渲染器
 * @param props
 */
function ExampleRenderer(props: { schema: any; doc: string }) {
  const { schema, doc } = props
  const [value, setValue] = useState(() => getInitDefaultValue(schema))
  return (
    <div className='example-form-schema'>
      <div className='example-content'>
        <pre>{JSON.stringify(schema, null, 2)}</pre>
        <FormSchema value={value} schema={schema} onChange={setValue} />
      </div>
      <Markdown>{doc}</Markdown>
    </div>
  )
}

/**
 *
 */
export default function Example() {
  const tabs = [
    { title: '基础表单', key: 'base' },
    { title: '组合套件', key: 'font' },
    { title: '图表套件', key: 'echart' },
    { title: '容器表单', key: 'group' },
    { title: '高级表单', key: 'complex' }
  ]
  const options = [
    { value: 'a', label: 'A' },
    { value: 'b', label: 'B' },
    { value: 'c', label: 'C' },
    { value: 'd', label: 'D' }
  ]

  const baseSchema = {
    text: {
      title: '标题',
      type: 'text',
      defaultValue: '我是 text',
      valuePath: 'text'
    },
    textArea: {
      title: '多行文本',
      type: 'text-area',
      defaultValue: '我是 多行文本',
      valuePath: 'textArea'
    },
    number: {
      title: '大小',
      type: 'number',
      defaultValue: 20,
      valuePath: 'number',
      caption: '注释',
      prefix: '增加',
      suffix: '元'
    },
    percent: {
      title: '百分比',
      type: 'percent',
      defaultValue: 10,
      valuePath: 'percent'
    },
    select: {
      title: '选择',
      type: 'select',
      defaultValue: 'a',
      valuePath: 'select',
      mode: 'multiple',
      options
    },
    color: {
      title: '颜色',
      type: 'color',
      icon: '文字颜色',
      defaultValue: '#f45',
      valuePath: 'textColor'
    },
    radio: {
      title: '单选',
      type: 'radio',
      valuePath: 'radio',
      defaultValue: 'a',
      options
    },
    radioIcon: {
      title: '单选（图标）',
      type: 'radio',
      valuePath: 'radio2',
      defaultValue: 'a',
      options: [
        { value: 'a', icon: '预览' },
        { value: 'b', icon: '商城' },
        { value: 'c', icon: '学院' },
        { value: 'd', icon: '多行' }
      ]
    },
    checkbox: {
      title: '多选',
      type: 'checkbox',
      valuePath: 'checkbox',
      defaultValue: ['a', 'b'],
      options
    },
    switch: {
      title: '开关',
      type: 'switch',
      defaultValue: false,
      openText: '开启',
      closeText: '关闭',
      valuePath: 'switch'
    },
    slice: {
      title: '滑块',
      type: 'slider',
      defaultValue: 10,
      valuePath: 'slider'
    },
    date: {
      title: '日期',
      type: 'date',
      format: 'YYYY-MM-DD HH:mm:ss',
      defaultValue: '{{ dayjs().startOf(`day`).add(-20, `s`).format() }}',
      valuePath: 'date'
    },
    notifyBtn: {
      type: 'notify-btn',
      btnTitle: '编辑模板',
      eventKey: 'edit',
      valuePath: 'value'
    }
  }

  const fontSchema = {
    distance: {
      title: '距离套件',
      type: 'distance',
      defaultValue: {},
      valuePath: 'distance'
    },
    font: {
      title: '字体套件',
      type: 'font',
      defaultValue: {
        fontSize: 16,
        fontFamily: 'Microsoft Yahei',
        fontWeight: 'normal',
        fontStyle: 'normal',
        color: '#222',
        lineHeight: 16
      },
      valuePath: 'style'
    },
    margin: {
      title: '边距套件',
      type: 'margin',
      defaultValue: {},
      valuePath: 'style'
    },
    border: {
      title: '边框套件',
      type: 'border',
      defaultValue: {},
      valuePath: 'style'
    },
    backgroundImage: {
      title: '背景图片套件',
      type: 'background-image',
      useUrl: true,
      enableCropper: true,
      defaultValue: {},
      valuePath: 'style'
    },
    background: {
      title: '背景套件',
      type: 'background',
      defaultValue: {},
      valuePath: 'style'
    },
    boxShadow: {
      title: '阴影套件',
      type: 'box-shadow',
      defaultValue: {},
      valuePath: 'style'
    }
  }

  const echartSchema = {
    echartBase: {
      title: 'echart 基础设置',
      type: 'echart-base-config',
      defaultValue: {},
      valuePath: 'options'
    },
    echartTitle: {
      title: 'echart 标题',
      type: 'echart-title',
      defaultValue: {},
      valuePath: 'options'
    },
    echartTitles: {
      title: 'echart 标题（多标题）',
      type: 'echart-titles',
      defaultValue: {},
      valuePath: 'options2.title'
    },
    echartAxis: {
      title: 'echart 坐标 x 轴',
      type: 'echart-xAxis',
      defaultValue: {},
      valuePath: 'options.xAxis'
    },
    echartXAxes: {
      title: 'echart 坐标 x 轴（多 x 轴）',
      type: 'echart-xAxes',
      defaultValue: [],
      valuePath: 'options2.xAxis'
    },
    echartYAxis: {
      title: 'echart 坐标 Y 轴',
      type: 'echart-yAxis',
      defaultValue: {},
      valuePath: 'options.yAxis'
    },
    echartYAxes: {
      title: 'echart 坐标 Y 轴（多 Y 轴）',
      type: 'echart-yAxes',
      defaultValue: [],
      valuePath: 'options2.yAxis'
    },
    echartLegend: {
      title: 'echart 图例',
      type: 'echart-legend',
      defaultValue: {},
      valuePath: 'options.legend'
    },
    echartGrid: {
      title: 'echart 网格',
      type: 'echart-grid',
      defaultValue: {},
      valuePath: 'options.grid'
    },
    echartTooltip: {
      title: 'echart 提示框',
      type: 'echart-tooltip',
      defaultValue: {},
      valuePath: 'options.tooltip'
    },
    echartTheme: {
      title: 'echart 主题',
      type: 'echart-theme',
      defaultValue: {},
      valuePath: 'options'
    },
    echartAnimation: {
      title: 'echart 动画',
      type: 'echart-animation',
      defaultValue: {},
      valuePath: 'options'
    }
  }

  const groupSchema = {
    tabs: {
      title: '标签组',
      type: 'tabs',
      hidden: !true,
      children: [
        {
          title: 'Tab 1',
          children: {
            text: {
              title: '标题',
              type: 'text',
              suffix: '元',
              defaultValue: '666',
              valuePath: 'tabs.a'
            }
          }
        },
        {
          title: 'Tab 2',
          children: {
            switch: {
              title: 'tab 里的开关',
              type: 'switch',
              defaultValue: false,
              openText: '开启',
              closeText: '关闭',
              valuePath: 'tabs.b'
            }
          }
        }
      ]
    },
    group1: {
      title: '组1',
      type: 'group',
      children: {
        switch: {
          title: '组里的开关',
          type: 'switch',
          defaultValue: false,
          openText: '开启',
          closeText: '关闭',
          valuePath: 'group1.a'
        }
      }
    },
    group2: {
      title: '组2',
      type: 'group',
      layout: 'row',
      children: {
        switch: {
          title: 'A',
          type: 'switch',
          defaultValue: false,
          openText: '开启',
          closeText: '关闭',
          valuePath: 'group2.a'
        },
        switch2: {
          title: 'B',
          type: 'switch',
          defaultValue: false,
          openText: '开启',
          closeText: '关闭',
          valuePath: 'group2.b'
        }
      }
    },
    list1: {
      title: '列表 A',
      type: 'list',
      valuePath: 'list1',
      mode: 'tabs',
      tabPrefix: '轴',
      defaultValue: [
        {
          text: '666',
          color: '#f34'
        }
      ],
      children: {
        text: {
          title: '标题',
          type: 'text',
          valuePath: 'text'
        },
        color: {
          title: '颜色',
          type: 'color',
          valuePath: 'color'
        }
      }
    },
    list2: {
      title: '列表 B',
      type: 'list',
      valuePath: 'list2',
      tabPrefix: '轴',
      defaultValue: [
        {
          text: '666',
          color: '#f34'
        }
      ],
      children: {
        text: {
          title: '标题',
          type: 'text',
          valuePath: 'text'
        },
        color: {
          title: '颜色',
          type: 'color',
          valuePath: 'color'
        }
      }
    }
  }

  const complexSchema = {
    code: {
      title: '代码编辑器',
      type: 'code',
      language: 'typescript',
      defaultValue: '/** 更新代码 */\nfunction add(a, b) {\n  return a + b\n}',
      valuePath: 'code'
    }
  }

  return (
    <CustomTabs
      className='example-tabs'
      options={tabs}
      tabsMap={{
        base: <ExampleRenderer schema={baseSchema} doc={BASE_DOCS} />,
        font: <ExampleRenderer schema={fontSchema} doc={FONT_DOCS} />,
        echart: <ExampleRenderer schema={echartSchema} doc={ECHART_DOCS} />,
        group: <ExampleRenderer schema={groupSchema} doc={GROUP_DOCS} />,
        complex: <ExampleRenderer schema={complexSchema} doc={COMPLEX_DOCS} />
      }}
    />
  )
}
