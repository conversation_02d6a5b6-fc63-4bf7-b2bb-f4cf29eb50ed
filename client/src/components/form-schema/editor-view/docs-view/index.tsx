import './index.less'

import { Drawer } from 'antd'
import React from 'react'

import CustomTabs from '@/components/customs/custom-tabs'
import DOCS from '@/components/form-schema/README.md'
import Markdown from '@/components/markdown-view'
import withRefModal from '@/components/with-ref-modal'

import TYPE_DOCS from './docs/type.md'
import Example from './example'

export interface DocsViewProps {
  title?: string
}

/**
 * 文档组件
 */
const DocsView = withRefModal<DocsViewProps>(props => {
  const { modal, visible, title = '表单模型文档' } = props

  const options = [
    { title: '示例代码', key: 'example' },
    { title: '文档说明', key: 'doc' },
    { title: '基础类型', key: 'type' }
  ]

  return (
    <Drawer
      open={visible}
      title={title}
      onClose={() => modal.hide()}
      placement='right'
      className='form-schema-editor-view-docs-drawer'
      getContainer={() => document.getElementById('abi-app-modal') || document.body}
      width={800}
      zIndex={2000}
    >
      <CustomTabs
        options={options}
        tabsMap={{
          example: <Example />,
          doc: <Markdown>{DOCS}</Markdown>,
          type: <Markdown>{TYPE_DOCS}</Markdown>
        }}
      />
    </Drawer>
  )
})

export default DocsView
