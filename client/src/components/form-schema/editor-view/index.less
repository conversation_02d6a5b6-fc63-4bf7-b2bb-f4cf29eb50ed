.form-schema-editor-view {
  display: flex;
  height: 800px;
  min-height: 500px;

  .schema-code {
    position: relative;
    flex: 1;
  }

  .schema-setting {
    width: 310px !important;
    min-width: 310px !important;
    border: 1px solid #f1f1f1;
    overflow: auto;
    margin: 0 16px;
  }

  // .config-code {
  //   width: 360px;
  //   max-width: 360px;
  // }

  .toolbox {
    position: absolute;
    z-index: 10;
    top: 30px;
    right: 20px;
    cursor: pointer;
    box-shadow: 1px 2px 12px rgba(#111, 0.12);
    user-select: none;
    border-radius: 3px;
    font-size: 14px;
    padding: 3px 6px;
    background-color: #fff;
    > div {
      margin-top: 3px;
      padding-top: 3px;
      border-top: 1px solid #f1f1f1;
      &:first-of-type {
        margin-top: 0;
        padding-top: 0;
        border: none;
      }
      &:hover {
        color: var(--primary-color);
      }
      .anticon {
        margin-right: 3px;
      }
    }
  }
}

.doc-popover-content {
  max-height: 80vh;
  overflow: auto;
  max-width: 640px;
  overflow-x: hidden;
}
