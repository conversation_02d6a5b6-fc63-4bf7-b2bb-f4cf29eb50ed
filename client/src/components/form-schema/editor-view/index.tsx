import './index.less'

import { BugOutlined,QuestionOutlined, RedoOutlined } from '@ant-design/icons'
import { Badge } from 'antd'
import cn from 'classnames'
import isEqual from 'fast-deep-equal'
import React, { CSSProperties, memo, useRef } from 'react'

import CodeEditer from '@/components/code-editor'
import { CustomPopover } from '@/components/customs/custom-popover'
import { FormSchema } from '@/components/form-schema'

// import DOCS from '@/components/form-schema/README.md'
// import Markdown from '@/components/markdown-view'
import DocsView from './docs-view'

export interface FormSchemaEditorProps {
  schema: Record<string, any>
  config: Record<string, any>
  verifyMessage?: string[]
  schemaCode: string
  configCode: string
  className?: string
  style?: CSSProperties
  onSchemaChange: (val: any) => any
  onRefresh: (val: any) => any
  onConfigChange: (val: any) => any
}

const FormSchemaMemo = memo((props: any) => <FormSchema {...props} />, isEqual)

/**
 * 表单模型设计器
 * @param props
 */
function FormSchemaEditor(props: FormSchemaEditorProps) {
  const { schemaCode, schema, config, verifyMessage, configCode, className, style } = props
  const { onSchemaChange, onRefresh, onConfigChange } = props

  const docsRef = useRef<{ show: Function }>()

  return (
    <section className={cn('form-schema-editor-view', className)} style={style}>
      <div className='schema-code'>
        <CodeEditer
          value={schemaCode}
          onChange={onSchemaChange}
          language='javascript'
          options={{
            minimap: { enabled: !true }
          }}
        />

        <div className='toolbox'>
          {/* <Popover
            trigger={['click']}
            placement='rightTop'
            content={
              <div className='doc-popover-content markdown-view'>
                <Markdown>{DOCS}</Markdown>
              </div>
            }
          > */}
          <div onClick={() => docsRef.current?.show()}>
            <QuestionOutlined />
            文档
          </div>
          {/* </Popover> */}

          {/* <CustomPopover
            trigger={['click']}
            placement='rightTop'
            content={<div className='doc-popover-content markdown-view'>多点击几次，更新有点延迟</div>}
          > */}
          <div onClick={onRefresh}>
            <RedoOutlined />
            刷新
          </div>
          {/* </CustomPopover> */}

          <CustomPopover
            trigger={['click']}
            placement='rightTop'
            content={
              <div className='doc-popover-content'>
                {verifyMessage?.map((text, index) => (
                  <li key={index}>{text}</li>
                ))}
              </div>
            }
          >
            <div>
              <BugOutlined />
              <Badge color='red' count={verifyMessage?.length} size='small'>
                错误
              </Badge>
            </div>
          </CustomPopover>
        </div>
      </div>

      <FormSchemaMemo className='schema-setting' schema={schema} value={config} onChange={onConfigChange} />
      {/* <CodeEditer className='config-code' value={configCode} options={{ lineNumbers: 'off' }} /> */}

      <DocsView ref={docsRef} />
    </section>
  )
}

export default memo(FormSchemaEditor, isEqual)
