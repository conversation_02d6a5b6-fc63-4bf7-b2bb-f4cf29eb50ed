import './style'

import cn from 'classnames'
import _ from 'lodash'
import React, { CSSProperties, useMemo } from 'react'

import ItemRenderer from './components/item-renderer'
import { FORM_MAP } from './const'
import type { FormSchemaBase, InjectComponentMap } from './type'
import { Provider } from './utils/context'

export interface FormSchemaProps<T = any> {
  /** 表单模型 */
  schema?: Record<string, FormSchemaBase>
  /** 表单联动的数据 */
  value?: T
  id?: string
  onChange: (value?: T) => T
  className?: string
  style?: CSSProperties
  /** 动态注入的组件 */
  injectComponentMap?: InjectComponentMap
  /**
   * 用于动态修饰单个 schema
   * 例如从 redux 里读取数据，插入到 type='select' 的  options 里实现动态 options
   */
  schemaModifyFn?: (key: string, schemaItem: FormSchemaBase) => FormSchemaBase
  /**
   * 需要获取组件数据时触发
   */
  getComponentData?: () => ({
    fieldMap: any[]
    data: any[]
  }) | undefined | void
}

/**
 * 表单模型渲染器入口
 * @param props
 */
export function FormSchema(props: FormSchemaProps) {
  const {
    schema = {},
    className = '',
    id = '',
    style,
    value,
    onChange,
    schemaModifyFn,
    getComponentData,
    injectComponentMap = {}
  } = props
  // if (_.isEmpty(schema)) return null

  const keys = useMemo(() => _.keys(schema).sort((a, b) => {
    const diff = _.get(schema[a], 'order', 999) - _.get(schema[b], 'order', 999)
    if (diff > 0) return 1
    if (diff < 0) return -1
    return 0
  }), [schema])

  return (
    <Provider value={injectComponentMap}>
      <div className={cn('abi-form-schema-main', className)} style={style} id={id}>
        {keys.map(key => {
          const innerSchema = schemaModifyFn?.(key, schema[key]) || schema[key]
          const comkey = key + innerSchema.name + innerSchema.title + innerSchema.valuePath
          const { type } = schema[key]
          const Com = FORM_MAP[type] || (() => <span />)

          if (!FORM_MAP[type]) {
            console.warn(`form-schema type=${type}, is not define`)
          }
          const fn = schema[key]?.displayHide
          // eslint-disable-next-line no-eval
          try {
            if (fn && eval(fn)(value)) return null
            if (schema[key].hidden) return null
          } catch (err) {
            return null
          }

          return (
            <ItemRenderer
              schema={innerSchema}
              key={comkey}
              value={value}
              Component={Com}
              onChange={onChange}
              getComponentData={getComponentData}
              injectComponentMap={injectComponentMap || ({} as any)}
            />
          )
        })}
      </div>
    </Provider>
  )
}

export default FormSchema
