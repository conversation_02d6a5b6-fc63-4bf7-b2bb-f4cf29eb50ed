// import UploadImage from '@/components/upload-file/upload-image'
import './background-image.less'

import { useReactive } from 'ahooks'
import { Select, Switch } from 'antd'
import React, { useEffect } from 'react'

import ItemLayout from '../components/item-layout'
import type { FormSchemaBase } from '../type'

export interface BackgroundImageProps extends FormSchemaBase {
  value?: {
    backgroundImage?: string
    backgroundRepeat?: 'no-repeat' | 'repeat'
    backgroundSize?: 'auto' | 'contain' | 'cover'
    backgroundAttachment?: 'scroll' | 'fixed'
  } // url
  onChange: (value?: BackgroundImageProps['value']) => any
  /** 是否开启剪切功能 */
  enableCropper?: boolean
}

/**
 * 背景图片套件
 * @param props
 */
export default function BackgroundImage(props: BackgroundImageProps) {
  const { title, caption, icon, value, onChange, enableCropper } = props
  const { injectComponentMap } = props

  const UploadImage = injectComponentMap?.UploadImage || (() => <div />)

  const state = useReactive({
    value: {} as BackgroundImageProps['value'],
    file: {} as Partial<{
      name: string
      url: string
      path: string
      origin: string
    }>
  })
  const options = [
    { value: 'auto', label: '自动' },
    { value: 'cover', label: '拉伸' },
    { value: 'contain', label: '平铺' }
  ]

  const onFileChange = (file: any) => {
    state.file = file
    onChange({
      ...value,
      backgroundImage: file ? `url(${file.path})` : undefined
    })
  }

  const onUpdate = (field: string, val: string) => {
    state[field] = val
    onChange({ ...value, [field]: val })
  }

  useEffect(() => {
    if (!state.file) state.file = {}
    if (!state.value) state.value = {}
    state.value.backgroundImage = value?.backgroundImage
    state.value.backgroundSize = value?.backgroundSize || 'auto'
    state.value.backgroundRepeat = value?.backgroundRepeat || 'no-repeat'
    // state.value.backgroundAttachment = value?.backgroundAttachment || 'scroll'

    if (value?.backgroundImage) {
      state.file.path = value.backgroundImage.replace(/^url\((.*)\).*/, '$1')
    } else {
      state.file.path = undefined
    }
  }, [value])

  return (
    <ItemLayout
      title={title}
      caption={caption}
      icon={icon}
      layout='column'
      className='abi-form-schema-background-image-view'
    >
      <div className='abi-form-schema-background-image-view-content'>
        <div className='left-panel'>
          <div>
            <span>重复</span>
            <Switch
              checkedChildren='开启'
              unCheckedChildren='关闭'
              checked={state.value?.backgroundRepeat === 'repeat'}
              onChange={val => onUpdate('backgroundRepeat', val ? 'repeat' : 'no-repeat')}
            />
          </div>
          <div>
            <span>填充</span>
            <Select
              options={options}
              placeholder='请选择'
              value={state.value?.backgroundSize}
              onChange={val => onUpdate('backgroundSize', val)}
            />
          </div>
        </div>
        <div className='right-panel'>
          <UploadImage
            title='上传图片'
            enableCropper={enableCropper}
            onClean={() => onFileChange(undefined)}
            imageSrc={state.file?.path}
            onSuccess={onFileChange}
          />
        </div>
      </div>
    </ItemLayout>
  )
}

BackgroundImage.defaultValue = () => ({})
