// @import '../../styles/variable.less';

.abi-form-schema-background-view {
  padding: 12px 8px 4px;
  padding-right: 4px;
  border-bottom: 1px solid @border-color-base;

  &:last-of-type {
    border: none;
  }

  .background-color {
    width: 100%;
    .abi-color-select-dot {
      width: 45px;
    }
  }

  &-content {
    margin-top: 12px;

    .tit {
      color: #888;
      font-size: 14px;
    }

    & > .gradient-panel {
      display: flex;
      padding-bottom: 4px;

      & > .gradient-view-box {
        display: flex;
        flex: 1;
        flex-wrap: wrap;
        & > .view-box {
          display: flex;
          flex-direction: column;
          width: 50%;
          padding: 4px 4px;

          .abi-color-select {
            width: 100%;
          }
        }
      }

      .type {
        width: 65px;
        padding-top: 6px;
        color: #777;
        font-size: 14px;
      }
    }

    & > .color-panel {
      display: flex;
      align-items: center;
      padding: 8px;
      padding-left: 0;

      .type {
        width: 90px;
        color: #777;
        font-size: 14px;
      }
    }
  }
}
