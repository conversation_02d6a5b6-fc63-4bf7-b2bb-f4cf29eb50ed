import './background.less'

import { useReactive } from 'ahooks'
import { Radio, Select } from 'antd'
import _ from 'lodash'
import React, { useEffect, useMemo } from 'react'

import ColorSelect from '../../color-picker/color-select'
import Title from '../components/title'
import { GRADIENT_DIRECTION } from '../const'
import { FormSchemaBase } from '../type'

export interface BackgroundViewProps extends FormSchemaBase {
  value?: Partial<{
    backgroundColor: string | 'none'
    backgroundImage: string | 'none'
  }>
  onChange: (value?: BackgroundViewProps['value']) => any
  ignore?: ('linear-gradient' | 'background-color' | (string & {}))[]
}

/**
 * 背景套件
 * @param props
 */
export default function BackgroundView(props: BackgroundViewProps) {
  const { value, title, caption, icon, onChange, ignore } = props

  const types = useMemo(() => {
    const list = [
      { value: 'background-color', label: '纯颜色' },
      { value: 'linear-gradient', label: '渐变色' }
    ]
    if (!_.isEmpty(ignore)) return list.filter(i => !ignore?.includes(i.value))
    return list
  }, [ignore])

  // TODO: 后面添加渐变的颜色设置
  const state = useReactive({
    backgroundColor: '#fff' as string | undefined,
    backgroundImage: undefined as string | undefined,
    // 渐变方向，颜色
    gradientDirection: 'to top',
    gradientStart: '#fff',
    gradientEnd: '#fff',
    active: 'background-color' as 'background-color' | 'linear-gradient'
  })

  const update = (field: 'backgroundColor' | 'backgroundImage', val?: string) => {
    const newValue = { ...value, [field]: val }
    state[field] = val
    onChange(newValue as any)
  }

  const gradientParse = (str: string) => {
    // 'linear-gradient(to top, #fff, rgba(65, 117, 5, 1))'.match(/(to\s\w+)|(#\w+)|rgba?\(.*?\)/g)
    const res = str.match(/(to\s\w+\s?\w+)|(#\w+)|rgba?\(.*?\)/gi) || {}
    return [res[0], res[1], res[2]]
  }

  const gradientChange = (field: string, val: string | undefined) => {
    state[field] = val
    const { gradientDirection, gradientStart, gradientEnd } = state
    // 生成新的字符串
    const str = `linear-gradient(${gradientDirection}, ${gradientStart}, ${gradientEnd})`
    update('backgroundImage', str)
  }

  // 类型改变时，清空非对应的字段
  const onTypeChange = (e: any) => {
    state.active = e.target.value
    if (state.active === 'background-color') {
      update('backgroundImage', undefined)
      state.gradientDirection = 'to top'
      state.gradientStart = '#fff'
      state.gradientEnd = '#fff'
    }
  }

  useEffect(() => {
    state.backgroundColor = value?.backgroundColor || '#fff'
    state.backgroundImage = value?.backgroundImage || undefined

    // 存在渐变
    if ((state.backgroundImage || '')?.indexOf('linear-gradient') > -1) {
      state.active = 'linear-gradient'
      try {
        const [direct, start, end] = gradientParse(state.backgroundImage || '')
        state.gradientDirection = direct
        state.gradientStart = start
        state.gradientEnd = end
      } catch (err) {
        console.error(err)
      }
    } else {
      state.active = 'background-color'
    }
  }, [value])

  // ... background
  return (
    <div className='abi-form-schema-background-view'>
      <Title title={title} caption={caption} icon={icon} />

      <div className='abi-form-schema-background-view-content'>
        <Radio.Group value={state.active} onChange={onTypeChange} size='small' optionType='button' options={types} />

        {state.active === 'linear-gradient' && (
          <div className='gradient-panel'>
            <span className='type'>渐变色</span>
            <div className='gradient-view-box'>
              <div className='view-box'>
                <span className='tit'>渐变方向</span>
                <Select
                  options={GRADIENT_DIRECTION}
                  placeholder='渐变方向'
                  value={state.gradientDirection}
                  onChange={val => gradientChange('gradientDirection', val)}
                />
              </div>
              <div className='view-box' />
              <div className='view-box'>
                <span className='tit'>开始颜色</span>
                <ColorSelect
                  mode='rgba'
                  value={state.gradientStart}
                  onChange={val => gradientChange('gradientStart', val)}
                />
              </div>
              <div className='view-box'>
                <span className='tit'>结束颜色</span>
                <ColorSelect
                  mode='rgba'
                  value={state.gradientEnd}
                  onChange={val => gradientChange('gradientEnd', val)}
                />
              </div>
            </div>
          </div>
        )}
        {state.active === 'background-color' && (
          <div className='color-panel'>
            <span className='type'>背景色</span>
            <ColorSelect
              mode='rgba'
              value={state.backgroundColor}
              onChange={val => update('backgroundColor', val)}
              className='background-color'
              allowClear
            />
          </div>
        )}
      </div>
    </div>
  )
}

BackgroundView.defaultValue = () => ({})
