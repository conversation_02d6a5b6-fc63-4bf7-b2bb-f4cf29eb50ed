// @import '../../styles/variable.less';

.abi-form-schema-beautify {
  padding: 2px 2px 8px;
  // border-bottom: 1px solid @border-color-base;

  .gradient-direction {
    width: 45px;
    text-align: center;
    margin-left: 12px;
    .ant-select-selection-item {
      transform: translateX(-2px);
    }
  }

  &:last-of-type {
    border: none;
  }

  & > .abi-form-schema-item-title {
    flex: none;
    align-items: flex-start;
    width: 60px;
    min-width: 60px;
    padding-top: 4px;
    padding-left: 8px;
  }

  & > .view-box {
    display: flex;
    margin: 10px 8px;
    user-select: none;
    align-items: center;

    > .ant-checkbox-wrapper {
      margin-right: 12px;
    }

    .abi-color-select {
      margin-left: 12px;
    }

    &.opacity-setting {
      display: flex;
      align-items: center;
      .ant-slider {
        flex: 1;
        margin: 0 16px;
      }
    }

    &:last-of-type {
      margin-bottom: 6px;
    }

    > .ant-checkbox-wrapper {
      padding-bottom: 2px;
    }

    &.border-setting {
      margin-left: 36px;
    }

    .ant-select-selection-item > div {
      margin-top: 10px !important;
    }
  }

  .abi-color-select {
    width: 26px;
    height: 26px;
  }

  .rows {
    display: flex;
    align-items: center;
  }

  // 边框设置
  & > .view-box {
    .border-list-overlay {
      .ant-popover-inner-content {
        padding: 5px;
      }
      .border-list {
        display: flex;
        align-items: center;

        .border-all {
          margin-left: 0;
        }
      }
    }

    .border-all,
    .border-left,
    .border-right,
    .border-top,
    .border-bottom {
      width: 20px;
      height: 20px;
      border-radius: 2px;
      background-color: rgba(1, 1, 1, 0.05);
      margin-left: 10px;
      cursor: pointer;
      border-color: tint(@primary-color, 80%);
      border-width: 0;
      box-shadow: 0 0 2px rgba(1, 1, 1, 0.12);
      &:hover {
        border-color: tint(@primary-color, 40%);
      }
      &.selected {
        border-color: tint(@primary-color, 30%);
      }
      &.actived {
        border-color: tint(#3b4, 30%);
      }
    }
    .border-all {
      border-width: 3px;
    }
    .border-left {
      border-left-width: 3px;
    }
    .border-right {
      border-right-width: 3px;
    }
    .border-top {
      border-top-width: 3px;
    }
    .border-bottom {
      border-bottom-width: 3px;
    }
  }
}
