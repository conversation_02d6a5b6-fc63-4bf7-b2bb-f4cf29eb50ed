import './beautify.less'

import { SwapRightOutlined } from '@ant-design/icons'
import { Checkbox, InputNumber, Popover, Select, Slider } from 'antd'
import cn from 'classnames'
import { produce } from 'immer'
import _ from 'lodash'
import React, { useEffect, useMemo, useState } from 'react'

import ColorSelect from '../../color-picker/color-select'
import { BORDER_STYLE, FONT_DEFAULT, GRADIENT_DIRECTION } from '../const'
import ItemLayout from '../echart-custom/echart-custom-group'
import type { FormSchemaBase } from '../type'

const borderOptions = [
  { label: 'all', value: '' },
  { label: 'top', value: 'Top' },
  { label: 'bottom', value: 'Bottom' },
  { label: 'left', value: 'Left' },
  { label: 'right', value: 'Right' }
]
const selectOption = [
  { label: '无', value: 'none' },
  { label: '纯色', value: 'backgroundColor' },
  { label: '渐变', value: 'backgroundImage' }
]

type Border = {
  width: number
  color: string
  style: string
}

export interface BeautifyViewProps extends FormSchemaBase {
  value?: {
    opacity: number
    backgroundColor?: string
    backgroundImage?: string

    borderColor?: string
    borderWidth?: number
    borderStyle?: string

    borderTopColor?: string
    borderTopWidth?: number
    borderTopStyle?: string

    borderBottomColor?: string
    borderBottomWidth?: number
    borderBottomStyle?: string

    borderLeftColor?: string
    borderLeftWidth?: number
    borderLeftStyle?: string

    borderRightColor?: string
    borderRightWidth?: number
    borderRightStyle?: string

    boxShadow?: string
  }
  onChange: (value: BeautifyViewProps['value']) => any
}

function MySlider({ value, onChange }) {
  const [val, setVal] = useState(value || 100)

  useEffect(() => {
    setVal(value)
  }, [value])

  return (
    <>
      <span>透明度</span>
      <Slider value={val} onChange={v => setVal(v)} onAfterChange={va => onChange(va)} />
      <span>{val}%</span>
    </>
  )
}

/**
 * 外观样式套件
 * @param props
 */
export default function BeautifyView(props: BeautifyViewProps) {
  const { title, caption, icon, value, onChange, unfold = true } = props
  const V = value!

  const [borderType, setBorderType] = useState<'' | 'Left' | 'Right' | 'Top' | 'Bottom' | (string & {})>('')
  const [color, setColor] = useState('none')

  const update = (key: keyof typeof V, val: any) => {
    if (key === 'opacity') val = _.round(val / 100, 2)
    const newData = { ...V, [key]: val }
    onChange(newData)
  }

  // 解析 shadow 的颜色出来
  const parseShadow = (str?: string) => {
    if (!str) return undefined
    const res = str.match(/(-?\d+)|(rgb(a?)\(.*?\)|#\w+)|(inset)/g)
    if (!res) return
    let sColor
    if (res.length === 4) sColor = res[3] || undefined
    if (res.length === 5) {
      if (str.indexOf('inset') > -1) {
        sColor = res[3] || undefined
      } else {
        sColor = res[4] || undefined
      }
    }
    if (res.length === 6) sColor = res[4] || undefined
    return sColor
  }

  const toNumber = val => {
    if (val === undefined) return undefined
    const num = _.toNumber(val)
    if (_.isNaN(num)) return 0
    return num
  }

  const {
    opacity,
    backgroundColor,
    backgroundImage,
    boxShadow,
    borderColor,
    borderWidth,
    borderTopWidth,
    borderBottomWidth,
    borderLeftWidth,
    borderRightWidth
  } = useMemo(
    () => ({
      opacity: _.round((value?.opacity === undefined ? 1 : value?.opacity || 0) * 100, 0),
      backgroundColor: value?.backgroundColor,
      boxShadow: value?.boxShadow === undefined ? '1px 2px 5px #fff' : value?.boxShadow,

      borderColor: value?.borderColor,
      borderWidth: toNumber(value?.borderWidth) || 0,
      borderTopWidth: toNumber(value?.borderTopWidth) || 0,
      borderBottomWidth: toNumber(value?.borderBottomWidth) || 0,
      borderLeftWidth: toNumber(value?.borderLeftWidth) || 0,
      borderRightWidth: toNumber(value?.borderRightWidth) || 0,
      backgroundImage: value?.backgroundImage
    }),
    [value]
  )

  const border = useMemo(
    () => ({
      color: value?.[_.camelCase(`border_${borderType}_color`)],
      style: value?.[_.camelCase(`border_${borderType}_style`)] || 'solid',
      width: toNumber(value?.[_.camelCase(`border_${borderType}_width`)]) || 0
    }),
    [value, borderType]
  )

  const gradientParse = (str: string) => {
    try {
      if (!str || str?.indexOf('none') > -1) return ['to right', '#fff', '#fff']
      // 'linear-gradient(to top, #fff, rgba(65, 117, 5, 1))'.match(/(to\s\w+)|(#\w+)|rgba?\(.*?\)/g)
      const res = str.match(/(to\s\w+\s?\w+)|(#\w+)|rgba?\(.*?\)/gi) || {}
      return [res[0], res[1], res[2]]
    } catch (err) {
      console.error(err)
      return ['to right', '#fff', '#fff']
    }
  }

  const [gradientDirection, gradientStart, gradientEnd] = useMemo(
    () => gradientParse(backgroundImage || ''),
    [backgroundImage]
  )

  const boxShadowColor = parseShadow(value?.boxShadow)
  const hasBorder =
    borderWidth > 0 || borderTopWidth > 0 || borderBottomWidth > 0 || borderLeftWidth > 0 || borderRightWidth > 0

  const onBorderChange = (v: Partial<Border>) => {
    const newValue = produce(V, draft => {
      _.keys(v).forEach(key => {
        draft[_.camelCase(`border_${borderType}_${key}`)] = v[key]
      })
    })
    onChange(newValue)
  }

  const gradientChange = (field: string, val: string | undefined) => {
    const obj = { gradientDirection, gradientStart, gradientEnd }
    obj[field] = val
    // 生成新的字符串
    const str = `linear-gradient(${obj.gradientDirection}, ${obj.gradientStart}, ${obj.gradientEnd})`
    update('backgroundImage', str)
  }

  const renderBorderPopoevr = () => (
    <Popover
      trigger={['click']}
      placement='bottom'
      overlayClassName='border-list-overlay'
      destroyTooltipOnHide={{ keepParent: true }}
      getPopupContainer={() => document.getElementById('abi-form-schema-border-setting') || document.body}
      content={
        <div className='border-list'>
          {borderOptions.map(item => (
            <div
              key={item.label}
              className={cn({
                [`border-${item.label}`]: true,
                actived: borderType === item.value,
                selected: _.get(value || {}, _.camelCase(`border_${item.value}_width`))
              })}
              style={{ borderStyle: _.get(value || {}, _.camelCase(`border_${item.value}_style`), 'solid') }}
              onClick={() => setBorderType(item.value)}
            />
          ))}
        </div>
      }
    >
      <div
        className={cn({
          [`border-${_.camelCase(borderType) || 'all'}`]: true,
          selected: true
        })}
        style={{ borderStyle: border.style }}
      />
    </Popover>
  )

  const renderBorderSetting = (v: Border) => (
    <div className='view-box border-setting' id='abi-form-schema-border-setting'>
      <Select
        value={v.style}
        style={{ width: 90, marginRight: 8 }}
        onChange={val => {
          onBorderChange({
            style: val,
            color: v.color || '#ccc',
            width: v.width || 1
          })
        }}
      >
        {BORDER_STYLE.map(item => (
          <Select.Option key={item.value} value={item.value}>
            <div style={{ border: `5px ${item.value} #555`, marginTop: 8 }} />
          </Select.Option>
        ))}
      </Select>
      <InputNumber
        precision={0}
        value={v.width}
        onChange={val => onBorderChange({ width: val || 0, style: v.style || 'solid' })}
        min={0}
        style={{ width: 65 }}
      />
      {renderBorderPopoevr()}
    </div>
  )

  const handleChange = (val: any) => {
    // 判断是当前选择的是什么类型
    if (val === 'none') {
      setColor('none')
      if (backgroundColor !== undefined) {
        update('backgroundColor', undefined)
      } else {
        update('backgroundImage', undefined)
      }
    } else if (val === 'backgroundColor') {
      setColor('backgroundColor')
      update('backgroundImage', undefined)
    } else {
      setColor('backgroundImage')
      update('backgroundColor', undefined)
    }
  }

  const colorType = useMemo(() => {
    if (color === 'backgroundColor' || backgroundColor !== undefined) {
      return '纯色'
    }
    if (color === 'backgroundImage' || backgroundImage !== undefined) {
      return '渐变'
    }
    return '无'
  }, [color, backgroundColor, backgroundImage])

  useEffect(() => {
    if (backgroundColor !== undefined) {
      setColor('backgroundColor')
    }
    if (backgroundImage !== undefined) {
      setColor('backgroundImage')
    }
  }, [backgroundColor, backgroundImage])

  return (
    <ItemLayout
      className='abi-form-schema-beautify'
      title={title}
      caption={caption}
      icon={icon}
      layout='column'
      unfold={unfold}
    >
      <div className='view-box opacity-setting'>
        <MySlider value={opacity} onChange={(val: string) => update('opacity', val)} />
      </div>
      <div className='view-box'>
        <Checkbox
          checked={backgroundColor !== undefined || backgroundImage !== undefined}
          onChange={e => {
            if (color === 'backgroundColor') {
              update('backgroundColor', e.target.checked ? backgroundColor || '#fff' : undefined)
            } else if (color === 'backgroundImage') {
              update('backgroundImage', e.target.checked ?
                backgroundImage || 'linear-gradient(to right, #f45, #39f)' : undefined)
            }
          }}
        />
        <Select
          value={colorType}
          onChange={val => handleChange(val)}
          options={selectOption}
        />

        {color === 'backgroundColor' && (
          <ColorSelect
            mode='rgba'
            value={backgroundColor || 'rgba(255,255,255,1)'}
            showText={false}
            onChange={val => update('backgroundColor', val)}
          />
        )}
        {color === 'backgroundImage' && (
          <>
            <ColorSelect
              mode='rgba'
              value={gradientStart}
              showText={false}
              onChange={val => gradientChange('gradientStart', val)}
            />
            <Select
              size='small'
              placeholder='渐变方向'
              value={gradientDirection}
              onChange={val => gradientChange('gradientDirection', val)}
              className='gradient-direction'
              showArrow={false}
            // style={{ width: 60, marginLeft: 12 }}
            >
              {GRADIENT_DIRECTION.map(item => (
                <Select.Option key={item.value} title={item.label}>
                  <SwapRightOutlined rotate={item.rotate || 0} />
                </Select.Option>
              ))}
            </Select>
            <ColorSelect
              mode='rgba'
              value={gradientEnd}
              showText={false}
              onChange={val => gradientChange('gradientEnd', val)}
            />
          </>
        )}
      </div>
      <div className='view-box'>
        <Checkbox
          checked={hasBorder}
          onChange={e => {
            const flag = e.target.checked
            onChange({
              ...V,
              borderColor: flag ? borderColor || '#ccc' : undefined,
              borderWidth: flag ? 1 : undefined,
              borderStyle: flag ? 'solid' : undefined,
              borderTopColor: undefined,
              borderTopWidth: undefined,
              borderTopStyle: undefined,
              borderBottomColor: undefined,
              borderBottomWidth: undefined,
              borderBottomStyle: undefined,
              borderLeftColor: undefined,
              borderLeftWidth: undefined,
              borderLeftStyle: undefined,
              borderRightColor: undefined,
              borderRightWidth: undefined,
              borderRightStyle: undefined
            })
          }}
        />
        <div className='rows'>
          <span>描边</span>
          <ColorSelect
            mode='rgba'
            value={border.color || 'rgba(255,255,255,1)'}
            showText={false}
            onChange={val => onBorderChange({ color: val, style: border.style || 'solid' })}
          />
        </div>
      </div>

      {renderBorderSetting(border)}

      <div className='view-box'>
        <Checkbox
          checked={boxShadowColor !== undefined}
          onChange={e => update('boxShadow', e.target.checked ? boxShadow : undefined)}
        />
        <span>阴影</span>
        <ColorSelect
          value={boxShadowColor || 'rgba(255,255,255,1)'}
          showText={false}
          mode='rgba'
          onChange={val => {
            update('boxShadow', val ? boxShadow.replace(boxShadowColor || '#fff', val) : undefined)
          }}
        />
      </div>
    </ItemLayout>
  )
}

BeautifyView.defaultValue = ({ defaultValue }) => ({
  ...FONT_DEFAULT,
  opacity: 1,
  ...defaultValue
})
