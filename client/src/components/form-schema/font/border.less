// @import '../../styles/variable.less';

.abi-form-schema-border-view {
  display: block;
  padding: 12px 0;
  border-bottom: 1px solid @border-color-base;

  &:last-of-type {
    border: none;
  }

  & > .abi-form-schema-item-title {
    flex: none;
    padding-left: 8px;
  }

  & > .border-content-panel {
    display: flex;

    & > .border-left-panel {
      flex: 1;
      min-width: 90px;
      padding: 8px 2px;
    }

    & > .border-right-panel {
      flex: 1;
      min-width: 200px;
      padding-right: 12px;

      & > .view-box {
        display: flex;
        align-items: center;
        margin-top: 6px;

        .ant-radio-button-wrapper {
          height: 26px;
          padding: 0 10px;
          line-height: 26px;
        }

        // .ant-radio-button-wrapper-checked {
        //   .anticon {
        //     transform: scale(1);
        //   }
        // }

        &:first-of-type {
          margin-top: 0;
        }

        & > .tit {
          margin-right: 8px;
          color: #888;
          font-size: 14px;
        }
      }
    }
  }
}

// 四个点点
.direct-box {
  display: flex;
  align-items: center;
  justify-content: center;

  // 四个点点
  & > span {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    height: 22px;
    margin: 3px;
    background-color: #f2f8fc;
    border: 1px dashed darken(#f2f8fc, 15%);
    cursor: pointer;

    &.select {
      background-color: tint(@primary-color, 82%);
      border: 2px dashed rgba(tint(@primary-color, 82%), 0.8);
    }
    &.active {
      background-color: tint(@primary-color, 92%);
      border: 2px dashed rgba(tint(@primary-color, 50%), 1);
    }
    // 方向
    &.all.select {
      border: 2px solid rgba(@primary-color, 0.7);
    }
    &.left.select {
      border-left: 3px solid rgba(@primary-color, 0.8);
    }
    &.right.select {
      border-right: 3px solid rgba(@primary-color, 0.8);
    }
    &.top.select {
      border-top: 3px solid rgba(@primary-color, 0.8);
    }
    &.bottom.select {
      border-bottom: 3px solid rgba(@primary-color, 0.8);
    }
    // 激活的
    &.all.active {
      border: 3px solid rgba(@primary-color, 0.7);
    }
    &.left.active {
      border-left: 4px solid rgba(@primary-color, 0.8);
    }
    &.right.active {
      border-right: 4px solid rgba(@primary-color, 0.8);
    }
    &.top.active {
      border-top: 4px solid rgba(@primary-color, 0.8);
    }
    &.bottom.active {
      border-bottom: 4px solid rgba(@primary-color, 0.8);
    }
  }
}
