import './border.less'

import { CloseCircleOutlined, DashOutlined, MinusOutlined, SmallDashOutlined } from '@ant-design/icons'
import { useReactive } from 'ahooks'
import { InputNumber, Radio, Tooltip } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React from 'react'

import ColorSelect from '../../color-picker/color-select'
import Title from '../components/title'
import { DIRECT_MAP } from '../const'
import type { FormSchemaBase } from '../type'

const borderStyleOptions = [
  { value: 'none', icon: CloseCircleOutlined },
  { value: 'solid', icon: MinusOutlined },
  { value: 'dashed', icon: DashOutlined },
  { value: 'dotted', icon: SmallDashOutlined }
]

export interface BorderViewProps extends FormSchemaBase {
  value?: Partial<{
    borderStyle: string
    borderWidth: number
    borderColor: string
    [key: string]: any
    // Left, Right, Top, Bottom
    // borderLeftStyle: string
    // borderLeftWidth: string
    // borderLeftColor: string
  }>
  onChange: (value: BorderViewProps['value']) => any
}

/**
 * 边框套件
 * @param props
 */
export default function BorderView(props: BorderViewProps) {
  const { title, caption, icon, value, onChange } = props

  const state = useReactive({
    color: undefined as string | undefined,
    width: 'none' as number | string | undefined,
    style: 'none' as string,
    active: ''
  })

  const update = (field: 'color' | 'width' | 'style', val: any) => {
    if (!state.active) return
    const newValue: any = { ...value }
    const direc = state.active === 'all' ? '' : state.active

    // 无边框时，清空其他
    if (field === 'style' && val === 'none') {
      state.width = undefined
      state.style = 'none'
      state.color = undefined
      val = undefined
      newValue[_.camelCase(`border_${direc}_color`)] = undefined
      newValue[_.camelCase(`border_${direc}_width`)] = undefined
      newValue[_.camelCase(`border_${direc}_style`)] = undefined
    }

    // 同步数据
    state[field] = val
    newValue[_.camelCase(`border_${direc}_${field}`)] = val
    // 排除 undefined 的
    onChange(newValue)
  }

  const getFields = (key: string) => {
    if (key !== 'all') key = `_${key}_`
    if (key === 'all') key = '_'
    return [_.camelCase(`border${key}width`), _.camelCase(`border${key}style`), _.camelCase(`border${key}color`)]
  }

  // 重置选的值
  const resetValue = (key: string) => {
    const fields = getFields(key)
    let w = value?.[fields[0]]
    if (_.isString(w)) w = _.toNumber(w)
    state.width = w || undefined
    state.style = value?.[fields[1]] || 'none'
    state.color = value?.[fields[2]] || '#fff'
  }

  // 检查是否有设置的
  const checkDirect = (key: string) => getFields(key).some(i => value?.[i])

  // 清空对应方向的值
  const delValue = (key: string) => {
    const fields = getFields(key)
    const newValue: any = { ..._.omit(value, fields) }
    onChange(newValue)
  }

  // 选中
  const onSelect = (key: string) => {
    if (key === state.active) {
      // 删除同方向字段
      if (state.style === 'none' || state.style === undefined) {
        delValue(key)
      }
      state.active = ''
      state.width = undefined
      state.style = 'none'
      state.color = undefined
      return
    }
    state.active = key
    resetValue(key)
  }

  const cleanSelect = (e: any) => {
    state.active = ''
    state.color = '#fff'
    state.style = 'none'
    state.width = undefined
    e.stopPropagation()
  }

  const onWidthEnter = (_val: any) => {
    const val = Number.parseInt(_val, 10)
    update('width', Number.isNaN(val) ? undefined : val)
  }

  const renderDot = (key: any) => (
    <Tooltip
      title={`${DIRECT_MAP[key] || ''}边框`}
      destroyTooltipOnHide={{ keepParent: true }}
      placement={key === 'all' || !key ? 'top' : key}
    >
      <span
        onClick={e => {
          e.stopPropagation()
          onSelect(key)
        }}
        className={cn({
          select: checkDirect(key),
          active: state.active === key,
          [key]: true
        })}
      />
    </Tooltip>
  )

  return (
    <div className='abi-form-schema-border-view'>
      <Title title={title} caption={caption} icon={icon} />
      <div className='border-content-panel'>
        <div className='border-left-panel' onClick={cleanSelect}>
          <div className='direct-box'>{renderDot('top')}</div>
          <div className='direct-box'>
            {renderDot('left')}
            {renderDot('all')}
            {renderDot('right')}
          </div>
          <div className='direct-box'>{renderDot('bottom')}</div>
        </div>

        <div className='border-right-panel'>
          <div className='view-box'>
            <span className='tit'>样式</span>
            <Radio.Group value={state.style || 'none'} onChange={e => update('style', e.target.value)}>
              {borderStyleOptions.map(item => (
                <Radio.Button value={item.value} key={item.value}>
                  <item.icon />
                </Radio.Button>
              ))}
            </Radio.Group>
          </div>
          <div className='view-box'>
            <span className='tit'>宽度</span>
            <InputNumber
              placeholder='输入宽度'
              min={0}
              addonAfter='px'
              style={{ width: 140 }}
              value={state.width === 'none' || state.width === undefined ? undefined : Number(state.width)}
              onChange={onWidthEnter}
            />
          </div>
          <div className='view-box'>
            <span className='tit'>颜色</span>
            <ColorSelect
              value={state.color}
              style={{ width: 140 }}
              mode='rgba'
              onChange={val => update('color', val)}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

BorderView.defaultValue = () => ({})
