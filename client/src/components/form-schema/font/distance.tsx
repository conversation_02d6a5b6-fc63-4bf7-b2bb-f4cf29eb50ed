import './distance.less'

import { CloseCircleOutlined } from '@ant-design/icons'
import { useReactive } from 'ahooks'
import { InputNumber, Radio, Select, Tooltip } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React, { useEffect, useMemo } from 'react'

import Title from '../components/title'
import { DIRECT_MAP } from '../const'
import { FormSchemaBase } from '../type'

const dirceOptions = [
  { value: 'auto', label: 'auto' },
  { value: 'top', label: 'top' },
  { value: 'bottom', label: 'bottom' },
  { value: 'left', label: 'left' },
  { value: 'right', label: 'right' }
]

export interface DistanceViewProps extends FormSchemaBase {
  value?: Partial<{
    top: number | string
    bottom: number | string
    left: number | string
    right: number | string
  }>
  onChange: (value: DistanceViewProps['value']) => any
  ignore?: ('absolute' | 'percent' | 'enum')[]
}

/**
 * 距离套件
 * @param props
 */
export default function DistanceView(props: DistanceViewProps) {
  const { title, caption, icon, value, onChange, ignore = ['enum'] } = props

  const state = useReactive({
    active: '' as 'left' | 'right' | 'top' | 'bottom' | (string & {}),
    value: undefined as any,
    type: 'absolute' as 'absolute' | 'percent' | 'enum' //
  })

  const typeOptions = useMemo(() => {
    const list = [
      { value: 'absolute', label: '绝对值' },
      { value: 'percent', label: '百分比' },
      { value: 'enum', label: '枚举量' }
    ]
    if (_.isEmpty(ignore)) return list
    return list.filter(i => !ignore.includes(i.value as any))
  }, [ignore])

  const cleanSelect = e => {
    state.active = ''
    state.value = undefined
    e.stopPropagation()
  }

  // 检查是否有设置的
  const checkDirect = key => value?.[key] || value?.[key] === 0
  // 重置数据
  const resetValue = key => {
    let val = value?.[key]
    if (_.isString(val)) {
      if (val?.indexOf('%') > -1) {
        val = Number.parseInt(val, 10)
        if (_.isNaN(val)) val = undefined
        state.type = 'percent'
      } else {
        state.type = 'enum'
      }
      if (val === 'auto') val = undefined
    } else {
      state.type = 'absolute'
    }

    state.value = val || undefined
  }

  // 选中
  const onSelect = (key: string) => {
    state.active = key
    resetValue(key)
  }

  const onClean = () => {
    const field = state.active
    const newValue = { ...value, [field]: undefined }
    state.active = ''
    state.value = undefined
    onChange(newValue)
  }

  // 更新数据
  const onEnter = (_val: string | null) => {
    if (!state.active) return

    let val: number | undefined = Number.parseInt(_val || '', 10)
    if (Number.isNaN(val)) val = 0

    state.value = val
    const newValue = { ...value, [state.active]: val }
    if (state.type === 'percent') newValue[state.active] = `${val}%`
    if (state.type === 'enum') {
      newValue[state.active] = _val
      state.value = _val
    }

    onChange(newValue)
  }

  const onTypeChange = e => {
    if (!state.active) return
    state.type = e.target.value
    if (state.type === 'enum') onEnter('auto')
    else onEnter(state.value)
  }

  // 同步数据
  useEffect(() => {
    if (!value || !state.active) return
    const newValue = value[state.active]
    if (state.value !== newValue) state.value = newValue
  }, [value, state.active])

  const renderDot = (key: string) => (
    <Tooltip
      title={`${DIRECT_MAP[key] || ''}距离`}
      destroyTooltipOnHide={{ keepParent: true }}
      placement={key === 'all' || !key ? 'top' : (key as any)}
    >
      <span
        onClick={e => {
          e.stopPropagation()
          if (state.active === key) return
          onSelect(key)
        }}
        className={cn({
          select: checkDirect(key),
          active: state.active === key,
          [key]: true
        })}
      />
    </Tooltip>
  )

  return (
    <div className='abi-form-schema-distance-view'>
      <Title title={title} caption={caption} icon={icon} />
      <div className='distance-content-panel'>
        <div className='distance-left-panel' onClick={cleanSelect}>
          <div className='direct-box'>{renderDot('top')}</div>
          <div className='direct-box'>
            {renderDot('left')}
            {renderDot('all')}
            {renderDot('right')}
          </div>
          <div className='direct-box'>{renderDot('bottom')}</div>
        </div>
        <div className='distance-right-panel'>
          <p className='tit'>{`${DIRECT_MAP[state.active] || ''}距离`}</p>
          <Radio.Group
            value={state.type}
            onChange={onTypeChange}
            options={typeOptions}
            optionType='button'
            size='small'
            className='type'
          />

          {(state.type === 'absolute' || state.type === 'percent') && (
            <div>
              <InputNumber
                placeholder='请输入'
                style={{ width: 150 }}
                addonAfter={state.type === 'percent' ? '%' : 'px'}
                value={state.value}
                onChange={onEnter}
              />
              <CloseCircleOutlined onClick={onClean} />
            </div>
          )}

          {state.type === 'enum' && (
            <div>
              <Select
                placeholder='对齐方向'
                options={dirceOptions}
                style={{ width: 145 }}
                value={state.value}
                onChange={onEnter}
              />
              <CloseCircleOutlined onClick={onClean} />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
