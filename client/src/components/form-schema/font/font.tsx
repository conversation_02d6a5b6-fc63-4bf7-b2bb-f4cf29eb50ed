import './font.less'

import {
  Align<PERSON>enterOutlined,
  AlignLeftOutlined,
  Align<PERSON>ightOutlined,
  BoldOutlined,
  ColumnHeightOutlined,
  ColumnWidthOutlined,
  FontSizeOutlined,
  ItalicOutlined,
  StrikethroughOutlined,
  SwapRightOutlined,
  UnderlineOutlined
} from '@ant-design/icons'
import { InputNumber, Select } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React, { useMemo, useState } from 'react'

import ColorSelect from '../../color-picker/color-select'
import ItemLayout from '../components/item-layout'
import { FONT_DEFAULT, FONT_FAMILY, FONT_STYLE, GRADIENT_DIRECTION } from '../const'
import type { FormSchemaBase } from '../type'

export interface FontViewProps extends FormSchemaBase {
  value?: {
    fontSize: number
    fontFamily: string
    fontWeight: string
    fontStyle: string
    color: string
    colorGradient?: string[]
    direction?: string
    textAlign: 'left' | 'center' | 'right' | (string & {})
    lineHeight: number | string | undefined // 行高
    letterSpacing?: number // 字体间距
    textDecoration?: string
  }
  onChange: (value: FontViewProps['value']) => any
  ignore?: (
    | 'fontSize'
    | 'fontFamily'
    | 'fontWeight'
    | 'fontStyle'
    | 'lineHeight'
    | 'colorGradient'
    | 'color'
    | 'textAlign'
    | 'textDecoration'
    | 'letterSpacing'
  )[]
  showGradient?: boolean
}


/**
 * 文本样式套件
 ` * @param props
 */
export default function FontView(props: FontViewProps) {
  const { title, caption, icon, value, onChange, ignore, showGradient } = props
  const V = value!
  const selectOption = [
    {
      label: '纯色',
      value: 'color'
    },
    {
      label: '渐变',
      value: 'gradient'
    }
  ]
  const update = (key: keyof typeof V, val: any) => {
    // 行高不带单位时是比例
    if (key === 'lineHeight') {
      if (!_.isNumber(val)) val = undefined
      else val = `${val}px`
    }
    const newVal = _.omit({ ...V, [key]: val }, ignore || [])
    onChange(newVal as any)
  }

  const toNumber = val => {
    if (val === undefined) return undefined
    const num = _.toNumber(val)
    if (_.isNaN(num)) return 0
    return num
  }

  const {
    fontSize,
    fontFamily,
    fontWeight,
    fontStyle,
    color,
    colorGradient,
    direction,
    textAlign,
    letterSpacing,
    lineHeight,
    textDecoration
  } = useMemo(
    () => ({
      fontSize: toNumber(value?.fontSize) || FONT_DEFAULT.fontSize,
      fontFamily: value?.fontFamily || FONT_DEFAULT.fontFamily,
      fontWeight: value?.fontWeight || FONT_DEFAULT.fontWeight,
      fontStyle: value?.fontStyle || FONT_DEFAULT.fontStyle,
      color: value?.color || FONT_DEFAULT.color,
      colorGradient: value?.colorGradient || FONT_DEFAULT.colorGradient,
      direction: value?.direction || FONT_DEFAULT.direction,
      textAlign: value?.textAlign || undefined,
      lineHeight: toNumber(value?.lineHeight) || undefined,
      textDecoration: value?.textDecoration,
      letterSpacing: value?.letterSpacing || FONT_DEFAULT.letterSpacing
    }),
    [value]
  )
  const [colorProperties, setColorProperties] = useState(color === 'black' ? 'gradient' : 'color')
  const handleChange = (val: any) => {
    if (val === 'color') {
      update('color', '#222')
      update('colorGradient', ['#222', '#222'])
      setColorProperties('color')
    } else {
      update('color', undefined)
      setColorProperties('gradient')
    }
  }

  const colorType = useMemo(() => {
    if (colorProperties === 'color') {
      return '纯色'
    }
    if (colorProperties === 'gradient') {
      return '渐变'
    }
    return '文本颜色'
  }, [colorProperties, color])

  return (
    <ItemLayout className='abi-form-schema-font-view' title={title} caption={caption} icon={icon} layout='column'>
      <div className='view-box'>
        <Select
          placeholder='字体'
          value={fontFamily}
          onChange={val => update('fontFamily', val)}
          style={{ width: '48%', marginRight: 8 }}
        >
          {FONT_FAMILY.map(item => (
            <Select.Option value={item.value} key={item.value}>
              <span style={{ fontFamily: item.value }}>{item.label}</span>
            </Select.Option>
          ))}
        </Select>
        <Select
          placeholder='样式'
          options={FONT_STYLE}
          value={fontStyle}
          onChange={val => update('fontStyle', val)}
          style={{ width: '48%' }}
        />
      </div>
      <div className='view-box'>
        {!ignore?.includes('letterSpacing') && (
          <InputNumber
            placeholder='字距'
            value={letterSpacing}
            onChange={val => update('letterSpacing', val)}
            addonBefore={<ColumnWidthOutlined />}
            min={-10}
            max={168}
            precision={0}
            style={{ width: '48%', marginRight: 8 }}
          />
        )}
        {!ignore?.includes('lineHeight') && (
          <InputNumber
            placeholder='行高'
            value={lineHeight}
            onChange={val => update('lineHeight', val)}
            addonBefore={<ColumnHeightOutlined />}
            min={0}
            max={168}
            precision={0}
            style={{ width: '48%' }}
          />
        )}
      </div>

      <div className='view-box'>
        <InputNumber
          placeholder='字号'
          value={fontSize}
          onChange={val => update('fontSize', val)}
          min={2}
          max={168}
          addonBefore={<FontSizeOutlined />}
          style={{ width: '48%', marginRight: 8 }}
        />
        {!showGradient && (
          <ColorSelect mode='rgba' value={color} onChange={val => update('color', val)} showText={false} />
        )}
      </div>
      <div className='view-box'>
        {showGradient && (
          <>
            <Select options={selectOption} value={colorType} onChange={val => handleChange(val)} />
            {colorProperties === 'color' && (
              <ColorSelect mode='rgba' value={color} onChange={val => update('color', val)} showText={false} />
            )}
            {colorProperties === 'gradient' && (
              <>
                <ColorSelect
                  mode='rgba'
                  value={colorGradient[0]}
                  onChange={val => update('colorGradient', [val, colorGradient[1]])}
                  showText={false}
                />

                <Select
                  size='small'
                  placeholder='方向'
                  className='gradient-direction'
                  showArrow={false}
                  value={direction}
                  onChange={val => update('direction', val)}
                  style={{ width: 60, margin: 6 }}
                >
                  {GRADIENT_DIRECTION.map(item => (
                    <Select.Option key={item.value} title={item.label}>
                      <SwapRightOutlined rotate={item.rotate || 0} />
                    </Select.Option>
                  ))}
                </Select>

                <ColorSelect
                  mode='rgba'
                  value={colorGradient[1]}
                  onChange={val => update('colorGradient', [colorGradient[0], val])}
                  showText={false}
                />
              </>
            )}
          </>
        )}
      </div>
      <div className='view-box font-setting'>
        {!ignore?.includes('textAlign') && (
          <>
            <AlignLeftOutlined
              className={cn({ active: textAlign === 'left' })}
              onClick={() => update('textAlign', 'left')}
            />
            <AlignCenterOutlined
              className={cn({ active: textAlign === 'center' })}
              onClick={() => update('textAlign', 'center')}
            />
            <AlignRightOutlined
              className={cn({ active: textAlign === 'right' })}
              onClick={() => update('textAlign', 'right')}
            />
          </>
        )}

        <BoldOutlined
          className={cn({ active: fontWeight === 'bold' })}
          onClick={() => update('fontWeight', fontWeight === 'bold' ? 'normal' : 'bold')}
        />
        <ItalicOutlined
          className={cn({ active: fontStyle === 'oblique' })}
          onClick={() => update('fontStyle', fontStyle === 'oblique' ? 'normal' : 'oblique')}
        />
        {!ignore?.includes('textDecoration') && colorProperties !== 'gradient' && (
          <>
            <UnderlineOutlined
              className={cn({ active: textDecoration === 'underline' })}
              onClick={() => update('textDecoration', textDecoration === 'underline' ? 'none' : 'underline')}
            />
            <StrikethroughOutlined
              className={cn({ active: textDecoration === 'line-through' })}
              onClick={() => update('textDecoration', textDecoration === 'line-through' ? 'none' : 'line-through')}
            />
          </>
        )}
      </div>
    </ItemLayout>
  )
}

FontView.defaultValue = () => ({ ...FONT_DEFAULT })
