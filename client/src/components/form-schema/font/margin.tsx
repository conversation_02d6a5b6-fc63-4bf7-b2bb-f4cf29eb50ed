import './margin.less'

import { CloseCircleOutlined } from '@ant-design/icons'
import { useReactive } from 'ahooks'
import { InputNumber,Tooltip } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React from 'react'

import Title from '../components/title'
import { DIRECT_MAP } from '../const'
import type { FormSchemaBase } from '../type'

export interface MarginViewProps extends FormSchemaBase {
  onChange: (value: any) => any
  value?: Partial<{
    padding: number | string
    margin: number | string
    [key: string]: number | string
    // paddingLeft: number | string
  }>
}

/**
 * 边距套件
 * @param props
 */
export default function MarginView(props: MarginViewProps) {
  const { title, caption, icon, value, type, onChange } = props
  const state = useReactive({
    value: undefined as number | undefined | string,
    active: ''
  })

  const getFields = key => {
    if (key !== 'all') key = `_${key}`
    if (key === 'all') key = ''
    return _.camelCase(`${type}${key}`)
  }

  // 检查是否有设置的
  const checkDirect = key => value?.[getFields(key)]
  // 重置数据
  const resetValue = key => {
    const field = getFields(key)
    let val = value?.[field]
    if (_.isString(val)) val = _.toNumber(val)
    state.value = val || undefined
  }

  // 选中
  const onSelect = key => {
    state.active = key
    resetValue(key)
  }

  // 更新数据
  const onEnter = _val => {
    const val = Number.parseInt(_val, 10)
    state.value = val
    // 非数字的不更新
    if (_.isNumber(val) && !_.isNil(val) && state.active) {
      const field = getFields(state.active)
      onChange({ ...value, [field]: val })
    }
  }

  const onClean = e => {
    const field = getFields(state.active)
    const newValue = { ...value, [field]: undefined }
    state.active = ''
    state.value = undefined
    onChange(newValue)
  }

  const cleanSelect = e => {
    state.active = ''
    state.value = undefined
    e.stopPropagation()
  }

  const renderDot = key => (
    <Tooltip
      title={`${DIRECT_MAP[key] || ''}边距`}
      destroyTooltipOnHide={{ keepParent: true }}
      placement={key === 'all' || !key ? 'top' : key}
    >
      <span
        onClick={e => {
          e.stopPropagation()
          onSelect(key)
        }}
        className={cn({
          select: checkDirect(key),
          active: state.active === key,
          [key]: true
        })}
      />
    </Tooltip>
  )

  return (
    <div className='abi-form-schema-margin-view'>
      <Title title={title} caption={caption} icon={icon} />
      <div className='margin-content-panel'>
        <div className='margin-left-panel' onClick={cleanSelect}>
          <div className='direct-box'>{renderDot('top')}</div>
          <div className='direct-box'>
            {renderDot('left')}
            {renderDot('all')}
            {renderDot('right')}
          </div>
          <div className='direct-box'>{renderDot('bottom')}</div>
        </div>
        <div className='margin-right-panel'>
          <p className='tit'>{`${DIRECT_MAP[state.active] || ''}边距`}</p>
          <div>
            <InputNumber
              placeholder='输入边距'
              style={{ width: 150 }}
              addonAfter='px'
              value={state.value}
              onChange={onEnter}
            />
            <CloseCircleOutlined onClick={onClean} />
          </div>
        </div>
      </div>
    </div>
  )
}

MarginView.defaultValue = () => ({})
