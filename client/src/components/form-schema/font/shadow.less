// @import '../../styles/variable.less';

.abi-form-schema-shadow-view {
  display: block;
  padding: 12px 8px !important;
  border-bottom: 1px solid @border-color-base;

  &-content {
    padding-top: 1px;

    > .ant-radio-group {
      margin-bottom: 4px;
    }

    > .view-box-warp {
      margin-top: 8px;
      padding: 0 2px;

      > .view-box-list {
        display: flex;
        align-items: center;
        justify-content: space-around;

        > .view-box {
          width: 46%;
          // ...
          > span:first-of-type {
            color: #888;
            font-size: 14px;
          }
          > .slider {
            display: flex;
            align-items: center;
            > .ant-slider {
              flex: 1;
              margin: 8px 0;
            }
          }
        }
      }
    }

    .val-text {
      margin-left: 5px;
      color: #888;
      font-size: 13px;
    }

    .view-box-row {
      display: flex;
      align-items: center;
      height: 32px;
      padding: 0 5px;
      > span:first-of-type {
        width: 75px;
        color: #888;
        font-size: 14px;
      }
      > .abi-color-select {
        flex: 1;
        > .abi-color-select-dot {
          width: 60px;
        }
      }
    }
  }
}
