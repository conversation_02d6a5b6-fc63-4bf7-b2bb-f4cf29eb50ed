import './shadow.less'

import { useReactive } from 'ahooks'
import { Radio, Slider } from 'antd'
import _ from 'lodash'
import React, { useEffect } from 'react'

import ColorSelect from '../../color-picker/color-select'
import ItemLayout from '../components/item-layout'
import type { FormSchemaBase } from '../type'

export interface ShadowViewProps extends FormSchemaBase {
  value?: Partial<{
    boxShadow: string
    textShadow: string
  }>
  onChange: (value: ShadowViewProps['value']) => any
  mode?: 'box-shadow' | 'text-shadow'
}

/**
 * 阴影，支持字体和背景阴影
 * @param props
 */
export default function ShadowView(props: ShadowViewProps) {
  const { title, caption, mode = 'box-shadow', icon, value, onChange } = props

  const insets = [
    { value: '', label: '外阴影' },
    { value: 'inset', label: '内阴影' }
  ]

  const state = useReactive({
    offsetX: 0,
    offsetY: 0,
    blur: 0,
    spread: 0,
    color: '#fff',
    inset: '',
    active: mode as 'box-shadow' | 'text-shadow'
  })

  const list = [
    { key: 'offsetX', value: state.offsetX, title: 'X 偏移', min: -50, max: 50 },
    { key: 'offsetY', value: state.offsetY, title: 'Y 偏移', min: -50, max: 50 },
    { key: 'blur', value: state.blur, title: '模糊距离', min: 0, max: 50 },
    { key: 'spread', value: state.spread, title: '阴影大小', min: 0, max: 50, hide: mode === 'text-shadow' }
  ]

  const sync = (isClean?: boolean) => {
    const { offsetX, offsetY, blur, color, inset, spread } = state
    const field = _.camelCase(mode)
    let str
    if (mode === 'box-shadow') {
      str = `${offsetX}px ${offsetY}px ${blur}px ${spread}px ${color} ${inset}`
    }
    if (mode === 'text-shadow') {
      str = `${offsetX}px ${offsetY}px ${blur}px ${color}`
    }
    // 清空
    if (isClean) {
      onChange({ ...value, [field]: undefined })
    } else {
      onChange({ ...value, [field]: str })
    }
  }

  const update = (field: string, val: any) => {
    state[field] = val
    if (field === 'color' && val === undefined) {
      state[field] = '#fff'
    }
    if (field === 'color' || field === 'inset') {
      sync(val === undefined)
    }
  }

  // 反解析
  const parseShadow = (str: string) => {
    const res = str.match(/(-?\d+)|(rgb(a?)\(.*?\)|#\w+)|(inset)/g)
    if (!res) return
    state.offsetX = Number.parseInt(res[0], 10) || 0
    state.offsetY = Number.parseInt(res[1], 10) || 0
    state.blur = Number.parseInt(res[2], 10) || 0
    if (res.length === 4) {
      state.color = res[3] || '#fff'
    }
    if (res.length === 5) {
      if (str.indexOf('inset') > -1) {
        state.color = res[3] || '#fff'
        state.inset = res[4] || ''
      } else {
        state.spread = Number.parseInt(res[3], 10)
        state.color = res[4] || '#fff'
      }
    }
    if (res.length === 6 && mode === 'box-shadow') {
      state.spread = Number.parseInt(res[3], 10)
      state.color = res[4] || '#fff'
      state.inset = res[5] || ''
    }
  }

  const reset = () => {
    state.offsetX = 0
    state.offsetY = 0
    state.blur = 0
    state.spread = 0
    state.inset = ''
    state.color = '#fff'
  }

  useEffect(() => {
    state.active = mode
    if (mode === 'box-shadow' && value?.boxShadow) {
      parseShadow(value?.boxShadow)
    } else if (mode === 'text-shadow' && value?.textShadow) {
      parseShadow(value?.textShadow)
    } else {
      reset()
    }
  }, [value, mode])

  return (
    <ItemLayout title={title} caption={caption} icon={icon} layout='column' className='abi-form-schema-shadow-view'>
      <div className='abi-form-schema-shadow-view-content'>
        <div className='view-box-warp'>
          {_.chunk(list, 2).map((arr, index) => (
            <div key={index} className='view-box-list'>
              {arr.map(item => (
                <div className='view-box' key={item.title}>
                  {!item.hide && <span>{item.title}</span>}
                  {!item.hide && (
                    <div className='slider'>
                      <Slider
                        min={item.min}
                        max={item.max}
                        value={item.value}
                        step={1}
                        onChange={val => update(item.key, val)}
                        onAfterChange={() => sync()}
                      />
                      <span className='val-text'>{item.value}</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ))}

          {mode === 'box-shadow' && (
            <div className='view-box-row'>
              <span>阴影方向</span>
              <Radio.Group
                options={insets}
                size='small'
                value={state.inset}
                onChange={e => update('inset', e.target.value)}
              />
            </div>
          )}

          <div className='view-box-row'>
            <span>阴影颜色</span>
            <ColorSelect value={state.color} onChange={val => update('color', val)} mode='rgba' allowClear />
          </div>
        </div>
      </div>
    </ItemLayout>
  )
}

ShadowView.defaultValue = () => ({})
