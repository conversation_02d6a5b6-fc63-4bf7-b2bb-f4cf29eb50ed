import './group.less'

import cn from 'classnames'
import React, { useEffect, useState } from 'react'

import GroupTitle from '../components/group-title'
import { FormSchema } from '../index'
import type { FormSchemaBase } from '../type'

export interface GroupViewProps extends FormSchemaBase {
  value?: any
  layout?: 'row' | 'column'
  unfold?: boolean
  onChange: (value: any) => any
  schema?: FormSchemaBase & {
    children?: Record<string, FormSchemaBase>
  }
}

/**
 * 组
 * @param props
 */
export default function GroupView(props: GroupViewProps) {
  const { title, caption, icon, schema, layout, unfold = true, value, onChange } = props
  const [_unfold, setUnfold] = useState(unfold)

  useEffect(() => {
    setUnfold(unfold)
  }, [unfold])

  return (
    <div className='abi-form-schema-group-view' style={{ borderBottom: _unfold ? '1px solid #f1f1f1' : 'none' }}>
      {title && <GroupTitle caption={caption} icon={icon} title={title} unfold={_unfold} onUnfoldChange={setUnfold} />}
      <div
        className={cn({
          'abi-form-schema-group-view-content': true,
          'abi-form-schema-group-view-content-row': layout === 'row'
        })}
        style={{ display: _unfold ? 'block' : 'none' }}
      >
        <FormSchema schema={schema?.children} value={value} onChange={onChange} />
      </div>
    </div>
  )
}
