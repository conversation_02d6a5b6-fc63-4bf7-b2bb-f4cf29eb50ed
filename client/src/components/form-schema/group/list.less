// @import '~@/components/customs/custom-tabs.less';
// @import '../../styles/variable.less';

// 列表元素
.abi-form-schema-list-view {
  border-bottom: 1px solid @border-color-base;

  &:last-of-type {
    border: none;
  }

  & > .abi-form-schema-item-title {
    margin-top: 6px;
  }

  &-item {
    position: relative;
    margin: 5px 0;
    padding: 5px;
    border: 2px dashed tint(@primary-color, 90%);
    & > .anticon-close-circle {
      position: absolute;
      top: 2px;
      right: 2px;
      color: #888;
      font-size: 14px;
      cursor: pointer;
      user-select: none;
      &:hover {
        color: #f56;
      }
    }
  }

  &-create {
    padding: 6px;
    font-size: 14px;
    text-align: center;
    border: 1px dashed rgba(@primary-color, 0.5);
    border-radius: 4px;
    cursor: pointer;
    &:hover {
      background-color: rgba(@primary-color, 0.08);
    }
  }

  .abi-form-schema-list-view-tabs {
    // .tabs-theme();

    .anticon-plus-circle {
      cursor: pointer;
      user-select: none;
      &:hover {
        color: @primary-color;
      }
    }
    .anticon-close-circle {
      z-index: 2;
      margin-left: 3px;
      cursor: pointer;
      user-select: none;
      &:hover {
        color: @primary-color;
      }
    }

    &.ant-tabs {
      .ant-tabs-nav {
        margin: 0;
        background-color: #fff;
        box-shadow: none;
        .ant-tabs-nav-list {
          padding: 0;
        }
        .ant-tabs-tab {
          flex: none !important;
          margin: 0 12px !important;
          padding-top: 5px;
          padding-bottom: 5px;
          .anticon {
            opacity: 0.8 !important;
          }
        }
      }
      .ant-tabs-tabpane {
        padding: 5px !important;
      }
      .ant-tabs-ink-bar {
        display: block !important;
        opacity: 0.75;
      }
      .ant-tabs-tab-active {
        .anticon {
          color: @primary-color;
        }
      }
    }
  }
}
