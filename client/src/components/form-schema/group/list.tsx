import './list.less'

import { CloseCircleOutlined, PlusCircleOutlined } from '@ant-design/icons'
import { Tabs } from 'antd'
import _ from 'lodash'
import React, { useEffect, useState } from 'react'

import ItemLayout from '../components/item-layout'
import { FormSchema } from '../index'
import type { FormSchemaBase } from '../type'

const { TabPane } = Tabs

export interface ListViewProps extends FormSchemaBase {
  defaultValue: any[]
  value?: any[]
  onChange: (value: any[]) => any
  minItem?: number // 最小的子项目
  maxItem?: number // 最大的子项目
  mode?: 'list' | 'tabs'
  showAddButton?: boolean // 是否显示新增按钮
  tabPrefix?: string // tab 上的前缀
  schema?: FormSchemaBase & {
    // 对象
    children?: Record<string, FormSchemaBase>
  }
}

/**
 * 列表，支持 curd
 * @param props
 * @example
 * {
 *   type: 'list',
 *   title: '花姑娘',
 *   children: {
 *     ...
 *   }
 * }
 */
export default function ListView(props: ListViewProps) {
  const { title, icon, caption, schema, mode = 'list', value, onChange, valuePath } = props
  const { tabPrefix = '', maxItem = 10, minItem = 0, showAddButton = true } = props

  // 单值数组
  const _schema = schema?.children
  const [list, setList] = useState<any[]>([])

  // 同步
  const update = (arr: any[]) => {
    // 单值转置
    const newList = [...arr]
    onChange(newList)
  }

  const _onChange = (item: any, index: number) => (val: string) => {
    list[index] = val
    update([...list])
  }
  // 删除
  const onDel = (index: number) => (e: any) => {
    e.stopPropagation()
    if (list.length > 1) {
      list.splice(index, 1)
      update([...list])
    }
  }
  // 新建
  const onCreate = (e: any) => {
    e.stopPropagation()
    if (list.length > 0) {
      update([...list, list[list.length - 1]])
    }
  }

  useEffect(() => {
    // TODO: 特殊处理嵌套产生的问题，这里变成了一个对象
    if (value && valuePath) {
      if (!_.isArray(value) && _.isArray(value[valuePath])) {
        setList([...value[valuePath]])
      } else if (_.isArray(value)) {
        setList([...value])
      }
    } else {
      setList([])
    }
  }, [value, valuePath])

  const renderContent = () => {
    if (mode === 'list')
      return (
        <>
          {list.map((item, index) => (
            <div key={index} className='abi-form-schema-list-view-item'>
              <FormSchema schema={_schema} value={item} onChange={_onChange(item, index)} />
              {list.length > 0 && list.length > minItem && <CloseCircleOutlined onClick={onDel(index)} />}
            </div>
          ))}
          {list.length < maxItem && showAddButton && (
            <div className='abi-form-schema-list-view-create' onClick={onCreate}>
              + 新增
            </div>
          )}
        </>
      )
    if (mode === 'tabs')
      return (
        <Tabs
          hideAdd
          animated={false}
          className='abi-form-schema-list-view-tabs'
          tabBarExtraContent={list?.length < maxItem && showAddButton && <PlusCircleOutlined onClick={onCreate} />}
        >
          {list?.map((item, index) => (
            <TabPane
              forceRender
              key={index}
              tab={
                <>
                  {`${tabPrefix} ${index + 1}`}
                  {index > 0 && list?.length > minItem && <CloseCircleOutlined onClick={onDel(index)} />}
                </>
              }
            >
              <FormSchema schema={_schema} value={item} onChange={_onChange(item, index)} />
            </TabPane>
          ))}
        </Tabs>
      )
    return null
  }

  return (
    <ItemLayout title={title} icon={icon} caption={caption} layout='column' className='abi-form-schema-list-view'>
      {renderContent()}
    </ItemLayout>
  )
}
