import './menu.less'

import { Radio } from 'antd'
import _ from 'lodash'
import React, { useMemo, useState } from 'react'

import { FormSchema } from '../index'
import type { FormSchemaBase } from '../type'

export interface MenuViewProps extends FormSchemaBase {
  value?: any
  onChange: (value: any) => any
  algin: 'left' | 'center' | 'right' // 对齐
  schema?: FormSchemaBase & {
    // 数组
    children?: {
      title: string
      children?: Record<string, FormSchemaBase>
    }[]
  }
}

/**
 * 类似菜单的组
 * @param props
 * @example
 * {
 *   type: 'menu',
 *   children: [
 *     { ... }
 *   ]
 * }
 */
export default function MenuView(props: MenuViewProps) {
  const { schema, value, onChange, algin = 'left' } = props
  const [active = 0, setActive] = useState(0)

  const _schema = useMemo(() => schema?.children?.[active].children, [active, schema])
  const list = _.map(schema?.children, (item, index) => (
    <Radio.Button value={index} key={item.title + index}>
      {item.title}
    </Radio.Button>
  ))

  return (
    <div className='abi-form-schema-menu'>
      <header className='abi-form-schema-menu-header' style={{ textAlign: algin }}>
        <Radio.Group value={active} onChange={e => setActive(e.target.value)}>
          {list}
        </Radio.Group>
      </header>
      <div className='abi-form-schema-menu-content'>
        <FormSchema key={active} schema={_schema} value={value} onChange={onChange} />
      </div>
    </div>
  )
}
