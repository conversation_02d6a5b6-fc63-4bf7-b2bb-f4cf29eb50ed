// @import '~@/components/customs/custom-tabs.less';
// @import '../../styles/variable.less';

// tabs 组
.abi-form-schema-tabs-view {
  height: auto;
  // .tabs-theme();
  border-bottom: 1px solid @border-color-base;

  &:last-of-type {
    border: none;
  }

  &.ant-tabs {
    .ant-tabs-nav {
      margin: 0;
      background-color: #fff;
      box-shadow: none;
      .ant-tabs-nav-list {
        padding: 0;
      }
      .ant-tabs-tab {
        flex: none !important;
        margin: 0 12px !important;
        padding-top: 5px;
        padding-bottom: 5px;
        .anticon {
          opacity: 0.8;
        }
      }
    }
    .ant-tabs-tabpane {
      padding: 5px;
    }
    .ant-tabs-ink-bar {
      display: block !important;
      opacity: 0.75;
    }
    .ant-tabs-tab-active {
      .anticon {
        color: @primary-color;
      }
    }
  }

  &.tabs-vertical {
    .ant-tabs-nav {
      min-width: 33px;
      height: auto !important;
      .ant-tabs-nav-list {
        padding: 0;
      }
      .ant-tabs-tab {
        margin: 0;
        padding: 12px 0;
      }
      .ant-tabs-tab-btn {
        line-height: 1;
        text-align: center;
        writing-mode: vertical-rl;
      }
    }
    .ant-tabs-tabpane {
      padding-left: 8px !important;
    }
  }

  .abi-form-schema-main {
    // background-color: #fefeff;
    // border: 1px solid @border-color-base;
    &:hover {
      border-radius: 3px;
      box-shadow: 0 0 4px rgba(#111, 0.24);
    }
  }
}

// 临时修复
.abi-form-schema-tabs-view {
  .ant-tabs-nav {
    background-color: #fff !important;
    height: 32px !important;

    &::before {
      border-bottom: 1px solid #f1f1f1;
    }
  }
}
