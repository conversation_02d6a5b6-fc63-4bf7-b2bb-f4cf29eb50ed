import './tabs.less'

import { Tabs } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React from 'react'

import { FormSchema } from '../index'
// import Icon from '@/components/icons/iconfont-icon'
import type { FormSchemaBase } from '../type'

const { TabPane } = Tabs

export interface TabsViewProps extends FormSchemaBase {
  value?: any
  onChange: (value: any) => any
  direction?: 'vertical' | 'horizontal'
  schema?: FormSchemaBase & {
    // 对象
    children?: {
      title: string
      icon?: string
      children?: Record<string, FormSchemaBase>
    }[]
  }
}

/**
 * tabs 组
 * @param props
 * @example
 * {
 *   type: 'tabs',
 *   children: [
 *     {
 *       title: 'tab1',
 *       icon: '圆角', // 可选
 *       children: { ... }
 *     }
 *   ]
 * }
 */
export default function TabsView(props: TabsViewProps) {
  const { title, value, onChange, schema, direction = 'horizontal' } = props

  const renderTab = (item: any) => {
    if (!item.icon) return item.title
    return (
      <span>
        {/* <Icon name={item.icon} /> */}
        {item.title}
      </span>
    )
  }

  const list = _.map(schema?.children, (item, index) => (
    <TabPane tab={renderTab(item)} key={item.title + index} forceRender>
      <FormSchema schema={item?.children} value={value} onChange={onChange} />
    </TabPane>
  ))

  return (
    <Tabs
      title={title}
      tabPosition={direction === 'horizontal' ? 'top' : 'left'}
      animated={false}
      size='small'
      className={cn({
        'abi-form-schema-tabs-view': true,
        [`tabs-${direction}`]: true
      })}
    >
      {list}
    </Tabs>
  )
}
