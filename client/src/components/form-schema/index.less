// @import '../styles/variable.less';

// 主要入口
.abi-form-schema-main {
  overflow: hidden;
  font-size: 14px;
}

// 行元素
.abi-form-schema-row,
.abi-form-schema-column {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 32px;
  padding: 6px 8px;
  line-height: 1;
  border-bottom: 1px solid #f1f1f1;

  &:last-of-type,
  &:last-child {
    border: none;
  }

  .form-input {
    width: 100%;
    .ant-input-number-prefix {
      color: #999;
    }
  }

  span.ant-radio + * {
    padding-right: 0;
  }
  .ant-checkbox + span {
    padding-right: 0;
  }

  .flex-1 {
    flex: 1;
  }

  .anticon-eye,
  .anticon-eye-invisible {
    user-select: none;
    font-size: 14px;
    opacity: 0.9;
    cursor: pointer;

    &:hover {
      color: @primary-color;
    }
  }

  .ant-input-number-group-addon {
    background-color: #fff;
    padding: 0 8px;

    + .ant-input-number {
      border-left: none;
      box-shadow: none;
      border-color: @border-color-base;
      .ant-input-number-input {
        padding-left: 0;
      }
    }
  }
}

// 列元素
.abi-form-schema-column {
  flex-direction: column;
  align-items: inherit;
  border-bottom: 1px solid #f1f1f1;
  > .abi-form-schema-item-title {
    margin-bottom: 6px;
    .anticon:last-of-type {
      margin-right: 0;
    }
  }
  .ant-input-textarea {
    position: relative;
    &.ant-input-textarea-show-count::after {
      position: absolute;
      right: 4px;
      bottom: 4px;
      z-index: 2;
      font-size: 13px;
    }
  }
}

.abi-form-schema-item-checkbox {
  > div {
    display: flex;
    flex-wrap: wrap;
  }
  .ant-checkbox-wrapper {
    margin: 0;
    margin-right: 8px;
    margin-bottom: 2px;
  }
}


// 临时修复
.abi-form-schema-tabs-view {
  .ant-tabs-nav {
    background-color: #fff !important;
    border-bottom: 1px solid #f1f1f1;
    height: 32px !important;
  }
}

.abi-form-schema-echart-custom-legend,
.abi-form-schema-echart-custom-beautify {
  & > .view-box {
    margin-left: 10px;
  }
}

.abi-form-schema-column.abi-form-schema-font-view {
  margin-bottom: 8px;
}
