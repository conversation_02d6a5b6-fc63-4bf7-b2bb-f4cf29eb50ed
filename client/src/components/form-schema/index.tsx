/* eslint-disable react/no-unused-prop-types */
import './index.less'

import React, { createElement, CSSProperties } from 'react'

import Icon from '@/components/icons/iconfont-icon'
import Markdown from '@/components/markdown-view'
import UploadImage from '@/components/upload-file/upload-image'

import { FormSchema as F2 } from './f2'
import type { FormSchemaBase } from './type'

export { default as FormSchemaEditor } from './editor-view'
export { getSchemaDefaultValue as getInitDefaultValue } from './utils'

// 注入的组件
const injectComponentMap = {
  Icon,
  UploadImage,
  Markdown
}

export interface FormSchemaProps<T extends object = any> {
  /** 表单模型 */
  schema?: Record<string, FormSchemaBase>
  /** 表单联动的数据 */
  value?: T
  id?: string
  onChange?: (value?: T) => T
  className?: string
  style?: CSSProperties
  /**
   * 用于动态修饰单个 schema
   * 例如从 redux 里读取数据，插入到 type='select' 的  options 里实现动态 options
   */
  schemaModifyFn?: (key: string, schemaItem: FormSchemaBase) => FormSchemaBase
  /**
   * 需要获取组件数据时触发
   */
  getComponentData?: () => ({
    fieldMap: any[]
    data: any[]
  }) | undefined | void
}

/**
 * 表单模型渲染器入口
 * @param props
 */
export function FormSchema(props: FormSchemaProps) {
  return createElement(F2 || (() => null), { ...props, injectComponentMap } as any)
}

/**
 * 验证 valuepath 重复性
 * @param schema
 */
export function verifySchema(schema: Record<string, FormSchemaBase>) {
  return []
}
