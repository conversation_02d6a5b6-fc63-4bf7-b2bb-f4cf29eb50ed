import { Button } from 'antd'
import React from 'react'

import { PubSubVerbose as PubSub } from '@/utils/pubsub'

import ItemLayout from '../components/item-layout'
import type { FormSchemaBase } from '../type'

export interface NotifyBtnViewProps extends FormSchemaBase {
  btnTitle?: string
  eventKey?: string
  value?: string
  onChange: (val: string) => any
}

/** 通知组件按钮，一般用于控制自定义组件弹出编辑界面 */
export default function NotifyBtnView(props: NotifyBtnViewProps) {
  const {
    btnTitle, eventKey, title, icon, caption,
    value, onChange
  } = props

  return (
    <ItemLayout title={title} icon={icon} caption={caption} className='abi-form-schema-text'>
      <Button
        className='w-full'
        type='primary'
        onClick={() => {
          const selectedDom = document.querySelector('.screen-element.selected')
          if (selectedDom) {
            PubSub.publish(`notify:${selectedDom.id}/${eventKey || 'edit'}`, { onChange })
          }
        }}
      >{btnTitle || '编辑'}</Button>
    </ItemLayout>
  )
}
