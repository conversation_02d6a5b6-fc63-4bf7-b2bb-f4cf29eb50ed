import type { FormKey } from './const'

export interface InjectComponentMap {
  Icon?: (props) => JSX.Element
  UploadImage?: (props) => JSX.Element
  Markdown?: (props) => JSX.Element
}

export interface FormSchemaBase {
  title?: string // 字段显示名称
  icon?: string // iconfont 图表库的图标，不是 antd 图表，会显示在 title 前
  type: FormKey | (string & {}) // 类型（必填）
  name?: string // 字段名称（英文）
  defaultValue?: string | number | any | object | any[] // 默认值
  description?: string // 配置描述
  valuePath?: string // 映射到字段的 value path
  array?: {
    valuePath?: string // 如果有 array.valuePath 会作为父级，与 valuePath 拼接成一个遍历现象
    iterate?: number // 数组模拟迭代次数
  }
  caption?: string | any // 配置项标注（注释），支持 markdown
  nodes?: string // 注释内容，不会显示在界面上

  // 通过eval 执行是否隐藏
  displayHide?: string
  // TODO: 目前未实现，显示规则，关联联动时用，对标的是 showInPanel
  displayRules?: {
    // 定义通过逻辑时是显示还是隐藏
    visible?: boolean
    // 定义条件的与或关系
    logicalType: 'or' | 'and'
    // 定义条件的逻辑关系
    conditions: {
      path: string
      op: 'eq' | 'ne' | 'gt' | 'lt' | 'gte' | 'lte' | 'in' | 'nin'
      value: string
    }[]
  }
  order?: number // 位置排序用
  // TODO: 未实现
  ignore?: string[] // 套件有的，用于忽略某些配置项
  hidden?: boolean // 隐藏掉
  layout?: 'row' | 'column'
  unfold?: boolean

  /** 动态注入的组件，例如上传组件 */
  injectComponentMap?: InjectComponentMap
  /** 获取组件的数据 */
  getComponentData?: () => ({
    fieldMap: Record<string, {
      id: string,
      name: string,
      dataType: 'string' | 'date' | 'number',
      [key: string]: any
    }>
    data: any[]
  }) | undefined | void
}

export interface FormSchemaType {
  [key: string]: FormSchemaBase & {
    valuePath?: string
    openText?: string
    closeText?: string
    options?: { value: any; label: string }[]
    layout?: 'row' | 'column'
    maxLength?: number
    algin?: 'left' | 'center' | 'right'
    maxItem?: number
    tabPrefix?: string
    mode?: string
    min?: number
    max?: number
    unfold?: boolean
    suffix?: string
    prefix?: string
    step?: number

    children?:
    | {
      title: string
      children?: FormSchemaType
    }[]
    | FormSchemaType
    | any
  }
}
