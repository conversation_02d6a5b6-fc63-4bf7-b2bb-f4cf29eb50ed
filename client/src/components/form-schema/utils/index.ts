import _ from 'lodash'

import { FONT_COM_KEYS, FONT_COM_MAP, FORM_MAP, IS_GROUP, IS_NESTED_LIST } from '../const'
import type { FormSchemaBase } from '../type'

/**
 * 获取组件注入的数据
 * @returns
 */
export const getInjectValue = (value: any, { type, valuePath, array }: FormSchemaBase) => {
  if (IS_GROUP.has(type)) return value
  // 这几个特殊处理

  // 字体 font 哪些 valuePath 可以填一个空值
  if (FONT_COM_KEYS.has(type)) {
    return _.pick(!!valuePath ? _.get(value, valuePath) : value, FONT_COM_MAP[type])
  }

  // TODO: arrayValuePath 取值法
  if (array?.valuePath && valuePath) {
    return _.get(value, `${array.valuePath}[0].${valuePath}`)
  }

  return _.get(value, valuePath || '')
}

export const schemaValueChange = (allValue, schema, onChange) => val => {
  const _value = _.cloneDeep(allValue) // TODO: 这里不深拷有问题
  const { valuePath, type, array } = schema

  if (valuePath === undefined && !IS_GROUP.has(type)) {
    console.warn('form-schema update, valuePath is isEmpty.', schema)
  }

  // 对象类型，用于填充默认值
  if (type === 'object') {
    return onChange(_.merge({}, _value, val))
  }

  // valuePath 为空时，字体套件比较特殊，直接映射到根上
  if (FONT_COM_KEYS.has(type)) {
    FONT_COM_MAP[type].forEach(k => {
      const path = !valuePath ? k : `${valuePath}.${k}`
      _.set(_value, path, val[k])
    })
    return onChange(_value)
  }

  // 合并对象内容
  if (['echart-theme', 'echart-base-config', 'echart-custom-theme'].includes(type)) {
    const prev = _.get(_value, valuePath || '')
    const newValue = _.mergeWith(prev, val, (objValue, srcValue) => {
      if (_.isArray(srcValue) && _.isArray(objValue)) return srcValue
      return undefined
    })
    _.set(_value, valuePath, newValue)
    return onChange(_value)
  }

  // 嵌套列表，常见于 echart 那几个数组类型的
  if (IS_NESTED_LIST.has(type)) {
    const listValuePath = _.last<string>(valuePath.split('.'))
    if (listValuePath) _.set(_value, valuePath, val[listValuePath])
    return onChange(_value)
  }

  // 组类型处理
  if (IS_GROUP.has(type)) {
    // TODO: 组件嵌套 list 时产生的问题
    if (type === 'list' && valuePath) {
      _.set(_value, valuePath, val)
    }
    // 如果是组类型则直接更新
    return onChange(val)
  }

  // 大部分 base 都会到这里
  // 最后到这里
  // 有路径且非组类型
  if (valuePath && !IS_GROUP.has(type)) {
    // TODO: array.valuePath
    // 如果有 array.valuePath 会作为父级，与 valuePath 拼接成一个遍历现象
    if (array?.valuePath && valuePath) {
      let list = _.get(_value, array.valuePath, [])
      if (_.isArray(list)) {
        list = list.map(item => {
          _.set(item, valuePath, val)
          return item
        })
        _.set(_value, `${array.valuePath}`, list)
        return onChange(_value)
      }
    }

    // 一般的更新都到这里
    _.set(_value, valuePath, val)
    return onChange(_value)
  }
}

export const getComponentDefaultValue = (comp: any, item: FormSchemaBase) => {
  if (!comp) return
  const defaultValue = comp.defaultValue || comp.type?.defaultValue
  if (_.isFunction(defaultValue)) return defaultValue(item)
  return defaultValue
}

// 深度递归对象
export const deepObject = (obj: any, parent: any, callback: (o: any, p: any, key: string) => any) => {
  // 判断说明是数据类型
  if (typeof obj !== 'object') return
  // eslint-disable-next-line guard-for-in
  // eslint-disable-next-line no-restricted-syntax
  for (const key in obj) {
    if (typeof obj[key] === 'object') {
      callback(obj[key], parent, key)
      deepObject(obj[key], obj, callback)
    }
  }
}

/**
 * 获取初始化默认数据
 * TODO: 将默认数据放到定义表的 configDefine 里
 * 这个函数非常重要
 * @param schema
 * @param cb
 */
export const getSchemaDefaultValue = (schema: Record<string, FormSchemaBase> = {}) => {
  const _data = {}

  const getDefaultValue = (item: FormSchemaBase, parent: FormSchemaBase) => {
    if (!FORM_MAP[item?.type]) return // 不存在

    if ('type' in item && 'valuePath' in item && 'defaultValue' in item) {
      const { type, valuePath, defaultValue } = item

      if (parent?.type === 'list') return
      if (!valuePath) return // 空路径

      const prev = _.get(_data, valuePath)
      let defaultVal = defaultValue
      // 空对象取默认值
      if (_.isEmpty(defaultVal) && _.isObject(defaultVal)) {
        defaultVal = getComponentDefaultValue(FORM_MAP[type], item)
      }

      // _.isEmpty(item.defaultValue) && _.isObject(item.defaultValue) ?
      // item.defaultValue

      // array.valuePath 的处理
      if (item.array?.valuePath) {
        const arrPrev = _.get(_data, item.array.valuePath, []) || []
        // 进行迭代
        if ((item.array?.iterate || 0) > 0) {
          const dfv = _.isArray(defaultVal) ? defaultVal[0] : defaultVal
          const list = [...new Array(item.array.iterate)].map(() => dfv)
          _.set(_data, item.array.valuePath, _.merge([], arrPrev, list))
        } else {
          _.set(_data, item.array.valuePath, _.merge(arrPrev, defaultVal))
        }
        return
      }

      // 如果存在之前的数据，那么进行合并
      if (prev && _.isObject(defaultVal)) {
        _.set(_data, valuePath, _.merge({}, prev, defaultVal))
      } else {
        _.set(_data, valuePath, defaultVal)
      }
    }
  }

  deepObject(schema, null, getDefaultValue)

  return _data
}

/**
 * 验证 valuepath 重复性
 * @param schema
 */
export function verifySchema(schema: Record<string, FormSchemaBase>) {
  const valuePathArray: FormSchemaBase[] = []
  const errorMessages: any = {
    valuePath: [],
    valuePathRequired: [],
    defaultValue: []
  }

  deepObject(schema, null, (item: any, parent: any, key: string) => {
    if (!item) return

    if (item.type && item.valuePath === undefined && !IS_GROUP.has(item.type)) {
      errorMessages.valuePathRequired.push(`"${key}（${item.title}）"`)
    }
    if (item.type && item.valuePath) valuePathArray.push(item)
    if (item.type === 'list' && item.defaultValue === undefined) {
      errorMessages.defaultValue.push(`"${item.title}"`)
    }
  })

  const valuePathGroup = _.groupBy(valuePathArray, 'valuePath')
  // 检查 valuePath
  _.keys(valuePathGroup).forEach(key => {
    if (valuePathGroup[key].length > 1) {
      errorMessages.valuePath.push(key)
    }
  })

  // ... ..
  return _.keys(errorMessages)
    .map(key => {
      if (key === 'valuePath' && errorMessages[key].length > 0)
        return `schema valuePath 重复，请检查：${errorMessages[key].join('，')}`

      if (key === 'valuePathRequired' && errorMessages[key].length > 0)
        return `schema valuePath 未填写，请检查：${errorMessages[key].join('，')}`

      if (key === 'defaultValue' && errorMessages[key].length > 0)
        return `schema title 为 ${errorMessages[key].join('')} 的需要添加 defaultValue`

      return ''
    })
    .filter(i => i)
}

export default getSchemaDefaultValue
