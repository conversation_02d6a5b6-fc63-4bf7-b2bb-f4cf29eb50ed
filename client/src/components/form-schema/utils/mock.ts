import type { FormSchemaBase } from '../type'


/**
 * 获取 demo 数据
 */
export function getDemoSchema() {
  const data: any = {
    echartGrid: {
      title: 'echart 网格',
      type: 'echart-grid',
      defaultValue: {},
      valuePath: 'echart.option.grid'
    },
    echartTheme: {
      title: 'echart 主题',
      type: 'echart-theme',
      unfold: !false,
      defaultValue: {},
      valuePath: 'echart.option'
    },
    opacity: {
      title: '透明度',
      type: 'slider',
      max: 1,
      min: 0,
      step: 0.01,
      layout: 'row',
      showValue: false,
      suffix: '{{ (value*100).toFixed(0) }}%',
      defaultValue: 1,
      valuePath: 'style.opacity'
    },
    shadow: {
      title: '阴影套件',
      type: 'shadow',
      defaultValue: {},
      valuePath: 'style'
    },
    code: {
      title: '代码编辑器',
      type: 'code',
      language: 'typescript',
      defaultValue: '/** 更新代码 */\nfunction add(a, b) {\n  return a + b\n}',
      valuePath: 'code'
    },
    backgroundImage: {
      title: '背景图片套件',
      type: 'background-image',
      useUrl: true,
      enableCropper: true,
      defaultValue: {},
      valuePath: 'style'
    },
    echartAnimation: {
      title: 'echart 动画',
      type: 'echart-animation',
      unfold: false,
      defaultValue: {},
      valuePath: 'echart.option.animation'
    },
    echartTitle: {
      title: 'echart 标题',
      type: 'echart-title',
      unfold: false,
      defaultValue: {},
      valuePath: 'echart.option.title'
    },
    echartTooltip: {
      title: 'echart 提示框',
      type: 'echart-tooltip',
      // unfold: false,
      defaultValue: {},
      valuePath: 'echart.option.tooltip'
    },
    echartAxis: {
      title: 'echart 坐标 x 轴',
      type: 'echart-xAxis',
      unfold: false,
      defaultValue: [],
      valuePath: 'echart.option.xAxis'
    },
    echartYAxis: {
      title: 'echart 坐标 Y 轴',
      type: 'echart-yAxis',
      unfold: false,
      defaultValue: [],
      valuePath: 'echart.option.yAxis'
    },
    echartLegend: {
      title: 'echart 图例',
      type: 'echart-legend',
      defaultValue: {},
      valuePath: 'echart.option.legend'
    },
    background: {
      title: '背景套件',
      type: 'background',
      defaultValue: {},
      valuePath: 'style'
    },
    percent: {
      title: '百分比',
      type: 'percent',
      defaultValue: undefined,
      valuePath: 'percent'
    },
    distance: {
      title: '距离套件',
      type: 'distance',
      defaultValue: {},
      valuePath: 'distance'
    },
    font: {
      title: '字体套件',
      type: 'font',
      defaultValue: {
        fontSize: 16,
        fontFamily: 'Microsoft Yahei',
        fontWeight: 'normal',
        fontStyle: 'normal',
        color: '#222',
        lineHeight: 16
      },
      valuePath: 'style'
    },
    border: {
      title: '边框套件',
      type: 'border',
      defaultValue: {},
      valuePath: 'style'
    },
    margin: {
      title: '边距套件',
      type: 'margin',
      defaultValue: {},
      valuePath: 'style'
    },
    size: {
      title: '大小',
      type: 'number',
      defaultValue: 20,
      valuePath: 'options.size',
      caption: 'xxx',
      prefix: '增加',
      suffix: '元'
    },
    color: {
      title: '颜色',
      type: 'color',
      icon: '文字颜色',
      defaultValue: '#f45',
      valuePath: 'options.font.theme.color'
    },
    text: {
      title: '标题',
      type: 'text',
      icon: '末行中对齐',
      defaultValue: '666',
      valuePath: 'options.text'
    },
    textArea: {
      title: '多行文本',
      type: 'text-area',
      defaultValue: '666',
      valuePath: 'options.textArea'
    },
    switch: {
      title: '开关',
      type: 'switch',
      defaultValue: false,
      openText: '开启',
      closeText: '关闭',
      valuePath: 'options.v1'
    },
    slice: {
      title: '滑块',
      type: 'slider',
      defaultValue: 10,
      valuePath: 'options.slider'
    },
    date: {
      title: '日期',
      type: 'date',
      format: 'YYYY-MM-DD HH:mm:ss',
      // defaultValue: 'startOf day +2s -10s',
      defaultValue: '{{ dayjs().startOf(`day`).add(-20, `s`).format() }}',
      valuePath: 'testDate'
    },
    date2: {
      title: '日期（范围）',
      type: 'date',
      mode: 'range',
      format: 'YYYY-MM-DD HH:mm:ss',
      defaultValue: [
        // 'startOf day +2s -10s',
        '{{ dayjs().startOf(`day`).add(-20, `s`).format() }}',
        '2022-03-31 18:10:09'
      ],
      valuePath: 'testDate2'
    },
    select: {
      title: '选择',
      type: 'select',
      defaultValue: '123',
      valuePath: 'options.v2',
      mode: 'multiple',
      options: [
        { value: '123', label: '哈哈哈' },
        { value: '234', label: '哈哈哈2' }
      ]
    },
    radio: {
      title: '单选',
      type: 'radio',
      valuePath: 'options.v3',
      options: [
        { value: '123', label: '哈哈哈' },
        { value: '234', label: '哈哈哈2' },
        { value: '233', label: '哈哈哈3' },
        { value: '2322', label: '哈哈哈4' }
      ]
    },
    checkbox: {
      title: '多选',
      type: 'checkbox',
      valuePath: 'options.v5',
      options: [
        { value: '123', label: '哈哈哈' },
        { value: '234', label: '哈哈哈2' },
        { value: '233', label: '哈哈哈3' },
        { value: '2322', label: '哈哈哈4' }
      ]
    },
    group1: {
      title: '组1',
      type: 'group',
      children: {
        switch: {
          title: '组里的开关',
          type: 'switch',
          defaultValue: false,
          openText: '开启',
          closeText: '关闭',
          valuePath: 'options.v1'
        }
      }
    },
    group2: {
      title: '组2',
      type: 'group',
      direction: 'row',
      children: {
        switch: {
          title: '组里的开关',
          type: 'switch',
          defaultValue: false,
          openText: '开启',
          closeText: '关闭',
          valuePath: 'options.v1'
        },
        switch2: {
          title: '组里的开关2',
          type: 'switch',
          defaultValue: false,
          openText: '开启',
          closeText: '关闭',
          valuePath: 'options.v1'
        }
      }
    },
    group3: {
      title: '组3',
      type: 'group',
      hidden: true,
      children: {
        text: {
          title: '标题',
          type: 'text',
          defaultValue: '666',
          valuePath: 'options.text'
        },
        group1: {
          title: '组1',
          type: 'group',
          children: {
            switch: {
              title: '组里的开关',
              type: 'switch',
              defaultValue: false,
              openText: '开启',
              closeText: '关闭',
              valuePath: 'options.v1'
            }
          }
        }
      }
    },
    tabs: {
      title: '标签组',
      type: 'tabs',
      hidden: !true,
      children: [
        {
          title: 'x 轴',
          children: {
            text: {
              title: '标题',
              type: 'text',
              suffix: '元',
              defaultValue: '666',
              valuePath: 'options.text'
            }
          }
        },
        {
          title: 'Y 轴',
          children: {
            switch: {
              title: 'tab 里的开关',
              type: 'switch',
              defaultValue: false,
              openText: '开启',
              closeText: '关闭',
              valuePath: 'options.v1'
            }
          }
        }
      ]
    },
    // menus: {
    //   title: '菜单',
    //   type: 'menu',
    //   algin: 'center',
    //   children: [
    //     {
    //       title: '菜单1',
    //       children: {
    //         switch: {
    //           title: '菜单里的开关',
    //           type: 'switch',
    //           defaultValue: false,
    //           openText: '开启',
    //           closeText: '关闭',
    //           valuePath: 'options.v1'
    //         }
    //       }
    //     },
    //     {
    //       title: '菜单2',
    //       children: {
    //         text: {
    //           title: '文本',
    //           type: 'text',
    //           suffix: '元',
    //           defaultValue: '666',
    //           valuePath: 'options.menuText'
    //         }
    //       }
    //     }
    //   ]
    // },
    tabs2: {
      title: '标签组',
      type: 'tabs',
      direction: 'vertical',
      hidden: !true,
      children: [
        {
          title: '图例',
          children: {
            text: {
              title: '标题',
              type: 'text',
              suffix: '元',
              defaultValue: '666',
              valuePath: 'options.tabs.text'
            }
          }
        },
        {
          title: '提示框',
          children: {
            switch: {
              title: '滴滴',
              type: 'switch',
              defaultValue: false,
              openText: '开启',
              closeText: '关闭',
              valuePath: 'options.tabs.v1'
            }
          }
        }
      ]
    },
    list: {
      title: 'x 轴',
      type: 'list',
      valuePath: 'fontList',
      mode: 'tabs',
      tabPrefix: '轴',
      defaultValue: [
        {
          fontText: '666',
          fontColor: '#f34'
        }
      ],
      children: {
        text: {
          title: '标题',
          type: 'text',
          defaultValue: '666',
          valuePath: 'fontText'
        },
        color: {
          title: '颜色',
          type: 'color',
          defaultValue: '#f45',
          valuePath: 'fontColor'
        }
      }
    }
  }

  const data2: any = {
    echartBase: {
      title: 'echart 基础设置',
      type: 'echart-base-config',
      defaultValue: {},
      valuePath: 'echart.option'
    },
    echartTheme: {
      title: 'echart 主题',
      type: 'echart-theme',
      unfold: !false,
      defaultValue: {},
      valuePath: 'echart.option'
    },
    // echartTheme: {
    //   title: 'echart 主题',
    //   type: 'echart-theme',
    //   defaultValue: {},
    //   valuePath: 'echart.option'
    // },
    // echartAnimation: {
    //   title: 'echart 动画',
    //   type: 'echart-animation',
    //   unfold: false,
    //   defaultValue: {},
    //   valuePath: 'echart.option.animation'
    // },
    radio: {
      title: '单选',
      type: 'radio',
      valuePath: 'options.v3',
      options: [
        { value: '123', label: '哈哈哈', icon: '预览' },
        { value: '234', label: '哈哈哈2', icon: '商城' },
        { value: '233', label: '哈哈哈3', icon: '学院' },
        { value: '2322', label: '哈哈哈4', icon: '多行' }
      ]
    },
    background: {
      title: '背景套件',
      type: 'background',
      defaultValue: {},
      valuePath: 'style'
    },
    shadow: {
      title: '阴影套件',
      type: 'shadow',
      defaultValue: {},
      valuePath: 'style'
    },
    shadow2: {
      title: '阴影套件',
      type: 'shadow',
      mode: 'text-shadow',
      defaultValue: {},
      valuePath: 'style'
    },
    font: {
      title: '字体套件',
      type: 'font',
      defaultValue: {
        fontSize: 16,
        fontFamily: 'Microsoft Yahei',
        fontWeight: 'normal',
        fontStyle: 'normal',
        color: '#222'
        // lineHeight: 16
      },
      valuePath: 'style'
    },
    barWidth: {
      title: '柱子宽度',
      type: 'number',
      suffix: 'px',
      valuePath: 'barWidth',
      array: {
        valuePath: 'options.series',
        iterate: 1
      },
      defaultValue: { barWidth: 12 }
    },
    textArea: {
      title: '多行文本',
      type: 'text-area',
      defaultValue: '666',
      valuePath: 'options.textArea'
    }
  }

  const data3: any = {
    opacity: {
      title: '透明度',
      valuePath: 'opacity',
      type: 'slider',
      step: 0.01,
      max: 1,
      min: 0,
      defaultValue: 1,
      suffix: '{{ _.round(value * 100, 0) }}%'
    }
  }

  return data as Record<string, FormSchemaBase>
}
