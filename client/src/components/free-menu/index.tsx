import { Dropdown, DropdownProps, Menu, MenuProps } from 'antd'
import { ItemType } from 'antd/es/menu/hooks/useItems'
import React, { CSSProperties } from 'react'
import ReactDOM from 'react-dom'

interface FreeMenuOpts {
  extraDropdownProps?: Partial<DropdownProps>
  extraMenuProps?: Partial<MenuProps>
  overlayExtraStyle?: Partial<CSSProperties>
}

/** 在任意位置展示菜单 */
export default function showFreeMenu(
  x: number,
  y: number,
  menuItems: ItemType[],
  onMenuItemClick: (info: any) => any,
  opts?: FreeMenuOpts
) {
  const { extraDropdownProps, extraMenuProps, overlayExtraStyle } = opts || {}
  const div = document.createElement('div')
  div.setAttribute('style', 'pointer-events: none;')
  const body = document.body

  body.appendChild(div)
  const cleanUp = () => {
    ReactDOM.unmountComponentAtNode(div)
    body.removeChild(div)
  }

  const tmpDivSize = 20
  const freeDivStyle = {
    position: 'absolute' as const,
    zIndex: 99,
    top: y - tmpDivSize / 2 + (window.scrollY || 0), // fix ie11 window.scrollY === undefined
    left: x - tmpDivSize / 2,
    width: tmpDivSize,
    height: tmpDivSize,
    ...(overlayExtraStyle || {})
  }
  const vDom = (
    <Dropdown
      trigger={['click']}
      overlay={
        <Menu
          onClick={inf => {
            onMenuItemClick(inf)
            cleanUp()
          }}
          items={menuItems}
          {...(extraMenuProps || {})}
        />
      }
      visible
      onOpenChange={visible => {
        if (!visible) cleanUp()
      }}
      {...(extraDropdownProps || {})}
    >
      <div style={freeDivStyle}>{'\u00a0'}</div>
    </Dropdown>
  )
  ReactDOM.render(vDom, div)
  return cleanUp
}
