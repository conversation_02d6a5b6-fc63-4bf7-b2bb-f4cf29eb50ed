
.genie-guide-helper {
  position: fixed;
  z-index: 99999;
  background-color: #fff;
  // background-color: @primary-color !important;
  box-shadow: 1px 1px 12px rgba(#111, 24%);
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  user-select: none;
  cursor: pointer;

  transition-property: top left;
  transition-duration: 0.75s;
  transition-timing-function: ease-in-out;
  animation: GenieShake 1.5s infinite alternate; /* 调用闪动动画 */

  .anticon {
    font-size: 26px;
  }

  &.start {
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}

.genie-guide-helper-overlay {
  background-color: transparent !important;
  z-index: 99999;

  .genie-guide-content {
    // background-color: @primary-color;
    background-color: #fff;
    box-shadow: 1px 1px 12px rgba(#111, 16%);
    padding: 6px 8px;
    border-radius: 5px;
    position: relative;
    max-width: 320px;

    > .close-icon {
      position: absolute;
      z-index: 5;
      top: 8px;
      right: 8px;
      font-size: 13px;
      color: #777;
      cursor: pointer;
      user-select: none;
    }

    > h4 {
      color: #333;
      margin-bottom: 2px;

      .skip {
        font-size: 13px;
      }
    }

    > summary {
      // color: rgba(#fff, 0.8);
      display: flex;
      .text {
        display: inline-block;
      }
    }
  }

  .genie-guide-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 12px;

    button {
      margin-left: 12px;
      box-shadow: 1px 1px 12px rgba(#111, 20%);

      &:first-child {
        margin-left: 0;
      }
    }
  }
}

.genie-guide-highlight-box {
  animation: HighlightBox 1.5s infinite alternate; /* 调用闪动动画 */
  box-shadow: 0 0 6px rgba(@primary-color, 0.3)!important;
  border-radius: 4px;
}

/* 定义闪动动画 */
@keyframes HighlightBox {
  0% {
    opacity: 0.85;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}

@keyframes GenieShake {
  0% {
    transform: translateX(0) rotate(0deg);
  }
  25% {
    transform: translateY(1px) scale(1) rotate(8deg);
  }
  50% {
    transform: rotate(-8deg);
  }
  75% {
    transform: translateY(1px) scale(0.9) rotate(8deg);
  }
  100% {
    transform: translateX(0) scale(1) rotate(0deg);
  }
}
