import './index.less'

import Icon, { CloseOutlined } from '@ant-design/icons'
import { MenuDropdown } from '@sugo/design'
import { fastMemo } from '@sugo/design/functions'
import { history } from '@umijs/max'
import { useAsyncEffect, useMemoizedFn, useUpdateEffect } from 'ahooks'
import { Button, Dropdown, Tooltip } from 'antd'
import cn from 'classnames'
import isMobile from 'is-mobile'
import _ from 'lodash'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import Ityped from 'react-ityped'

import { useNewBi } from '@/hooks/use-new-bi'
import { Cloud } from '@/services'
import { getNodePosition } from '@/utils/dom'

import { ReactComponent as GenieSvg } from './genie.svg'

export interface GenieGuideHelperProps {
  genieGuides: {
    key: string
    title?: string
    summary?: string
    selector?: string | (() => HTMLDivElement | any)
    wait?: number
    offset?: number[]
    autoClick?: boolean
    autoChange?: number
    [key: string]: any
  }[]
}

/**
 * 小精灵助手
 *
 * @returns
 * @deprecated 弃用
 */
function _GenieGuideHelper(props: GenieGuideHelperProps) {
  const { genieGuides = [] } = props

  // const [activeKey, setActiveKey] = useLocalStorageState<string | boolean | undefined>('sugo-bi-genie-guide-helper')

  const fixValue = v => {
    if (v === 'true') return true
    if (v === 'false') return false
    return v
  }

  const [activeKey, set] = useState<string | boolean | null>(() => {
    const v = window.localStorage.getItem('sugo-bi-genie-guide-helper')
    return fixValue(v)
  })

  const setActiveKey = (key: string) => {
    set(key)
    setTimeout(async () => {
      const userId = _.get(window, 'user.id', 'dev')
      const res = await Cloud.$redis.hset('sugo-bi-genie-guide-helper', { [userId]: String(key) })
    }, 100)
  }

  const [, guide] = useMemo(() => {
    const idx = _.findIndex(genieGuides, i => i.key === activeKey)!
    return [idx, genieGuides[idx]]
  }, [activeKey, genieGuides])

  const isCenter = guide?.key === 'start' || guide?.key === 'complete' || !!guide?.autoChange

  const [open, setOpen] = useState(false)
  const [position, setPosition] = useState<any>({ top: -100, left: -100 })

  const { isNewBi } = useNewBi()

  const time1Ref = useRef<any>(0)
  const time2Ref = useRef<any>(0)
  const time3Ref = useRef<any>(0)
  const time4Ref = useRef<any>(0)

  const openedMapRef = useRef<Record<string, boolean>>({})

  const menuItems = [
    { key: 'start', label: '重置' },
    { key: 'prev', label: '上一步' },
    { key: 'next', label: '下一步' },
    { key: 'close', label: '不再显示' }
  ]

  const close = () => {
    setActiveKey(false)
  }

  const cleanTime = () => {
    clearTimeout(time1Ref.current)
    clearTimeout(time2Ref.current)
    clearTimeout(time3Ref.current)
    clearTimeout(time4Ref.current)
  }

  const fineElement = useMemoizedFn(selector => {
    // 移动到目标
    let count = 5
    let time: any = 0
    return new Promise(rs => {
      let el = _.isString(selector) ? document.querySelector(selector) : selector?.()
      if (!el) {
        time = setInterval(() => {
          el = _.isString(selector) ? document.querySelector(selector) : selector?.()
          count -= 1
          if (count === 0 || el) {
            clearInterval(time)
            if (!el) {
              console.log('实在找不到入口')
            }
            rs(el)
          }
        }, 1000)
      } else {
        rs(el)
      }
    })
  })

  const setElement = useMemoizedFn((key: string, offset: number[], el: Element | any) => {
    const rect = getNodePosition(el)

    setPosition({ left: rect.left + el.clientWidth + 8 + (offset[0] || 0), top: rect.top - 2 + (offset[1] || 0) })
    el.classList.add('genie-guide-highlight-box')

    if (guide.autoClick !== false && !el._clicked) {
      console.log('自动点击', key)
      if (!el._clicked) {
        el.addEventListener('click', () => {
          window._genie_guide_next?.(key)
        }, { once: true })
        el._clicked = true
      }
    } else {
      console.log('不自动点击', key)
    }
  })

  const genieGuideGo = useMemoizedFn((key: string) => {
    if ((key as any) === false) return
    const next = _.find(genieGuides, i => i.key === key)
    cleanTime()
    setOpen(false)
    console.log('key', next?.key)

    if (next) {
      const els = document.querySelectorAll('.genie-guide-highlight-box')
      _.forEach(els, el => {
        el.classList.remove('genie-guide-highlight-box')
      })

      // if (next.autoClick === false) return

      time3Ref.current = setTimeout(async () => {
        if (next.selector) {
          const el: any = await fineElement(next.selector)
          if (el) setElement(next.key, next.offset || [], el)
        } else {
          setPosition({ top: '40%', left: '50%' })
        }

        if (next.autoChange) {
          time4Ref.current = setTimeout(() => {
            window._genie_guide_next?.(key)
          }, next.autoChange)
        }

        setActiveKey(next?.key)

        time2Ref.current = setTimeout(() => {
          console.log('打开', next.title)
          openedMapRef.current[next.key!] = true
          setOpen(true)
        }, 800)
      }, 300 + (next.wait || 0))
    }
  })

  const genieGuideNext = useMemoizedFn(async (key: string) => {
    const idx = _.findIndex(genieGuides, i => i.key === key)
    const next = genieGuides[idx + 1]
    genieGuideGo(next?.key || 'start')
  })

  const genieGuidePrev = useMemoizedFn(async (key: string) => {
    const idx = _.findIndex(genieGuides, i => i.key === key)
    const next = genieGuides[idx - 1]
    genieGuideGo(next?.key || 'start')
  })

  useAsyncEffect(async () => {
    // 从 redis 里面加载
    const userId = _.get(window, 'user.id', 'dev')
    const res = await Cloud.$redis.hget('sugo-bi-genie-guide-helper', userId)
    if (res) {
      setActiveKey(fixValue(res))
    }
  }, [])

  useUpdateEffect(() => {
    console.log(activeKey)
  }, [activeKey])

  const renderAnimatedText = (text: string) => (
    <Ityped
      key={`${activeKey}_${open}`}
      strings={[text]}
      className='text'
      typeSpeed={60}
      startDelay={100}
      backspace={false}
      loop={false}
      showCursor={false}
    />
  )

  const dropdownRender = () => (
    <div>
      <div className='genie-guide-content'>
        <h4>
          {guide.title}

          <Button size='small' type='link' onClick={() => genieGuideNext(guide.key)} className='skip'>
            跳过
          </Button>
        </h4>
        <summary>
          {renderAnimatedText(guide.summary!)}
        </summary>
        <div style={{ height: 0, overflow: 'hidden' }}>{guide.summary}</div>
        <MenuDropdown
          menuItems={menuItems}
          overlayStyle={{ zIndex: 1000000 }}
          onMenuClick={e => {
            e.domEvent.stopPropagation()
            if (e.key === 'start') genieGuideGo('start')
            if (e.key === 'close') close()
            if (e.key === 'next') genieGuideNext(guide.key)
            if (e.key === 'prev') genieGuidePrev(guide.key)
          }}
        >
          <CloseOutlined className='close-icon' />
        </MenuDropdown>
      </div>
      <div className='genie-guide-actions'>
        {guide.key === 'start' &&
          <>
            <Button size='small' danger onClick={close}>不再显示</Button>
            <Button type='primary' size='small' onClick={e => {
              e.stopPropagation()
              if (window.location.href.indexOf('/framework/workspace') === -1) {
                history.push('/framework/workspace')
              }
              genieGuideNext(guide.key)
            }}>立刻开始</Button>
          </>
        }
        {guide.key === 'complete' &&
          <Button type='primary' size='small' onClick={close}>再见！</Button>
        }
      </div>
    </div>
  )

  useEffect(() => {
    if ((!activeKey && activeKey !== false) || activeKey === 'start') {
      // const start = _.find(genieGuides, i => i.key === 'start')
      setTimeout(() => {
        console.log('启动小精灵')
        genieGuideGo('start')
      }, 1000 * 2)
    } else if (_.isString(activeKey)) {
      genieGuideGo(activeKey)
    }

    window._genie_guide_next = genieGuideNext
  }, [])

  useUpdateEffect(() => {
    window._genie_guide_next = genieGuideNext
    window._genie_guide_activeKey = activeKey as string
  }, [activeKey])

  if (activeKey === false || !guide) return null
  if (isMobile()) return null
  if (!isNewBi && window.location.href.indexOf('/abi/framework') === -1) return null

  return (
    <Tooltip title='你好，我是指引小精灵，阿甘' align={{ offset: [0, 3] }}>
      <Dropdown
        placement={isCenter ? 'bottomCenter' : 'bottomLeft'}
        open={open}
        dropdownRender={dropdownRender}
        overlayClassName='genie-guide-helper-overlay'
        align={{ offset: isCenter ? [0, 8] : [50, -42] }}
      >
        <div className={cn('genie-guide-helper', guide?.key)}
          style={{
            top: position.top,
            left: position.left
          }}
          onClick={e => {
            e.stopPropagation()
            setOpen(true)
          }}
        >
          <Icon component={GenieSvg} />
        </div>
      </Dropdown>
    </Tooltip>
  )
}

export const GenieGuideHelper = fastMemo(_GenieGuideHelper)
