import _ from 'lodash'
import React from 'react'

import { escapeForRegex } from '@/utils'


interface HighlightTextProps {
  text: string | null | undefined
  highlight: string
  className?: string
  style?: React.CSSProperties
}

/** 高亮文本组件 */
function HighlightText(props: HighlightTextProps) {
  const { text = '', highlight = '', className, style } = props
  if (!highlight?.trim()) {
    return <span className={className} style={style}>{text}</span>
  }

  const regex = new RegExp(`(${escapeForRegex(highlight)})`, 'gi')
  const parts = text?.split(regex)

  return (
    <span className={className} style={style}>
      {_.filter(parts, Boolean).map((part, i) => (
        regex.test(part) ? <span key={i} className='bg-yellow-300'>{part}</span> : part
      ))}
    </span>
  )
}

export default HighlightText
