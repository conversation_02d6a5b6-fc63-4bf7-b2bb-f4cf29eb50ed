import Icon from '@ant-design/icons'
import { IconComponentProps } from '@ant-design/icons/lib/components/Icon'
import _ from 'lodash'
import React from 'react'

import { ICON_MAPPING } from '@/consts/icons'

export interface CustomIconProps extends IconComponentProps {
  type: keyof typeof ICON_MAPPING | (string & {})
}

/**
 * 图标组件
 * @param CustomIconProps
 */
export function CustomIcon(props: CustomIconProps) {
  return <Icon component={ICON_MAPPING[props.type]} {..._.omit(props as any, 'type')} />
}

export default CustomIcon
