import './index.less'

import { createFromIconfontCN } from '@ant-design/icons'
import { IconComponentProps } from '@ant-design/icons/lib/components/Icon'
import React, { CSSProperties } from 'react'

import PolarisIcon from '@/assets/icons/polaris.png'

import { CSS_PREFIX, ICON_MAP, IconfontType } from './iconfont-icon-const'

const IconFont = createFromIconfontCN({
  scriptUrl: [`${window.staticPrefix}iconfont/iconfont.js`]
})


export interface IconfontIconProps extends IconComponentProps {
  type?: IconfontType | (string & {})
  name?: keyof typeof ICON_MAP | (string & {})
  pointer?: boolean // 是否显示点击的效果
  size?: number // 大小
  color?: string
}

/**
 * IconfontIcon
 * @param props
 */
export default function IconfontIcon(props: IconfontIconProps) {
  const { type, name, pointer, size, color, title, ...rest } = props
  const _type = CSS_PREFIX + (ICON_MAP[name || ''] || type || '')
  const style: CSSProperties = { ...props.style }

  if (pointer) {
    style.cursor = 'pointer'
    style.userSelect = 'none'
  }
  if (size) style.fontSize = size
  if (color) style.color = color

  const innerProps: any = {
    style,
    type: _type
  }

  if (title !== undefined) {
    innerProps.title = title
  }
  if (title === undefined) {
    innerProps.title = name
  }

  if (name === 'indicePolaris') return (
    <img src={PolarisIcon} alt='' className='custom-img-icon' />
  )

  return <IconFont {...rest} {...innerProps} />
}

