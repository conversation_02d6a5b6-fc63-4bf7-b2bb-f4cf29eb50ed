
.joyride-guide-box {
  display: block;
  width: 250px;
  background-color: #fff;
  min-height: 150px;
  border-radius: 3px;
  box-shadow: 1px 2px 12px rgba(#111, 0.12);

  .content {
    min-height: 100px;
    padding: 12px;
    h4 {
      font-weight: bold;
      font-size: 15px;
    }
    summary {
      font-size: 14px;
      color: @primary-color;
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    user-select: none;
  }
}

.react-joyride__beacon {
  display: none !important;
}

.__floater__open {
  z-index: 12000 !important;
  transition-duration: 0.2s !important;
}

#react-joyride-step-0,
#react-joyride-step-1,
#react-joyride-step-2,
#react-joyride-step-3,
#react-joyride-step-4,
#react-joyride-step-5,
#react-joyride-step-6,
#react-joyride-step-7,
#react-joyride-step-8 {
  z-index: 1200 !important;
}
