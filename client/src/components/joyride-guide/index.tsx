import './index.less'

import { useLocalStorageState } from 'ahooks'
import { Button } from 'antd'
import React, { useEffect, useState } from 'react'
import Joyride, { Step } from 'react-joyride'

function TooltipToolbox({
  index,
  step,
  // backProps,
  closeProps,
  primaryProps,
  tooltipProps,
  size,
  enableNotShow
}) {
  return (
    <div {...tooltipProps} className='joyride-guide-box' style={{ zIndex: 1200 }}>

      <div className='content'>
        <h4>
          {step.title}
          <span>（{index + 1}/{size}）</span>
        </h4>
        <summary>{step.content}</summary>
      </div>

      <div className='footer'>
        {enableNotShow ?
          <Button {...closeProps} title='' type='dashed' size='small'>不再显示</Button> :
          <span />
        }
        {/* <Button size='small' {...backProps}>上一步</Button> */}

        {index === size - 1 &&
          <Button {...closeProps} title='' type='primary' size='small'>关闭</Button>
        }
        {index !== size - 1 &&
          <Button {...primaryProps} title='' size='small' type='primary'>下一步</Button>
        }
      </div>

    </div>
  )
}


export interface JoyrideGuideProps {
  steps: Step[]
  onStepIndexChange?: (data: any) => any
  localKey?: string
  enableNotShow?: boolean
  onClose?: (data: any) => any
}

/**
 *
 * 操作指南
 *
 * @example
 * steps:
 * [{
 *   title: '页面管理',
 *   target: '[data-left-menu-key=project] > span',
 *   content: '在这里可以管理你的页面，并且允许创建、编辑、删除页面。',
 *   placement: 'right'
 * }]
 *
*/
export default function JoyrideGuide(props: JoyrideGuideProps) {
  const { steps = [], onStepIndexChange, localKey = '', enableNotShow = true, onClose } = props

  const [local, setLocal] = useLocalStorageState(localKey)
  const [run, setRun] = useState(local !== 'hide')

  const onChange = data => {
    if (data.action === 'close') {
      onClose?.(data)
      setRun(false)
      // 存储到 localstorage 里
      if (localKey) setLocal('hide')
    }
    onStepIndexChange?.(data)
  }

  useEffect(() => {
    setTimeout(() => {
      const el = document.querySelector('.react-joyride__beacon') as HTMLDivElement
      if (el) el.click()
    }, 0)
  }, [])

  if (!run) return null

  return (
    <Joyride
      continuous
      // eslint-disable-next-line react/no-unstable-nested-components
      tooltipComponent={p => <TooltipToolbox {...p} enableNotShow={enableNotShow} />}
      steps={steps}
      run={run}
      disableOverlayClose
      callback={onChange}
      styles={{
        overlay: { zIndex: 1200 }
      }}
    />
  )
}
