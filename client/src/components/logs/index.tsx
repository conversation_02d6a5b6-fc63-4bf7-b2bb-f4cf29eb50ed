import { Console, Hook, Unhook } from 'console-feed'
import React, { memo, useEffect, useState } from 'react'

export interface LogsContainerProps {
  onLog: (...args: any[]) => any,
  prefix?: string
  console?: any
}

export const LogsContainer = memo((props: LogsContainerProps) => {
  const { onLog, prefix, console = window.console } = props

  const [logs, setLogs] = useState([])

  // run once!
  useEffect(() => {
    const hookedConsole = Hook(
      console,
      log => {
        if (prefix) {
          if (JSON.stringify(log).indexOf(prefix) === 0) {
            return
          }
        }
        setLogs(currLogs => [...currLogs, log])
        onLog?.(log)
      },
      false
    )
    return () => {
      Unhook(hookedConsole)
    }
  }, [prefix])

  if (logs.length === 0) return <div style={{ color: '#999', padding: 12 }}>暂无日志输出</div>

  return <Console logs={logs} variant='dark' />
})
