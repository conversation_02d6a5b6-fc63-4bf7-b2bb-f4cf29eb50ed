.markdown-view {
  p,
  li,
  h1,
  h2,
  h3,
  h4,
  h5 {
    > code {
      color: tint(#f34, 20%);
    }
  }

  h1,
  h2,
  h3,
  h4 {
    font-weight: bold;
  }
  pre {
    position: relative;
    background-color: tint(#fff3fe, 90%);
    font-size: 13px;
    padding: 4px;
    margin-right: 8px;
    border: 1px solid #f1f1f1;
    border-radius: 3px;
    code {
      // color: #444;
      &::after {
        position: absolute;
        content: attr(class);
        top: 0;
        right: 0;
        font-size: 12px;
        background-color: #fff;
        border-radius: 3px;
        padding: 2px 3px;
        box-shadow: 1px 2px 10px rgba(#111, 0.12);
      }
    }

    // 代码高亮颜色
    .code-highlight();
  }

  p + ul,
  p + ol {
    margin-top: -1px;
  }
  ul,
  ol {
    padding-left: 24px;
  }

  blockquote {
    background-color: #fafafa;
    border-left: 3px solid var(--tint-color-40);
    padding: 5px;
    padding-left: 12px;
    margin-right: 10px;
    margin-top: 6px;
    p:last-of-type,
    h1:last-of-type,
    h2:last-of-type,
    h3:last-of-type,
    h4:last-of-type {
      margin-bottom: 0;
    }
  }

  // & > :last-of-type {
  //   margin-bottom: 0;
  // }
}

// 代码高亮颜色
.code-highlight {
  .punctuation {
    color: #999;
  }
  .property {
    color: #555;
  }
  .operator {
    color: #78f;
  }
  .string {
    color: #3a9;
  }
  .number {
    color: #f80;
  }
  .class-name {
    color: #f45;
    font-weight: bold;
  }
  .keyword {
    color: #39f;
  }
  .module {
    color: #15e;
  }
  .builtin,
  .boolean {
    color: #f79;
  }
  .comment {
    color: #aaa;
  }
}
