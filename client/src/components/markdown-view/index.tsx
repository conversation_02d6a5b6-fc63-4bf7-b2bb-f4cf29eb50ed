// import type { ReactMarkdownOptions } from 'react-markdown/lib/react-markdown'
import './index.less'
import 'github-markdown-css/github-markdown-light.css'

import React, { lazy, Suspense, useState } from 'react'
import remarkGfm from 'remark-gfm'

const Markdown = lazy(() => import('react-markdown'))

export interface MarkdownViewProps {
  className?: string
  children?: any
  content?: string
}

/**
 * MarkdownView 组件
 * @param props
 */
export default function MarkdownView(props: MarkdownViewProps) {
  const { children, content, className = '', ...rest } = props

  useState(() => {
    if (!Object.hasOwn) {
      Object.hasOwn = function (obj, prop) {
        return Object.prototype.hasOwnProperty.call(obj, prop)
      }
    }
  })

  return (
    <Suspense fallback={null}>
      <Markdown className={`markdown-view ${className}`} remarkPlugins={[remarkGfm]} {...rest}>
        {children || content}
      </Markdown>
    </Suspense>
  )
}
