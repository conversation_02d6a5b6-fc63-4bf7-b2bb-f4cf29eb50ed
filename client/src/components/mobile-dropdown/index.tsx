

import './index.less'

import { fastMemo } from '@sugo/design/functions'
import { Dropdown, DropDownProps } from 'antd'
import classNames from 'classnames'
import React, { useEffect, useState } from 'react'

export interface MobileDropdownProps extends DropDownProps {
  children?: any
  renderOverlay?: (node: any, open: boolean, setOpen: (o: boolean) => any) => any
}

/**
 * 自定义组件
 * @returns
 */
function _MobileDropdown(props: MobileDropdownProps) {
  const { children = <span />, renderOverlay, ...rest } = props

  const [open, setOpen] = useState(false)
  const [id] = useState(() => Math.random().toString(32).slice(2))

  const dropdownRender = node => renderOverlay?.(node, open, setOpen)

  useEffect(() => {
    const prev = document.getElementById(id)
    if (open) {
      setTimeout(() => {
        // const el = document.querySelector(`.id-${id}`) as HTMLDivElement
        const div = prev || document.createElement('div')
        div.classList.add('design-mobile-dropdown-mark')
        div.id = id
        // if (el) div.style.top = el.style.top
        if (!prev) {
          document.body.appendChild(div)
        }
      }, 200)
    } else if (prev) {
      prev.remove()
    }
    return () => {
      prev?.remove()
    }
  }, [open])

  return (
    <Dropdown
      trigger={['click']}
      overlayClassName={classNames('design-mobile-dropdown-overlay', rest.overlayClassName, { [`id-${id}`]: true })}
      open={open}
      onOpenChange={setOpen}
      dropdownRender={dropdownRender}
      {...rest}
    >
      {children}
    </Dropdown >
  )
}

export const MobileDropdown = fastMemo(_MobileDropdown)
