import { useEffect,useState } from 'react'
import { createPortal } from 'react-dom'

export interface PortalContainerProps {
  domSelector?: string
  show?: boolean
  children: JSX.Element | JSX.Element[] | null
}

/**
 * Portal 容器
 * 可用于渲染出父节点之外
 * @param props
 * @doc https://react.docschina.org/docs/portals.html
 */
export default function PortalContainer(props: PortalContainerProps) {
  const { domSelector = '#abi-app-portal', show = true } = props
  const [element, setElement] = useState<Element | null>(null)

  useEffect(() => {
    if (!domSelector) {
      console.error('portal-container dom is not found')
      return
    }
    const dom = document.querySelector(domSelector)
    if (!dom) {
      console.error('portal-container dom is not found', domSelector)
      return
    }
    if (dom) setElement(dom)
  }, [domSelector])

  if (!element || !show) return null

  return createPortal(props.children, element)
}
