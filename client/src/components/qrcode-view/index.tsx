import cn from 'classnames'
import Color from 'color'
import QrCode from 'qrcode'
import React, { useEffect,useRef } from 'react'

export interface QrCodeViewProps {
  /** 二维码的内容 */
  data: string
  width?: number
  height?: number
  className?: string
  color?: string
  /** 质量，默认 0.3 */
  quality?: number
}

/**
 * 根据 url 生产二维码
 * @param props
 */
export default function QrCodeView(props: QrCodeViewProps) {
  const { data, width = 100, height = 100, className, color = '#48484f', quality = 0.3 } = props
  const imageRef = useRef<HTMLImageElement | null>(null)

  useEffect(() => {
    const mycolor = Color(color)
    const $color = mycolor.hex() + Math.floor(255 * mycolor.alpha()).toString(16)

    if (!data) return

    const opts = {
      errorCorrectionLevel: 'H',
      type: 'image/png',
      quality,
      margin: 1,
      width,
      // color: { dark: '#ff5566EE', light: '#ffffffFF' }
      color: { dark: $color, light: '#ffffffFF' }
    }
    // ... toData
    QrCode.toDataURL(data, opts, (err, dataUrl) => {
      if (err) throw err
      if (imageRef.current) imageRef.current.src = dataUrl
    })
  }, [data, color, quality, width])

  if (!data) return null

  return <img className={cn('qrcode-view', className)} style={{ width, height }} ref={imageRef} alt='二维码' />
}
