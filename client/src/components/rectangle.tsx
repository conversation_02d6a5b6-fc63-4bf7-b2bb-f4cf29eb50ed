// https://github.com/aush/react-rectangle

import _ from 'lodash'
import React from 'react'

// 转换宽高比，取逆
const calculateAspectRatio = (aspectRatio: number | [number, number] | { width: number; height: number } | string) => {
  if (_.isNumber(aspectRatio) && Number.isFinite(aspectRatio)) {
    return 1 / aspectRatio
  }
  if (_.isArray(aspectRatio) && aspectRatio[0] !== undefined && aspectRatio[1] !== undefined) {
    return aspectRatio[1] / aspectRatio[0]
  }
  if (_.isString(aspectRatio)) {
    const parsedValue = Number.parseFloat(aspectRatio)
    if (Number.isNaN(parsedValue)) {
      throw new Error(`Cannot parse input string: ${aspectRatio}`)
    }
    return 1 / Number.parseFloat(aspectRatio)
  }
  if (_.isObject(aspectRatio) && 'width' in aspectRatio) {
    return aspectRatio.height / aspectRatio.width
  }

  throw new Error(`Cannot parse props.aspectRatio: ${aspectRatio}`)
}

export { calculateAspectRatio }

export interface RectProps {
  aspectRatio: number | [number, number] | { width: number; height: number } | string
  children: any
  style?: object
}

/**
 * 固定宽高比辅助组件
 * @param aspectRatio
 * @param children
 * @param style
 * @param rest
 * @constructor
 */
export default function Rect({ aspectRatio = 1, children, style, ...rest }: RectProps) {
  const multiplier = calculateAspectRatio(aspectRatio)

  return (
    <div style={{ position: 'relative', ...(style || {}) }} {...rest}>
      <div style={{ display: 'block', paddingTop: `${100 * multiplier}%` }} />
      <div style={{ position: 'absolute', bottom: 0, left: 0, top: 0, right: 0 }}>{children}</div>
    </div>
  )
}
