import fastEqual from 'fast-deep-equal'
import _ from 'lodash'
import { Children, cloneElement, useEffect, useMemo, useRef } from 'react'

const useEqualCompareMemo = (factory: () => JSX.Element, deps: any[]) => {
  const ref = useRef<any[]>([])

  const isEqual = deps.every((dep, index) => fastEqual(ref.current[index], dep))
  // 如果不等，则重新缓存
  if (!isEqual) {
    ref.current = deps
  }

  return useMemo(factory, ref.current)
}

/**
 * 可以让你跳过不相干的函数引用改变引起的重新渲染
 * @param param
 */
export default function SkipNonRender({ children, skips }: any) {
  if (!skips) {
    // 默认跳过所有回调函数
    skips = prop => prop.startsWith('on')
  }

  const child = Children.only(children)
  const childProps = child.props
  const propsRef = useRef({})
  const nextSkippedPropsRef = useRef({})

  Object.keys(childProps)
    .filter(it => skips(it))
    .forEach(key => {
      // 代理函数只会生成一次，其值始终不变
      nextSkippedPropsRef.current[key] =
        nextSkippedPropsRef.current[key] || ((...args) => propsRef.current[key](...args))
      // function skipNonRenderPropsProxy(...args) {
      //   propsRef.current[key].apply(this, args)
      // }
    })

  useEffect(() => {
    propsRef.current = childProps
  })

  // 这里使用 useMemo 优化技巧
  // 除去回调函数，其他属性改变生成新的 React.Element
  return useEqualCompareMemo(
    () =>
      cloneElement(child, {
        ...child.props,
        ...nextSkippedPropsRef.current
      }),
    [_.omit(childProps, Object.keys(nextSkippedPropsRef.current))]
  )
}
