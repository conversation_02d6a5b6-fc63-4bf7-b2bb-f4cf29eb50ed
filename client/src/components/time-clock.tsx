
import { fastMemo } from '@sugo/design/functions'
import _ from 'lodash'
import React, { useEffect, useRef, useState } from 'react'

type Props = {
  className?: string
  style?: React.CSSProperties
  status?: 'running' | 'stopped' | (string & {})
}

// 时间计时器
export const TimeClock = fastMemo((props: Props) => {
  const { className, style, status } = props

  const [s, setTime] = useState(0)
  const timerRef = useRef<any>(null)
  const T = 100

  useEffect(() => {
    if (status === 'stopped') return
    const startTime = Date.now()
    let expected = T // 初始间隔200ms

    const step = () => {
      const now = Date.now()
      const elapsed = now - startTime
      // 更新时间和显示（保留1位小数）
      setTime(prev => _.floor(prev + T / 1000, 2))
      // 动态调整下一次执行时间
      expected += T
      const delay = Math.max(0, expected - elapsed)
      timerRef.current = setTimeout(step, delay)
    }

    timerRef.current = setTimeout(step, T)
    return () => {
      clearTimeout(timerRef.current)
    }
  }, [status])

  const t = s > 60 ? `${_.floor(s / 60, 0)}m ${_.round(s % 60, 2)}s` : `${s}s`

  return <span className={`time-clock ml-2 ${className}`} style={{ ...style, fontSize: 13 }}>{t}</span>
})
