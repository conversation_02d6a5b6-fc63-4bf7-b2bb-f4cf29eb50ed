import './trend-up-down-bar.less'

import { fastMemo } from '@sugo/design/functions'
import classNames from 'classnames'
import _ from 'lodash'
import React, { CSSProperties } from 'react'

export interface TrendUpDownBarProps {
  up?: number
  down?: number
  upColor?: string
  downColor?: string
  className?: string
  style?: CSSProperties
}

/**
 * 显示涨跌
 * @returns
 */
function _TrendUpDownBar(props: TrendUpDownBarProps) {
  const { down = 0, up = 0, upColor = '#ee5566', downColor = '#24bdab', className, style } = props
  const total = up + down
  const leftWidth = (down / total) * 100
  const rightWidth = (up / total) * 100

  return (
    <div className={classNames('trend-up-down-bar', className)} style={style}>
      {total === 0 && <div className='no-data'>暂无数据</div>}

      <div className='down-bg' style={{ width: `${leftWidth}%`, backgroundColor: downColor, display: down === 0 ? 'none' : 'flex' }}>
        <span>{down}个下跌</span>
        <span style={{ opacity: 0.75 }}>{_.floor(leftWidth, 0)}%</span>
      </div>
      <div className='up-bg' style={{ width: `${rightWidth}%`, backgroundColor: upColor, display: up === 0 ? 'none' : 'flex' }}>
        <span>{up}个上涨</span>
        <span style={{ opacity: 0.75 }}>{_.floor(rightWidth, 0)}%</span>
      </div>
    </div >
  )
}

export const TrendUpDownBar = fastMemo(_TrendUpDownBar)


