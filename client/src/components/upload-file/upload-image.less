.upload-file-upload-image {
  width: 100%;
  height: 100px;
  background-color: #fafafb;
  border-radius: 3px;
  margin-bottom: 4px;
  display: block;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 6px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid #e4e4e4;

  .ant-upload {
    width: 100%;
    height: 100%;
  }

  .ant-upload > span {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color: #777;
    background-color: #fdfdfd;
    .anticon {
      margin-bottom: 6px;
    }
  }

  .anticon-upload + div {
    color: #999;
    font-size: 13px;
  }

  .image-view {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .image-overlay {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    background-color: rgba(#111, 0.36);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color: #f1f1f1;
    z-index: -2;
    opacity: 0;
    transition: all 0.24s ease-in-out;
  }

  .use-cropper {
    position: absolute;
    z-index: 101;
    color: var(--primary-color);
    right: -3px;
    top: -3px;
    font-size: 16px;
    text-shadow: 1px 1px 2px rgba(#111, 0.12);
    text-align: center;
    width: 32px;
    height: 32px;
    line-height: 32px;
    > span {
      margin-bottom: 0 !important;
    }
  }

  .close-icon {
    position: absolute;
    top: -3px;
    left: -3px;
    z-index: 100;
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    .anticon {
      // color: #fff;
      color: var(--primary-color);
      font-size: 16px;
    }
  }

  &.has-image:hover {
    .image-overlay {
      opacity: 1;
      z-index: 10;
    }
    .anticon {
      color: #fff;
    }
  }
}

.image-edit-modal {
  .ant-modal-body,
  .ant-modal-header,
  .ant-modal-footer {
    background-color: #fff;
    border: none;
    opacity: 1;
  }
  .ant-modal-body {
    padding: 0;
  }
  .cropper-modal {
    background-color: #fff;
  }
}
