import 'cropperjs/dist/cropper.css'
import './upload-image.less'

import { CloseCircleOutlined, ScissorOutlined, UploadOutlined } from '@ant-design/icons'
import { useReactive } from 'ahooks'
import { message, Spin, Upload } from 'antd'
import cn from 'classnames'
import React, { lazy, Suspense, useEffect, useRef } from 'react'

// import Cropper from 'react-cropper'
// import { nanoid } from 'nanoid'
// import dayjs from 'dayjs'
import Modal from '@/components/customs/custom-modal'
import { FileService } from '@/services/files'
import type { FileType } from '@/types/entitys/files'

const Cropper = lazy(() => import('react-cropper'))

export interface UpLoadImageProps {
  className?: string
  /** 显示标题 */
  title?: string
  /** 是否开启剪辑功能 */
  enableCropper?: boolean

  imageSrc?: string
  // defaultImageSrc?: string
  /** 上传的接口函数 */
  // uploadServiceFn?: (info: { file: File, fileList: File[] }) => any
  onFileChange?: (info: { file: File; fileList: File[] }) => any
  onSuccess?: (fileResult: FileType, rawFile: File | string) => any
  onError?: (err: any) => any
  onClean?: () => any
  // 默认 10MB
  fileMaxSize?: number
  // 默认 128字节
  fileMinSize?: number

  defaultImageSrc?: string
}

interface State {
  file?: File
  fileList?: File[]
  imageSrc?: string
  rawImageSrc?: string
  cropper?: any
  editVisible?: boolean
}

/**
 * 图片上传组件
 * 内置的图片剪辑功能，但不可以是上 M 的图
 * @param props
 */
export function UpLoadImage(props: UpLoadImageProps) {
  const { className, title, enableCropper, imageSrc, onFileChange, defaultImageSrc } = props
  const { fileMinSize = 128, fileMaxSize = 1024 * 1024 * 10 } = props

  const cropperRef = useRef<HTMLImageElement>(null)
  const state = useReactive<State>({
    imageSrc: defaultImageSrc || imageSrc,
    rawImageSrc: imageSrc,
    file: undefined
  })

  useEffect(() => {
    state.imageSrc = imageSrc
    state.rawImageSrc = imageSrc
  }, [imageSrc])

  const getBase64 = (img, callback) => {
    try {
      const reader = new FileReader()
      reader.addEventListener('load', () => callback(reader.result))
      reader.readAsDataURL(img)
    } catch (err) {
      console.error(err)
    }
  }

  // 剪切图片之后执行
  const onCrop = async () => {
    const imageElement: any = cropperRef.current
    const cropper: any = imageElement?.cropper
    const dataImage = cropper.getCroppedCanvas().toDataURL()

    state.editVisible = false
    state.imageSrc = dataImage
    // 上传文件
    try {
      // const res = await FileService.upload(`image_${nanoid(4)}`, { base64: dataImage })
      const res = await FileService.upload({ base64: dataImage })
      if (res) {
        props.onSuccess?.(res, dataImage)
      } else {
        props.onError?.(new Error('上传失败'))
      }
    } catch (err) {
      console.error(err)
    }
  }

  const onBeforeUpload = (file: File) => {
    // 10MB
    if (file.size > fileMaxSize) {
      message.error('文件超出了最大限制')
      return false
    }
    if (file.size < fileMinSize) {
      message.error('文件超出了最小限制')
      return false
    }
    return true
  }

  const customRequest = async options => {
    const { onSuccess, onError, file } = options
    state.file = file
    getBase64(file, src => {
      state.imageSrc = src
      state.rawImageSrc = src
    })

    try {
      // const ext = file.name.replace(/(.*)\.(\w+)/, '$2')
      // const name = `${dayjs().format('YYYYMMDD')}_${nanoid(6)}.${ext}`
      // 上传
      const res = await FileService.upload(file)
      if (res) {
        onSuccess(res)
        props.onSuccess?.(res, file)
        // state.imageSrc = res.url
        // state.rawImageSrc = res.url
      } else {
        onError(new Error('上传失败'))
        props.onError?.(new Error('上传失败'))
      }
    } catch (err) {
      onError(err)
      props.onError?.(err)
    }
  }

  const renderCropperModal = () => (
    <Modal
      className='image-edit-modal'
      open={state.editVisible}
      width={640 * (4 / 3)}
      bodyStyle={{ width: 640 * (4 / 3), height: 640 }}
      onCancel={() => (state.editVisible = false)}
      onOk={onCrop}
      maskClosable={false}
      title='图片剪辑'
    >
      <Suspense fallback={<Spin />}>
        <Cropper
          ref={cropperRef}
          style={{ height: '100%', width: '100%' }}
          zoomTo={0.5}
          initialAspectRatio={1}
          src={state.rawImageSrc}
          viewMode={1}
          minCropBoxHeight={10}
          minCropBoxWidth={10}
          background={false}
          responsive
          autoCropArea={1.5}
          checkOrientation={false} // https://github.com/fengyuanchen/cropperjs/issues/671
          guides
          onInitialized={instance => {
            state.cropper = instance
          }}
        />
      </Suspense>
    </Modal>
  )

  const onChange = info => {
    state.file = info.file
    state.fileList = info.fileList
    onFileChange?.(info)
  }

  return (
    <>
      <Upload
        className={cn(className, {
          'upload-file-upload-image': true,
          'has-image': !!state.imageSrc
        })}
        accept='image/*'
        beforeUpload={onBeforeUpload}
        customRequest={customRequest}
        showUploadList={false}
        onChange={onChange}
      >
        {!state.imageSrc && (
          <>
            <UploadOutlined />
            {title && <div>{title}</div>}
          </>
        )}
        {state.imageSrc && (
          <>
            <img src={state.imageSrc} alt='' className='image-view' crossOrigin='anonymous' />
            <div className='image-overlay'>
              <UploadOutlined />
              {title && <div>{title}</div>}
            </div>
            <div className='close-icon'>
              <CloseCircleOutlined
                title='清除图片'
                onClick={e => {
                  e.stopPropagation()
                  props.onClean?.()
                  state.file = undefined
                  state.fileList = undefined
                  state.imageSrc = undefined
                  state.rawImageSrc = undefined
                }}
              />
            </div>
          </>
        )}
        {enableCropper && state.imageSrc && (
          <div
            className='use-cropper'
            onClick={e => {
              e.stopPropagation()
              state.editVisible = true
            }}
          >
            <ScissorOutlined />
          </div>
        )}
      </Upload>
      {enableCropper && renderCropperModal()}
    </>
  )
}

export default UpLoadImage
