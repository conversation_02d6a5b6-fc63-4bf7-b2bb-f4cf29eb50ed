import type { TableProps } from 'rc-table/lib/Table'
import React, { useRef } from 'react'

import useDynamicTableScrolling from '@/hooks/use-auto-table-size'

interface WithDynamicTableSizeProps {
    children: (props: {
        containerHeight: number,
        scroll?: TableProps['scroll'] & {
            scrollToFirstRowOnChange?: boolean;
        },
        ref?: React.Ref<HTMLDivElement> | undefined;
    }) => any

    standardDefaultHeight?: number
}

const WithDynamicTableSize = ({ children, standardDefaultHeight = 12 }: WithDynamicTableSizeProps) => {
    const tableContainerRef = useRef(null)
    const {
        scroll,
        containerHeight
    } = useDynamicTableScrolling(
        tableContainerRef,
        { standardDefaultHeight, defaultHeight: 800, defaultXScroll: 'max-content' }
    )
    return children({ containerHeight, scroll, ref: tableContainerRef })
}
export default WithDynamicTableSize