import { ComponentType, createElement, forwardRef, memo, useImperativeHandle, useRef, useState } from 'react'

export interface WithRefModalProps<InnerProps> {
  children?: any
  visible: boolean
  modal: {
    // 往下注入到 props
    show: (data?: Partial<InnerProps>) => any
    hide: (autoResetInnerProps?: any) => any
  }
}

/**
 * 装饰 Modal，使得其具有 ref 打开或关闭的能力
 * 内部集成了 visible 的逻辑，减少外部的变量导致组件重新渲染
 *
 * @param Component
 *
 * @example 示例
 *
 * ```js
 * export interface Props {
 *   title: string
 * }
 *
 * const MyModal = withRefModal<Props>(props => {
 *   const { modal, visible, title } = props
 *
 *   const onSubmit = e => {
 *     try {
 *       // ..
 *       modal.hide()
 *     } catch (err) {
 *       // ..
 *     } finally {
 *       // ...
 *     }
 *   }
 *
 *   return (
 *     <Modal visible={visible}>
 *       <div>
 *         <h3>{title}</h3>
 *         <Button onClick={e => modal.hide()}>点我关闭</Button>
 *       </div>
 *     </Modal>
 *   )
 * })
 * ```
 *
 * 使用
 * const ref = useRef<{ show: Function, hide: Function }>()
 * <MyModal ref={ref} />
 *
 * ref.current.show() 或 ref.current.show(props) // 打开弹层
 * ref.current.hide() // 隐藏弹窗
 *
 * 常见模式，点击弹层的确定提交，props 传一个 onSubmit，即可：
 *
 * ref.current.show({
 *  type: 'xxx',
 *  onSubmit: data => data
 * })
 *
 * 注意：show 参数是可以透传 props 的，传递数据时相当于 props 改变
 *
 *
 */
export default function withRefModal<Props = Record<string, any>>(
  Component: ComponentType<Props & WithRefModalProps<Props>>
) {
  return memo(
    forwardRef((props: Props & Partial<Omit<WithRefModalProps<Props>, 'modal'>>, ref) => {
      const [visible, setVisible] = useState(props.visible || false)
      const [innerProps, setInnerProps] = useState<Partial<Props>>({})

      const funcRef = useRef({
        show($innerProps?: Partial<Props>) {
          setVisible(true)
          if ($innerProps !== undefined) {
            setInnerProps($innerProps)
          }
        },
        hide(autoResetInnerProps?: boolean) {
          setVisible(false)
          if (autoResetInnerProps === true) {
            setInnerProps({})
          }
        }
      })

      useImperativeHandle(ref, () => funcRef.current)

      return createElement(Component, { ...props, visible, ...innerProps, modal: funcRef.current }, props.children)
    })
  )
}
