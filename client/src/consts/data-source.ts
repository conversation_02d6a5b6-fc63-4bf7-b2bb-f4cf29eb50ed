import _ from 'lodash'


/**
 * 接入数据类型
 * @typedef {Object} AccessDataType
 * @property {Number} File 文件导入
 * @property {Number} SDK 行为sdk导入
 * @property {Number} Log 日志导入
 * @property {Number} Tag 标签导入
 * @property {Number} MySQL 数据库查询
 * @property {Number} OfflineCalc 指标模型接入
 */
export const AccessDataType = {
  File: 0,
  SDK: 1,
  Log: 2,
  Tag: 3,
  MySQL: 4,
  OfflineCalc: 5
}

/** 前端数据存的维度类型 */
export const DruidColumnType = {
  Long: 0,
  Float: 1,
  String: 2,
  DateString: 3,
  Date: 4,
  Int: 5,
  Text: 6,
  Double: 7,
  BigDecimal: 8,
  // 多值列
  LongArray: 100,
  FloatArray: 101,
  StringArray: 102,
  DateArray: 104,
  IntArray: 105,
  DoubleArray: 107
}

/** 维度类型反向映射 js 类型 */
export const DimJsTypeInvertDict: Record<number, 'number' | 'date' | 'string'> = {
  0: 'number',
  1: 'number',
  2: 'string',
  3: 'date',
  4: 'date',
  5: 'number',
  6: 'string',
  7: 'number',
  8: 'number'
}

/** 前端固定一个时间列查询 */
export const TIME_DATE = 'time_date'

/** 为指标查询虚构的列，用于筛选条件/筛选器，实际查询时会去掉，传入专门的 basePeriod 参数 */
export const BASE_PERIOD = '__basePeriod__'

/** 固定的单位列 */
export const INDEX_UNIT = 'index_unit'

/** 固定的指标专业列 */
export const INDEX_MAJOR = 'major'

/** 固定的指标名称列 */
export const INDEX_NAME = 'index_name'

/** 固定的指标版本列 */
export const INDEX_VER = 'index_version'

/** 固定的时间粒度列 */
export const INDEX_TIME_PERIOD = 'time_period'

/** 统计粒度 - 周期列表，日，月，年互斥 */
export const GRANULARITY_OPTIONS = [
  { id: 'DAY', name: '日', group: 'day', type: 'value', format: 'YYYYMMDD' },
  { id: 'DAY_ACC', name: '月度日累计', group: 'day', type: 'sum', format: 'YYYYMMDD' },
  { id: 'DATE_ACC', name: '年度日累计', group: 'day', type: 'sum', format: 'YYYYMMDD' },
  { id: 'WEEK', name: '周', format: 'YYYY[W]ww' },
  { id: 'MONTH', name: '月', group: 'month', type: 'value', format: 'YYYYMM' },
  { id: 'QUARTER', name: '季度', group: 'quarter', type: 'value', format: 'YYYYMM' },
  { id: 'MONTH_ACC', name: '年度月累计', group: 'month', type: 'sum', format: 'YYYYMM' },
  { id: 'YEAR', name: '年', group: 'year', type: 'value', format: 'YYYY' }
]

/** 统计粒度 - 用于数据视图或数据库的查询 */
export const GRANULARITY_OPTIONS_FOR_DB = [
  { id: 'MINUTE', name: '时分', group: 'minute', type: 'value', format: 'YYYYMMDDHHmm' },
  { id: 'TIME', name: '时分秒', group: 'time', type: 'value', format: 'YYYYMMDDHHmmss' },
  { id: 'DAY', name: '日', group: 'day', type: 'value', format: 'YYYYMMDD' },
  { id: 'WEEK', name: '周', format: 'YYYY[W]ww' },
  { id: 'MONTH', name: '月', group: 'month', type: 'value', format: 'YYYYMM' },
  { id: 'YEAR', name: '年', group: 'year', type: 'value', format: 'YYYY' }
]

/** 统计粒度字典 */
export const GRANULARITY_OPTIONS_DICT = _.keyBy(GRANULARITY_OPTIONS, 'id')

/** 统计粒度转换为 antd DatePicker 的 picker 类型 */
export const GRANULARITY_TO_PICKER_DICT: Record<string, 'month' | 'year' | 'week' | undefined> = {
  MONTH: 'month',
  MONTH_ACC: 'month',
  YEAR: 'year',
  WEEK: 'week',
  DAY: undefined,
  DATE_ACC: undefined,
  DAY_ACC: undefined
}

/**
 * 字段类型的操作符
 * type 用于过滤选项
 */
export const CONDITIONAL_OPTS = [
  { label: '等于', value: 'equal', type: ['string', 'number', ''] },
  { label: '不等于', value: 'not equal', type: ['string', 'number', ''] },
  { label: '包含', value: 'in', type: ['string', 'number', 'date', ''] },
  { label: '不含', value: 'not in', type: ['string', 'number', 'date', ''] },
  { label: '含有', value: 'contains', type: ['string'] },
  { label: '不含有', value: 'not contains', type: ['string'] },
  { label: '大于', value: 'greaterThan', type: ['number'] },
  { label: '小于', value: 'lessThan', type: ['number'] }
]

/** 筛选值的空值选项 */
export const EMPTY_VALUE_OR_NULL = '空字符串 / NULL'

/** 聚合模式翻译字典 */
export const AGG_MODE_TRANSLATE_DICT = {
  sum: '求和',
  count: '计数',
  avg: '平均值',
  max: '最大值',
  min: '最小值',
  countDistinct: '去重计数',
  first: '首个值',
  last: '末尾值',
  unknown: '未知'
}

export const COMPARE_SPECS = [
  'CUR_VALUE',
  'COMPARED_RATE',
  'COMPARED_GROWTH_VALUE',
  'COMPARED_PRE_VALUE',
  'SEQUENTIAL_RATE',
  'SEQUENTIAL_GROWTH_VALUE',
  'SEQUENTIAL_PRE_VALUE',
  'MONTH_ACC',
  'DAY_ACC',
  'DATE_ACC'
]
