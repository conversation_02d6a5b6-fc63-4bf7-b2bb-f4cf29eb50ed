// import dayjs from 'dayjs'
import _ from 'lodash'

// import type { ColumnsType } from '@/components/basic-table'
import type { ColumnType, DbType } from '@/types/dataset'

export const isTieke = document.querySelector('html')?.getAttribute('data-theme') === 'tieke'
  || process?.env?.THEME === 'tieke'

export const DS_PAGE_NAME = isTieke ? '数据集' : '数据视图'

/** 全部分组 */
export const ALL_GROUP = {
  title: '全部',
  id: 'all'
}


/** 预览数据，最大显示条数10000 */
export const LIMIT_MAX = 10000

/**
 * 数据集类型隐射
 */
export const DATASET_TYPE_MAP = {
  entities: {
    db: '数据库数据集',
    sql: 'SQL数据集',
    api: 'API数据集',
    excel: 'EXCEL文件'
  },
  keys: ['db', 'sql', 'api', 'excel']
}

/**
 * 禁用状态的数据集类型
 */
export const DATASET_TYPE_DISABLEDS = ['db', 'api', 'excel']

/**
 * 数据集分组操作组
 */
export const GROUP_OPERATIONS = [
  {
    key: 'update',
    label: '修改分组'
  },
  {
    key: 'delete',
    label: '删除分组'
  }
]

/**
 * 字段类型
 */
export const DATA_TYPE_OPTIONS = [
  { label: '文本', value: 'string' },
  { label: '数值', value: 'number' },
  { label: '日期', value: 'date' }
]

/**
 * 各字段类型的默认显示格式
 */
export const SHOW_FORMAT_DEFAULT_VALUE = {
  string: null,
  number: 'f',
  date: 'YYYY-MM-DD'
}

/** 显示格式选项 */
export const SHOW_FORMAT_OPTIONS = [
  { label: '某年某月某日 时分秒', value: 'YYYY-MM-DD HH:mm:ss', type: 'time' },
  { label: '某年某月某日', value: 'YYYY-MM-DD', type: 'time' },
  { label: '某年某月', value: 'YYYY-MM', type: 'time' },
  { label: 'YYYYMMDD', value: 'YYYYMMDD', type: 'time' },
  { label: 'YYYY/MM/DD', value: 'YYYY/MM/DD', type: 'time' },
  { label: 'YYYYMM', value: 'YYYYMM', type: 'time' },
  { label: 'YYYY/MM', value: 'YYYY/MM', type: 'time' },
  { label: '百分比', value: '%', type: 'number' },
  { label: '数值', value: 'f', type: 'number' }
]

/**
 * 数据集字段精度
 */
export const DATASET_DECIMAL_OPTIONS = [
  {
    label: '0位',
    value: 0
  },
  {
    label: '2位',
    value: 2
  },
  {
    label: '4位',
    value: 4
  },
  {
    label: '6位',
    value: 6
  }
]

export const DEFAULT_DATA_FORMATER_MAP = {
  number: '.0f',
  string: null,
  date: 'YYYY-MM-DD'
}

// 数据源类型对应的保存字符串
export const DB_CONN_TYPES_MAP = {
  MYSQL: 'mysql',
  SQLSERVER: 'mssql',
  POSTGRESQL: 'postgres',
  ORACLE: 'oracle',
  HIVE: 'hive',
  OCEANBASEORACLE: 'oracle',
  MONGODB: 'mongodb',
  GAUSSDB: 'postgres',
  GREENPLUM: 'postgres',
  INCEPTOR: 'inceptor',
  OPENGAUSS: 'postgres',
  DORIS: 'doris',
  STARROCKS: 'starrocks'
}

// 获取数据源类型
export const getDbType = (type: DbType) => {
  const dbType = DB_CONN_TYPES_MAP?.[type]
  if (!dbType) {
    console.error('未支持的 db type:', type, '现在用 mysql')
  }
  return dbType || 'mysql'
}

/**
 * number类型字典表
 */
const SQL_NUMBER_TYPE = [
  // MYSQL
  'TINYINT', 'SMALLINT', 'MEDIUMINT', 'INT', 'INTEGER', 'BIGINT', 'FLOAT', 'DOUBLE', 'DECIMAL',
  // PG
  'NUMERIC', 'REAL', 'DOUBLE PRECISION', 'SMALLSERIAL', 'SERIAL', 'BIGSERIAL', 'MONEY',
  // oracle
  'NUMBER',
  // SQLSERVER
  'SMALLMONEY'
]

/**
 * 时间类型字典表
 */
const SQL_DATE_TYPE = [
  // MYSQL
  'DATE', 'TIME', 'YEAR', 'DATETIME', 'TIMESTAMP',
  // PG
  'INTERVAL',
  // SQLSERVER
  'SMALLDATETIME'
]

// 字段类型映射
export const formatSqlType = (type: string): ColumnType => {
  const t = _.toUpper(type)
  if (t.indexOf('INT') !== -1) return 'number'
  if (SQL_NUMBER_TYPE.includes(t)) return 'number'
  if (SQL_DATE_TYPE.includes(t)) return 'date'
  return 'string'
}

/**
 * sql变量 类型选择
 */
export const selectSqlType = [
  {
    value: 'string',
    label: '文本'
  },
  {
    value: 'number',
    label: '数字'
  },
  {
    value: 'date',
    label: '日期'
  },
  {
    value: 'listString',
    label: '字符串数组'
  },
  {
    value: 'listnumber',
    label: '数字数组'
  }
]
