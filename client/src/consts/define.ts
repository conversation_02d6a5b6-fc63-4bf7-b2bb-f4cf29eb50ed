import { ComponentDefine } from '@/types/editor-core/component'
import { ActionDefineMap, ActionHandlerDefine, EventDefineMap } from '@/types/editor-core/events'

/** 组件支持哪些默认事件 */
export const DefaultEventDefines: EventDefineMap = {
  components: {
    // 公共事件
    common: [
      'click',
      'doubleClick',
      'mouseEnter',
      'mouseLeave',
      'mouseMove',
      'change',
      'keyup',
      'didMounted',
      'printPreviewEnter',
      'printPreviewExit'
    ],
    // 特定组件事件
    page: []
  },
  events: {
    click: { name: 'click', title: '鼠标点击' },
    doubleClick: { name: 'doubleClick', title: '鼠标双击' },
    mouseEnter: { name: 'mouseEnter', title: '鼠标进入' },
    mouseLeave: { name: 'mouseLeave', title: '鼠标离开' },
    mouseMove: { name: 'mouseMove', title: '鼠标经过' },
    change: { name: 'change', title: '值变更' },
    keyup: { name: 'keyup', title: '松开按键' },
    didMounted: { name: 'didMounted', title: '装载完成', binder: 'unknown' },
    printPreviewEnter: { name: 'printPreviewEnter', title: '进入打印预览', binder: 'unknown' },
    printPreviewExit: { name: 'printPreviewExit', title: '退出打印预览', binder: 'unknown' }
  }
}

/** 组件支持哪些默认行为 */
export const DefaultActionDefines: ActionDefineMap = {
  components: {
    // 公共行为
    common: [
      'setVisibility',
      'openUrl',
      'gotoComponent',
      'changeComponentConfig',
      'changeComponentStyle',
      'changeComponentSize',
      'changeComponentDataSource',
      'setExternalFilter',
      'exportPdf',
      'exportWord',
      'triggerEvent',
      'triggerAction',
      'printPage',
      'initDrillDown', // 需要图表自己实现这个逻辑
      'copyConfig'
      // ...
    ],
    // 特定组件行为
    page: [
      'timedRefresh'
    ]
  },
  actions: {
    setVisibility: { name: 'setVisibility', title: '设置可见性' },
    openUrl: { name: 'openUrl', title: '打开页面' },
    gotoComponent: { name: 'gotoComponent', title: '页面滚动到组件位置' },
    changeComponentConfig: { name: 'changeComponentConfig', title: '改变组件的配置' },
    changeComponentStyle: { name: 'changeComponentStyle', title: '改变组件的样式' },
    changeComponentSize: { name: 'changeComponentSize', title: '改变组件的大小位置' },
    changeComponentDataSource: { name: 'changeComponentDataSource', title: '改变组件的数据源' },
    setExternalFilter: { name: 'setExternalFilter', title: '外部数据筛选' },
    exportPdf: { name: 'exportPdf', title: '导出页面为 PDF', notParams: true, description: '本页面' },
    exportWord: { name: 'exportWord', title: '导出页面为 Word', notParams: true, description: '本页面' },
    triggerEvent: { name: 'triggerEvent', title: '触发其他组件事件' },
    triggerAction: { name: 'triggerAction', title: '触发其他组件动作' },
    printPage: { name: 'printPage', title: '打印页面', description: '打印设置' },
    initDrillDown: { name: 'initDrillDown', title: '初始化下钻动作' },
    timedRefresh: { name: 'timedRefresh', title: '定时刷新数据' },
    copyConfig: {
      name: 'copyConfig',
      title: '复制配置',
      paramKeys: ['fromComponentKey', 'targetComponentKeys', 'processFn'],
      params: {
        fromComponentKey: { title: '源组件', type: 'componentKey', limit: 1 },
        targetComponentKeys: { title: '目标组件', type: 'componentKey' },
        processFn: {
          title: '处理逻辑',
          type: 'code',
          tooltip: '复制值的处理逻辑',
          defaultValue: `/**
 * 配置转换函数
 * @params{Object} targetConfig 目标配置
 * @params{Object} srcConfig 源配置
 * @params{Object} utils 工具函数，包括 loadsh 和 dayjs，例如 utils._
 *
 * @example: 复制值
 * function (targetConfig, srcConfig, utils) {
 *   return { ...targetConfig, value: srcConfig.value }
 * }
 */
function convertConfig(targetConfig, srcConfig, utils) {
  return {
    ...targetConfig,
    value: srcConfig.value
  }
}`
        }
      }
    }
  }
}

/** 自定义事件的默认例子 */
export const DEFAULT_CUSTOM_EVENT_DEFINES: ComponentDefine['eventActionDefine'] = {
  events: ['click', 'didMounted'],
  eventDefine: {
    click: { name: 'click', title: '单击', binder: 'unknown' }, // 表示屏蔽 dom 的事件绑定，只传给 echarts
    didMounted: { name: 'didMounted', title: '组件挂载完成', binder: 'unknown' }
  },
  actions: ['displayComponentInfo'],
  actionDefine: {
    displayComponentInfo: {
      name: 'displayComponentInfo',
      title: '显示组件信息',
      paramKeys: ['targetCompKey'],
      params: {
        targetCompKey: { title: '目标组件', type: 'componentKey', limit: 1 }
      }
    }
  },
  defaultEventAction: { keys: [], entities: {} }
}

/** 自定义事件响应逻辑的默认例子 */
export const DEFAULT_CUSTOM_ACTION_HANDLER_DEFINES: ActionHandlerDefine = {
  displayComponentInfo: `(function compile(params, runtime, utils) {
    // 动作传参 demo 代码，仅供参考
    const targetCompKey = params.targetCompKey
    return function onEvent(ev, srcCompKey) {
      const srcComp = runtime.getComponentByKey(srcCompKey)
      const targetComp = runtime.getComponentByKey(targetCompKey)

      const srcCompInfo = srcComp.getInfo()
      const targetCompInfo = targetComp.getInfo()
      alert(
        \`源组件: \${srcCompInfo.alias || srcCompInfo.title}，目标组件：\${targetCompInfo.alias || targetCompInfo.title}\`
      )
    }
  })`,
  initDrillDown: `(function compile(params, runtime, utils) {
    // 下钻 demo 代码，仅供参考
    return function onEvent(ev, srcCompKey) {
      const srcComp = runtime.getComponentByKey(srcCompKey)
      srcComp.addEventListener('click', ev => {
        const domEv = ev.event.event
        srcComp.getDrillDownHelper().showDrillDownMenu(domEv.clientX, domEv.clientY, ev.name)
      })
    }
  })`
}

/**
 * 自定义图表初始化属性
 */
export const CUSTOM_CHART_INIT_PROPS: Partial<ComponentDefine> = {
  title: '未命名图表',
  version: '1.0.0',
  versionCode: 0,
  status: 'available',
  type: 'chart',
  category: 'chart',
  loadMode: 'live',
  group: 'barChart',
  icon: '一维柱图',
  styleDefine: { width: 480, height: 270 },
  configDefine: {},
  eventActionDefine: {},
  dataSourceDefine: {},
  extraConfig: {
    moduleAddress: 'https://api.observablehq.com/d/94ac81ea79285cf9.js?v=3',
    displayVariableName: 'main',
    displayMode: 'dom',
    variableOverridingMethod: `(function variableOverriding(props, utils) {
  return {
    compKey: props.compKey,
    // dataSource: props.dataSource,
    fields: props.fields || [],
    data: props.data || [],
    config: props.config,
    onEvents: props.onEvents,
    _: utils._,
    echarts: utils.echarts,
    // ReactMarkdown: utils.ReactMarkdown,
    // React: utils.React, ReactDOM: utils.ReactDOM, htm: utils.htm,
    // antd: utils.antd,
    // moment: utils.dayjs
  }
})`,
    variableDefineOverriding: {}
  }
}

// {
//   "key": "rectangle@1.0.0",
//   "name": "rectangle",
//   "title": "矩形",
//   "description": "",
//   "version": "1.0.0",
//   "type": "element",
//   "category": "base",
//   "group": "",
//   "device": "pc",
//   "icon": "",
//   "styleDefine": {},
//   "configDefine": {},
//   "dataSourceDefine": {},
//   "eventActionDefine": {
//     "events": ["click", "keyup", "doubleClick"],
//     "actions": ["setVisibility", "openUrl"]
//   }
// }

/** 同环比名称翻译字典 */
export const COMPARE_SPEC_TRANSLATE_DICT = {
  CUR_VALUE: '本期实际',
  COMPARED_RATE: '同比差异率',
  COMPARED_GROWTH_VALUE: '同比差异',
  COMPARED_PRE_VALUE: '去年同期',
  SEQUENTIAL_RATE: '环比差异率',
  SEQUENTIAL_GROWTH_VALUE: '环比差异',
  SEQUENTIAL_PRE_VALUE: '上期实际',

  PLAN_VALUE: '目标值',
  PLAN_GROWTH_VALUE: '目标差异',
  PLAN_FULL_RATE: '目标完成率',

  CUSTOM_PLAN: '预期值',
  CUSTOM_PLAN_GROWTH: '比预期增长',
  CUSTOM_PLAN_RATE: '预期完成率'
}
