/**
 * 内置组件的 name-path 映射表
 *
 * 本地内置组件
 * @mode local
 */
export { ELEMENT_MAP } from '@/components/elements/const'

/**
 * 元素悬浮模型
 */
export const FIXED_SCHEMA = {
  fixed_schema_group: {
    title: '元素悬浮',
    type: 'group',
    unfold: true,
    caption: '开启后组件变成悬浮模式，不会跟随滚动条而滚动，在预览时有效。',
    children: {
      switch: {
        title: '是否悬浮',
        type: 'switch',
        defaultValue: 'absolute',
        openText: '开启',
        closeText: '关闭',
        valuePath: 'position',
        valueMap: {
          fixed: true,
          absolute: false
        }
      },
      distance: {
        title: '悬浮位置',
        type: 'distance',
        caption: '组件离页面四侧的距离',
        defaultValue: {},
        valuePath: ''
      }
    }
  }
}

/**
 * 预览页最外层的容器 id
 */
export const PREVIEW_CONTAINER_ID = '#screen-page-content'

/**
 * tabs layout 类型（非常重要一个组件）
 * 支持多个类型的组件
 */
export const TABS_LAYOUT_NAMES = ['layout-tabs', 'layout-container']
