import _ from 'lodash'

export const FEEDBACK_GROUP_TYPE_OPTS = [
  { label: '问题反馈', value: 'issue', color: '#39f' },
  { label: 'BUG问题', value: 'bug', color: '#f56' },
  { label: '产品建议', value: 'recommend', color: '#78f' },
  { label: '技术支持', value: 'technical', color: '#f90' },
  { label: '使用帮助', value: 'help', color: '#3b6' }
]

export const FEEDBACK_GROUP_TYPE_DICT = _.keyBy(FEEDBACK_GROUP_TYPE_OPTS, 'value')

export const FEEDBACK_STATUS_OPTS = [
  { label: '未确认', value: '0', color: 'purple' },
  { label: '待处理', value: '1', color: 'gray' },
  { label: '已处理', value: '2', color: 'green' },
  { label: '不受理', value: '-1', color: 'red' }
]

export const FEEDBACK_STATUS_DICT = _.keyBy(FEEDBACK_STATUS_OPTS, 'value')

export const FEEDBACK_MEBUS = [
  {
    key: 'dashboards', title: '运营看板',
    children: [
      { key: 'dashboard', title: '数据看板' },
      { key: 'screen', title: '数据大屏' }
    ]
  },
  {
    key: 'workspace', title: '工作空间',
    children: [
      {
        key: 'data', title: '数据准备',
        children: [
          { key: 'mydatasets', title: '我的数据集' },
          { key: 'datasource', title: '数据源管理' },
          { key: 'dataview', title: '数据视图' },
          { key: 'table-model', title: '数据模型' }
        ]
      },
      {
        key: 'analysis', title: '数据看板',
        children: [
          { key: 'theme-analysis', title: '我的分析' },
          { key: 'self-analysis', title: '智助分析' }
        ]
      },
      {
        key: 'abi', title: '数据大屏',
        children: [
          { key: 'abi-project', title: '项目门户' },
          { key: 'abi-report', title: '数据报告' },
          { key: 'charts-gallery', title: '自定义图表' }
        ]
      },
      {
        key: 'screen', title: '数据应用',
        children: [
          { key: 'mini-app-manage', title: '微应用管理' },
          { key: 'pc-app-manage', title: '桌面应用商店' }
        ]
      }
    ]
  },
  // { key: 'copilot', title: '智能助手' },
  {
    key: 'templates', title: '模板中心',
    children: [
      { key: 'theme-analysis', title: '我的分析' },
      { key: 'abi-project', title: '项目门户' },
      { key: 'abi-report', title: '数据报告' }
    ]
  }
]
