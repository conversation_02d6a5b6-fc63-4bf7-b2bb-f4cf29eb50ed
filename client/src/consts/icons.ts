import { ReactComponent as Bi } from '@/assets/icons/bi.svg'
import { ReactComponent as BookSummary } from '@/assets/icons/book-summary.svg'
import { ReactComponent as Cursor } from '@/assets/icons/cursor.svg'
import { ReactComponent as DarkTheme } from '@/assets/icons/dark-theme.svg'
import { ReactComponent as Dot } from '@/assets/icons/dot.svg'
import { ReactComponent as FastAccess } from '@/assets/icons/fast-access.svg'
import { ReactComponent as Feedback } from '@/assets/icons/feedback.svg'
import { ReactComponent as Fish } from '@/assets/icons/fish.svg'
import { ReactComponent as HighEfficiency } from '@/assets/icons/high-efficiency.svg'
import { ReactComponent as LightTheme } from '@/assets/icons/hight-theme.svg'
import { ReactComponent as Hotkey } from '@/assets/icons/hotkey.svg'
import { ReactComponent as Individuation } from '@/assets/icons/individuation.svg'
import { ReactComponent as Magicwand } from '@/assets/icons/magicwand.svg'
import { ReactComponent as MyPie } from '@/assets/icons/my-pie.svg'
import { ReactComponent as ProjectItem } from '@/assets/icons/project-item.svg'
import { ReactComponent as TableModel } from '@/assets/icons/table-model.svg'
import { ReactComponent as Technology } from '@/assets/icons/technology.svg'
import { ReactComponent as ThemeDark } from '@/assets/icons/theme-dark.svg'
import { ReactComponent as ThemeLight } from '@/assets/icons/theme-light.svg'
import { ReactComponent as Trend } from '@/assets/icons/trend.svg'
import { ReactComponent as Verify } from '@/assets/icons/verify.svg'


// 自定义的 svg 图标，结合 svg-icon 组件使用
export const ICON_MAPPING = {
  dot: Dot,
  hotkey: Hotkey,
  verify: Verify,
  cursor: Cursor,
  themeLight: ThemeLight,
  themeDark: ThemeDark,
  fastAccess: FastAccess,
  highEfficiency: HighEfficiency,
  individuation: Individuation,
  technology: Technology,
  projectItem: ProjectItem,
  trend: Trend,
  fish: Fish,
  bi: Bi,
  myPie: MyPie,
  tableModel: TableModel,
  darkTheme: DarkTheme,
  lightTheme: LightTheme,
  feedback: Feedback,
  bookSummary: BookSummary,
  magicwand: Magicwand
} as const
