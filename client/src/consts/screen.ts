import {
  AlignCenterOutlined,
  Align<PERSON>eftOutlined,
  ApartmentOutlined,
  ApiOutlined,
  AppstoreAddOutlined,
  BgColorsOutlined,
  DatabaseOutlined,
  GatewayOutlined,
  // ExperimentOutlined,
  GroupOutlined,
  ReadOutlined,
  ScheduleOutlined,
  SendOutlined,
  ToTopOutlined,
  VerticalAlignBottomOutlined
} from '@ant-design/icons'
import _ from 'lodash'

import { DS_PAGE_NAME } from '@/consts/dataset'
import type { ComponentCategory, ComponentGroup, ComponentType } from '@/types/editor-core/component'

/** 最大的组件数量，不用乱调 */
export const MAX_COMPONENT_COUNT = 500
const isTieke = process.env.THEME === 'tieke'

/**
 * 组件 - 配置面板的 tabs 配置
 */
export const COM_CONFIG_PANEL_TABS = [
  { key: 'columnPicker', title: '选择列', _icon: GroupOutlined },
  { key: 'style', title: '样式', _icon: BgColorsOutlined },
  { key: 'attr', title: '属性', _icon: ScheduleOutlined },
  { key: 'event', title: '事件', _icon: ApiOutlined },
  { key: 'datasource', title: '数据', _icon: DatabaseOutlined }
]

export const COM_CONFIG_PANEL_MAP = _.keyBy(COM_CONFIG_PANEL_TABS, 'key')

/**
 * 页面 - 配置面板的 tabs 配置
 */
export const PAGE_CONFIG_PANEL_TABS = [
  { key: 'style', title: '样式', _icon: BgColorsOutlined },
  { key: 'event', title: '事件', _icon: ApiOutlined }
]

/**
 * 项目管理 - tabs 配置
 */
export const PROJECT_TOOL_TBAS = [
  { key: 'project', title: '页面', _icon: ReadOutlined },
  { key: 'layer', title: '图层', _icon: ApartmentOutlined }
]

/**
 * 元素工具箱 - tabs 配置
 */
export const ELEMENT_TOOL_TABS: { key: ComponentCategory; title: string }[] = [
  { key: 'base', title: '绘图' },
  { key: 'chart', title: 'BI组件' },
  { key: 'component', title: '控件' },
  { key: 'other', title: '更多' }
]

export const ELEMENT_CATEGORY_MAP = _.keyBy(ELEMENT_TOOL_TABS, 'key')

/**
 * 元素的分组字典
 */
export const ELEMENT_GROUP_MAP: Record<ComponentGroup, { key: ComponentGroup; title: string; parent?: string }> = {
  tabs: { key: 'tabs', title: '页签' },
  layout: { key: 'layout', title: '布局' },
  decoration: { key: 'decoration', title: '装饰' },
  filterRule: { key: 'filterRule', title: '数据筛选器' },
  timePicker: { key: 'timePicker', title: '时间输入控件', parent: 'filterRule' }, // 静态化
  textInput: { key: 'textInput', title: '文本输入控件', parent: 'filterRule' }, // 静态化
  numPicker: { key: 'numPicker', title: '数值输入控件', parent: 'filterRule' }, // 静态化
  selector: { key: 'selector', title: '选择控件', parent: 'filterRule' },
  button: { key: 'button', title: '按钮' },
  number: { key: 'number', title: '指标' }, // 静态化
  relationChart: { key: 'relationChart', title: '关系图' },
  table: { key: 'table', title: '数据表格' },
  barChart: { key: 'barChart', title: '柱图' },
  lineChart: { key: 'lineChart', title: '线图' },
  pieChart: { key: 'pieChart', title: '饼图' },
  ringChart: { key: 'ringChart', title: '圆环图' },
  mapChart: { key: 'mapChart', title: '地图' }
}

// 允许静态化的组件分组
export const ALLOW_STATIC_COMPONENT_GROUP = ['timePicker', 'textInput', 'numPicker', 'number']

/**
 * 组件类型字典
 */
export const COMPONENT_TYPE_MAP: Record<ComponentType, { key: ComponentType; title: string }> = {
  element: { key: 'element', title: '元件' },
  chart: { key: 'chart', title: '图表' },
  component: { key: 'component', title: '组件' },
  layout: { key: 'layout', title: '布局' },
  other: { key: 'other', title: '其他' }
}

/**
 * 设计器导航的菜单
 */
export const NAV_MENUS = [
  // { key: '学院', hideText: true, hide: true, icon: props => createElement(Icon, { ...props, name: '学院' }) },
  // { key: '标签', hideText: true, hide: true, icon: props => createElement(Icon, { ...props, name: '标签' }) },
  // { key: '调试', hideText: true, hide: true, icon: props => createElement(Icon, { ...props, name: '调试' }) },
  { key: 'dataset', title: DS_PAGE_NAME, icon: DatabaseOutlined, to: '/dataset' },
  {
    key: 'export',
    title: '',
    iconName: '导入导出',
    children: [
      { key: 'exportPage', title: '导出', icon: VerticalAlignBottomOutlined },
      { key: 'importPage', title: '导入', icon: ToTopOutlined }
    ]
  },
  { key: 'fullscreen', title: '全屏', iconName: '全屏' },
  { key: 'preview', title: '预览', iconName: '预览' },
  // { key: '全屏', hideText: true, icon: ExpandOutlined },
  {
    key: 'save',
    title: '保存当前页面',
    iconName: '保存'
  },
  {
    key: 'release',
    title: '发布',
    children: isTieke ? [
      { key: 'template', title: '发布为模板', icon: SendOutlined }
    ] : [
      { key: 'template', title: '发布为模板', icon: SendOutlined },
      { key: 'arhReport', title: '发布到经营分析会', icon: GatewayOutlined },
      { key: 'appStore', title: '发布到应用商店', icon: AppstoreAddOutlined }
    ]
  }
]

/**
 * 设备类型
 */
export const DEVICE_TYPE_MAP = {
  mobile: { key: 'mobile', title: '移动端', value: 1, icon: '移动端' },
  pc: { key: 'pc', title: '桌面端', value: 0, icon: '电脑' },
  screen: { key: 'screen', title: '大屏', value: 2, icon: '大屏' }
}

export const MOBILE_DEVICE = 1

/**
 * 样式对齐方式
 */
export const STYLE_ALIGNS = [
  { label: '左对齐', value: 'flex-start', icon: AlignLeftOutlined },
  { label: '居中对齐', value: 'center', icon: AlignCenterOutlined }
  // { label: '右对齐', value: 'flex-end', icon: AlignRightOutlined }
]

/**
 * 默认页面的样式配置
 */
export const DEFAULT_PAGE_STYLE = {
  width: 960,
  height: 540,
  justifyContent: 'flex-start', // 这个是对齐方式，在父级用的
  backgroundColor: '#fff',
  backgroundRepeat: 'no-repeat'
}

/**
 * 快捷键描述
 */
export const HOT_KEYS = [
  { title: '画布缩放', description: 'ctrl + 鼠标滚轮' },
  { title: '画布横向滚动', description: 'shift + 鼠标滚轮/←→' },
  { title: '画布垂直滚动', description: '鼠标滚轮/↑↓' },
  { title: '画布全屏', description: 'shift + f11' },
  { type: 'divider' },
  { title: '组件位移', description: '选中 + shift + ↑↓←→' },
  { title: '删除组件', description: '选中 + ctrl + d' },
  { title: '复制组件', description: '选中 + ctrl + c' },
  { title: '粘贴组件', description: '在画布上 + ctrl + v' },
  { title: '全选组件', description: '在画布上 + ctrl + a' },
  { title: '组件多选', description: '在画布上 + shift + 选中' },
  { type: 'divider' },
  { title: '预览当前页面', description: 'ctrl + q' },
  { title: '保存当前页面', description: 'ctrl + s' },
  { title: '撤销页面操作', description: 'ctrl + z' },
  { title: '恢复页面操作', description: 'ctrl + y' },
  { type: 'divider' },
  // { title: '关闭/打开项目管理', description: 'ctrl + g' },
  { title: '关闭/打开配置管理', description: 'ctrl + h' }
  // { title: '关闭/打开工具箱', description: 'ctrl + b' }
]

/**
 * 设备尺寸
 */
export const DEVICE_SIZE_OPTIONS = [
  { title: '智能电视', types: [0, 2], w: 1920, h: 1080 },
  { title: '20英寸显示器', types: [0, 2], w: 1600, h: 900 },
  { title: '19英寸显示器', types: [0, 2], w: 1366, h: 768 },
  { title: '4K', types: [0, 2], w: 3840, h: 2160 },
  { title: '宽屏', types: [0, 2], w: 1440, h: 1024 },
  { title: '大屏', types: [0, 2], w: 2048, h: 1152 },
  { title: 'A3', types: [0, 2], w: 1123, h: 1587 },
  { title: 'A4', types: [0, 2], w: 794, h: 1123 },
  { title: 'A5', types: [0, 2], w: 559, h: 794 },
  { title: 'B4', types: [0, 2], w: 945, h: 1334 },
  { title: 'B5', types: [0, 2], w: 665, h: 945 },
  { title: '信封', types: [0, 2], w: 416, h: 831 },
  { title: '法律专用纸张', types: [0, 2], w: 816, h: 1346 },
  // 下面为移动端
  { title: 'Huawei P8', types: [1], w: 360, h: 640 },
  { title: 'Huawei P40', types: [1], w: 395, h: 856 },
  { title: 'iPhone 13 Pro', types: [1], w: 390, h: 844 },
  { title: 'iPhone 13 Pro Max', types: [1], w: 428, h: 926 },
  { title: 'iPhone 11 Pro/X', types: [1], w: 375, h: 812 },
  { title: 'iPhone 11 Pro Max/X', types: [1], w: 414, h: 896 },
  { title: 'iPhone 8', types: [1], w: 375, h: 667 },
  { title: 'iPhone 8 Plus', types: [1], w: 414, h: 736 },
  { title: 'iPhone SE', types: [1], w: 320, h: 568 },
  { title: 'Google Pixel 2', types: [1], w: 411, h: 731 },
  { title: 'Google Pixel 2XL', types: [1], w: 411, h: 823 },
  // 平板
  { title: 'iPad mini', types: [1], w: 768, h: 1024 },
  { title: 'iPad Pro 11"', types: [1], w: 834, h: 1194 },
  { title: 'iPad Pro 12.9"', types: [1], w: 1024, h: 1366 }
]

export const RELEASE_STATUS_TEXT = {
  default: '未发布', // unReleased
  released: '已发布', // released
  revoke: '已撤销', // revoke
  pause: '已暂停', // pause
  approval: '审批中', // approval
  approvalFailed: '审批不通过', // approvalFailed
  undefined: ''
} as const

/**
 * 元素的排除字段
 *
 * 这些不放在 container 层
 */
export const ELEMENT_OMIT_FIELD = ['width', 'height', 'transform', 'transition', 'borderRadius', 'zIndex', 'opacity']

export const LUCKYSHEET_FILE = 'luckysheet.html'

// 需要更新 luckysheet 内容请参考这里的说明： https://observablehq.com/d/0dd3e7784f456a31
// TODO 迁移至 gitpod 编辑器
export const LUCKYSHEET_URL = `${window.location.origin}/custom-charts/0dd3e7784f456a31/${LUCKYSHEET_FILE}`

// 打印的纸张高度
export const PAGE_SIZE_OPTS = {
  A5: 793.8,
  A4: 1123,
  A3: 1587.6,
  A2: 2245.31,
  A1: 3178.98,
  A0: 4494.42
}

// Windows 系统默认是 96dpi，Apple 系统默认是 72dpi。
// 1mm = 3.78px（96dpi）
// 1cm = 0.394in
// const isMac = /macintosh|mac os x/i.test(window.navigator.userAgent)
export const PRINT_SIZE_OPTIONS = [
  { label: 'A3', value: 'A3', w: 29.7, h: 42.0 },
  { label: 'A4', value: 'A4', w: 21.0, h: 29.7 },
  { label: 'A5', value: 'A5', w: 14.8, h: 21.0 },
  { label: 'B4', value: 'B4', w: 24.99, h: 35.3 },
  { label: 'B5', value: 'B5', w: 17.6, h: 24.99 },
  { label: '信封', value: 'letter', w: 21.6, h: 27.9 },
  { label: '法律专用纸', value: 'legal', w: 21.6, h: 35.6 }
].map(i => ({
  ...i,
  label: `${i.label}（${i.w}cm x ${i.h}cm）`
}))

// 打印页面大小
export const PRINT_SIZE_MAP: Record<string, { width: number, height: number }> = (() => {
  // function getDpi() {
  //   for (let i = 56; i < 2000; i++) {
  //     if (matchMedia(`(max-resolution: ${i}dpi)`).matches === true) return i
  //   }
  // }
  // 根据 css 标准，1 英寸就是 96 px https://www.w3.org/TR/css-values-3/#absolute-lengths
  const dpi = /* getDpi() || */ 96
  return PRINT_SIZE_OPTIONS.reduce((o, v) => ({
    ...o,
    [v.value]: {
      width: _.floor(v.w * 10 * (dpi / 25.4), 4),
      height: _.floor(v.h * 10 * (dpi / 25.4), 4)
    }
  }), {})
})()

// 打印方向
export const PRINT_DIRECTION_OPTIONS = [
  { label: '纵向', value: 'portrait' },
  { label: '横向', value: 'landscape' }
]

/** 编辑器指南 */
export const SCREEN_GUIDE = [
  {
    title: '页面管理',
    target: '[data-left-menu-key=project] > span',
    content: '在这里可以管理你的页面，并且允许创建、编辑、删除页面。',
    placement: 'right'
  },
  {
    title: '组件图层',
    target: '[data-left-menu-key=layer] > span',
    content: '可以通过点击图层列表中的组件，来选中、移动或删除组件，调整图层层级关系。',
    placement: 'right'
  },
  {
    title: '组件工具箱',
    target: '[data-left-menu-key=element] > span',
    content: '可以从一个丰富的组件库中选择你想要的组件，添加到你的页面上。如文本、按钮、图表、图片等。',
    placement: 'right'
  },
  {
    title: '画布编辑',
    target: '#abi-canvas',
    content: '将组件拖拽到画布里编辑，通过对齐、分布等调整完成你的页面设计',
    placement: 'left-start'
  },
  {
    title: '快捷工具栏',
    target: '.screen-top-toolbox > div',
    content: '在这里有些快捷功能可以帮助你，例如撤销恢复、组件对齐等',
    placement: 'bottom'
  },
  {
    title: '页面/组件配置',
    target: '.right-layout > .screen-toolbox-layout',
    content: '右边的面板可以设置页面与组件的配置项。',
    placement: 'right-start'
  },
  {
    title: '预览效果',
    target: '[data-nav-key=preview]',
    content: '编辑完成后，点击这里进行预览当前页面，查看预览效果。'
  },
  {
    title: '保存页面',
    target: '[data-nav-key=save]',
    content: '编辑完成后，点击这里进行保存当前页面。'
  },
  {
    title: '发布页面',
    target: '[data-nav-key=release]',
    content: '保存完成后，点发布按钮把页面发布出去，分享给他人。'
  }
]
