import { klona as cloneDeep } from 'klona/lite'
import _ from 'lodash'

import type { Action, ActionFn } from '@/cores/evaction/types/action'

import { changeComponentStyleFn } from './change-component-style'

// 临时建立的缓存区域，用于缓存旧的 config
const cache: Record<
  string,
  {
    trigger: boolean
    config: Record<string, any>
    time: number
  }
> = {}

/**
 * 改变组件的 config
 */
export const changeComponentConfigFn: ActionFn<Action.ChangeComponentConfig> = (context, runtime) => {
  const { componentKey, config, isTrigger } = context

  return e => {
    if (e && _.isFunction(e?.stopPropagation)) {
      e.stopPropagation()
    }

    // 这个特殊处理，config._isStyle 的转发到 changeComponentStyleFn
    if (config._isStyle) return changeComponentStyleFn({ ...context, style: config }, runtime)?.(e)

    // const com = runtime.getComponentByKey(componentKey)
    // if (!com) return

    runtime.updateStateImmer(state => {
      const key = componentKey
      if (!state.configLinkage.entities) state.configLinkage.entities = {}

      const prevConfig = cloneDeep(_.get(state.configLinkage.entities, key))

      // 组件不存在
      if (!state.components.entities[key]) return

      if (!isTrigger) {
        state.configLinkage.entities[key] = _.merge({}, config)
        if (cache[key]) delete cache[key]
        return state
      }

      // 恢复前状态
      if (cache[key]?.trigger) {
        state.configLinkage.entities[key] = cache[key].config
        cache[key].trigger = false
        return state
      }

      // 缓存前状态
      cache[key] = {
        trigger: true,
        config: prevConfig, // 去掉 proxy
        time: Date.now()
      }
      state.configLinkage.entities[key] = _.merge({}, config)
      return state
    })
  }
}
