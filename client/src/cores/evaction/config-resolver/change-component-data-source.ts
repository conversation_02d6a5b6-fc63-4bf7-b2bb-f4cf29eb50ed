import { klona as cloneDeep } from 'klona/lite'
import _ from 'lodash'

import type { Action, ActionFn } from '@/cores/evaction/types/action.d'
import type { DataSourceConfig } from '@/types/editor-core/data-source'

// 临时建立的缓存区域，用于缓存旧的 Style
const cache: Record<
  string,
  {
    trigger: boolean
    dataSource: DataSourceConfig
    time: number
  }
> = {}

/**
 * 改变组件的 dataSource
 */
export const changeComponentDataSourceFn: ActionFn<Action.ChangeComponentDataSource> = (context, runtime) => {
  const { componentKey, dataSource, isTrigger } = context

  return e => {
    if (e && _.isFunction(e?.stopPropagation)) {
      e.stopPropagation()
    }

    const comp = runtime.getComponentByKey(componentKey)
    if (!comp) return

    comp.updateComponentImmer(component => {
      const key = componentKey
      const prevDataSource = component.dataSource || {}
      if (!isTrigger) {
        component.dataSource = dataSource // merge 不支持去掉配置
        if (cache[key]) delete cache[key]
        return component
      }

      // 恢复前状态
      if (cache[key]?.trigger) {
        component.dataSource = cache[key].dataSource
        cache[key].trigger = false
        return component
      }
      // 缓存前状态

      cache[key] = {
        trigger: true,
        dataSource: cloneDeep(prevDataSource), // 去掉 proxy
        time: Date.now()
      }
      component.dataSource = dataSource
      return component
    })

    comp?.cleanChartData()
  }
}
