import { klona as cloneDeep } from 'klona/lite'
import _ from 'lodash'

import type { Action, ActionFn } from '@/cores/evaction/types/action.d'

// 临时建立的缓存区域，用于缓存旧的 Style
const cache: Record<
  string,
  {
    trigger: boolean
    style: Record<string, any>
    time: number
  }
> = {}

const omitField = ['width', 'height', 'transform', 'borderRadius']

/**
 * 改变组件的 style
 */
export const changeComponentStyleFn: ActionFn<Action.ChangeComponentStyle> = (context, runtime) => {
  const { componentKey, isTrigger } = context
  const style = _.omit(context.style, omitField)

  return e => {
    if (e && _.isFunction(e?.stopPropagation)) {
      e.stopPropagation()
    }

    const com = runtime.getComponentByKey(componentKey)
    if (!com) return

    com.updateComponentImmer(component => {
      const key = componentKey
      const prevStyle = component.style || {}

      if (!isTrigger) {
        component.style = _.merge({}, prevStyle, style)
        if (cache[key]) delete cache[key]
        return component
      }

      // 恢复前状态
      if (cache[key]?.trigger) {
        component.style = {
          ...cache[key].style,
          transition: style.transition
        }
        cache[key].trigger = false
        return component
      }

      // 缓存前状态
      cache[key] = {
        trigger: true,
        style: cloneDeep(prevStyle),
        time: Date.now()
      }

      component.style = _.merge({}, prevStyle, style)

      return component
    })
  }
}
