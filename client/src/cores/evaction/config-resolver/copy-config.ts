import _ from 'lodash'

import type { Action, ActionFn } from '@/cores/evaction/types/action.d'
import { PRE_PROCESSING_UTILS } from '@/utils/query'


/**
 * 复制配置的逻辑实现
 * 对应配置界面代码：client/src/pages/screen/components/workbench/event-config/copy-config-config-panel.tsx
 */
export const copyConfigActionConfigToFn: ActionFn<Action.CopyConfig> = (context, runtime) => {
  const { fromComponentKey, targetComponentKeys, processFn } = context

  let processFun: null | ((targetCfg, srcCfg, utils) => any) = null
  return e => {
    if (e && _.isFunction(e?.stopPropagation)) {
      e.stopPropagation()
    }
    if (!processFun) {
      // eslint-disable-next-line no-eval
      processFun = eval(`(${processFn})`)
    }
    if (!_.isFunction(processFun)) {
      throw new Error('未配置好复制配置转换函数')
    }

    const srcComp = runtime.getComponentByKey(fromComponentKey)
    const srcConfig = srcComp.getInfoWithLinkage()?.config || {}
    _.forEach(_.castArray(targetComponentKeys), compKey => {
      const comp = runtime.getComponentByKey(compKey)
      comp.updateComponentConfigLinkage(prev => processFun!(prev, srcConfig, PRE_PROCESSING_UTILS))
    })
  }
}
