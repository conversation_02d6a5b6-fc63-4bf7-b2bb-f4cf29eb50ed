import _ from 'lodash'

import type { Action,ActionFn } from '@/cores/evaction/types/action.d'
import { getCoords, getElementTransformXY } from '@/utils/dom'

/**
 * 触发滚动条跳到目标组件的上方
 */
export const gotoComponentFn: ActionFn<Action.GotoComponent> = (context, runtime) => {
  const { componentKey, offset = '0' } = context

  return e => {
    if (e && _.isFunction(e?.stopPropagation)) {
      e.stopPropagation()
    }

    let com: any = runtime.getComponentByKey(componentKey)
    if (!com) return

    com = com.getInfo()

    let { y: top = 0 } = getElementTransformXY(com.style)
    if (com.style.position === 'fixed') {
      top = com.style.top
    }
    // TODO: 本地的 dev preview 偏移量有一些差异性，用这个去解决
    if (!window.isDevPreview) {
      const element = document.getElementById(com.key)
      if (element) top = getCoords(element).top
      // 真实的环境取元素的真实 top 值，本地预览环境取
    }
    // if (top > 10) top -= 10 // 加一个小偏移
    const offsetTop = _.parseInt(offset.toString(), 10)
    if (_.isNumber(offsetTop)) {
      top += offsetTop
    }

    const container = document.querySelector('#dev-mobile-device')
    const devContainer = document.querySelector('.screen-design-dev-preview')
    const target = container || devContainer || window
    // 滚动到目标
    target.scrollTo({ top, behavior: 'smooth' })
  }
}
