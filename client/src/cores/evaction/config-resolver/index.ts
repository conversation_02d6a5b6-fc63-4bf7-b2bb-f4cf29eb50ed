import { changeComponentConfigFn } from '@/cores/evaction/config-resolver/change-component-config'
import { changeComponentDataSourceFn } from '@/cores/evaction/config-resolver/change-component-data-source'
import { changeComponentSizeFn } from '@/cores/evaction/config-resolver/change-component-size'
import { changeComponentStyleFn } from '@/cores/evaction/config-resolver/change-component-style'
import { copyConfigActionConfigToFn } from '@/cores/evaction/config-resolver/copy-config'
import { gotoComponentFn } from '@/cores/evaction/config-resolver/goto-component'
import { openUrlActionConfigToFn } from '@/cores/evaction/config-resolver/open-url'
import { exportPdfFn, exportWordFn } from '@/cores/evaction/config-resolver/page-exporter'
import { printPageFn } from '@/cores/evaction/config-resolver/print-page'
import { setExternalFilterActionConfigToFn } from '@/cores/evaction/config-resolver/set-external-filter'
import { setVisibilityActionConfigToFn } from '@/cores/evaction/config-resolver/set-visibility'
import { timedRefreshActionConfigToFn } from '@/cores/evaction/config-resolver/timed-refresh'
import { triggerActionToFn } from '@/cores/evaction/config-resolver/trigger-action'
import { triggerEventActionConfigToFn } from '@/cores/evaction/config-resolver/trigger-event'

/** 动作实现函数字典 */
export const ActionNameImplementFnDict = {
  // TODO 惰性引入
  setVisibility: setVisibilityActionConfigToFn,
  openUrl: openUrlActionConfigToFn,
  gotoComponent: gotoComponentFn,
  changeComponentConfig: changeComponentConfigFn,
  changeComponentStyle: changeComponentStyleFn,
  changeComponentSize: changeComponentSizeFn,
  changeComponentDataSource: changeComponentDataSourceFn,
  exportPdf: exportPdfFn,
  exportWord: exportWordFn,
  triggerEvent: triggerEventActionConfigToFn,
  triggerAction: triggerActionToFn,
  printPage: printPageFn,
  setExternalFilter: setExternalFilterActionConfigToFn,
  timedRefresh: timedRefreshActionConfigToFn,
  copyConfig: copyConfigActionConfigToFn
}

// TODO: 请去添加常量 /consts/define.ts
