import './open-url.less'

import { CloseOutlined } from '@ant-design/icons'
import { history } from '@umijs/max'
import { Modal } from 'antd'
import _ from 'lodash'
import qs from 'querystring'
import React from 'react'

import {
  defaultPopWindowParams,
  EXTERNAL_LINK_URL_KEY,
  GO_BACK_URL_KEY,
  INDICES_FUNCTION_URL_KEY
} from '@/consts/event'
import { EditorCorePreviewRuntimeAPI } from '@/cores/evaction/rumtime-api/preview'
import type { Action, ActionFn, VariablesOption } from '@/cores/evaction/types/action.d'
import { withExtraQuery } from '@/utils/query'

/** 获取最终跳转地址入参类型 */
type GetFinalUrlProps = {
  /** 内部链接 */
  pageUrl: string
  /** 外部链接 */
  externalLink: string
  /** 页面动态变量参数 */
  variables: VariablesOption[]
  /** 指标功能页 */
  indicesFunction?: Action.OpenUrlActionConfig['indicesFunction']
  /** 目标指标 */
  targetMetric?: string
  /** 运行时 */
  runtime: EditorCorePreviewRuntimeAPI

  context: Action.OpenUrlActionConfig
}

// 判断是否有域名
function hasDomain(url) {
  const domainRegex = /^((https?:)?\/\/)?([a-z0-9-]+\.[a-z0-9-]+(\.[a-z0-9-]+)*(:\d+)?)/i
  return domainRegex.test(url)
}

// 打开弹层
const openUrlModal = (url, opt: { top, left, width, height, center }) => {
  const { left, top, center } = opt
  const width = /\d+%/.test(`${opt.width}`) ? opt.width : _.parseInt(opt.width) + 10
  const height = /\d+%/.test(`${opt.height}`) ? opt.height : _.parseInt(opt.height) + 10

  const id = Math.random().toString(32).slice(2)

  const modal = Modal.confirm({
    icon: null,
    className: 'open-url-modal',
    style: center ? { height, width } : { top, left, height, width },
    centered: center,
    width,
    content: (
      <>
        <CloseOutlined
          className='close-icon'
          onClick={() => modal.destroy()}
        />
        <iframe id={id} src={url} title='-' />
      </>
    )
  })

    ; (window as any).__close_modal__ = () => modal.destroy()

  return modal
}

/** 获取最终跳转地址 */
const getFinalUrl = ({ pageUrl, externalLink, variables = [], indicesFunction, targetMetric, runtime }: GetFinalUrlProps) => {

  /** 变量参数 */
  const variablesParams = _.reduce(
    variables,
    (sum, next) => {
      if (!next?.componentKey) return sum
      const data = runtime.getComponentInfoWithLinkage(next.componentKey)
      const value = data?.config?.value
      if (!next?.queryKey || _.isNil(value)) return sum
      return {
        ...sum,
        [next.queryKey]: typeof value === 'object' ? JSON.stringify(value) : value
      }
    },
    {}
  )

  if (pageUrl === EXTERNAL_LINK_URL_KEY) {
    let url
    if (hasDomain(externalLink)) {
      url = externalLink
    } else {
      url = `${window.origin}/${externalLink.replace(/^\//, '')}`
    }

    return { url: withExtraQuery(url, qs.stringify(variablesParams)), search: '' }
  }

  if (pageUrl === INDICES_FUNCTION_URL_KEY) {
    const [baseId, specId] = targetMetric?.split(':') || []
    return {
      url: indicesFunction === 'factor-analysis' ? `/console/mut/factor-analysis/${baseId}` : `/console/monitor-alarms?metricId=${baseId}`,
      search: qs.stringify(variablesParams)
    }
  }

  const query = qs.parse(window.location.search.replace(/^\?/, ''))
  const queryFinal = _.merge(query, variablesParams, { screenId: pageUrl })
  const s = qs.stringify(queryFinal)
  return {
    url: `${window.location.origin}${window.location.pathname}?${s}`,
    search: s
  }
}

// 打开页面事件配置的逻辑实现
export const openUrlActionConfigToFn: ActionFn<Action.OpenUrlActionConfig> = (context, runtime) => {
  const {
    pageUrl,
    externalLink,
    openWay,
    openAfter,
    variables = [],
    indicesFunction,
    targetMetric,
    popWindowParams = { ...defaultPopWindowParams }
  } = context

  return e => {
    if (e && _.isFunction(e?.stopPropagation)) {
      e.stopPropagation()
    }

    const closePage = (wait = 100) => {
      if (openAfter === 'closePage') {
        const fn = () => {
          if (window.parent && (window.parent as any).__close_modal__) {
            (window.parent as any).__close_modal__()
          } else {
            window.close()
          }
        }
        if (wait > 0) setTimeout(fn, wait)
        else fn()
      }
    }

    // 返回上页

    const { url: finalUrl, search } = getFinalUrl({
      pageUrl, externalLink, variables, runtime, indicesFunction, targetMetric,
      context
    })

    console.log('open url:', finalUrl)

    // 给主应用使用
    window.__navTo__ = finalUrl

    if (pageUrl === GO_BACK_URL_KEY) {
      window.history.back()
      closePage()
      return
    }

    if (openWay === 'blank') {
      closePage() // 先关闭再打开
      window.open(finalUrl)
      return
    }
    if (openWay === 'parent') window.parent.location.href = finalUrl
    if (openWay === 'popWindow') {
      const { width, height, center, left, top } = popWindowParams
      // const left = center ? Math.round((window.innerWidth - width) / 2) : popWindowParams.left
      // const top = center ? Math.round((window.innerHeight - height) / 2) : popWindowParams.top

      // window.open(finalUrl, 'popup', qs.stringify({
      //   left: toLeft,
      //   top: toTop,
      //   menubar: 'no',
      //   status: 'no',
      //   toolbar: 'no',
      //   location: 'no',
      //   width,
      //   height
      // }).replace(/&/g, ','))
      openUrlModal(finalUrl, { top, left, width, height, center })
    }

    if (openWay === 'self') {
/*
      if (pageUrl !== EXTERNAL_LINK_URL_KEY && pageUrl !== INDICES_FUNCTION_URL_KEY && pageUrl !== GO_BACK_URL_KEY) {
        // 如果之前的页面没查询完就跳转，会卡死
        history.push({
          pathname: _.startsWith(window.location.pathname, window.routerBase)
            ? window.location.pathname.replace(window.routerBase!, '')
            : window.location.pathname,
          search: `?${search}`
        })
      } else {
        window.location.href = finalUrl
      }
*/
      window.location.href = finalUrl
    }

    closePage()
  }
}
