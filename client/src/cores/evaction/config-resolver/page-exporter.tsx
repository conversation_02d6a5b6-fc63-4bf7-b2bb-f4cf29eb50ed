import { LoadingOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import _ from 'lodash'
import React from 'react'
import ReactDOM, { unmountComponentAtNode } from 'react-dom'
import request from 'umi-request'

import type { Action, ActionFn } from '@/cores/evaction/types/action.d'

function Mark({ pageType, onCancel }) {
  const styles = `
.export-page-mark-modal {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 10999;
  background-color: rgba(1, 1, 1, 0.68);

  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  color: #fff;
}
.export-page-mark-modal > h3,
.export-page-mark-modal > h4,
.export-page-mark-modal > .anticon {
  font-size: 16px;
  color: #fff;
}
.export-page-mark-modal .anticon-loading {
  margin-right: 12px;
  line-height: 1.5;
}
.export-page-mark-modal .cancel-button {
  margin-top: 20px;
  padding: 10px 20px;
  font-size: 16px;
  background-color: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.export-page-mark-modal .cancel-button:hover {
  background-color: #ff7875;
}
`
  return (
    <>
      <style>{styles}</style>
      <div className='export-page-mark-modal'>
        <h3>正在导出 {pageType}，期间请不要做任何操作</h3>
        <h4>提示：导出前请保证数据加载完整</h4>
        <div>
          <LoadingOutlined style={{ fontSize: 24 }} spin />
          导出中...
        </div>
        <button type='button' className='cancel-button' onClick={onCancel}>
          取消导出
        </button>
      </div>
    </>
  )
}

const processPageExport = async (type: 'word' | 'pdf', runtime) => {
  const screen = runtime.getScreenInfo()
  const portal = document.getElementById('abi-app-portal')
  if (!portal) return

  const controller = new AbortController()
  const { signal } = controller

  const currentUrl = new URL(window.location.href)
  currentUrl.searchParams.set('forceRenderOnExport', 'true')
  const localUrl = currentUrl.toString()
  // 获取当前页面的所有的gv开头的localStore 生成一个key value的对象
  const localStore = window.localStorage
  const localStoreKeys = _.keys(localStore)
  const localStoreParams = _(localStoreKeys)
    .filter(key => key.startsWith('gv_'))
    .map(key => ({ [key]: localStore.getItem(key) }))
    .reduce((result, value) => ({ ...result, ...value }), {})

  const onCancel = () => {
    controller.abort() // 取消请求
  }

  ReactDOM.render(<Mark pageType={type.toUpperCase()} onCancel={onCancel} />, portal)
  const pageName = `${screen.title}_${dayjs().format('YYYYMMDDHHmmss')}`
  const extension = type === 'word' ? 'docx' : 'pdf'

  try {
    const blob = await request('/abi/api/export-file', {
      method: 'POST',
      responseType: 'blob',
      credentials: 'include',
      signal,
      data: {
        url: localUrl,
        localStoreParams,
        type,
        name: `${pageName}.${extension}`
      }
    })

    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = pageName
    a.click()
    window.URL.revokeObjectURL(url)
  } catch (err: any) {
    if (err.name === 'AbortError') {
      console.log('export page aborted')
    } else {
      console.error('export page error:', err)
    }
  } finally {
    unmountComponentAtNode(portal)
  }
}

/**
 * 导出 PDF，默认识别整个画布页
 */
export const exportWordFn: ActionFn<Action.GotoComponent> = (context, runtime) => async (e: any) => {
  if (e && _.isFunction(e?.stopPropagation)) {
    e.stopPropagation()
  }
  await processPageExport('word', runtime)
}

/**
 * 导出 Word，默认识别整个画布页
 * @param context
 * @param runtime
 * @returns
 */
export const exportPdfFn: ActionFn<Action.GotoComponent> = (context, runtime) => async (e: any) => {
  if (e && _.isFunction(e?.stopPropagation)) {
    e.stopPropagation()
  }
  await processPageExport('pdf', runtime)
}
