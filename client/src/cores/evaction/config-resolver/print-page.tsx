import _ from 'lodash'
import * as transform from 'transform-parser'

import { PREVIEW_CONTAINER_ID } from '@/consts/elements'
import { PAGE_SIZE_OPTS } from '@/consts/screen'
import type { Action, ActionFn } from '@/cores/evaction/types/action.d'
import { store } from '@/stores'
import { PubSubVerbose as PubSub } from '@/utils/pubsub'


/** 计算安全页高，避免打印预览超出页面（某些表格横屏打印时，有些页面表格会超出本页，未知原因） */
export function getSafePaperHeight(paperHeight: number) {
  if (paperHeight < 1) {
    return paperHeight
  }
  return Math.round(paperHeight - paperHeight ** 0.5 * 1.5)
}

/**
 * 打印页面，默认为整个页
 */
export const printPageFn: ActionFn<Action.PagePrint> = (context, runtime) => {
  const { rawKey } = context

  return async e => {
    if (e && _.isFunction(e?.stopPropagation)) e.stopPropagation()
    // const target = e?.target as HTMLDivElement
    // if (target) target.classList?.add('no-print')

    const canvas = document.getElementById(PREVIEW_CONTAINER_ID.substring(1))
    const isDevPreview = window.isDevPreview

    const innerComps = await Promise.all(_.map(
      canvas?.querySelectorAll(isDevPreview ? 'div[id^="dev-"]' : 'div[id*="@"]'),
      async (dom: HTMLElement) => {
        const compKey = dom.id.replace('dev-', '')
        const isReport = _.includes(compKey, 'component-luckysheet')
        const domHeight = dom.clientHeight
        const contentHeightCalcFnCache : null | ((pageHeight: number) => number) = isReport
          ? await new Promise(resolve => {
            PubSub.publish(`notify:${compKey}/getContentHeightCalcFn`, resolve)
          })
          : () => domHeight
        const defaultPageHeight = getSafePaperHeight(PAGE_SIZE_OPTS.A4) - 40
        const getHeight = (pageHeight?: number) => isReport
          ? contentHeightCalcFnCache?.(pageHeight || defaultPageHeight) ?? domHeight
          : domHeight
        return {
          id: compKey,
          y: transform.parse(dom.style.transform).translate[1],
          // yPadding 默认为 40，参考 paddingStyle, client/src/pages/screen/containers/dev-preview/print-preview/utils.ts
          height: getHeight(defaultPageHeight),
          getHeight // 主要是为了封装 runtime，之后页面高度变化后，实时计算内容高度
        }
      }
    ))

    // 触发进入打印
    store.dispatch({
      type: 'editorCorePreview/enterPrint',
      payload: {
        enable: true,
        params: {
          ...context,
          autoPaging: (context as any).autoPaging !== undefined ? (context as any).autoPaging : true
        },
        triggerId: rawKey,
        compHeightDict: {
          screen: { id: 'screen', height: canvas?.scrollHeight || 0, y: 0, getHeight: () => canvas?.scrollHeight || 0 },
          ..._.keyBy(innerComps, 'id')
        }
      }
    })
  }
}
