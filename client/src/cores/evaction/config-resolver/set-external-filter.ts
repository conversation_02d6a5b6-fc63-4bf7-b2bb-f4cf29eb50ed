import isEqual from 'fast-deep-equal'
import { klona as cloneDeep } from 'klona/lite'
import _ from 'lodash'

import type { Action, ActionFn } from '@/cores/evaction/types/action.d'
import { useQueryParams } from '@/hooks/use-query-params'

// 临时建立的缓存区域，用于缓存旧的 config
const cache: Record<
  string,
  {
    trigger: boolean
    filter: Record<string, { col: string; eq: any; bind: string; type?: string }>
    time: number
  }
> = {}

/**
 * 设置可见性的配置的逻辑实现
 */
export const setExternalFilterActionConfigToFn: ActionFn<Action.ExternalFilter> = (context, runtime) => {
  const { componentKey, filter = [], isTrigger } = context

  return e => {
    if (e && _.isFunction(e?.stopPropagation)) {
      e.stopPropagation()
    }

    let needReadLoadData = false
    const query = useQueryParams()
    // TODO: 这里可以扩展支持 localstroage 的读取

    // ?xxx=2015-01-08,2022-11-08
    const queryData = _.mapValues(query, val => {
      if (String(val).indexOf(',') > -1) return decodeURIComponent(val).split(',')
      return decodeURIComponent(val)
    })

    runtime.updateStateImmer(state => {
      const key = componentKey
      const prev = state.filterLinkage.entities[key] // 当前值
      const next: Record<string, any> = { ...prev } // 修饰后的值

      if (!state.components.entities[key]) return // 组件不存在

      _.forEach(filter, f => {
        if (!f.bind || queryData[f.bind] === undefined) return // 绑定的变量一定要存在才行
        needReadLoadData = true
        const dim = f.col
        const val = { ...f, eq: queryData[f.bind] }
        if (_.isArray(next[dim])) {
          // 用 bind 判断是否已经添加过，col 必须相同
          const o = next[dim].find(i => i.col === f.col && (i.bind === f.bind || isEqual(i.eq, f.eq)))
          if (!o) {
            next[dim].push(val) // 已经添加了
          } else {
            o.eq = queryData[f.bind]
          }
        } else {
          next[dim] = [val] // 新增加
        }
      })

      // ...
      // 添加 url 外部条件联动
      // 1. 获取组件外部条件配置
      // 2. 设置 url 的参数值到 filterLinkage 里
      // 2. 合并相同的 col name 值

      if (!isTrigger) {
        state.filterLinkage.entities[key] = next
        if (cache[key]) delete cache[key]
        return state
      }

      // 恢复前状态
      if (cache[key]?.trigger) {
        state.filterLinkage.entities[key] = cache[key].filter as any
        cache[key].trigger = false
        return state
      }

      // 缓存前状态
      cache[key] = {
        trigger: true,
        filter: cloneDeep(next), // 去掉 proxy
        time: Date.now()
      }
      state.filterLinkage.entities[key] = next
      return state
    })

    if (needReadLoadData) {
      setTimeout(() => {
        const comp = runtime.getComponentByKey(componentKey)
        comp?.cleanChartData()
      }, 100)
    }
  }
}
