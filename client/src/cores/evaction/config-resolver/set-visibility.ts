import _ from 'lodash'

import type { Action, ActionFn } from '@/cores/evaction/types/action.d'

/**
 * 设置可见性的配置的逻辑实现
 * 对应配置界面代码：client/src/pages/screen/components/workbench/event-config/set-visibility-config-panel.tsx
 */
export const setVisibilityActionConfigToFn: ActionFn<Action.SetVisibility> = (context, runtime) => {
  const { componentKey, visibility } = context

  const targetVisibility = +visibility !== 0
  return e => {
    if (e && _.isFunction(e?.stopPropagation)) {
      e.stopPropagation()
    }

    _.forEach(_.castArray(componentKey), compKey => {
      const comp = runtime.getComponentByKey(compKey)
      if (+visibility === -1) {
        // 反转可见性
        const visible = comp.getInfoWithLinkage()?.config.visible
        comp.setVisibility(!visible)
        return
      }
      comp.setVisibility(targetVisibility)
    })
  }
}
