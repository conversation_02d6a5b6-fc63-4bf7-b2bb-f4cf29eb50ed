import _ from 'lodash'

import type { Action, ActionFn } from '@/cores/evaction/types/action.d'
import { store } from '@/stores'

// 用于记录定时器的 id
// rawKey: info
const timerMap: Record<string, { timer: any, count: number }> = {}

// 启动定时器
const runTimer = (context, callback) => {
  const { interval = 5, unit = 'm', repeat = 1, rawKey } = context
  const time = (() => {
    if (unit === 's') return interval * 1000
    if (unit === 'm') return interval * 1000 * 60 // 1分钟
    if (unit === 'h') return interval * 1000 * 60 * 60 // 1小时
    return 1000 * 60
  })()

  if (!timerMap[rawKey]) timerMap[rawKey] = { timer: undefined, count: 0 }

  // 先清定时器
  clearTimeout(timerMap[rawKey].timer)
  // 执行
  timerMap[rawKey].timer = setTimeout(async () => {
    timerMap[rawKey].count += 1
    try {
      await callback()
    } catch (err) {
      // ...
    }
    if (timerMap[rawKey].count >= repeat) {
      clearTimeout(timerMap[rawKey].timer)
    } else {
      // 递归执行
      runTimer(context, callback)
    }
  }, time)
}

/**
 * 设置定时器，定时刷新数据，
 * 可以设置定时次数，1 次或多次，或永久
 * 可以设置刷新的组件，全部或某些
 */
export const timedRefreshActionConfigToFn: ActionFn<Action.TimedRefresh> = (context, runtime) => {
  const { componentKeys = [] } = context

  return e => {
    if (e && _.isFunction(e?.stopPropagation)) {
      e.stopPropagation()
    }

    runTimer(_.pick(context, ['interval', 'unit', 'repeat', 'rawKey']), async () => {
      await store.dispatch({
        type: 'editorCorePreview/asyncQueryChartData',
        payload: { componentKey: componentKeys }
      })
    })

  }
}
