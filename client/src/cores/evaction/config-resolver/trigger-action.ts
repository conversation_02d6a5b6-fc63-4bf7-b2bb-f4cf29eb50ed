import _ from 'lodash'

import { ActionNameImplementFnDict } from '@/cores/evaction/config-resolver/index'
import { UTILS_FOR_EVENT_HANDLER } from '@/cores/evaction/rumtime-api/preview'
import type { Action, ActionFn } from '@/cores/evaction/types/action.d'


/** 触发其他组件事件的配置的逻辑实现 */
export const triggerActionToFn: ActionFn<Action.TriggerAction> = (context, runtime) => {
  const { componentKey, params } = context

  function compile(): ((e: Event | undefined, compKey: string) => any) {
    try {
      const customEvHandlers =
        componentKey === 'page'
          ? null
          : runtime.getComponentByKey(componentKey).getDefineInfo().eventActionDefine?.actionHandler

      const targetActName = params.actionName
      const compileFn = customEvHandlers?.[targetActName]
        ? // eslint-disable-next-line no-eval
        eval(`(${customEvHandlers?.[targetActName]})`)
        : ActionNameImplementFnDict[targetActName]

      return compileFn(params, runtime, UTILS_FOR_EVENT_HANDLER)
    } catch (err) {
      console.error(err)
    }
  }

  // 缓存，避免重复编译
  let eventHandlerFn: ((e: Event | undefined, compKey: string) => any) | null = null
  return e => {
    if (e && _.isFunction(e?.stopPropagation)) {
      e.stopPropagation()
    }
    if (!eventHandlerFn) {
      eventHandlerFn = compile()
    }
    return eventHandlerFn(e, componentKey)
  }
}
