import _ from 'lodash'

import type { Action, ActionFn } from '@/cores/evaction/types/action.d'

/** 触发其他组件事件的配置的逻辑实现 */
export const triggerEventActionConfigToFn: ActionFn<Action.TriggerEvent> = (context, runtime) => {
  const { componentKey, eventName } = context

  let onEvents: Record<string, Function> | null = null
  return e => {
    if (e && _.isFunction(e?.stopPropagation)) {
      e.stopPropagation()
    }

    // 缓存 onEvents，避免每次都去 runtime.genEventHandler 生成
    if (!onEvents) {
      const customHandlers = runtime.genEventHandler(componentKey, 'unknown')
      const domHandlers = runtime.genEventHandler(componentKey, 'dom')
      onEvents = { ...domHandlers, ...customHandlers }
    }
    if (eventName === 'change' && onEvents.onChange && !_.has(onEvents, 'change')) {
      // 兼容 dom 的 change 事件，主要为了适配筛选器组件
      return onEvents.onChange(e)
    }
    onEvents[eventName](e)
  }
}
