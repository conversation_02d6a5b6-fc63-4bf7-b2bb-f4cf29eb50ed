import { useCreation } from 'ahooks'

import { DataSourceRuntimeAPI } from '@/cores/evaction/rumtime-api/data-source'
import { EditorCorePreviewRuntimeAPI } from '@/cores/evaction/rumtime-api/preview'
import { store } from '@/stores'
import dataSourceModel, { useCommit as useDataSourceCommit } from '@/stores/models/data-source'
import editorCorePreview, { useCommit } from '@/stores/models/editor-core-preview'
import { isDebugEnv } from '@/utils/logger'

interface Options {
  isPreview?: boolean
  [key: string]: any
}

/** 取得数据源 运行时 API */
export function useDataSourceRuntimeAPI(options: Options | undefined) {
  const dataSourceCommit = useDataSourceCommit()
  return useCreation(() => {
    // 非预览模式不触发
    if (!options?.isPreview) return undefined

    const modelState = new Proxy(
      {},
      {
        get(_target: any, p: string | symbol, receiver: any): any {
          const state = store.getState()[dataSourceModel.name!]
          return Reflect.get(state, p, receiver)
        }
      }
    )
    return new DataSourceRuntimeAPI({
      modelState,
      loadMoreDataSourceInfo: payload => dataSourceCommit('loadMoreDataSourceInfo', payload),
      verbose: isDebugEnv()
    })
  }, [options?.isPreview])
}

/** 取得编辑器运行时环境 API，一般是预览时使用 */
export function useEditorCoreRuntimeAPI(options?: Options) {
  const commit = useCommit()
  const dataSourceRuntimeAPI = useDataSourceRuntimeAPI(options)

  const result = useCreation(() => {
    // 非预览模式不触发
    if (!options?.isPreview) return undefined
    const modelState = new Proxy(
      {},
      {
        get(_target: any, p: string | symbol, receiver: any): any {
          const state = store.getState()[editorCorePreview.name!]
          return Reflect.get(state, p, receiver)
        }
      }
    )
    return new EditorCorePreviewRuntimeAPI({
      modelState,
      stateUpdater: updater => commit('update', updater),
      getDataSourceRuntimeAPI: () => dataSourceRuntimeAPI!,
      reloadChartData: (componentKey: string | string[]) => commit('asyncQueryChartData', { componentKey }),
      verbose: isDebugEnv(),
      ...options
    })
  }, [options?.isPreview])

  return result
}
