import _ from 'lodash'

import { DrillDownHelper } from '@/cores/evaction/rumtime-api/helper/drill-down'
import { EditorCorePreviewRuntimeAPI } from '@/cores/evaction/rumtime-api/preview'
import { getColumnTreeDataWithoutAll } from '@/pages/screen/components/workbench/data-config/utils'
import { ALL_ID } from '@/services/indices-table'
import type { Component } from '@/types/editor-core/component'
import { Config } from '@/types/editor-core/config'
import { DataLoaderConfig } from '@/types/editor-core/data-source'
import { EventBinder } from '@/types/editor-core/events'
import { FilterLinkage } from '@/types/editor-core/filter-linkage'

/** 组件运行时 API；这个类只写一些简单的数据读取和设置；需要扩展业务逻辑的话，应该参考 helper/drill-down */
export class ComponentRuntimeAPI {
  readonly previewRuntimeApi: EditorCorePreviewRuntimeAPI

  readonly componentKey: string

  constructor(editorCorePreviewRuntimeApi: EditorCorePreviewRuntimeAPI, componentKey: string) {
    this.previewRuntimeApi = editorCorePreviewRuntimeApi
    this.componentKey = componentKey
  }

  /** 获取组件实例信息 */
  getInfo() {
    return this.previewRuntimeApi.getComponentInfo(this.componentKey)
  }

  /** 获取组件实例信息，合并联动信息后的 */
  getInfoWithLinkage() {
    return this.previewRuntimeApi.getComponentInfoWithLinkage(this.componentKey)
  }

  /** 获取组件定义信息 */
  getDefineInfo() {
    const defineKey = this.getInfo().defineKey
    return this.previewRuntimeApi.getComponentDefine(defineKey)
  }

  /** 编译 eventAction，获取 dom 事件处理回调 */
  genEventHandler(binder: EventBinder) {
    return this.previewRuntimeApi.genEventHandler(this.componentKey, binder)
  }

  /** 获取组件的数据源查询列配置 */
  getQueryColumns() {
    const srcCompInfo = this.getInfo()
    const ds = srcCompInfo.dataSource
    const queryCfg = ds[ds.dataSourceType]
    const fieldsBinding = queryCfg?.fieldsBinding
    const orders = queryCfg?.sortedFieldNames || _.keys(fieldsBinding)
    return fieldsBinding ? _.compact(_.map(orders, fieldName => fieldsBinding[fieldName])) : []
  }

  /** 更新组件 */
  updateComponentImmer(updater: (com: Component) => Component | void) {
    this.previewRuntimeApi.updateStateImmer(state => {
      const item = state.components.entities[this.componentKey]
      if (!item) return
      const next = updater(item)
      if (!next) return
      state.components.entities[this.componentKey] = next
    })
  }

  /** 更新查询配置 */
  updateDataSourceQueryConfig(updater: (queryCfg: DataLoaderConfig | undefined) => DataLoaderConfig | void) {
    this.updateComponentImmer(prevComp => {
      const dataSourceType = prevComp.dataSource.dataSourceType
      const next = updater(prevComp.dataSource[dataSourceType])
      if (!next) return
      prevComp.dataSource[dataSourceType] = next as any
    })
  }

  /** 更新组件 config 联动配置 */
  updateComponentConfigLinkage(updater: (prev: Config | undefined) => Config | void) {
    this.previewRuntimeApi.updateStateImmer(state => {
      state.configLinkage.entities = state.configLinkage.entities || {}
      const item = state.configLinkage.entities[this.componentKey]
      const next = updater(item)
      if (!next) return
      state.configLinkage.entities[this.componentKey] = next
    })
  }

  /** 更新组件 filter 联动配置 */
  updateComponentFilterLinkage(updater: (prev: FilterLinkage | undefined) => FilterLinkage | void) {
    this.previewRuntimeApi.updateStateImmer(state => {
      const item = state.filterLinkage.entities[this.componentKey]
      const next = updater(item)
      if (!next) return
      state.filterLinkage.entities[this.componentKey] = next
    })
  }

  /** 设置可见性 FIXME 通过 helper 扩展 */
  setVisibility(visible: boolean) {
    this.previewRuntimeApi.updateStateImmer(draft => {
      // TODO 通过 linkage 修改?
      const comp = draft.components.entities[this.componentKey]
      if (!comp) return // 目标组件已被删除
      comp.config.visible = visible
    })
    return this
  }

  /** 获取图表数据 */
  getChartData() {
    return this.previewRuntimeApi.getComponentChartData(this.componentKey)
  }

  /** 通过清空 queryData，来触发重新查询逻辑 */
  cleanChartData() {
    console.log('重置数据')
    this.previewRuntimeApi.updateStateImmer(previewState => {
      // TODO: 不能清空，不然疯狂出 loading
      // previewState.chartData.entities[this.componentKey] = null // null 表示查询中，如果用 delete 则会读取 demo 数据
      previewState.chartData.keys = _.without(previewState.chartData.keys, this.componentKey)
    })
  }

  /** 更直接的刷新数据的方法 */
  reloadChartData() {
    console.log('刷新数据')
    this.previewRuntimeApi.reloadChartData(this.componentKey)
  }

  /**
   * 添加组件事件触发回调
   * @param eventName  dom 事件一般是 click，change 这种，组件一般是 onClick，onChange 这种；
   *                   例如：想事件只被 echarts 触发，则可监听 click 事件且设置 eventDefine.click.binder: 'unknown'
   * @param handlerFn  例如下拉框的 onChange: val => ...
   */
  addEventListener(eventName, handlerFn) {
    this.previewRuntimeApi.updateStateImmer(state => {
      const entities = state.eventActionLinkage.entities
      if (!entities[this.componentKey]) {
        entities[this.componentKey] = { keys: [], entities: {} }
      }
      const myEvAct = entities[this.componentKey]
      const nextEntities = {
        ...myEvAct.entities,
        [eventName]: [
          ...(myEvAct.entities[eventName] || []),
          { actionName: `${eventName}_TempHandler`, runtimeHandler: handlerFn }
        ]
      }
      entities[this.componentKey] = { keys: _.keys(nextEntities), entities: nextEntities }
    })
  }

  /** 更新配置 */
  setConfig(nextConfig: Config | ((prevConfig: Config) => Config)) {
    this.updateComponentConfigLinkage(prevConfig => {
      if (_.isFunction(nextConfig)) {
        return nextConfig(prevConfig || {})
      }
      return nextConfig
    })
  }

  /** 更新组件的值 */
  setConfigValue(nextValue: any) {
    this.setConfig({ value: nextValue })
  }

  /** 设置图表数据 */
  setDataSourceQueryResult(data: any[]) {
    this.previewRuntimeApi.updateStateImmer(prev => {
      prev.chartData.entities[this.componentKey] = data
      prev.chartData.keys = _.keys(prev.chartData.entities)
    })
  }

  /** 获取数据源查询配置 */
  getDataSourceQueryConfig() {
    const dataSourceConfig = this.getInfo().dataSource
    return dataSourceConfig[dataSourceConfig.dataSourceType]
  }

  /** 获取下钻 helper */
  getDrillDownHelper() {
    return new DrillDownHelper(this)
  }

  /** 获取数据源所选指标 ID */
  getDataSourceQueryMetrics() {
    const queryConfig = this.getDataSourceQueryConfig()
    return _(queryConfig?.fieldsBinding)
      .pickBy((c, k) => !_.includes(k, '$') && c?.type === 'indicesSpec')
      .map(c => c!.id)
      .value()
  }

  /**
   * 加载数据源指标树 FIXME 重构，提取到 helper
   * @param mode  selectedSpecs 查询已选了指标；stored 不查询，直接读 store；all 查询已选 + 专业 + 基础指标
   */
  async loadDataSourceMetricsTree(mode: 'all' | 'selectedSpecs' | 'stored' = 'all') {
    const dataSourceRuntimeAPI = this.previewRuntimeApi.getDataSourceRuntimeAPI()
    if (!dataSourceRuntimeAPI) {
      return []
    }
    const comp = this.getInfo()
    const dataSourceConfig = comp.dataSource
    const queryConfig = dataSourceConfig[dataSourceConfig.dataSourceType]
    if (!queryConfig || !dataSourceRuntimeAPI) {
      return []
    }
    // 根据 value 获取 spec，避免 id 翻译失败
    if (mode !== 'stored') {
      const selectedKeys = this.getDataSourceQueryMetrics()
      const baseIdxIds = _.uniq(_.map(selectedKeys, specId => specId.split(':')[0]))
      await Promise.all(
        _.map(baseIdxIds, baseIdxId =>
          dataSourceRuntimeAPI.loadMoreDataSourceInfo({ type: 'indicesSpec', id: baseIdxId })
        )
      )
    }
    if (mode === 'all') {
      await dataSourceRuntimeAPI.loadMoreDataSourceInfo({
        type: 'indicesMajor',
        id: `${queryConfig.tableId}`
      })
      await dataSourceRuntimeAPI.loadMoreDataSourceInfo({
        type: 'indicesBase',
        id: ALL_ID
      })
    }

    const treeData = getColumnTreeDataWithoutAll(dataSourceRuntimeAPI.modelState!, queryConfig, 'number')
    return treeData
  }

  /**
   * 设置数据源所选指标
   * @param _specIds 口径 ID
   */
  setDataSourceQueryMetrics(_specIds: string[]) {
    throw new Error('Unimplemented')
  }
}
