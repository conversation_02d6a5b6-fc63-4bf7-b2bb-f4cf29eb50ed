import { DataSourceInfo, DataSourceInfoType } from '@/types/data-source'

export type DataSourceRuntimeAPIProps = {
  modelState: DataSourceInfo
  loadMoreDataSourceInfo: (payload: { type: DataSourceInfoType; id?: string }) => any
  verbose?: boolean
}

/** 数据源运行时控制 API */
export class DataSourceRuntimeAPI {
  readonly modelState: DataSourceInfo

  readonly loadMoreDataSourceInfo: (payload: { type: DataSourceInfoType; id?: string }) => any

  readonly verbose: boolean

  constructor(props: DataSourceRuntimeAPIProps) {
    this.modelState = props.modelState
    this.loadMoreDataSourceInfo = props.loadMoreDataSourceInfo
    this.verbose = props.verbose || false
  }
}
