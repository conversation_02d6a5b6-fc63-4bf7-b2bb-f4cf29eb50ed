import _ from 'lodash'

import { ComponentRuntimeAPI } from '@/cores/evaction/rumtime-api/component'
import { DataFilterCondition } from '@/types/editor-core/data-source'
import { FilterRuleCompConfig } from '@/types/editor-core/filter-linkage'
import { tryJsonParse } from '@/utils/json-utils'
import { delayPromised } from '@/utils/promise-utils'


/** 数据筛选器运行时 API TODO 改成 helper 的形式扩展 */
export default class FilterRuleComponentRuntimeAPI extends ComponentRuntimeAPI {
  /** 初始化数据筛选器 */
  async initFilterRule() {
    const filterRuleCompInfo = this.getInfo()
    const config = filterRuleCompInfo.config as FilterRuleCompConfig
    if (config.saveCompValueToSession) {
      await this.loadStorageToInputCompValue(config)
    }
    // 隐藏了按钮，则值变动后触发查询，注意：输入组件始终要联动，只是图表组件需要根据配置开启联动
    this.applyFilterRuleWhenConditionChange()
    if (config.applyDefaultValueOnMounted || (!config.showQueryBtn && config.saveCompValueToSession)) {
      // 等输入组件初始化完成
      setTimeout(() => this.applyFilterRule(), 100)
    }
  }

  /** 如果隐藏了查询按钮，则值变动后触发查询 */
  applyFilterRuleWhenConditionChange() {
    const filterRuleCompInfo = this.getInfo()
    const config = filterRuleCompInfo.config as FilterRuleCompConfig
    // 获取内部筛选器
    const inputChartColumnMap = config.filterRule?.inputChartColumnMap || {}
    _.keys(inputChartColumnMap).forEach(key => {
      const inputComp = this.previewRuntimeApi.getComponentByKey(key)
      // 添加 onChange 监听
      inputComp.addEventListener('onChange', () => {
        // 给一些时间让状态设置到 model
        setTimeout(() => this.applyFilterRule('filterComp'), 50)
      })
    })
  }

  /** 读取 sessionStorage 的 filer 组件的 value 到 filter 组件  */
  private async loadStorageToInputCompValue(config: FilterRuleCompConfig) {
    const filterCompValueMap = sessionStorage.getItem('filterCompValueMap')
    const parseValueMap = tryJsonParse(filterCompValueMap, {})
    const inputChartColumnMap = config.filterRule?.inputChartColumnMap || {}
    _.keys(inputChartColumnMap)
      .filter(key => parseValueMap?.[key])
      .forEach(key => {
        const inputComp = this.previewRuntimeApi.getComponentByKey(key)
        inputComp.setConfigValue(parseValueMap[key])
      })
    await delayPromised(50)
  }

  /** 将 filer 组件已设置的 value 保存到 sessionStorage */
  private setInputCompValueToSessionStorage(config: FilterRuleCompConfig, inputValDict) {
    if (!config.saveCompValueToSession) return
    const filterCompValueMap = sessionStorage.getItem('filterCompValueMap')
    let parseValueMap = tryJsonParse(filterCompValueMap, {})
    parseValueMap = {
      ...(_.isPlainObject(parseValueMap) ? parseValueMap : {}),
      ...inputValDict
    }
    sessionStorage.setItem('filterCompValueMap', JSON.stringify(parseValueMap))
  }

  /** 重置数据筛选器 */
  resetFilterRule() {
    const filterRuleCompInfo = this.getInfo()
    const config = filterRuleCompInfo.config as FilterRuleCompConfig
    const inputChartColumnMap = config.filterRule?.inputChartColumnMap || {}
    _.keys(inputChartColumnMap).forEach(key => {
      const inputComp = this.previewRuntimeApi.getComponentByKey(key)
      // 如果组件绑定了全局变量，则全局变量也清除值
      const inputCompConfig = inputComp.getInfo().config
      if (inputCompConfig?.globalVarName) {
        localStorage.removeItem(`gv_${inputCompConfig.globalVarName}`)
      }
      inputComp.setConfigValue(null)
    })
  }

  /** 应用数据筛选器，如果 includeCharts 则只联动输入组件 */
  applyFilterRule(evSrc: 'filterComp' | 'queryBtn' = 'queryBtn') {
    // 判断当前是否快照页面，如果是快照页面，则不应用数据筛选器
    if (this.previewRuntimeApi.isSnapshotPage()) return
    // 配置 demo
    /*  const filterRuleConfig = {
      inputChartColumnMap: {
        'custom-3KiTl@1.0.0-II94C': {
          // 图表组件
          'custom-ZI74r@1.0.0-GhyI8': { type: 'indicesDims', id: 'time_date', name: 'time_date', title: '时间' }
        }
      }
    } */

    const define = this.getDefineInfo()
    if (define.group !== 'filterRule') {
      throw new Error(`组件 ${define.title || define.name} 不是数据筛选器`)
    }

    const filterRuleComp = this.getInfo()
    const config = filterRuleComp.config as FilterRuleCompConfig
    const inputChartColumnMap = config.filterRule?.inputChartColumnMap || {}
    const inputSubQueryColumnMap = config.filterRule?.inputSubQueryMap || {}
    if (_.isEmpty(inputChartColumnMap)) {
      throw new Error(`组件 ${define.title || define.name} 尚未配置好数据筛选器`)
    }
    // 读取组件的值，按照惯例是读 value
    const inputValDict = _.mapValues(
      inputChartColumnMap,
      (_v, inputCompKey) => this.previewRuntimeApi.getComponentInfoWithLinkage(inputCompKey)?.config.value
    )

    this.setInputCompValueToSessionStorage(config, inputValDict)

    const allQueryDataComps = this.previewRuntimeApi.getComponentInfosWithDataQuery()

    // 图表级别的筛选
    const chartFltsDict = _(inputChartColumnMap)
      .flatMap((chartColumnMap, inputCompKey) => {
        const inputComp = this.previewRuntimeApi.getComponentByKey(inputCompKey)?.getInfo()
        const inputCompConfig = inputComp?.config || {}
        return _.flatMap(chartColumnMap, (colInfo, chartKey) => {
          if (!colInfo) {
            // 没有绑定维度
            return []
          }
          // 展开 *
          if (chartKey === '*') {
            return allQueryDataComps
              .map(comp => ({ ...colInfo, inputKey: inputCompKey, chartKey: comp.key, inputCompConfig }))
              .filter(d => d.inputKey !== d.chartKey) // 不应该影响自己
          }
          return { ...colInfo, inputKey: inputCompKey, chartKey, inputCompConfig }
        })
      })
      .groupBy(info => info.chartKey)
      .mapValues(arr =>
        arr
          .map(inf => ({
            col: inf.name,
            op: inf.inputCompConfig.filterOp || 'in', // 组件 config 上有 filterOp 字段就取它，默认 in
            eq: inputValDict[inf.inputKey],
            type: inf.dataType
          }))
          .filter(flt => !_.isEmpty(_.compact(flt.eq)))
      )
      .value()

    // 子查询的筛选
    const chartSubQueryDict = _(inputChartColumnMap)
      .flatMap(chartColMap => chartColMap['*'] ? allQueryDataComps.map(c => c.key) : _.keys(chartColMap))
      .uniq()
      .thru(chartKeys =>
        _.zipObject(
          chartKeys,
          chartKeys.map(ck => {
            const chartComp = this.previewRuntimeApi.getComponentByKey(ck)?.getInfo()
            const dataSource = chartComp?.dataSource
            return dataSource?.dataSourceType !== 'combine' ? [] : dataSource.combine?.combineOrders || []
          })
        )
      )
      .value()
    const subQueryFltsDict = _(inputSubQueryColumnMap)
      .flatMap((subQueryColumnMap, inputCompKey) => {
        const inputComp = this.previewRuntimeApi.getComponentByKey(inputCompKey)?.getInfo()
        const inputCompConfig = inputComp?.config || {}
        return _.flatMap(subQueryColumnMap, (colInfo, queryKey) => {
          if (!colInfo) {
            // 没有绑定维度
            return []
          }
          // 展开 *
          if (queryKey === '*') {
            return _.flatMap(allQueryDataComps, comp =>
              _.map(chartSubQueryDict[comp.key], subQuery => ({
                ...colInfo,
                inputKey: inputCompKey,
                chartKey: comp.key,
                queryKey: subQuery,
                inputCompConfig
              }))
            ).filter(d => d.inputKey !== d.chartKey) // 不应该影响自己
          }
          return { ...colInfo, inputKey: inputCompKey, queryKey, inputCompConfig }
        })
      })
      .groupBy(info => info.queryKey)
      .mapValues(arr =>
        arr
          .map(inf => ({
            col: inf.name,
            op: inf.inputCompConfig.filterOp || 'in', // 组件 config 上有 filterOp 字段就取它，默认 in
            eq: inputValDict[inf.inputKey],
            type: inf.dataType
          }))
          .filter(flt => !_.isEmpty(_.compact(flt.eq)))
      )
      .value()

    // 设置筛选条件，清空 queryData，以便触发重新查询逻辑，分三种情况：
    // 1. 显示了查询按钮，如果 a. 没有按查询按钮，只触发输入组件的联动；b. 按了查询按钮，则图表组件重新查询
    // 2. 没显示查询按钮，只要输入组件有变化，全部组件都重新查询

    const inputCompKeys = _.keys(inputChartColumnMap)
    this.previewRuntimeApi.updateStateImmer(previewState => {
      const condChangedCompKeysSet = new Set()
      _.forEach(chartFltsDict, (flts, chartKey) => {
        if (!previewState.filterLinkage) {
          previewState.filterLinkage = { global: {}, entities: {}, subQuery: {} }
        }
        const nextFltsDict = _.groupBy(flts as DataFilterCondition[], flt => flt.col)
        const currFltsDict = previewState.filterLinkage.entities[chartKey]
        if (!_.isEqual(nextFltsDict, currFltsDict)) {
          condChangedCompKeysSet.add(chartKey)
        }
        previewState.filterLinkage.entities[chartKey] = nextFltsDict
      })
      _.forEach(subQueryFltsDict, (flts, queryKey) => {
        if (!previewState.filterLinkage) {
          previewState.filterLinkage = { global: {}, entities: {}, subQuery: {} }
        }
        previewState.filterLinkage.subQuery = previewState.filterLinkage.subQuery || {}
        const nextFltsDict = _.groupBy(flts as DataFilterCondition[], flt => flt.col)
        const currFltsDict = previewState.filterLinkage.subQuery[queryKey]
        if (!_.isEqual(nextFltsDict, currFltsDict)) {
          const belongsChartKey = _.findKey(chartSubQueryDict, arr => arr.includes(queryKey))
          condChangedCompKeysSet.add(belongsChartKey)
        }
        previewState.filterLinkage.subQuery[queryKey] = nextFltsDict
      })
      // 输入组件始终联动
      const preDropDataKeys = config.showQueryBtn
        ? evSrc === 'queryBtn'
          ? _.difference(_.keys(chartFltsDict), inputCompKeys)
          : _.intersection(_.keys(chartFltsDict), inputCompKeys)
        : _.keys(chartFltsDict)
      // 清除数据后会自动触发查询
      previewState.chartData.entities = _.mapValues(previewState.chartData.entities, (v, k) =>
        condChangedCompKeysSet.has(k) ? null : v
      )
      previewState.chartData.keys = _.without(previewState.chartData.keys, ...preDropDataKeys)
    })

    return this
  }
}
