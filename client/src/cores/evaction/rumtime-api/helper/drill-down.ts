import { CheckOutlined } from '@ant-design/icons'
import _ from 'lodash'
import { createElement } from 'react'

import showFreeMenu from '@/components/free-menu'
import { ComponentRuntimeAPI } from '@/cores/evaction/rumtime-api/component'
import { Action } from '@/cores/evaction/types/action.d'
import { ColumnInfo } from '@/types/editor-core/data-source'

/** 下钻 helper */
export class DrillDownHelper {
  readonly chartComponent: ComponentRuntimeAPI

  constructor(componentAPI: ComponentRuntimeAPI) {
    this.chartComponent = componentAPI
  }

  /** 获取组件的下钻配置 */
  getDrillConfig() {
    const compAPI = this.chartComponent

    const comp = compAPI.getInfo()
    const actionName = 'initDrillDown'
    const ev = _.findKey(comp.eventAction.entities, acts => _.some(acts, a => a.actionName === actionName))
    const actIdx = ev ? _.findIndex(comp.eventAction.entities[ev], a => a.actionName === actionName) : -1
    if (!ev || actIdx === -1) {
      return null
    }
    const act = comp.eventAction.entities[ev]![actIdx] as Action.InitDrillConfig
    return act.drillConfig
  }

  /** 显示下钻菜单 */
  showDrillDownMenu(x: number, y: number, dimVal: string) {
    const compAPI = this.chartComponent

    // 从事件配置中取得可以下钻的维度
    const comp = compAPI.getInfoWithLinkage()!
    const drillConfig = this.getDrillConfig()
    if (!drillConfig) {
      return
    }

    // 取得当前的下钻状态
    const drillStack: ColumnInfo[] = comp.config.drillStack || [drillConfig.baseCol]

    // 展示选择维度弹窗，已经选了的放上面
    const stackedColsSet = new Set(drillStack.map(c => c.id))
    const menuItems = _.uniqBy([...drillStack, drillConfig.baseCol, ...drillConfig.steps], 'id').map(c => ({
      label: c!.title,
      key: c!.id,
      icon: stackedColsSet.has(c!.id) ? createElement(CheckOutlined) : undefined,
      disabled: c!.id === _.last(drillStack)!.id
    }))

    showFreeMenu(x, y, menuItems, inf => {
      this.switchDrillSteps(dimVal, inf.key)
      compAPI.cleanChartData()
    })
  }

  /** 切换下钻步骤 */
  switchDrillSteps(dimVal: string, colId: string) {
    const compAPI = this.chartComponent
    // 如果 colId 已经存在于 comp.config.drillStack 则回退或不做处理
    const comp = compAPI.getInfoWithLinkage()!
    const drillConfig = this.getDrillConfig()
    if (!drillConfig) {
      throw new Error('没有下钻配置')
    }

    const drillStack: ColumnInfo[] = comp.config.drillStack || [drillConfig.baseCol]
    const idx = _.findIndex(drillStack, c => `${c?.id}` === colId)
    if (idx === 0) {
      // 清除下钻，还原分组维度，清除筛选条件
      compAPI.updateComponentConfigLinkage(config => {
        delete config!.drillStack
      })
      compAPI.updateDataSourceQueryConfig(queryCfg => {
        queryCfg!.fieldsBinding![drillConfig.baseFieldName] = drillConfig.baseCol
      })
      compAPI.updateComponentFilterLinkage(prev =>
        _.mapValues(prev, flts => _.filter(flts, f => f.filterSetId !== 'drillDownFilters'))
      )
      return
    }
    if (idx === -1) {
      // 下钻列，切换分组维度，添加筛选条件
      const targetCol = _.find(drillConfig.steps, c => `${c.id}` === colId)
      if (!targetCol) {
        throw new Error(`没有找到下钻列: ${colId}`)
      }
      compAPI.updateComponentConfigLinkage(config => {
        if (!config) {
          return { drillStack: [...drillStack, targetCol] }
        }
        config.drillStack = [...drillStack, targetCol]
      })
      compAPI.updateDataSourceQueryConfig(queryCfg => {
        queryCfg!.fieldsBinding![drillConfig.baseFieldName] = targetCol
      })
      const lastCol = _.last(drillStack)
      const preAdd = {
        col: lastCol!.name,
        op: 'in',
        eq: lastCol!.dataType === 'date' ? [dimVal, dimVal] : [dimVal],
        type: lastCol!.dataType,
        filterSetId: 'drillDownFilters'
      }
      compAPI.updateComponentFilterLinkage(prev => {
        if (!prev?.[preAdd.col]) {
          return { ...(prev || {}), [preAdd.col]: [preAdd] }
        }
        return _.mapValues(prev, (flts, colName) => (colName === preAdd.col ? [...flts, preAdd] : flts))
      })
      return
    }
    // 回退到上级，切换分组维度，删除部分筛选条件
    const targetCol = drillStack[idx]
    compAPI.updateComponentConfigLinkage(config => {
      config!.drillStack = _.take(drillStack, idx + 1)
    })
    compAPI.updateDataSourceQueryConfig(queryCfg => {
      queryCfg!.fieldsBinding![drillConfig.baseFieldName] = targetCol
    })
    const keepColNamesSet = new Set(_.take(drillStack, idx).map(c => c.name))
    compAPI.updateComponentFilterLinkage(prev =>
      _.mapValues(prev, flts => _.filter(flts, f => f.filterSetId !== 'drillDownFilters' || keepColNamesSet.has(f.col)))
    )
  }
}
