import { EditorCorePreviewRuntimeAPI } from '@/cores/evaction/rumtime-api/preview'
import { EventBinder } from '@/types/editor-core/events'

/**
 * 页面运行时 API
 */
export class PageRuntimeAPI {
  readonly previewRuntimeApi: EditorCorePreviewRuntimeAPI

  constructor(editorCorePreviewRuntimeApi: EditorCorePreviewRuntimeAPI) {
    this.previewRuntimeApi = editorCorePreviewRuntimeApi
  }

  genEventHandler(binder: EventBinder) {
    return this.previewRuntimeApi.genEventHandler('page', binder)
  }
}
