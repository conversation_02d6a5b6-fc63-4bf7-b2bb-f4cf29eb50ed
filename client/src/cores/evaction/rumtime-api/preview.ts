import { notification } from 'antd'
import dayjs from 'dayjs'
import { produce } from 'immer'
import _ from 'lodash'

import { ActionNameImplementFnDict } from '@/cores/evaction/config-resolver'
import { ComponentRuntimeAPI } from '@/cores/evaction/rumtime-api/component'
import { DataSourceRuntimeAPI } from '@/cores/evaction/rumtime-api/data-source'
import FilterRuleComponentRuntimeAPI from '@/cores/evaction/rumtime-api/filter-rule-component'
import { PageRuntimeAPI } from '@/cores/evaction/rumtime-api/page'
import type { State as EditorCorePreviewState } from '@/stores/models/editor-core-preview'
import { DataFilterCondition } from '@/types/editor-core/data-source'
import { EventBinder } from '@/types/editor-core/events'
import { modifyFn } from '@/utils/editor-core/element'
import { tryJsonParse } from '@/utils/json-utils'
import { PubSubVerbose as PubSub } from '@/utils/pubsub'
import { checkDataConfigValid, checkOp } from '@/utils/query'


export const UTILS_FOR_EVENT_HANDLER = { _, dayjs, PubSub }

export type EditorCorePreviewRuntimeAPIProps<T extends EditorCorePreviewState = any> = {
  modelState: T
  stateUpdater: (updater: (curr: T) => T) => void
  verbose?: boolean
  isMobile?: boolean
  getDataSourceRuntimeAPI?: () => DataSourceRuntimeAPI
  reloadChartData?: (componentKey: string | string[]) => any
}

/** 预览状态运行时控制 API */
export class EditorCorePreviewRuntimeAPI<T extends EditorCorePreviewState = any> {
  readonly modelState: T
  readonly updateState: (updater: (curr: T) => T) => void
  readonly verbose: boolean
  readonly isMobile: boolean
  readonly getDataSourceRuntimeAPI: () => DataSourceRuntimeAPI | null
  readonly reloadChartData: (componentKey: string | string[]) => any

  constructor(props: EditorCorePreviewRuntimeAPIProps<T>) {
    this.modelState = props.modelState
    this.updateState = props.stateUpdater
    this.verbose = !!props.verbose
    this.isMobile = !!props.isMobile
    this.getDataSourceRuntimeAPI = props.getDataSourceRuntimeAPI || (() => null)
    this.reloadChartData = props.reloadChartData || (() => undefined)
  }

  updateStateImmer(stateSetter: (stateDraft: EditorCorePreviewState) => any) {
    this.updateState(curr => produce(curr, stateSetter))
  }

  getEventDefineInfo() {
    return this.modelState.eventDefine
  }

  getScreenInfo() {
    return this.modelState.screen
  }

  /** 获取图表数据 */
  getComponentChartData(compKey: string) {
    return this.modelState.chartData.entities[compKey]
  }

  getComponentInfo(key: string) {
    return this.modelState.components.entities[key]
  }

  /** 获取合并 linkage 后的组件信息 */
  getComponentInfoWithLinkage(compKey: string) {
    const comp = this.getComponentInfo(compKey)
    if (!comp) {
      return null
    }
    const patch = modifyFn(this.modelState, compKey, comp)
    return { ...comp, ...patch }
  }

  /**
   * 获取组件定义
   * @param defKey 组件定义 defKey
   */
  getComponentDefine(defKey: string) {
    return this.modelState.componentDefine.entities[defKey]
  }

  /** 检查触发事件的前置条件 */
  checkPrecondition(precondition: DataFilterCondition[] | undefined) {
    if (!precondition) return true

    const colToVal = _.memoize(col => {
      if (!col) return undefined
      if (_.startsWith(col, 'gv_')) {
        return tryJsonParse(localStorage.getItem(col))
      }
      return this.getComponentInfoWithLinkage(col)?.config.value
    })
    return _.every(precondition, cond => {
      const { col, op, type, eq } = cond
      return checkOp(col, colToVal, op, eq, type)
    })
  }

  /** 编译 eventAction，获取 dom 事件处理回调 */
  genEventHandler(compKey, binder: EventBinder) {
    const commonEvDef = this.getEventDefineInfo().events
    // 自定义事件定义
    const customEvDef =
      compKey === 'page' ? null : this.getComponentByKey(compKey).getDefineInfo().eventActionDefine?.eventDefine
    // 实际配置的事件动作
    const eventAction =
      compKey === 'page' ? this.getScreenInfo().eventAction : this.getComponentInfoWithLinkage(compKey)?.eventAction
    // 自定义事件处理函数
    const customEvHandlers =
      compKey === 'page' ? null : this.getComponentByKey(compKey).getDefineInfo().eventActionDefine?.actionHandler

    const eventConfig = _.pickBy(eventAction?.entities, (_v, k) => {
      const evDef = customEvDef?.[k] || commonEvDef[k]
      const b = evDef?.binder // 运行时添加的事件，就不一定存在于 customEvDef 和 commonEvDef
      return !b || b === binder
    })

    const bodyDom = document.body

    const getCompileFn = a => {
      if (a.runtimeHandler) {
        return () => a.runtimeHandler
      }
      return customEvHandlers?.[a.actionName]
        ? // eslint-disable-next-line no-eval
          eval(`(${customEvHandlers?.[a.actionName]})`)
        : ActionNameImplementFnDict[a.actionName]
    }

    return _(eventConfig)
      .mapValues((actions, evName) => {
        const eventPreCond = eventAction?.settings?.[evName]?.precondition
        const fns = _.map(actions, (a, idx) => {
          try {
            const compileFn = getCompileFn(a)
            if (!compileFn) {
              logger.warn(`未找到事件处理行为的实现: ${a.actionName}`)
              return () => notification.error({ message: `未找到事件处理行为的实现: ${a.actionName}` })
            }
            const eventHandlerFn = compileFn(a, this, UTILS_FOR_EVENT_HANDLER)

            return this.wrapLogMiddleware(eventHandlerFn, compKey, evName, a.actionName, idx, a)
          } catch (err) {
            logger.error('事件生成函数错误：', err)
          }
        })
        return async ev => {
          let crashAt = -1
          try {
            // 检查前置条件
            if (_.some(eventPreCond) && !this.checkPrecondition(eventPreCond)) {
              logger.log(`事件 ${evName} 的前置条件不满足，不执行`, eventPreCond)
              return
            }
            for (let i = 0; i < fns.length; i++) {
              crashAt = i
              const actPreCond = actions![i].precondition
              if (_.some(actPreCond) && !this.checkPrecondition(actPreCond)) {
                logger.log(`事件 ${evName} 的第 ${i + 1} 个动作的前置条件不满足，不执行`, actPreCond)
                continue
              }
              // eslint-disable-next-line no-await-in-loop
              const res = await fns[i](ev, compKey)
              // 返回最后一个事件的执行结果
              if (i === fns.length - 1) {
                return res
              }
            }
          } catch (e) {
            const name = `${evName}.${actions?.[crashAt].actionName}`
            logger.groupCollapsed(
              `%c[Evaction: ${name}] %ccomponentKey=${compKey}（${crashAt + 1}）执行失败`,
              'color: #f45',
              'color: #333'
            )
            logger.error(e)
            logger.groupEnd()

            const msg = `${evName} 触发了 ${compKey} 组件执行第 ${crashAt + 1} 个动作 ${
              actions?.[crashAt].actionName
            } 失败：${(e as Error).message}`
            notification.error({ message: msg })
          }
        }
      })
      .mapKeys((_v, eventName) => {
        // dom 事件转换成 react 的绑定形式，click -> onClick
        if (binder === 'dom') {
          return `on${eventName.replace(/^[a-z]/, s => s.toUpperCase())}`
        }
        return eventName
      })
      .pickBy((_v, evName) => {
        if (binder === 'dom') {
          // 判断事件名称是否合法，doubleClick 这个需要特殊处理
          return _.toLower(evName) === 'ondoubleclick' || bodyDom[_.toLower(evName)] !== undefined
        }
        return true
      })
      .value()
  }

  wrapLogMiddleware(logicFn, componentKey, eventName, actionName, actionIdx, ctx) {
    if (!this.verbose) return logicFn
    return (...args) => {
      logger.groupCollapsed(
        `%c[Evaction: %c${eventName}%c.${actionName}%c] key=${componentKey} (+${actionIdx + 1})`,
        'color: #333',
        'color: #38f',
        'color: #78f',
        'color: #333'
      )
      logger.log(ctx)
      logger.groupEnd()
      return logicFn(...args)
    }
  }

  /** 根据组件实例 key 获取组件运行时 API 封装 */
  getComponentByKey(key: string): ComponentRuntimeAPI {
    if (_.startsWith(key, 'component-filter-rule@')) {
      return new FilterRuleComponentRuntimeAPI(this, key)
    }
    return new ComponentRuntimeAPI(this, key)
  }

  /** 获取页面运行时 API 封装 */
  getScreen() {
    return new PageRuntimeAPI(this)
  }

  /** 获取所有查询了数据的组件 */
  getComponentInfosWithDataQuery() {
    const defDict = this.modelState.componentDefine.entities
    return _.filter(this.modelState.components.entities, comp =>
      checkDataConfigValid({ ...comp, define: defDict[comp.defineKey] })
    )
  }

  /** 获取所有组件 */
  getAllComponent() {
    return this.modelState.components.entities
  }

  /** 获取所有有打印预览的组件事件 */
  getPrintPreviewEvent(type: 'enter' | 'exit') {
    const { components } = this.modelState
    const filterKey = type === 'enter' ? 'printPreviewEnter' : 'printPreviewExit'
    const list = Object.values(components.entities).filter(v => !_.isEmpty(v.eventAction.entities?.[filterKey]))
    const pageEvent = this.genEventHandler('page', 'unknown')
    const eventHandlerList = list.map(i => this.genEventHandler(i.key, 'unknown'))

    return pageEvent[filterKey] ? [...eventHandlerList, pageEvent] : eventHandlerList
  }

  /** 判断是否是快照页面 */
  isSnapshotPage() {
    return _.includes(window.location.search, 'arhType=snapshotable')
  }

  /** 判断是否处于打印预览模式 */
  isPrintPreview() {
    return this.modelState.print.enable
  }
}
