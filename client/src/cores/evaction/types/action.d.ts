import { EditorCorePreviewRuntimeAPI } from '@/cores/evaction/rumtime-api/preview'
import type { Component, ComponentKey } from '@/types/editor-core/component'
import type { ActionConfig } from '@/types/editor-core/events'
import { DrillConfig } from '@/types/editor-core/data-source'

/**
 * 动作函数
 */
export type ActionFn<T> = (ctx: T & { rawKey: string }, runtime: EditorCorePreviewRuntimeAPI) => void | ((e?: Event) => any) | undefined

/** 动态变量 */
export type VariablesOption = {
  /** 传参的key值 */
  queryKey: string
  /** 输入组件的key */
  componentKey: string
}

/** 打开方式 */
export type OpenWay = 'self' | 'blank' | 'popWindow' | 'parent'

/**
 * 弹出窗口配置
 * @property left 距离屏幕左侧大小
 * @property top 距离屏幕顶部大小
 * @property width 打开页面宽
 * @property height 打开页面高
 * @property center 页面是否居中
 */
export interface PopWindowParams {
  left: number
  top: number
  width: number
  height: number
  center: boolean
}

export namespace Action {
  /** 触发其他事件 */
  export interface TriggerEvent extends ActionConfig {
    componentKey: ComponentKey // 目标组件
    eventName: string // 目标事件
  }

  /** 触发其他动作 */
  export interface TriggerAction extends ActionConfig {
    componentKey: ComponentKey // 目标组件
    params: ActionConfig // 动作配置
  }

  /** 隐藏显示组件 */
  export interface SetVisibility extends ActionConfig {
    componentKey: ComponentKey | ComponentKey[] // 目标组件
    visibility: '1' | '0' | '-1' | 1 | 0 | -1 // 1: 可见, 0: 不可见, -1: 反转可见性
  }

  export interface TimedRefresh extends ActionConfig {
    componentKeys: string[] // ['*'] 这种是全部刷新
    interval: number // 不能低于 1 秒
    unit: 's' | 'm' | 'h' | (string & {}) // 秒，分，小时
    repeat: number // 99999 永久
  }

  /**
   * 打开页面配置信息定义
   * @property {String} pageUrl 内部页面
   * @property {String[]} params 参数
   * @property {String} externalLink 外部链接
   * @property {'self' | 'blank' | 'popWindow' | 'parent'} openWay 外部链接
   */
  export interface OpenUrlActionConfig extends ActionConfig {
    /** 内部链接地址 */
    pageUrl: string
    /** 参数 */
    params: string[]
    /** 外部链接地址 */
    externalLink: string
    /** 动态变量类型 */
    variables: VariablesOption[]
    /** 打开方式 */
    openWay: OpenWay
    /** 点击之后的操作 */
    openAfter?: 'default' | 'closePage'
    /** 弹出窗口配置 */
    popWindowParams: PopWindowParams

    /** 需要打开的指标功能页 */
    indicesFunction?: 'factor-analysis' | 'monitor-alarms'
    /** 目标指标 */
    targetMetric?: string
  }

  /**
   * 滚动条滚动到某组件位置
   */
  export interface GotoComponent extends ActionConfig {
    componentKey: ComponentKey // 目标组件
    offset?: string | number
  }

  /**
   * 页面打印
   */
  export interface PagePrint extends ActionConfig {
    componentKey: ComponentKey // 目标组件
    size: 'A4' | (string & {})
    direction: 'landscape' | 'portrait'
    pagination: 'auto' | 'default' | 'ai' // 自动，手动，智能
    paginateText?: string
    cursors: (number | string)[] // 手动分页时，切割数组，单位是 px
  }

  /** 外部过滤 */
  export interface ExternalFilter extends ActionConfig {
    componentKey: ComponentKey // 目标组件
    filter: any
  }

  /**
   * 修改组件的 config
   */
  export interface ChangeComponentConfig extends ActionConfig {
    componentKey: ComponentKey // 目标组件
    config: Omit<Component['config'], 'i18n' | 'filterRule' | 'lock' | 'visible'> // 目标值（合并对象）
  }

  /**
   * 修改组件的 style
   */
  export interface ChangeComponentStyle extends ActionConfig {
    componentKey: ComponentKey // 目标组件
    style: Component['style'] // 目标值（合并对象）
  }

  /**
   * 修改组件的 大小，位置，圆角等
   */
  export interface ChangeComponentSize extends ActionConfig {
    componentKey: ComponentKey // 目标组件
    style: Pick<Component['style'], 'width' | 'height' | 'transform' | 'borderRadius' | 'transition'> // 目标值（合并对象）
    enableMap?: Partial<Record<'width' | 'height' | 'top' | 'left', boolean>>
  }

  /**
   * 修改组件的 dataSource
   */
  export interface ChangeComponentDataSource extends ActionConfig {
    componentKey: ComponentKey // 目标组件
    dataSource: Component['dataSource'] // 目标值（合并对象）
  }

  /** 初始化组件的下钻响应 */
  export interface InitDrillConfig extends ActionConfig {
    drillConfig: DrillConfig
  }

  /** 复制配置动作参数 */
  export interface CopyConfig extends ActionConfig {
    fromComponentKey: ComponentKey
    targetComponentKeys: ComponentKey[] // 目标组件
    processFn: string // 配置复制函数
  }
}
