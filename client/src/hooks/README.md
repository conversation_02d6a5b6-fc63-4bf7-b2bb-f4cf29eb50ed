## 自定义 Hooks 函数

公共的可复用 hooks 逻辑抽象。

- `use-form-fields-adapter`：将 value 和 onChange 转换为 form 的 fields 和 onFieldsChange 的形式。
- `use-master-jwt`：读取主前端传给子应用的 jwt，设置到 Parse 公共 header，以便调用云函数时使用。
- `use-mobile-adapter`：移动端页面适配的钩子。
- `use-oAuth2-login`：参考隐藏式 oauth2 设计。
- `use-page-scale`：控制页面的缩放。
- `use-query-params`：获取 url 查询参数。
- `use-user`：获取用户信息。
- `use-vertical-list-dnd`：垂直列表拖动排序工具。
- `hotkey/use-ctrl-wheel`：监听按 ctrl + 滚动时的事件，可模拟缩放能力。
- `hotkey/use-shift-f11`：按 shift + f11 时全屏某个元素。
- `hotkey/use-shift-key-press`：按 shift 键 + 上下左右键，触发。
- `editor-core/canvas-hook`：获取画布的统一逻辑 hooks。
- `editor-core/preview-hook`：获取预览时画布的统一逻辑 hooks。
