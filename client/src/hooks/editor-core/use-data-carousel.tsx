import { useCreation, useDeepCompareEffect } from 'ahooks'
import _ from 'lodash'
import React, { useCallback, useEffect, useRef, useState } from 'react' // 导入 useEffect

import type { CarouselSetting } from '@/types/editor-core/data-source'

interface Params {
  // 配置useCallback
  carousel?: CarouselSetting;
  // 原始数据
  rawData: any[];
  // 主题颜色
  theme?: string[];
}

/**
 * 数据切片播放功能
 * @returns
 */
export default function useDataCarousel({ carousel, rawData, theme }: Params) {
  const [timerIndex, setTimerIndex] = useState(0)
  const hasDataCarousel = !!carousel?.enable
  // 计算最大索引，确保至少为 1，以避免除以零或 _.ceil(0) 带来的问题
  const maxIndex = Math.max(1, _.ceil(_.size(rawData) / (carousel?.limit || 3)))
  const isEnable = carousel?.enable && maxIndex > 1 // 只有数据多于一页时才启用轮播
  const interval = _.get(carousel, 'interval', 0)

  const data = useCreation(() => {
    // 切分数据
    if (isEnable) {
      const limit = carousel?.limit || 3
      return _.slice(rawData, timerIndex * limit, (timerIndex + 1) * limit)
    }
    return rawData
  }, [rawData, timerIndex, carousel, isEnable])

  // 使用 ref 来保存定时器 ID
  const intervalIdRef = useRef<number | null>(null)

  // 停止轮播逻辑
  const stopCarousel = useCallback(() => {
    if (intervalIdRef.current !== null) {
      window.clearInterval(intervalIdRef.current)
      intervalIdRef.current = null // 清除后重置 ref
    }
  }, [])

  // 启动轮播逻辑
  const startCarousel = () => {
    // 在启动新定时器之前，先清除任何现有的定时器
    stopCarousel()
    if (isEnable && interval > 0) {
      intervalIdRef.current = window.setInterval(() => {
        setTimerIndex(t => {
          if (t >= maxIndex - 1) return 0 // 转到最后，回归 0
          return t + 1
        })
      }, interval * 1000)
    }
  }

  // **新的 Effect**：专门管理轮播定时器
  // 当 isEnable, interval, maxIndex 任何一个改变时，此 effect 都会重新运行
  useEffect(() => {
    startCarousel() // 启动定时器
    return () => {
      stopCarousel() // 在 effect 清理时停止定时器
    }
  }, [isEnable, interval, maxIndex]) // 定时器逻辑的依赖项

  useEffect(() => () => {
    stopCarousel()
  }, [])

  // 此 useDeepCompareEffect 确保当 carousel 或 rawData 深度变化时，
  // 我们会重置 timerIndex。
  // 它有效地充当了一个重置机制。
  useDeepCompareEffect(() => {
    setTimerIndex(0)
    // 上面的 useEffect 会根据依赖项处理定时器的启动/停止
  }, [carousel, rawData])

  const onClick = (index: number) => (e: any) => {
    e.stopPropagation()
    stopCarousel() // 手动点击时停止
    setTimerIndex(index)
    startCarousel() // 如果需要，点击后重新启动
  }

  // 切片轮播幻灯片
  const carouselControl = hasDataCarousel ? (
    <div className='carousel-control'>
      {Array.from({ length: maxIndex }).map((_v, index) => (
        <div
          key={index}
          className='carousel-control-dot'
          style={index === timerIndex ? { backgroundColor: _.get(theme, '[0]', '#39f') } : {}}
          onClick={onClick(index)}
        />
      ))}
    </div>
  ) : null

  // 鼠标移动进去时，停止轮播
  const handleMouseEvent = isEnable
    ? {
      onMouseEnter: () => stopCarousel(),
      onMouseLeave: () => startCarousel()
    }
    : {}

  return { data, carouselControl, hasDataCarousel, handleMouseEvent }
}
