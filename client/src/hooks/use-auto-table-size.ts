import { useDebounceEffect, useSize } from 'ahooks'
import _ from 'lodash'
import React, { useState } from 'react'
/**
 * 自定义 Hook 用于动态设置表格的滚动属性。
 *
 * @param tableContainerRef - 表格容器的 React Ref 对象。
 * @param options - 配置选项，包括默认高度和水平滚动设置。
 * @returns 返回一个对象，其中包含滚动设置和表格容器距视窗顶部的距离。
 */

function useDynamicTableScrolling(
  tableContainerRef: React.RefObject<HTMLDivElement>,
  options?: {
    standardDefaultHeight?: number
    defaultHeight?: number
    defaultXScroll?: string | number
  }
) {
  const { standardDefaultHeight = 12, defaultHeight = 800, defaultXScroll = 'max-content' } = options || {}

  // 存储表格容器距视窗顶部的距离
  const [containerHeight, setContainerHeight] = useState<number>(0)
  // 存储表格的滚动设置
  const [scroll, setScroll] = useState({ y: defaultHeight, x: defaultXScroll })
  // 获取表格容器的尺寸
  const tableContainerSize = useSize(tableContainerRef)
  // 计算元素的外边距+高度的和
  const elementHeightCount = htmlElement =>
    htmlElement.offsetHeight +
    _.parseInt(getComputedStyle(htmlElement).marginTop) +
    _.parseInt(getComputedStyle(htmlElement).marginBottom)

  useDebounceEffect(
    () => {
      // 当表格容器的尺寸变化时，重新计算滚动设置
      if (!(tableContainerRef.current && tableContainerSize)) return
      // 获取表格容器的高度
      const windowHeight = window.innerHeight
      const vhValue = windowHeight * 0.01 // 1vh等于视窗高度的1%

      // 计算100vh对应的像素值
      const valueInPixels = 100 * vhValue
      // 计算表头的高度
      let antdTheadTableHeight = 0
      const antdTheadTable = (tableContainerRef.current.querySelector('.ant-table-thead') as HTMLElement) || null

      if (antdTheadTable) antdTheadTableHeight = elementHeightCount(antdTheadTable)

      // 获取要计算高度的元素
      const antdPaginationTable =
        (tableContainerRef.current.querySelector('.ant-table-pagination') as HTMLElement) || null
      let antdPaginationTableHeight = 64

      // 计算包括外边距在内的元素高度
      if (antdPaginationTable) antdPaginationTableHeight = elementHeightCount(antdPaginationTable)

      // 获取表格容器距视窗顶部的距离
      const topHeight = tableContainerRef.current.getBoundingClientRect().top
      setContainerHeight(valueInPixels - topHeight)

      // 计算表格应有的高度
      const antdTableYheight =
        valueInPixels - topHeight - antdPaginationTableHeight - antdTheadTableHeight - standardDefaultHeight
      // 更新滚动设置
      setScroll({ y: antdTableYheight, x: defaultXScroll })
    },
    [tableContainerSize, defaultHeight, defaultXScroll, tableContainerRef],
    { wait: 300 }
  )
  // 返回滚动设置和表格容器距视窗顶部的距离
  return { scroll, containerHeight }
}

export default useDynamicTableScrolling
