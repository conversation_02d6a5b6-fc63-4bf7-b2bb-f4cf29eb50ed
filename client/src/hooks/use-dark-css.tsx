import _ from 'lodash'
import { useMemo } from 'react'

/**
 * 找出暗黑主题的文件
 * @returns
 */
export const useInjectCss = () => useMemo(() => {
  try {
    let styleStr = ''
    // 尝试找节点，从 styleSheet 内容里面找
    const styleSheets = Array.from(document.styleSheets).filter(s =>
      _.find(s.cssRules, r => String(r.cssText).indexOf('html[data-theme="dark"]') > -1)
    )
    if (styleSheets.length > 0) {
      styleStr = _.map(styleSheets, ss => _.map(ss.cssRules, r => String(r.cssText)).join('\n')).join('\n')
    }
    return styleStr
  } catch (err) {
    console.error(err)
    return ''
  }
}, [])
