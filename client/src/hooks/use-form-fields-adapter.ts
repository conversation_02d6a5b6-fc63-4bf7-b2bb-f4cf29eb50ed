import _ from 'lodash'
import { FieldData } from 'rc-field-form/lib/interface'

/**
 * 将 value 和 onChange 转换为 form 的 fields 和 onFieldsChange 的形式
 * @param value
 * @param onChange
 * @param l2PropNames 需要修改的深层属性名，目前只支持到第二层
 */
export function useFormFieldsAdapter<T extends {}>(
  value: T | undefined | null,
  onChange: (next: T) => any,
  ...l2PropNames: string[]
) {
  const fields: FieldData[] = _.flatMap(value, (v, k) => {
    if (_.includes(l2PropNames, k)) {
      return _.map(v as Record<string, any>, (v0, k0) => ({ name: [k, k0], value: v0 })) as any[]
    }
    return { name: [k], value: v } as FieldData
  })

  // 表单变更回传
  const onFieldsChange = (__, allFields) => {
    function toObj(acc, curr) {
      if (_.isArray(curr.name) && _.size(curr.name) > 2) {
        // 忽略
        return acc
      }
      // 只考虑了 2 层
      return _.isArray(curr.name) && _.size(curr.name) === 2
        ? { ...acc, [curr.name[0]]: { ...acc[curr.name[0]], [curr.name[1]]: curr.value } }
        : { ...acc, [curr.name]: curr.value }
    }

    const next = _.reduce(allFields, toObj, value ? { ...value } : {})
    onChange(next)
  }
  return { fields, onFieldsChange }
}
