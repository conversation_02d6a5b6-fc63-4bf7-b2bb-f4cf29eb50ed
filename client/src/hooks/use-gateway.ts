import { useRequest } from 'ahooks'
import request from 'umi-request'

import { LZ } from '@/utils/json-utils'

/** 使用网关（一般是主前端）根据 cookie 生成的 jwt */
export function useGatewayJwt(opts?: { disabled?: boolean; onError?: (err: Error) => any }) {
  // 编辑页没有包含在主应用（乾坤）中，不能从 @@qiankunStateFromMaster 中读取 jwt
  // 如果主应用被其他主应用代理（如运营资源），可考虑配置转发 /app/(.*) 并修改 auth header，参考:
  // '/app/(.*)': {
  //   target: 'http://192.168.0.224:8000',
  //   context(pathname, req) {
  //     const headers = req.headers
  //     const referer = headers.referer || headers.Referer || ''
  //     return headers['x-master-jwt'] || /\/preview|\/console\/abi\/project/i.test(referer)
  //   },
  //   onProxyReq(proxyReq, req, res) {
  //     const jwt = '...'
  //     proxyReq.setHeader('Authorization', `Bearer ${jwt}`)
  //     proxyReq.setHeader('x-master-jwt', jwt)
  //   }
  // },
  return useRequest(
    async () => {
      const info = await request.post('/app/request-jwt-sign', {
        headers: {
          // 不能带 jwt，需要使用 cookie 的用户
          authorization: '',
          'x-master-jwt': ''
        },
        data: {
          q: LZ.stringify({ apiScopes: ['*'], pathScopes: ['*'], expiresIn: '7d',setCookie: true })
        }
      })
      return info?.token
    },
    {
      cacheKey: 'gateway-jwt',
      ready: !opts?.disabled,
      onError: opts?.onError
    }
  )
}
