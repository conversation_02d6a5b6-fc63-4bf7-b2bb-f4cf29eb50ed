import { useEventListener, useFullscreen, useKeyPress, useMemoizedFn } from 'ahooks'
import { useEffect, useRef } from 'react'

/**
 * 监听按 ctrl + 滚动时的事件
 * 可模拟缩放能力
 */
export function useCtrlWheel(
  elementSelecor: string | HTMLElement | SVGElement | null,
  callback?: (e: Event, direction: 'up' | 'down') => any
) {
  const action = useMemoizedFn((e, d) => callback?.(e, d))

  useEffect(() => {
    const dom = typeof elementSelecor === 'string' ? document.querySelector(elementSelecor) : elementSelecor
    if (!dom) return

    const onMousewheel = e => {
      if (e.ctrlKey) {
        e.preventDefault()
        e.stopPropagation()
        action(e, e.wheelDelta > 0 ? 'up' : 'down')
      }
    }
    dom.addEventListener('mousewheel', onMousewheel, { passive: false, capture: true })
    return () => {
      dom.removeEventListener('mousewheel', onMousewheel)
    }
  }, [elementSelecor, action])
}

/**
 * 按 shift + f11 时全屏某个元素
 */
export function useShiftF11(
  elementSelecor: string | HTMLElement | SVGElement | Document | null,
  callback?: (e: Event, isFullscreen: boolean) => any
) {
  const domRef = useRef<HTMLElement | null>(null)

  useEffect(() => {
    const dom: any = typeof elementSelecor === 'string' ? document.querySelector(elementSelecor) : elementSelecor
    domRef.current = dom
  }, [])

  const [isFullscreen, { enterFullscreen, exitFullscreen }] = useFullscreen(domRef)

  useKeyPress(['shift.f11'], e => {
    if (!domRef.current) return
    if (isFullscreen) {
      exitFullscreen()
      callback?.(e, false)
      return
    }
    enterFullscreen()
    callback?.(e, true)
  })
}

/**
 * 按 shift 键 + 上下左右键，触发
 * 包括连续的长按
 */
export function useShiftKeyPress(
  elementSelecor: string | HTMLElement | SVGElement | Document | null,
  callback?: (e: Event, direction: 'up' | 'down' | 'left' | 'right') => any
) {
  const dom = typeof elementSelecor === 'string' ? document.querySelector(elementSelecor) : elementSelecor

  useEventListener(
    'keydown',
    e => {
      if (!dom) return
      if (e.shiftKey) {
        const map = {
          ArrowLeft: 'left',
          ArrowRight: 'right',
          ArrowUp: 'up',
          ArrowDown: 'down'
        }
        const direct = map[e.code]
        if (direct) callback?.(e, direct)
      }
    },
    { target: dom }
  )
}
