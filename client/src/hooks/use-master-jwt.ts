import { useModel } from '@umijs/max'
import { useRequest } from 'ahooks'
import { useEffect } from 'react'

import { useGatewayJwt } from '@/hooks/use-gateway'
import { useHashParams } from '@/hooks/use-query-params'
import { decodeJwt, validateJwt } from '@/utils/jwt'
import { getAppMeta, getJwt } from '@/utils/query'

import { useOAuth2Login } from './use-oAuth2-login'


/** 是否跳过 oauth，使用为开发者预设的 jwt */
const skipOAuthForDev = false // window.isDev

const MASTER_JWT = window.initialState?.masterJwt

/** 读取主前端传给子应用的 jwt，设置到 Parse 公共 header，以便调用云函数时使用 */
export function useMasterJwt() {
  // https://juejin.cn/post/6875605462025240590
  const masterProps = useModel('@@qiankunStateFromMaster' as any)
  // console.log('@@qiankunStateFromMaster:', masterProps)
  const jwtFromMaster = masterProps?.jwt

  const hashParams = useHashParams({ cleanAfterRead: true })

  // 开发模式下读取配置的 jwt，不走单点
  const { data: cfgForDev, loading: loadingDevJwt } = useRequest(getAppMeta, {
    cacheKey: 'app-meta',
    ready: skipOAuthForDev && !(jwtFromMaster || getJwt()) // 已经有了就不用查了
  })
  const devJwt = cfgForDev?.mainAppJWT || cfgForDev?.dataSourceSystemJWT

  // 编辑页没有包含在主应用（乾坤）中，不能从 @@qiankunStateFromMaster 中读取 jwt
  const { data: gatewayJwt, loading: loadingGatewayJwt } = useGatewayJwt({
    disabled: jwtFromMaster || hashParams?.jwt, // 有 sessionStorage.masterJWT 依然要查，因为它有可能是其他服务的 jwt（如运营资源）
    onError: () => {
      // 如果报错，表示没有 cookie，强制使用 MASTER_JWT
      if (MASTER_JWT) {
        sessionStorage.setItem('masterJWT', MASTER_JWT)
      }
    }
  })

  const jwt = hashParams?.jwt || jwtFromMaster || gatewayJwt || getJwt() || devJwt

  useOAuth2Login({ disabled: loadingDevJwt || loadingGatewayJwt || !!jwt })
  useEffect(() => {
    // 保存到 sessionStorage，使刷新后仍保持登陆
    if (!jwt) {
      return
    }
    const savedJwt = getJwt('')
    // 如果 jwt 与 savedJwt 的 user_id 不同或者超时再更新，否则不更新，避免频繁更新
    if (!validateJwt(savedJwt) || decodeJwt(jwt)?.payload?.userId !== decodeJwt(savedJwt)?.payload?.userId) {
      sessionStorage.setItem('masterJWT', validateJwt(jwt) ? jwt : MASTER_JWT)
    }
  }, [jwt])

  return jwt
}
