import isMobile from 'is-mobile'
import _ from 'lodash'
import { CSSProperties,useEffect } from 'react'

/**
 * 移动端适配
 * https://zhuanlan.zhihu.com/p/339303865
 *
 * 等宽算法
 *
 * @param style
 */
const mobileAdapter = (style: CSSProperties, initScale?: number) => {
  if (style.width === undefined) return
  const WIDTH = Number.parseInt(style.width.toString(), 10)
  const scale = _.floor(initScale || window.screen.width / WIDTH, 2)
  const content = `width=${WIDTH}, initial-scale=${scale}, maximum-scale=${scale}, minimum-scale=${scale}, user-scalable=no`
  let meta = document.querySelector('meta[name=viewport]')
  if (!meta) {
    meta = document.createElement('meta')
    meta.setAttribute('name', 'viewport')
    document.head.appendChild(meta)
  }
  meta.setAttribute('content', content)
}

/**
 * 移动端适配
 */
export function useMobileAdapter(style: CSSProperties) {
  useEffect(() => {
    if (!isMobile()) return
    const resizeObserver = new ResizeObserver(() => {
      mobileAdapter(style || {})
    })
    resizeObserver.observe(document.body)
    return () => {
      resizeObserver.unobserve(document.body)
    }
  }, [style])
}
