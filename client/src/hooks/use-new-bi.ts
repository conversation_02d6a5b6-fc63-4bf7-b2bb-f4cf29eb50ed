// import { useEffect } from 'react'

export const getNewBi = () => {
  try {
    const isNewBi = window.wujieProps?.isNewBi || window.location.href.indexOf('isNewBi') > -1
    return {
      ...window.wujieProps,
      isNewBi,
      markNewBi: isNewBi ? 'isNewBi' : ''
    }
  } catch (err) {
    return { isNewBi: false, markNewBi: '' }
  }
}

export const useNewBi = () => {
  const obj = getNewBi()
  // const isNewBi = obj.isNewBi

  return obj
}

