/* eslint-disable no-restricted-globals */
import { useRequest } from 'ahooks'
import _ from 'lodash'

import { getAppMeta } from '@/utils/query'

// 手动调用
export const oAuth2 = async (injectConfig?: any) => {
  const config = injectConfig || (await getAppMeta())
  let cfgOrigin = config?.mainAppOrigin || config?.dataSourceSystemOrigin // 一般是主应用域名
  cfgOrigin = _.replace(cfgOrigin, /\/$/, '')

  const { origin, pathname, search } = window.location
  const currUri = `${origin}${pathname}${search}`

  const searchP = new URLSearchParams(String(search).replace(/^\?/, ''))

  const ssoTokenKey = config?.ssoTokenKey || 'snapTicket'
  const snapTicket = searchP.get(ssoTokenKey) || undefined

  const newSearch = new URLSearchParams('')
  newSearch.set('responseType', 'jwt')
  newSearch.set('clientId', 'abi')
  newSearch.set('redirectUri', currUri)
  if (snapTicket !== undefined) {
    newSearch.set('snapTicket', String(snapTicket))
  }

  if ((cfgOrigin === 'https://abi.sugoio.com' || cfgOrigin === 'http://abi.sugoio.com') && origin !== cfgOrigin) {
    cfgOrigin = origin
  }

  window.localStorage.setItem('oAuthAt', `${Date.now() / 1000}`)
  console.log('转跳到：', `${cfgOrigin || origin}?${newSearch.toString()}`)
  const href = `${cfgOrigin || origin}?${newSearch.toString()}`
  window.locationTo(href)
}

/**
 * 参考隐藏式 oauth2 设计
 * https://www.ruanyifeng.com/blog/2019/04/oauth-grant-types.html
 */
export function useOAuth2Login(opts) {
  const { disabled } = opts
  useRequest(getAppMeta, {
    cacheKey: 'app-meta',
    ready: !disabled,
    onSuccess: oAuth2
  })
}


// 挂到全局方便调用
window.checkOAuth2 = async (functionName: string) => {
  if (functionName === 'getAppMeta') return // 不然会死循环

  if (!window.sessionStorage.getItem('masterJWT')) {
    await oAuth2()
  }
}
