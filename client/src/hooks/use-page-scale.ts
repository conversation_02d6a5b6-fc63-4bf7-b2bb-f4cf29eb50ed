import _ from 'lodash'
import { useEffect } from 'react'
import ResizeObserver from 'resize-observer-polyfill'

/**
 * 控制页面的缩放
 * @param targetSelector 某元素
 */
export function usePageScale(enable: boolean, targetSelector?: string | HTMLElement) {
  useEffect(() => {
    if (!enable) return
    const el = (_.isString(targetSelector) ? document.querySelector(targetSelector) : targetSelector) as HTMLElement

    if (!el) {
      console.error('找不到需要缩放的元素')
      return
    }

    const setZoom = e => {
      const conatinerWidth = e[0].contentRect.width
      const targetWidth = el.clientWidth
      const zoom = _.round(conatinerWidth / targetWidth - 0.01, 8)
      if (_.isNumber(zoom)) {
        // ;(el.style as any).zoom = zoom
        el.style.transform = `scale(${zoom})`
        el.style.transformOrigin = 'top left'
        el.setAttribute('data-zoom', zoom.toString())
      }
    }

    const observer = new ResizeObserver(e => {
      try {
        requestAnimationFrame(() => setZoom(e))
      } catch (err) {
        console.error(err)
      }
    })
    observer.observe(document.body)
    return () => {
      observer.unobserve(document.body)
    }
  }, [targetSelector, enable])
}
