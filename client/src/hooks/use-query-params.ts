import { parse } from 'querystring'

const cache: Record<string, any> = {}
const hashCache: Record<string, any> = {}

/** 获取 url query 参数 */
export function useQueryParams() {
  const search = window.location.search.replace(/^\?/, '')
  if (cache[search]) return cache[search]

  const obj = parse(search) || {}
  cache[search] = obj
  return obj as Record<string, any>
}

/** 清除 hash */
function removeHash() {
  // https://stackoverflow.com/a/5298684/1745885
  window.history.replaceState('', document.title, window.location.pathname + window.location.search)
}

/** 解析 hash 里的参数 */
export function useHashParams(opts?: { cleanAfterRead: boolean }) {
  const search = window.location.hash.replace(/^#/, '')
  if (opts?.cleanAfterRead && search) removeHash()

  if (hashCache[search]) return hashCache[search]

  const obj = parse(search) || {}
  hashCache[search] = obj

  return obj as Record<string, any>
}

function getTopMostWindow() {
  let topMostWindow: Window = window
  while (topMostWindow) {
    try {
      // 如果同源，这将成功
      if (topMostWindow.parent.document) {
        if (topMostWindow.parent === topMostWindow) {
          break
        }
        topMostWindow = topMostWindow.parent
      }
    } catch (e) {
      // 如果跨域，我们会捕获到错误，然后跳出循环
      break
    }
  }
  return topMostWindow
}

/** 获取 最顶层的url query 参数 */
export function useTopQueryParams() {
  const search = getTopMostWindow()?.location.search.replace(/^\?/, '')
  if (!search) return {}
  if (cache[search]) return cache[search]

  const obj = parse(search) || {}
  cache[search] = obj
  return obj as Record<string, any>
}
