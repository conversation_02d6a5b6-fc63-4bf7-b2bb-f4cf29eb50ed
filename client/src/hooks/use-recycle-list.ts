import dayjs from 'dayjs'
import { useState } from 'react'

type Opt = {
  /** 恢复后触发 */
  onRestored?: (id: string) => any

  /** 云服务的 model，例如：Cloud.Demo */
  CloudModel: any
  groupModelName: string
}

/**
 * 实现回收站的 hooks
 */
export const useRecycleList = (opt: Opt) => {
  const { CloudModel, onRestored, groupModelName } = opt || {}

  const [state, setState] = useState({
    recycleList: [] as { id: string, title: string, deletedAt: string }[],
    recycleListLoading: false
  })

  const update = data => setState(s => ({ ...s, ...data }))

  // 只能有权限的
  const loadRecycleList = async () => {
    try {
      update({ recycleListLoading: true })
      const list = await CloudModel.findAll({
        attributes: ['id', 'title', 'deletedAt', 'groupId', 'deletedBy', 'createdBy'],
        paranoid: false,
        order: [['deletedAt', 'DESC']],
        usePermission: 'new',
        where: { deletedAt: { $gte: dayjs().add(-90, 'day').toDate() } },
        relations: [{
          tableName: 'SugoUser',
          fieldMapping: {
            createdUser: {
              sourceName: 'createdBy',
              attributes: ['id', 'first_name']
            },
            deletedUser: {
              sourceName: 'deletedBy',
              attributes: ['id', 'first_name']
            }
          }
        }, {
          tableName: `#${groupModelName}`,
          fieldMapping: {
            group: {
              sourceName: 'groupId',
              attributes: ['id', 'title']
            }
          }
        }]
      })
      update({ recycleList: list })
    } finally {
      update({ recycleListLoading: false })
    }
  }

  const restoreItem = async (id: string) => {
    await CloudModel.update({ deletedAt: null }, {
      where: { id },
      paranoid: false
    })
    await loadRecycleList()
    await onRestored?.(id)
  }

  return {
    loadRecycleList,
    restoreItem,
    state
  }
}
