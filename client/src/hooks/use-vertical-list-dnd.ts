import { useReactive } from 'ahooks'
import React from 'react'

type Status = 'upper' | 'lower' | 'current'
interface UseVerticalListDndParams {
  payloadStoreAt?: string
  // 避免多个拖拽功能冲突，支持根据 transferFormat 判断是否响应 dragOver 和 drop
  transferFormat?: string
  detectHover?: (offsetY: number, offsetYByStart: number, fromPayload: string) => Status | null
  onDragEnd?: (from: string, to: string, pos: Status) => any
}

// https://stackoverflow.com/a/28487486/1745885
// 因为安全原因 onDragOver 不能获取 payload，所以临时存放在这
let draggingPayload: string | null = null
let draggingStartRect: DOMRect | null = null

/** 垂直列表拖动排序工具 */
export function useVerticalListDnd(options: UseVerticalListDndParams) {
  const reactiveState = useReactive<{ hover: null | Status }>({
    hover: null
  })
  const dragPayloadStoreAtKey = options.payloadStoreAt || 'data-drag-payload'
  const transferFormat = options.transferFormat || 'text/plain'

  /** 开始拖拽回调 */
  function onDragStart(ev: React.DragEvent<HTMLElement>) {
    ev.dataTransfer.effectAllowed = 'move'

    const payload = ev.currentTarget.getAttribute(dragPayloadStoreAtKey)
    if (payload !== null || payload !== undefined) {
      ev.dataTransfer.setData(transferFormat, payload || '')
    }

    draggingPayload = payload
    draggingStartRect = ev.currentTarget.getBoundingClientRect()
  }

  /** 拖拽悬浮回调（会多次触发，慎用） */
  function onDragOver(ev: React.DragEvent<HTMLElement>) {
    ev.dataTransfer.effectAllowed = 'move'

    // 其他业务拖过来的，忽略
    if (!ev.dataTransfer.types.includes(transferFormat)) return

    ev.preventDefault() // 必须，表示可 drop
    ev.stopPropagation()

    const currentTarget = ev.currentTarget
    const rect = currentTarget.getBoundingClientRect()
    // const offsetX = (ev.clientX || ev.pageX) - rect.left
    const offsetY = (ev.clientY || ev.pageY) - rect.top
    const offsetYFromStart = (ev.clientY || ev.pageY) - draggingStartRect!.top

    const targetPayload = ev.currentTarget.getAttribute(dragPayloadStoreAtKey)
    const detectHover =
      options.detectHover ||
      (() => {
        const pos = offsetYFromStart > 0 ? 'lower' : 'upper'
        return draggingPayload === targetPayload ? 'current' : pos
      })
    reactiveState.hover = detectHover(offsetY, offsetYFromStart, draggingPayload!)
  }

  /** 拖拽离开回调 */
  function onDragLeave() {
    reactiveState.hover = null
  }

  /** 拖拽松开回调（结束） */
  function onDrop(ev: React.DragEvent<HTMLElement>) {
    if (!ev.dataTransfer.types.includes(transferFormat)) {
      // 其他业务拖过来的，忽略
      return
    }
    ev.preventDefault()
    ev.stopPropagation()

    const fromPayload = ev.dataTransfer.getData(transferFormat)
    const targetPayload = ev.currentTarget.getAttribute(dragPayloadStoreAtKey)
    options.onDragEnd?.(fromPayload, targetPayload || '', reactiveState.hover || 'upper')
    reactiveState.hover = null
    draggingPayload = null
    draggingStartRect = null
    // 情调
    ev.dataTransfer.clearData()
  }

  return {
    hoverState: reactiveState.hover,
    onDragStart,
    onDragOver,
    onDragLeave,
    onDrop
  }
}
