
.abi-layout-nav {
  height: 48px;
  display: flex;
  align-items: center;
  box-shadow: 1px 0 6px rgba(@primary-color, 0.18);
  z-index: 899;
  position: sticky;
  background-color: #fff;
  left: 0;
  right: 0;
  border-bottom: 1px solid #f4f4f4;

  .nav-title {
    flex: 1;
    padding: 0 12px;
    display: flex;
    align-items: center;

    > .title {
      min-width: 200px;
      margin: 0;
      font-weight: bold;
      font-size: 20px;
      &:hover {
        color: @primary-color;
        cursor: pointer;
      }
    }
  }

  .nav-actions {
    padding: 0 16px;
    display: flex;
    align-items: center;
    overflow-x: auto;

    .ant-tabs-nav {
      margin-bottom: 0;
      &::before {
        border: none;
      }
    }

    .ant-tabs-content-holder {
      display: none;
    }

    .ant-tabs-tab {
      user-select: none;
    }

    .ant-tabs-tab + .ant-tabs-tab {
      margin: 0 0 0 15px;
    }

    .load-btn {
      margin-left: 20px;
    }
  }
}

.load-btn.float {
  position: fixed;
  right: 0;
  top: 0;
  z-index: 99888;
}
