import './index.less'

import { RedoOutlined } from '@ant-design/icons'
import { But<PERSON>, Tabs } from 'antd'
import isMobileDeviceFn from 'is-mobile'
import React, { Fragment, useEffect, useState } from 'react'
import { history, Outlet, useLocation } from 'umi'

import { AbiFrameworkLayout } from '@/pages/framework'

const isDev = process.env.NODE_ENV !== 'production'

const routers = [
  { label: '项目门户', key: '/project' },
  { label: '数据报告', key: '/report' },
  { label: '自定义图表', key: '/charts/gallery' },
  // { label: '数据视图管理', key: '/dataset' },
  // { label: '微应用管理', key: '/mini-app-manage' },
  // { label: '桌面应用商店', key: '/pc-app-manage' },
  { label: '我的分析', key: '/theme-analysis' },
  { label: '表模型设计', key: '/table-model' },
  { label: '监控错误日志', key: '/web-log' },
  { label: '我的仪表', key: '/framework/workspace?active=theme-analysis' }
  // { label: '后台管理', key: '/admin-manage' }
]

export default function LayoutNav() {
  const location = useLocation()

  const href = window.location.href || ''
  const { search = '' } = location
  const isAbiFrameWork = href.indexOf('/abi/framework') > -1 || search.indexOf('isNewBi') > -1

  const [updateKey, setUpdateKey] = useState(() => Math.random().toString(32))
  const [activeKey, setActiveKey] = useState(() => routers.find(i => i.key !== '/' && href.indexOf(i.key) > -1)?.key || '/')
  const onTo = (key: string) => {
    history.push(key)
    setActiveKey(key)
  }

  const loadBtn = (
    <Button
      className='load-btn float'
      type='primary'
      size='small'
      title='刷新页面'
      icon={<RedoOutlined />} onClick={() => setUpdateKey(Math.random().toString(32))}
    />
  )

  if (isAbiFrameWork) {
    if (!isDev) return <AbiFrameworkLayout key={updateKey} />
    return (
      <Fragment key={updateKey}>
        <AbiFrameworkLayout />
        {loadBtn}
      </Fragment>
    )
  }

  // 微应用方式
  if (window.__POWERED_BY_WUJIE__) return <Outlet />
  if (isMobileDeviceFn()) return <Outlet />
  if (/\/console\/abi\/project\/(.*)/.test(href)) return <Outlet />
  if (/\/abi\/preview\/(.*)/.test(href)) return <Outlet />
  if (/\/charts\/gallery\/(.*)/.test(href)) return <Outlet />
  if (/\/theme-analysis\/preview\/(.*)/.test(href)) return <Outlet />
  if (search.indexOf('hideNav') > -1) return <Outlet />
  if (!isDev) return <Outlet />

  return (
    <Fragment key={updateKey}>
      <div className='abi-layout-nav'>
        <div className='nav-title'>
          <h3 className='title' onClick={() => onTo('/')}>Data Brush 数字妙笔</h3>
        </div>
        <div className='nav-actions'>
          <Tabs items={routers} activeKey={activeKey} onChange={onTo} />
          {/* 开发时刷新页面用的 */}
          {loadBtn}
        </div>
      </div>
      <Outlet />
    </Fragment>
  )
}
