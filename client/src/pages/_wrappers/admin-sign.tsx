import { Outlet } from '@umijs/max'
import { Input, message } from 'antd'
import React, { useRef, useState } from 'react'

import { useQueryParams } from '@/hooks/use-query-params'

/**
 * demo 包装
 */
export default function AdminWarpper() {
  const inputRef = useRef<any>(null)
  const [ok, setOk] = useState(false)

  const query = useQueryParams()
  const sign = query.sign || window.localStorage.getItem('sign') || undefined

  if (sign === window.demoSign || ok) return <Outlet />

  const onEnter = () => {
    const target = inputRef.current
    const val = target?.input?.value

    if (val === window.demoSign) {
      setOk(true)
    } else {
      setOk(false)
      message.error('错误')
    }
  }

  return (
    <div className='p-8 pt-32 text-center'>
      <h2>访问受限</h2>
      <Input.Password
        ref={inputRef}
        style={{ width: 200 }}
        maxLength={16}
        placeholder='输入签名'
        onKeyPress={e => e.code === 'Enter' && onEnter()}
      />
    </div>
  )
}
