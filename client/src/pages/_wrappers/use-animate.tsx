import './use-animate.less'

import { history, Outlet } from '@umijs/max'
import cn from 'classnames'
import React, { useEffect, useState } from 'react'

export default function UseAnimateWarpper() {
  const [animate, setAnimate] = useState(false)

  useEffect(() => {
    setAnimate(true)
    const unlisten = (history as any)?.listen(() => {
      setAnimate(false)
    })
    return () => {
      unlisten?.()
    }
  }, [])

  return (
    <div className={cn({ 'use-animate-warpper': animate })}>
      <Outlet />
    </div>
  )
}

