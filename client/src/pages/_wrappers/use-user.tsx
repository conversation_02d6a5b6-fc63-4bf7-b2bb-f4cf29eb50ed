import './use-user.less'

import { LoadingOutlined } from '@ant-design/icons'
import { Outlet, useModel } from '@umijs/max'
import { useDeepCompareEffect } from 'ahooks'
import { message } from 'antd'
import isEqual from 'fast-deep-equal'
import isMobileDeviceFn from 'is-mobile'
import _ from 'lodash'
import React, { useMemo, useState } from 'react'

import { AppLoading } from '@/components/app-loading'
import { oAuth2 } from '@/hooks/use-oAuth2-login'
import { store } from '@/stores'
import { useCommit, useModelState } from '@/stores/models/user'
import { getJwt, getUser } from '@/utils/query'

const pickKey = ['id', 'username', 'status', 'first_name', 'email', 'type', 'SugoRoles']
const isFrameWork = () => window.location.href.indexOf('console/abi/framework') > -1
// const isDev = process.env.NODE_ENV !== 'production'

const loaded = false

/**
 * 加载用户到 redux 里
 * @returns
 */
export default function () {
  const masterProps = useModel('@@qiankunStateFromMaster' as any)
  const user = _.pick(masterProps?.userInfo || {}, pickKey)
  const commit = useCommit()
  const userId = user?.id || useModelState(s => s.id)
  const runInMobile = useMemo(() => isMobileDeviceFn(), [])

  const [firstLoaded, setFirstLoaded] = useState(() => {
    if (loaded) return false
    if (runInMobile) return false // 移动端不显示这边的 loading
    // if (isFrameWork()) return true
    return false
  })

  const jwt = getJwt() // 用户登录的 jwt 覆盖 initialState 里的 jwt 后需要重新获取用户信息

  const getUserInfo = async () => {
    const prevUser = store.getState().user // 只能这样取，不然会死循环
    // masterProps、redux 里都没有时才请求
    if (_.isEmpty(user) && !prevUser?.id) {
      try {
        const res = await getUser()
        if (!res?.user) {
          commit('reset') // 清空 redux
          message.error('请登录')
          return
        }

        // 从接口获取到 user info
        const newUser = _.pick(res.user, pickKey)
        if (!isEqual(prevUser, newUser)) {
          commit('update', _.pick(res.user, pickKey))
        }
      } catch (err) {
        console.error(err)
        window.sessionStorage.removeItem('masterJWT')
        // 如果是运营资源环境，可能会死循环：没有 cookie -> oauth 获取产品 jwt -> 获取运营资源 user 失败 -> oauth ...
        const oAuthAt = window.localStorage.getItem('oAuthAt') ?? 0
        if ((Date.now() / 1000 - +oAuthAt) < 10) {
          // 死循环了，主动跳出
          message.error('获取用户信息失败，请重新登录')
          return
        }
        oAuth2()
      }
    } else if (!isEqual(prevUser, user)) {
      commit('update', user)
    }
  }

  // 载入用户到 redux
  useDeepCompareEffect(() => {
    if (!userId) getUserInfo()
  }, [user, jwt])

  // useEffect(() => {
  //   if (isFrameWork() && !loaded) {
  //     setTimeout(() => setFirstLoaded(false), 1000 * (isDev ? 10 : 2))
  //   }
  //   loaded = true
  // }, [])

  const Loading = (
    <div className='page-loading' key='abi-app-loading' id='abi-app-loading'>
      {isFrameWork() ?
        <AppLoading /> :
        <LoadingOutlined />
      }
    </div>
  )

  if (!userId) return Loading

  if (firstLoaded && !loaded) return (
    <>
      <Outlet />
      {Loading}
    </>
  )

  return <Outlet />
}
