import { <PERSON><PERSON>, <PERSON>down, <PERSON><PERSON>, <PERSON><PERSON><PERSON>rm, <PERSON>,Tabs } from 'antd'
import cn from 'classnames'
import { klona as cloneDeep } from 'klona/lite'
import _ from 'lodash'
import React, { useRef } from 'react'

import Icon from '@/components/icons/iconfont-icon'
import { ICON_MAP } from '@/components/icons/iconfont-icon-const'
import { DefaultActionDefines,DefaultEventDefines } from '@/consts/define'
import { COM_CONFIG_PANEL_MAP,COMPONENT_TYPE_MAP, ELEMENT_CATEGORY_MAP, ELEMENT_GROUP_MAP } from '@/consts/screen'

import FormSchemaEditorModal from './form-schema-modal'

const { TabPane } = Tabs

const loadMore = {
  live: '自定义',
  local: '内置',
  pack: '第三方包'
}

// 定义盒子
export function DefineBox(props) {
  const { keyField = 'key', title, map = [] } = props
  const list = _.values(map)
  return (
    <div className='define-box'>
      <h3 className='define-box-title'>{title}</h3>
      {list.map(item => (
        <div key={item[keyField]} className='define-box-row'>
          <span className='define-box-row-title'>{item.title}</span>
          <span className='define-box-row-key'>{item[keyField]}</span>
        </div>
      ))}
    </div>
  )
}

// 图标盒子
export function IconDefineBox(props) {
  const { title, map = [] } = props
  return (
    <div className='define-box'>
      <h3 className='define-box-title'>{title}</h3>
      <div className='define-box-icons'>
        {Object.keys(map).map(key => (
          <div key={map[key]} className='define-box-icons-item'>
            <Icon type={map[key]} />
            <span className='define-box-row-title'>{key}</span>
          </div>
        ))}
      </div>
    </div>
  )
}

// 定义列表
export function DefineList(props) {
  const { group, actived, onSelect, onExport, onImport, loading } = props

  return (
    <div className='left-panel'>
      <header className='flex items-center p-1'>
        <span className='flex-1'>组件定义列表</span>

        {/* <div className='mr-1'>
          <Button className='mr-2' size='small' type='dashed' onClick={onExport}>
            导出
          </Button>
          <Button size='small' type='dashed' onClick={onImport}>
            导入
          </Button>
        </div> */}
      </header>

      <Tabs size='small' animated={false} type='line'>
        {_.keys(group).map(key => (
          <TabPane key={key} tabKey={key} tab={`${loadMore[key]} ${group[key]?.length} 个`}>
            <Spin spinning={loading}>
              <div>
                {group[key].map(item => (
                  <div
                    key={item.id + item.key}
                    onClick={() => onSelect(item)}
                    className={cn({
                      'element-item': true,
                      'element-item-active': item.key === actived
                    })}
                  >
                    {item.icon && <Icon name={item.icon} />}
                    <span className='define-name'>{item.title || '空 title 请检查'}</span>
                    <span className='define-key'> {item.key}</span>
                  </div>
                ))}
              </div>
            </Spin>
          </TabPane>
        ))}
      </Tabs>
    </div>
  )
}

// 代码的工具栏
export function CodeToolbox(props) {
  const formSchemaEditorRef = useRef<ModalDefaultRef>(null)
  const { isCreate, isChange, editData, createData, loading, errorText, actived } = props
  const { onCreate, onUpdate, onDelete, onConfigSchemaChange } = props

  const onOpenFormSchema = (type: 'config' | 'style' | 'base') => {
    formSchemaEditorRef.current?.show({
      type,
      data: cloneDeep(editData),
      onSubmit: onConfigSchemaChange
    })
  }

  return (
    <header className='p-1 pl-4 pr-4 flex items-center'>
      <Button size='small' type={isCreate && createData ? 'primary' : 'default'} onClick={onCreate} loading={loading}>
        新增定义
      </Button>
      <Button size='small' type='primary' disabled={!(isChange && !errorText)} onClick={onUpdate} loading={loading}>
        更新此定义
      </Button>
      {actived && !isCreate && (
        <Popconfirm onConfirm={onDelete} title='确认删除'>
          <Button type='dashed' size='small' danger disabled={!actived}>
            删除此定义
          </Button>
        </Popconfirm>
      )}
      {actived && !isCreate && (
        <Dropdown
          trigger={['click']}
          overlay={
            <Menu
              onClick={e => onOpenFormSchema(e.key as any)}
              items={[
                { label: '基本表单', key: 'base' },
                { label: '样式表单', key: 'style' },
                { label: '属性表单', key: 'config' }
              ]}
            />
          }
        >
          <Button size='small'>进入编辑表单</Button>
        </Dropdown>
      )}

      <div className='flex-1'>
        {isCreate && <span>正在新增 ...</span>}
        {isChange && <span>正在编辑 ...</span>}
      </div>

      <Dropdown
        placement='bottomLeft'
        trigger={['click']}
        overlayClassName='prefix-const-overlay'
        overlay={
          <div>
            <header>分组，分类常量定义</header>
            <DefineBox keyField='key' title='组件类型（type）' map={COMPONENT_TYPE_MAP} />
            <DefineBox keyField='key' title='组件面板（panels）' map={COM_CONFIG_PANEL_MAP} />
            <DefineBox keyField='key' title='组件分类（category）' map={ELEMENT_CATEGORY_MAP} />
            <DefineBox keyField='key' title='组件分组（group）' map={ELEMENT_GROUP_MAP} />
            <DefineBox keyField='name' title='事件定义（events）' map={DefaultEventDefines.events} />
            <DefineBox keyField='name' title='动作定义（actions）' map={DefaultActionDefines.actions} />
            <IconDefineBox title='图标定义（icon）' map={ICON_MAP} />
          </div>
        }
      >
        <Button type='dashed' size='small'>
          常量预设
        </Button>
      </Dropdown>

      <FormSchemaEditorModal ref={formSchemaEditorRef} />
    </header>
  )
}
