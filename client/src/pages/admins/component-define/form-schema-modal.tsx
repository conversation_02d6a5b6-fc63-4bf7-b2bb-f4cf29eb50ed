import './form-schema-modal.less'

import { useMemoizedFn,useReactive } from 'ahooks'
import { <PERSON><PERSON>, <PERSON><PERSON>, Drawer } from 'antd'
import _ from 'lodash'
import React, { useEffect } from 'react'

import { FormSchemaEditor, getInitDefaultValue, verifySchema } from '@/components/form-schema'
import withMdal from '@/components/with-ref-modal'
import { ELEMENT_OMIT_FIELD } from '@/consts/screen'
import { getCodeStringByData, getDataByCodeString } from '@/utils'

const configOmitField = ['visible', 'lock', 'i18n']
const styleOmitField = ELEMENT_OMIT_FIELD

export interface FormSchemaModalProps {
  type?: 'config' | 'style' | 'base'
  data?: Partial<{
    configDefine: Record<string, any>
    styleDefine: Record<string, any>
    configSchema: Record<string, any>
    styleSchema: Record<string, any>
    baseSchema: Record<string, any>
  }>
  onSubmit?: (val: any) => any
  getContainer?: () => any
}

/**
 * 进入表单模型设计器
 */
const FormSchemaModal = withMdal<FormSchemaModalProps>(props => {
  const { modal, visible, data, type, getContainer } = props

  const messageMap = {
    style: '样式设置的 valuePath 需要指向组件的 style 字段',
    config: '属性设置的 valuePath 映射到组件的 config 字段',
    base: '基础设置的 valuePath 映射到组件的 config 字段'
  }

  const state = useReactive({
    schema: {},
    define: {},
    schemaCode: getCodeStringByData('模型', 'schema', {}),
    resultCode: getCodeStringByData('输出', 'config', {}),
    type: type || 'config',
    get verifyMessage() {
      try {
        return verifySchema(getDataByCodeString(state.schemaCode, 'schema'))
      } catch (err: any) {
        return [err.message]
      }
    }
  })

  const onSchemaChange = _.debounce(val => (state.schemaCode = val), 200)
  const setResultCode = val => {
    const varr = state.type === 'style' ? 'style' : 'config'
    state.resultCode = getCodeStringByData('输出', varr, val)
  }
  const onRefresh = (cb?: any) => {
    try {
      state.schema = getDataByCodeString(state.schemaCode, 'schema')
      state.define = getInitDefaultValue(state.schema)
      state.schemaCode = getCodeStringByData('模型', 'schema', state.schema)
      setResultCode(state.define)
    } catch (err) {
      if (typeof cb === 'function') cb(err)
    }
  }

  const onSubmit = async e => {
    const isError = ''
    onRefresh(err => isError === err)
    if (isError) return

    await new Promise(rs => setTimeout(rs, 360))

    const params: any = { type }

    if (type === 'config') {
      params.configSchema = _.omit(state.schema, configOmitField)
      params.configDefine = _.merge({}, data?.configDefine, state.define)
    }
    if (type === 'style') {
      params.styleSchema = _.omit(state.schema, styleOmitField)
      params.styleDefine = _.merge({}, data?.styleDefine, state.define)
    }
    if (type === 'base') {
      params.baseSchema = _.omit(state.schema, configOmitField)
      params.configDefine = _.merge({}, data?.configDefine, state.define)
    }

    props.onSubmit?.(params)
    modal.hide()
  }

  const onConfigChange = useMemoizedFn(val => {
    state.define = val
    setResultCode(val)
  })

  // 初始化
  useEffect(() => {
    if (!data) return

    if (type === 'config') {
      const configDefine = _.merge({}, data?.configDefine, getInitDefaultValue(data?.configSchema))
      state.schema = data?.configSchema || {}
      state.define = _.omit(configDefine, configOmitField)
    }
    if (type === 'style') {
      const styleDefine = _.merge({}, data?.styleDefine, getInitDefaultValue(data?.styleSchema))
      state.schema = data?.styleSchema || {}
      state.define = _.omit(styleDefine, styleOmitField)
    }
    if (type === 'base') {
      const configDefine = _.merge({}, data?.configDefine, getInitDefaultValue(data?.configSchema))
      state.schema = data?.baseSchema || {}
      state.define = _.omit(configDefine, configOmitField)
    }

    state.type = type || 'base'
    state.schemaCode = getCodeStringByData('模型', 'schema', state.schema)
    setResultCode(state.define)
  }, [data, type])

  return (
    <Drawer
      title={`表单模型设计器 - ${type === 'style' ? '样式设置' : '属性设置'}`}
      open={visible}
      width='75%'
      height='100%'
      getContainer={getContainer}
      maskClosable={false}
      onClose={modal.hide}
      className='component-define-form-schema-modal'
      extra={
        <footer className='extra-footer'>
          <div>
            <Alert type='warning' description={messageMap[type!]} showIcon />
          </div>
          <Button className='mr-2' onClick={modal.hide}>
            取消
          </Button>
          <Button type='primary' danger onClick={onSubmit}>
            保存
          </Button>
        </footer>
      }
    >
      <div className='modal-content form-schema-page'>
        {data && (
          <FormSchemaEditor
            config={state.define}
            configCode={state.resultCode}
            schema={state.schema}
            schemaCode={state.schemaCode}
            onConfigChange={onConfigChange}
            onRefresh={onRefresh}
            onSchemaChange={onSchemaChange}
            verifyMessage={state.verifyMessage}
          />
        )}
      </div>
    </Drawer>
  )
})

export default FormSchemaModal
