.component-define-demo {
  height: 100%;
  border-radius: 3px;

  .main-panel {
    display: flex;
  }

  .left-panel {
    width: 320px;
    min-width: 320px;
    max-height: calc(100vh - 50px);
    overflow-y: auto;
    border-right: 1px solid #f4f4f4;

    > header {
      font-weight: bold;
    }

    .ant-tabs-tab {
      font-size: 14px;
      margin-left: 20px;
      &:first-of-type {
        margin: 0;
      }
    }

    .group-name {
      padding: 4px 12px;
      border-left: 3px solid var(--primary-color-60);
      margin: 4px 0;
    }

    .element-item {
      background-color: #fff;
      border-bottom: 1px solid #f1f1f1;
      padding: 6px 12px;
      cursor: pointer;
      &:hover,
      &.element-item-active {
        color: var(--primary-color);
        font-weight: 480;
        background-color: var(--primary-color-10);
      }

      .anticon {
        margin-right: 8px;
      }
      .define-key {
        margin-left: 6px;
        font-size: 13px;
        color: #789;
        opacity: 0.9;
      }
    }
  }

  .content-panel {
    flex: 1;
    display: flex;
    flex-direction: column;

    > header {
      border-bottom: 1px solid #f1f1f1;
      padding-bottom: 10px;
      > button {
        margin-right: 12px;
        &:last-of-type {
          margin: 0;
        }
      }
    }

    // 修改样式
    .variable-editor {
      background-color: #fff !important;
      border: 1px solid var(--primary-color-12);
      box-shadow: 0 0 6px var(--primary-color-12);
      outline: none;
    }

    .content-panel-code {
      position: relative;
      height: 100%;
      .show-error {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 1;
        color: #fff;
        background-color: #f45;
        padding: 2px 4px;
        border-radius: 3px;
        font-size: 15px;
      }

      .js-code {
        width: 100%;
        height: 90vh;
        min-width: 300px;
        min-width: 300px;
        max-width: 1600px;
      }
    }
  }
}

// 预设内容
.prefix-const-overlay {
  max-height: 600px;
  width: 360px;
  box-shadow: 1px 2px 8px rgba(1, 1, 1, 0.12);
  border: 1px solid #f1f1f1;
  background-color: #fff;
  padding: 16px;
  padding-right: 0;
  border-radius: 5px;
  overflow: hidden;
  overflow-y: auto;

  .define-box {
    margin: 8px 6px;
    margin-left: 0;
    padding: 4px;
    border-radius: 3px;

    &-title {
      color: #78f;
      font-size: 15px;
      position: sticky;
      top: -10px;
      background-color: #fff;
      border: 1px solid #f1f1f1;
      padding: 4px;
      border-radius: 5px;
    }

    &-row {
      &-key {
        color: #788;
        margin-left: 6px;
      }
    }
    &-icons {
      display: flex;
      flex-wrap: wrap;

      &-item {
        padding: 2px 4px;
        border-radius: 3px;
        display: flex;
        align-items: center;
        color: #678;
        .anticon {
          margin-right: 3px;
          color: #444;
          font-size: 16px;
        }
      }
    }
  }
}
