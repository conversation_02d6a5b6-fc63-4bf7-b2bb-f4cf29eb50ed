import './index.less'

import { useAsyncEffect, useReactive, useTitle } from 'ahooks'
import { message } from 'antd'
import _ from 'lodash'
import React from 'react'

import CodeEditor from '@/components/code-editor'
import { Service } from '@/services'
import { getDataByCodeString } from '@/utils'

import { CodeToolbox, DefineList } from './components'
import { DEFAULT_DATA, getSourceCode, verifyError } from './utils'

const omitField = ['id', 'objectId', 'createdAt', 'updatedAt']

/**
 * 组件定义表
 */
export default function ComponentDefineDemo() {
  // const exportRef = useRef<any>(null)
  // const importRef = useRef<any>(null)

  const state = useReactive({
    actived: '',
    isChange: false,
    isCreate: false,
    createData: DEFAULT_DATA,
    editData: {} as any,

    list: [] as any,
    // 代码字符串
    sourceCode: '',
    // errorText: '',

    loading: false,
    listLoading: false,

    reset: () => {
      state.actived = ''
      state.isChange = false
      state.isCreate = false
      state.editData = {}
      state.sourceCode = ''
    },
    get group() {
      return _.groupBy(state.list, 'loadMode')
    },
    // 选中节点
    get selected() {
      return state.list.find(i => i.key === state.actived)
    },
    // 错误信息
    get errorText() {
      const data = state.isCreate ? state.createData : _.omit(state.editData, 'id')
      return verifyError(state)(data, !state.isCreate)
    }
  })

  const { selected, errorText } = state
  // 初始化数据
  const initData = async () => {
    state.listLoading = true
    state.list = await Service.ComponentDefine.findAll({
      limit: 1000,
      attributes: {
        exclude: ['thumb']
      }
    })
    state.listLoading = false
  }

  useTitle(`${state.selected?.title || ''} - 组件自定义编辑器`)
  useAsyncEffect(initData, [])

  const genSourceCode = data => {
    try {
      state.sourceCode = getSourceCode(data)
    } catch (err) {
      console.error(err)
      message.error('生成代码错误')
    }
  }

  // 创建（编辑新建按钮）
  const onCreate = async e => {
    if (errorText) return

    state.isChange = false
    state.editData = {}
    genSourceCode(_.omit(getDataByCodeString(state.sourceCode, 'data'), omitField))

    state.loading = true
    // 下一次点击才到这里
    if (state.isCreate) {
      const data = _.omit(state.createData, omitField)
      const keys = _.keys(DEFAULT_DATA)

      const res = await Service.ComponentDefine.create(_.pick(data, keys) as any)
      if (res) {
        state.actived = data.key!
        message.success(`已创建：${data.title}`)
        setTimeout(initData, 500)
      }
    }
    state.isCreate = true
    state.loading = false
  }

  // 编辑
  const onUpdate = async () => {
    const data = state.editData
    if (!state.isChange) return
    if (verifyError(state)(_.omit(data, 'id'), true)) {
      return message.error('有错误')
    }
    const keys = _.keys(DEFAULT_DATA)
    await Service.ComponentDefine.update(_.pick(data, keys), {
      where: { id: data.id }
    })
    state.isChange = false
    message.success(`已更新：${selected.title}`)

    setTimeout(initData, 500)
  }

  // 删除
  const onDelete = async () => {
    const id = selected.id
    if (id) {
      await Service.ComponentDefine.delete({ where: { id } })
    }
    state.reset()
    setTimeout(initData, 500)
  }

  const onSelect = item => {
    state.reset()
    state.actived = item.key
    genSourceCode(item)
  }

  const onChange = _.debounce(val => {
    state.sourceCode = val || ''
    if (state.actived && !state.isCreate) state.isChange = true
    try {
      if (state.isCreate) state.createData = getDataByCodeString(state.sourceCode, 'data')
      else state.editData = getDataByCodeString(state.sourceCode, 'data')
    } catch (err) {
      logger.log('sourceCode error', err)
    }
  }, 360)

  const onConfigSchemaChange = val => {
    const item = state.list.find(i => i.key === state.actived)
    if (item && val.type === 'config') {
      state.editData.configSchema = val.configSchema
      state.editData.configDefine = val.configDefine
    }
    if (item && val.type === 'style') {
      state.editData.styleSchema = val.styleSchema
      state.editData.styleDefine = val.styleDefine
    }
    if (item && val.type === 'base') {
      state.editData.baseSchema = val.baseSchema
      state.editData.configDefine = val.configDefine
    }
    if (item) genSourceCode(state.editData)
  }

  return (
    <div className='component-define-demo'>
      {/* <header className='nav-header'>组件定义编辑器</header> */}
      <section className='main-panel'>
        <DefineList
          group={{
            local: state.group?.local || [],
            live: state.group?.live || []
          }}
          loading={state.listLoading}
          actived={state.actived}
          onSelect={onSelect}
        // onExport={() =>
        //   exportRef.current.show({
        //     list: _.map(state.list, i => _.pick(i, ['id', 'key', 'title', 'device', 'loadMode']))
        //   })
        // }
        // onImport={() => {
        //   importRef.current.show({})
        // }}
        />

        <div className='content-panel'>
          <CodeToolbox
            {...state}
            onDelete={onDelete}
            onCreate={onCreate}
            onUpdate={onUpdate}
            onConfigSchemaChange={onConfigSchemaChange}
          />
          <div className='content-panel-code'>
            <CodeEditor className='js-code' value={state.sourceCode} onChange={onChange} />
            <div className='show-error'>{errorText}</div>
          </div>
        </div>
      </section>
    </div>
  )
}
