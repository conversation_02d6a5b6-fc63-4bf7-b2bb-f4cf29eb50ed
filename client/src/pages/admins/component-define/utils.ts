import sortKeys from 'sort-keys-recursive'

import { ICON_MAP } from '@/components/icons/iconfont-icon-const'
import { DefaultActionDefines,DefaultEventDefines } from '@/consts/define'
import { COM_CONFIG_PANEL_MAP,COMPONENT_TYPE_MAP, ELEMENT_CATEGORY_MAP, ELEMENT_GROUP_MAP } from '@/consts/screen'
import type { ComponentDefine } from '@/types/editor-core/component'

export const verifyError =
  state =>
  (data, isUpdate = false) => {
    if (data.id) return '非法 id 属性，请修改再提交'
    if (state.list.find(i => i.key === data.key) && !isUpdate) return 'key 已经存在了，请修改再提交'
    if (state.list.find(i => i.name === data.name) && !isUpdate) return 'name 已经存在了，请修改再提交'
    if (data.type && !COMPONENT_TYPE_MAP[data.type]) return '组件的 type 错误，不在常量定义里'
    if (data.category && !ELEMENT_CATEGORY_MAP[data.category]) return '组件的 category 错误，不在常量定义里'
    if (data.group && !ELEMENT_GROUP_MAP[data.group]) return '组件的 group 错误，不在常量定义里'
    if (data.type && data.panels) {
      const flag = data.panels?.some(k => !COM_CONFIG_PANEL_MAP[k])
      if (flag) return '组件的 panels 错误，不在常量定义里'
    }
    if (data.icon && !ICON_MAP[data.icon]) return '组件的 icon 错误，不在常量定义里'
    // if (data.eventActionDefine?.events) {
    //   const flag = data.eventActionDefine.events?.some(k => !DefaultEventDefines.events[k])
    //   if (flag) return '组件的 events 错误，不在常量定义里'
    // }
    // if (data.eventActionDefine?.actions) {
    //   const flag = data.eventActionDefine.actions?.some(k => !DefaultActionDefines.actions[k])
    //   if (flag) return '组件的 actions 错误，不在常量定义里'
    // }
    return ''
  }

export const DEFAULT_DATA: Omit<ComponentDefine, 'id' | 'loadMode'> = {
  key: 'rectangle@1.0.0',
  name: 'rectangle',
  title: '矩形',
  description: '',
  version: '1.0.0',
  versionCode: 0,
  status: 'available',
  type: 'element',
  category: 'base',
  group: '',
  device: '',
  icon: '矩形',
  thumb: '',
  panels: ['style', 'attr', 'event', 'datasource'],
  styleSchema: {},
  styleDefine: {},
  configDefine: {},
  configSchema: {},
  baseSchema: {},
  dataSourceDefine: {},
  // moveableModifyConfig: {},
  eventActionDefine: {
    events: ['click', 'keyup', 'doubleClick'],
    actions: ['setVisibility', 'openUrl']
  },
  extraConfig: {},
  package: ''
}

export const getSourceCode = data => {
  const keyIndexDict = Object.keys(DEFAULT_DATA).reduce(
    (obj, val, index) => ({
      ...obj,
      [val]: index
    }),
    {}
  )
  const obj = sortKeys(data, {
    compareFunction: (a, b) => keyIndexDict[a] - keyIndexDict[b]
  })
  const code = JSON.stringify(obj, null, 2)?.replace(/\s"(.*)":/gm, ' $1:')
  return `/** 你的代码 */\nconst data = ${code || '{}'}`
}
