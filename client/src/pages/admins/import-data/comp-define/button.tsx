import './button.less'

import { ToTopOutlined,VerticalAlignBottomOutlined } from '@ant-design/icons'
import { Dropdown, Tooltip } from 'antd'
import React, { useRef } from 'react'

import Icon from '@/components/icons/iconfont-icon'

import DefineExportModal from './export-modal'
import DefineImportModal from './import-modal'

export interface MyBtnProps {
  type: 'define' | 'project'
  projectId?: string
  className?: string
  onImportComplete?: () => any
}

export default function MyBtn(props: MyBtnProps) {
  const { type, className, projectId, onImportComplete } = props
  const defineExportRef = useRef<ModalDefaultRef>(null)
  const defineImportRef = useRef<ModalDefaultRef>(null)

  return (
    <>
      <Dropdown
        placement='bottom'
        trigger={['click']}
        overlayClassName='project-export-import-overlay'
        overlay={
          <div className='project-export-import-menus'>
            <div onClick={() => defineExportRef.current?.show?.({ type, projectId })}>
              <ToTopOutlined />
              导出
            </div>
            <div onClick={() => defineImportRef.current?.show?.({ type, projectId })}>
              <VerticalAlignBottomOutlined />
              导入
            </div>
          </div>
        }
      >
        <Tooltip title='迁移' placement='left'>
          <Icon name='导入导出' className={className} />
        </Tooltip>
      </Dropdown>

      <DefineImportModal ref={defineImportRef} onImportComplete={onImportComplete} />
      <DefineExportModal ref={defineExportRef} />
    </>
  )
}
