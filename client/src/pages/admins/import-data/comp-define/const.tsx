import { Tooltip } from 'antd'
import dayjs from 'dayjs'
import React from 'react'

import { getFromNow } from '@/utils'


export const defineColumns: any[] = [
  {
    title: '主键（key）',
    dataIndex: 'key',
    width: 185,
    sorter: (a, b) => a.key.localeCompare(b.key)
  },
  { title: '名称', dataIndex: 'title' },
  {
    title: '设备',
    dataIndex: 'device',
    width: 80,
    filters: [
      { text: '通用', value: 'none' },
      { text: '移动端', value: 'mobile' },
      { text: '桌面端', value: 'pc' }
    ],
    onFilter: (value: string, record) => record.device.includes(value),
    render: text => (
      <span style={{ color: text === 'mobile' ? '#39f' : '#f80' }}>
        {{ none: '-', mobile: '移动端', pc: '桌面端' }[text] || '-'}
      </span>
    )
  },
  {
    title: '模式',
    dataIndex: 'loadMode',
    width: 80,
    filters: [
      { text: '内置', value: 'local' },
      { text: '自定义', value: 'live' }
    ],
    onFilter: (value: string, record) => record.loadMode.includes(value),
    render: text => ({ local: '内置', live: '自定义' }[text] || '通用')
  },
  {
    title: '更新时间',
    dataIndex: 'updatedAt',
    render: text => (
      <Tooltip title={dayjs(text || '').format('YYYY-MM-DD HH:mm:ss')}>
        {text ? getFromNow(new Date(text).getTime()) : '-'}
      </Tooltip>
    )
  }
]

export const projectColumns: any[] = [
  { title: '名称', dataIndex: 'title' },
  // {
  //   title: '类型',
  //   dataIndex: 'mode',
  //   width: 80,
  //   filters: [
  //     { text: '项目', value: 'project' },
  //     { text: '报告', value: 'report' }
  //   ],
  //   onFilter: (value: string, record) => record.mode?.includes(value),
  //   render: text => (
  //     <span style={{ color: text === 'project' ? '#39f' : '#f80' }}>
  //       {{ project: '项目', report: '报告' }[text] || '-'}
  //     </span>
  //   )
  // },
  {
    title: '类型',
    dataIndex: 'type',
    width: 80,
    filters: [
      { text: '大屏端', value: 0 },
      { text: '移动端', value: 2 },
      { text: '桌面端', value: 1 }
    ],
    onFilter: (value: number, record) => value === record.type,
    render: text => (
      <span style={{ color: text === 1 ? '#78f' : '#444' }}>
        {{ 0: '大屏端', 2: '移动端', 1: '桌面端' }[text] || '-'}
      </span>
    )
  },
  {
    title: '是否模板',
    dataIndex: 'isTemplate',
    width: 80,
    render: text => (text ? '是' : '-')
  }
]
