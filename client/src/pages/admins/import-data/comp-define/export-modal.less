.component-define-export-modal {
  .tip {
    font-size: 14px;
    color: #777;
    margin-left: 8px;
    margin-bottom: 6px;
  }

  .cdn-check {
    margin-left: 8px;
  }

  .ant-upload-list-item-name {
    .ant-upload-list-item-card-actions {
      display: none !important;
    }
  }
  .ant-upload.ant-upload-drag {
    user-select: none;
  }
  .ant-modal-close-x {
    display: none;
  }
  .ant-modal-body {
    height: 610px;
    overflow-y: auto;
    padding-bottom: 12px;
    .ant-table-cell {
      padding: 6px !important;
    }
    .ant-table-tbody > tr > td {
      border-bottom-color: #f7f7f7;
    }
  }
  .ant-steps {
    margin-bottom: 20px;
    .ant-steps-item-description {
      opacity: 0.6;
    }
  }
  .ant-modal-footer {
    text-align: left;
    display: flex;
    justify-content: space-between;
  }

  .message-panel {
    border: 1px solid #f1f1f1;
    border-radius: 3px;
    padding: 10px 12px;
    margin-bottom: 12px;
    max-height: 400px;
    overflow-y: auto;

    &.error {
      border-color: #f45;
      background-color: rgba(#f45, 0.025);
      max-height: 400px;
      overflow-y: auto;

      > li {
        margin-left: 24px;
        font-size: 15px;
      }
    }
    &.success {
      border-color: #3b6;
    }

    .msg-text {
      font-size: 14px;
      color: #777;
    }
  }
  .ant-upload {
    background-color: #fff;
    border-color: var(--primary-color);
  }

  // 数据源映射
  .data-source-map {
    > p:first-of-type {
      color: #e45;
      font-weight: bold;
    }
    .data-source-select {
      width: 300px;
      padding: 0;
      .data-source-select-title {
        display: none;
      }
    }

    .data-source-map-item {
      display: flex;
      align-items: center;
      justify-content: space-around;
      margin-bottom: 4px;

      .ant-input-group-wrapper:first-of-type {
        width: 320px;
        .ant-input-group-addon {
          background-color: #4b6;
          border-color: #4b6;
          color: #fff;
          padding: 0 6px;
        }
        .ant-input {
          border-color: #4b6;
          box-shadow: none;
        }
      }
      .anticon-swap-right:first-of-type {
        margin: 0 16px;
        font-size: 18px;
        color: #777;
      }
    }
  }
}
