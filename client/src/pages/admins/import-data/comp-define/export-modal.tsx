import './export-modal.less'

import { LoadingOutlined } from '@ant-design/icons'
import { useAsyncEffect, useReactive } from 'ahooks'
import { Checkbox, Steps, Tooltip } from 'antd'
import _ from 'lodash'
import React from 'react'

import Modal from '@/components/customs/custom-modal'
import withRefModal from '@/components/with-ref-modal'
import { Service } from '@/services'

import { defineColumns, projectColumns } from './const'
import MyTable from './my-table'

const { Step } = Steps

const ExportModal = withRefModal(props => {
  const { modal, visible, type, projectId } = props

  const state = useReactive({
    list: [] as any[],
    current: 0,
    selected: [] as any[],
    loading: false,
    errorMap: {} as Record<string, any>,
    downloadUrl: '',
    fileName: '',
    needCDN: false,
    isFristDownload: false,
    tableKey: ''
  })
  const { current, selected, loading, downloadUrl, errorMap, fileName, list } = state
  const count = selected.length
  const failCount = _.keys(errorMap).length
  const successCount = count - failCount
  const isDefine = type === 'define'
  const cloudFunctionName: any = isDefine ? 'exportComponentDefine' : 'exportProject'

  const onOk = async () => {
    if (current === 0) {
      state.current = 1
      state.loading = true
      try {
        await new Promise(rs => setTimeout(rs, 1000 * 1))
        const res = await Service.$execute(cloudFunctionName, { ids: selected, needCDN: state.needCDN })
        if (!_.isEmpty(res.error)) state.errorMap = res.error
        state.downloadUrl = res.path || ''
        state.fileName = res.fileName || ''
      } finally {
        state.loading = false
      }
    }
    if (current === 1) {
      modal.hide()
    }
  }

  const onDownLoad = () => {
    state.isFristDownload = true
    const aDom = document.createElement('a')
    aDom.style.display = 'none'
    aDom.href = downloadUrl
    aDom.setAttribute('download', fileName)
    document.body.appendChild(aDom)
    aDom.click()
    document.body.removeChild(aDom)
  }

  useAsyncEffect(async () => {
    if (visible === true) {
      state.current = 0
      state.selected = []
      state.errorMap = {}
      state.downloadUrl = ''
      state.loading = false
      state.needCDN = false
      state.fileName = ''
      state.tableKey = Math.random().toString()
      if (isDefine) {
        state.list = await Service.ComponentDefine.findAll({
          limit: 1000,
          attributes: ['id', 'key', 'title', 'device', 'loadMode', 'updatedAt'],
          order: { updatedAt: 'DESC' }
        })
      } else {
        state.list = await Service.Screen.findAll({
          limit: 2000,
          where: { projectId },
          attributes: ['id', 'title', 'type'],
          order: { createdAt: 'ASC' }
        })
      }
    }
  }, [visible, isDefine])

  const render1 = () => (
    <div>
      {isDefine &&
        <Tooltip title='除了第一次导入，一般不需要勾选，CDN 导出包会大很多' placement='right'>
          <span className='cdn-check'>
            <Checkbox checked={state.needCDN} onChange={e => state.needCDN = e.target.checked}>
              是否包含 CDN
            </Checkbox>
          </span>
        </Tooltip>
      }
      <MyTable
        key={state.tableKey}
        list={list}
        selected={state.selected}
        onSelect={ids => (state.selected = ids)}
        columns={isDefine ? defineColumns : projectColumns}
        rowKey='id'
      />
    </div>
  )

  const render2 = () => (
    <div>
      {loading && (
        <div className='message-panel'>
          <div>
            <LoadingOutlined className='mr-2' />
            导出资源中...
          </div>
        </div>
      )}
      {downloadUrl && !loading && (
        <div className='message-panel success'>
          <h4>
            导出成功，点击下面链接下载资源包，其中成功 {successCount} 项，失败 {failCount} 项
          </h4>
          <a href='#!' onClick={onDownLoad}>
            资源包.tar
          </a>
        </div>
      )}
      {!_.isEmpty(errorMap) && !loading && (
        <ol className='message-panel error'>
          <h4>导出错误项：</h4>
          {_.values(errorMap).map((i, index) => (
            <li key={i.key + index}>
              {i.title}：<span className='msg-text'>{i.error}</span>
            </li>
          ))}
        </ol>
      )}
    </div>
  )

  return (
    <Modal
      title={isDefine ? '导出组件定义' : '导出项目页面'}
      open={visible}
      width={720}
      okButtonProps={{ disabled: count === 0 || loading }}
      className='component-define-export-modal'
      okText={current === 0 ? `导出 ${count} 个` : '关闭'}
      cancelText={current === 0 ? '取消' : '上一步'}
      maskClosable={false}
      onOk={onOk}
      onCancel={() => {
        if (current === 1) state.current = 0
        else modal.hide()
      }}
    >
      <div>
        <Steps current={current}>
          <Step title='选择导出' description={isDefine ? '选择导出的组件定义' : '选择导出的项目页面'} />
          <Step title='下载资源' description='下载导出的资源包' />
        </Steps>

        {current === 0 && render1()}
        {current === 1 && render2()}
      </div>
    </Modal>
  )
})

export default ExportModal
