import './export-modal.less'

import { InboxOutlined, SwapRightOutlined } from '@ant-design/icons'
import { useReactive } from 'ahooks'
import { Input, message, Steps, Upload } from 'antd'
import isEqual from 'fast-deep-equal'
import isJson from 'is-json'
import _ from 'lodash'
import React, { useEffect } from 'react'

import Modal from '@/components/customs/custom-modal'
import withRefModal from '@/components/with-ref-modal'
import { DS_PAGE_NAME } from '@/consts/dataset'
import { useDataSourcePicker } from '@/pages/screen/components/workbench/data-config/data-source-picker'
import { Service } from '@/services'
import { FileService } from '@/services/files'
import { useCommit as useDataSourceCommit, useModelState as useDataSourceModelState } from '@/stores/models/data-source'

import { defineColumns, projectColumns } from './const'
import MyTable from './my-table'

const { Step } = Steps
const { <PERSON>agger } = Upload

export interface ImportModalProps {
  type: 'define' | 'project'
  projectId: string
  onImportComplete: () => any
}

type DataSourceType = {
  tableId: string
  type: string
  tableName: string // 中文名
  dbName: string // 表名
  dbId?: string
  dbType?: string
}

export interface ImportModalState {
  current: number
  file?: File
  fileList: any[]
  uploadConfig?: {
    projectId: string
    keys: string[] // 导入的 keys 数组（key 是自动生成的，目前直接用 screen），对应的是 screenId
    // 旧 id <=> 新的信息
    dataSourceMap?: Record<string, DataSourceType>
  }
  mateData: any
  selected: string[]
  importLoading: boolean
  importSuccess: boolean
  dataSourceNothingnes?: DataSourceType[]
  dataSourceMap: Record<string, any> // 旧 id => 新的数据源配置
  disabled: boolean
  result: any
  okText: string
}

export interface DataSourceSelectProps {
  value: any
  onChange: (value: any) => any
}

// 数据源选择组件
const DataSourceSelect = (props: DataSourceSelectProps) => {
  const { value, onChange } = props
  const dataSourceCommit = useDataSourceCommit()
  const dataSourcePickerInfo = useDataSourceModelState(s => s, isEqual)
  const loadMoreDataSourceInfo = (type: string, id?: string) => dataSourceCommit('loadMoreDataSourceInfo', { type, id })

  const dataSourcePickerPanel = useDataSourcePicker({
    dataSourcePickerInfo,
    loadMoreDataSourceInfo,
    value: value || { dataSourceType: 'dataCenter' },
    onChange,
    recordLastDataLoadConfig: false
  })

  return dataSourcePickerPanel
}

const TYPE_MAP = {
  dataset: DS_PAGE_NAME,
  indicesTable: '指标库',
  dataCenter: '数据中心'
}

const defaultState: ImportModalState = {
  current: 0,
  file: undefined,
  fileList: [],
  uploadConfig: undefined,
  mateData: {}, // { success: [] }
  selected: [], // 放 keys
  importLoading: false,
  importSuccess: false,
  dataSourceNothingnes: undefined,
  dataSourceMap: {},
  disabled: true,
  result: undefined,
  okText: '下一步'
}

const ImportModal = withRefModal<Partial<ImportModalProps>>(props => {
  const { visible, modal, type, projectId, onImportComplete } = props

  const state = useReactive<ImportModalState>(_.cloneDeep(defaultState))

  const { current, selected, mateData, dataSourceNothingnes } = state
  const count = selected.length
  const isDefine = type === 'define'
  const isScreen = type === 'project'
  const cloudFunctionName: any = isDefine ? 'importComponentDefine' : 'importProject'

  // 上传完成后，进行压缩包解析
  const onUploaded = async (uploadConfig: ImportModalState['uploadConfig']) => {
    const data = await Service.$execute(cloudFunctionName, { type: 'parse', uploadConfig })
    if (!data) {
      state.mateData = undefined
      message.error('解析资源包失败！')
      return
    }
    // 判断是否存在不匹配的数据源
    state.mateData = data
    state.disabled = false
  }

  const customRequest = async options => {
    const { onSuccess, onError, file } = options
    state.file = file
    try {
      const uploadConfig = await FileService.upload(file)
      if (uploadConfig) {
        state.uploadConfig = uploadConfig
        onSuccess(uploadConfig)
        onUploaded(uploadConfig)
      } else {
        onError(new Error('上传失败'))
      }
    } catch (err) {
      onError(err)
    }
  }

  // 获取数据源映射关系的缓存
  const getDataSourceMapCache = async (tableIds: string[]) => {
    const dict = {}
    if (tableIds.length === 0) return dict
    const res = await Service.$redis.hmget('data-source-map', tableIds)
    _.forEach(res, (json, index) => {
      if (json && isJson(json)) {
        dict[tableIds[index]] = JSON.parse(json)
      }
    })
    return dict
  }

  const setDataSourceMapCache = async () => {
    if (!_.isEmpty(state.dataSourceMap)) {
      const str = _.mapValues(state.dataSourceMap, val => JSON.stringify(val)) || '[]'
      await Service.$redis.hmset('data-source-map', str)
    }
  }

  // 导入
  const onImport = async () => {
    if (count === 0) return
    state.importLoading = true
    try {
      const params = {
        type: 'import',
        uploadConfig: state.uploadConfig,
        importConfig: {
          projectId,
          keys: state.selected,
          dataSourceMap: _.mapValues(state.dataSourceMap, val => ({
            dataSourceType: val.dataSourceType,
            [val.dataSourceType]: val[val.dataSourceType]
          }))
        }
      }
      await setDataSourceMapCache()
      const res = await Service.$execute(cloudFunctionName, params)
      if (_.isArray(res)) {
        state.importSuccess = true
        state.result = res
        state.okText = '确定'
      }
      state.okText = '关闭'
      onImportComplete?.()
    } finally {
      state.importLoading = false
      // modal.hide()
    }
  }

  // 如果是页面导入，则需要检查数据源映射
  const onCheckDataSource = async () => {
    const tables = _.values(state.mateData?.datasource || {}) // 取旧的数据源表
    const tableInfo = await Service.$execute('datasource.getTableInfo', tables)
    const mapCache = await getDataSourceMapCache(tables.map(i => i.tableId))
    // diff，匹配出不存在本系统的
    const nothingness = tables.filter(i => !tableInfo[i.tableId] && i.tableId) // tableId 必须存在
    if (!_.isEmpty(nothingness)) {
      // 有不存在的值
      state.dataSourceNothingnes = nothingness
      state.dataSourceMap = mapCache || {}
      state.disabled = _.size(state.dataSourceMap) !== _.size(state.dataSourceNothingnes)
    } else {
      state.disabled = false
      onImport()
    }
  }

  useEffect(() => {
    if (visible) {
      _.forEach(defaultState, (val, key) => {
        state[key] = val
      })
    }
  }, [visible])

  const renderUpload = () => (
    <Dragger
      name='file'
      multiple={false}
      accept='.tar'
      maxCount={1}
      fileList={state.fileList || []}
      customRequest={customRequest}
      onChange={({ fileList }) => state.fileList = fileList}
    >
      <p className='ant-upload-drag-icon'>
        <InboxOutlined />
      </p>
      <p className='ant-upload-text'>单击或将文件拖动到此区域以上载资源包 .tar</p>
      {!isDefine && <p>导入页面，请先确保已导入相关依赖的组件定义表</p>}
    </Dragger>
  )

  // 导入时映射数据源关系
  const renderDataSouce = () => (
    <div className='data-source-map'>
      <p>部分数据源未匹配，请手动设置数据表映射绑定：</p>
      <div>
        <div className='data-source-map-item'>
          <h4>源数据表</h4>
          <h4>目标数据表</h4>
        </div>
        {_.map(state.dataSourceNothingnes, i => (
          <div className='data-source-map-item' key={i.tableId}>
            <Input
              value={`${i.tableName}（${i.dbName}）`}
              title={`${i.tableName}（${i.dbName}）`}
              readOnly
              placeholder='表名称'
              addonBefore={i.dbType || TYPE_MAP[i.type]}
            />
            <SwapRightOutlined />
            <DataSourceSelect
              key={i.tableId}
              value={state.dataSourceMap[i.tableId]}
              onChange={val => {
                state.dataSourceMap[i.tableId] = val
                state.disabled = _.size(state.dataSourceMap) !== _.size(state.dataSourceNothingnes)
              }}
            />
          </div>
        ))}
      </div>
    </div>
  )

  const renderTable = () => (
    <div>
      {isDefine && <p className='tip'>注：导入存在的 key 会进行覆盖操作，反之就是创建。</p>}
      {isScreen && !dataSourceNothingnes && <p className='tip'>注：导入页面后，不同环境的数据源，需要配置数据源映射关系。</p>}
      {state.dataSourceNothingnes ?
        renderDataSouce() :
        <MyTable
          rowKey='key'
          columns={isDefine ? defineColumns : projectColumns}
          list={mateData?.success || []}
          onSelect={ids => {
            state.disabled = ids.length <= 0
            state.selected = ids
          }}
          selected={state.selected}
          key={String(visible)}
        />
      }
    </div>
  )

  const renderResult = () => state.mateData && (
    <div>
      {state.importSuccess && (
        <div className='message-panel success'>
          成功完成导入：{_.size(state.result)} 个
          <ol className='mb-0 mt-2'>
            {_.map(state.result, (i, index) => (
              <li key={i.key + index}>{i.title}，{i.key}</li>
            ))}
          </ol>
        </div>
      )}
      {!state.importSuccess && renderTable()}
    </div>
  )

  const onOk = () => {
    if (current === 0) {
      state.current = 1
      state.okText = '导入'
      state.disabled = true
    }
    if (current === 1) {
      if (!state.importSuccess) {
        if (isScreen && !dataSourceNothingnes) {
          return onCheckDataSource() // 这里结束后会调用 import
        }
        return onImport()
      }
      // 已经导入完成，点确定关闭
      modal.hide()
    }
  }

  return (
    <Modal
      title={isDefine ? '导入组件定义' : '导入项目页面'}
      open={visible}
      width={720}
      maskClosable={false}
      className='component-define-export-modal'
      okButtonProps={{
        disabled: state.disabled,
        loading: state.importLoading
      }}
      okText={state.okText}
      cancelText={current === 0 ? '取消' : '关闭'}
      onOk={onOk}
      onCancel={() => modal.hide()}
    >
      <div>
        <Steps current={current}>
          <Step title='上传资源' description={`选择导出${isDefine ? '组件定义' : '项目页面'}的资源包`} />
          <Step title='导入' description={`选择导入的${isDefine ? '组件定义' : '页面'}`} />
        </Steps>

        {current === 0 && renderUpload()}
        {current === 1 && renderResult()}
        {current === 2 && isScreen && !!dataSourceNothingnes && renderDataSouce()}
      </div>
    </Modal>
  )
})

export default ImportModal
