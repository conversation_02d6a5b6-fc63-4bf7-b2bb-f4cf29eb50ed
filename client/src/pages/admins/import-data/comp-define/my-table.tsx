import { Checkbox, Input,Table } from 'antd'
import React, { useMemo, useState } from 'react'

export interface MyTableProps {
  list: { title: string; [key: string]: any }[]
  selected: string[]
  onSelect: (ids: string[]) => any
  columns: any[]
  rowKey: string
}

/**
 * 封装的 table
 */
export default function MyTable(props: MyTableProps) {
  const { list, selected, onSelect, columns, rowKey } = props
  const count = selected.length
  const [currentDataSource, setCurrentDataSource] = useState<undefined | any[]>(undefined)

  const [keyword, setKeyword] = useState('')

  const dataSource = useMemo(() => {
    if (keyword) return list.filter(i => i.title.toLocaleLowerCase().indexOf(keyword.toLocaleLowerCase()) > -1)
    return list
  }, [list, keyword])

  return (
    <div>
      <header className='flex items-center mb-2 pl-2'>
        <Checkbox
          checked={count === dataSource.length}
          indeterminate={count < dataSource.length && count > 0}
          onClick={() => {
            const arr = currentDataSource || dataSource || []
            onSelect(count > 0 ? [] : arr.map(i => i[rowKey]).slice(0, 200))
          }}
        >
          全选/全不选（最多 200 项）
        </Checkbox>
        <span className='flex-1'>已选中：{count} 个</span>
        <Input.Search size='small' placeholder='输入关键字' onSearch={setKeyword} style={{ width: 200 }} allowClear />
      </header>
      <Table
        dataSource={dataSource}
        columns={columns}
        rowKey={i => i[rowKey]}
        size='small'
        pagination={{
          showTotal: () => `共 ${currentDataSource ? currentDataSource.length : dataSource.length} 个`
        }}
        onChange={(...arg) => {
          setCurrentDataSource(arg[3].currentDataSource)
        }}
        rowSelection={{
          type: 'checkbox',
          selectedRowKeys: selected,
          onChange: ids => onSelect(ids.slice(0, 200) as string[])
        }}
      />
    </div>
  )
}
