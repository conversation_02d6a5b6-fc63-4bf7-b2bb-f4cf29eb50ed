import './index.less'

import { Button } from 'antd'
import React, { useRef } from 'react'

import DefineExportModal from './comp-define/export-modal'
import DefineImportModal from './comp-define/import-modal'

/**
 * 数据导入导出
 * @returns
 */
export default function ImportData() {
  const defineExportRef = useRef<ModalDefaultRef>(null)
  const defineImportRef = useRef<ModalDefaultRef>(null)

  return (
    <div className='import-data-page'>
      <div>
        <h3>如果你的页面组件有差异性，先迁移组件定义表，再迁移页面应用数据</h3>
        <div className='box-panel'>
          <h3>组件定义表</h3>
          <div>
            <Button className='mr-2' size='small' onClick={() => defineExportRef.current?.show({ type: 'define' })}>
              导出
            </Button>
            <Button size='small' onClick={() => defineImportRef.current?.show({ type: 'define' })}>
              导入
            </Button>
          </div>
        </div>
        <div className='box-panel'>
          <h3>项目应用数据</h3>
          <div>
            <Button className='mr-2' size='small' onClick={() => defineExportRef.current?.show({})}>
              导出
            </Button>
            <Button size='small' onClick={() => defineImportRef.current?.show({})}>
              导入
            </Button>
          </div>
        </div>
      </div>

      <DefineImportModal ref={defineImportRef} />
      <DefineExportModal ref={defineExportRef} />
    </div>
  )
}
