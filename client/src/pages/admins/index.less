@width: 208px;

.admin-page {
  position: relative;
  height: calc(100vh - 50px);
  max-height: calc(100vh - 50px);

  .menus {
    position: absolute;
    left: 0;
    width: @width;
    padding: 16px 0;
    z-index: 10;
    height: 100%;
    border-right: 1px solid #f1f1f1;
    background-color: #393939;
    box-shadow: 1px 2px 16px rgba(#111, 0.1);

    > a {
      display: block;
      font-size: 15px;
      border-bottom: 1px solid rgba(#111, 0.1);
      padding: 8px 20px;
      color: rgba(#fff, 0.9);
      transition: none;
      height: 42px;

      display: flex;
      align-items: center;
      opacity: 0.92;

      .anticon {
        margin-right: 8px;
        font-size: 17px;
      }

      &.active {
        color: tint(@primary-color, 70%);
        background-color: rgba(lighten(@primary-color, 2%), 0.12);
        font-weight: bold;
        opacity: 1;
      }
      &:hover {
        color: lighten(@primary-color, 6%);
        background-color: rgba(#111, 0.1);
        opacity: 1;
      }
    }
  }

  .content-tabs {
    margin-left: @width;
    height: 100%;
    padding: 16px;

    .ant-tabs-content-holder {
      border: none;
    }

    .ant-tabs-tabpane,
    .ant-tabs-content {
      height: 100%;
      padding: 0 !important;
    }
  }
}
