import './index.less'

import { AppstoreAddOutlined,DesktopOutlined, FundProjectionScreenOutlined } from '@ant-design/icons'
import { Link } from '@umijs/max'
import { Tabs } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React, { useMemo, useState } from 'react'

import { useQueryParams } from '@/hooks/use-query-params'

import ComponentDefine from './component-define'
import ImportData from './import-data'

// 配置的路由表
const tabs = [
  {
    link: 'index',
    title: '仪表盘',
    Component: () => <div>请选择一个路由</div>,
    Icon: DesktopOutlined
  },
  // {
  //   link: 'data-table',
  //   title: '数据表管理',
  //   Component: () => <div>xxx</div>,
  //   Icon: DatabaseOutlined
  // },
  {
    link: 'component-define',
    title: '组件定义维护',
    Component: ComponentDefine,
    Icon: AppstoreAddOutlined
  },
  {
    link: 'project-transfer',
    title: '项目数据迁移',
    Component: ImportData,
    Icon: FundProjectionScreenOutlined
  }
  // {
  //   link: 'monitor-logger',
  //   title: '前端监控日志',
  //   Component: () => <div>xxx</div>,
  //   Icon: AlertOutlined
  // }
]

/**
 * 后台管理页面入口
 * @returns
 */
export default function Admin() {
  // const match = useMatch({ path: '/back-manage/:key' })
  const query = useQueryParams()
  const [activeKey, setActiveKey] = useState(_.get(query, 'page', 'index') as string)
  const menus = useMemo(() => tabs.map(i => ({ title: i.title, link: i.link, Icon: i.Icon })), [])

  return (
    <div className='admin-page'>
      <div className='menus'>
        {menus.map(i => (
          <Link
            to={`/admin-manage?page=${i.link}`}
            key={i.link}
            className={cn({ 'active': i.link === activeKey })}
            onClick={() => setActiveKey(i.link)}
          >
            {i.Icon && <i.Icon />}
            {i.title}
          </Link>
        ))}
      </div>

      <Tabs
        activeKey={activeKey}
        tabPosition='left'
        renderTabBar={() => <div />}
        className='content-tabs'
        animated={false}
      >
        {tabs.map(i => (
          <Tabs.TabPane key={i.link} tab={i.title} tabKey={i.link}>
            {i.Component && <i.Component />}
          </Tabs.TabPane>
        ))}
      </Tabs>
    </div>
  )
}
