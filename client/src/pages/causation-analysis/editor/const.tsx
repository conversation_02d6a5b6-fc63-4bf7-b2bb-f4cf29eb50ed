import {
  AreaChartOutlined,
  Bar<PERSON><PERSON>Outlined,
  BgColorsOutlined, BlockOutlined,
  ClearOutlined, ConsoleSqlOutlined,
  CopyOutlined, DeleteOutlined, DeploymentUnitOutlined,
  DotChartOutlined,
  FieldBinaryOutlined,
  LineChartOutlined,
  NumberOutlined,
  PercentageOutlined, Pie<PERSON><PERSON>Outlined,
  PlusOutlined, RedoOutlined,
  RetweetOutlined,
  RiseOutlined,
  SettingOutlined, SnippetsOutlined,
  TableOutlined,
  TagsOutlined
} from '@ant-design/icons'
import _ from 'lodash'
import React, { CSSProperties } from 'react'

export const QUERY_TYPE_OPTS = [
  { label: '我的指标', min: '指标', value: 'indices', icon: <RiseOutlined /> },
  { label: '数据库表', min: '库表', value: 'datatable', icon: <TableOutlined /> },
  { label: '数据集合', min: '集合', value: 'mydataset', icon: <BlockOutlined /> },
  { label: '数据视图', min: '视图', value: 'dataview', icon: <ConsoleSqlOutlined /> },
  { label: '数据模型', min: '模型', value: 'tablemodel', icon: <DeploymentUnitOutlined /> }
  // { label: '用户标签', min: '标签', value: 'usertags', icon: <TagsOutlined /> },
  // { label: '单图卡片', min: '单图', value: 'slice', icon: <BarChartOutlined /> }
]

export const QUERY_TYPE_MAP = _.keyBy(QUERY_TYPE_OPTS, 'value')
