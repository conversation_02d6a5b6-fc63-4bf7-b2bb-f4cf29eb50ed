/* eslint-disable react/no-children-prop */
import './add-value-field-modal.less'

import { FieldDataIcon, MultSelect, TimeRangeSelectProPopover, TreeLayerSelect, TreeRelateSelect } from '@sugo/design'
import { translateTimeRelativeRangeSelect } from '@sugo/design/dist/esm/components/time-relative-range-select/utils'
import { fastMemo, indiceGroupRelateLogicFn, withRefModal } from '@sugo/design/functions'
import { useMemoizedFn } from 'ahooks'
import { Col, Form, Input, Modal, Row, Select } from 'antd'
import _ from 'lodash'
import React, { useEffect, useMemo } from 'react'

import { QUERY_TYPE_OPTS } from '@/pages/causation-analysis/editor/const'
import { useAction, useCompute, useStore } from '@/pages/causation-analysis/editor/reactive'

export interface AddValueFieldProps {
  queryType?: string
  onSubmit?: any
  editData?: any
}

/**
 * 添加字段
 * @returns
 */
const _AddValueField = withRefModal<AddValueFieldProps>(props => {
  const { modal, visible, onSubmit, editData } = props

  const isEdit = !!editData
  const [form] = Form.useForm()
  const initialValues = {
    queryType: 'datatable',
    currentPeriodTime: { direction: 'recent', count: 1, unit: 'month' },
    previousPeriodTime: 'mom'
  }

  const queryType = useStore(s => s.queryType)
  const dataSourceTree = useCompute(s => s.dataSourceTree)
  const fieldList = useCompute(s => s.fieldList)

  const { indicesGroupTree, indicesList, indicesLoading } = useStore(s => ({
    indicesGroupTree: s.indicesGroupTree,
    indicesList: _.values(s.indicesMap),
    indicesLoading: s.indicesLoading
  }))

  const { dimensionFields, timeFields, valueFields } = useMemo(() => {
    const v1 = _.filter(fieldList, f => f.dataType === 'number').map(i => ({ ...i, label: i.title, value: i.id, mark: i.name }))
    const v2 = _.filter(fieldList, f => f.dataType === 'string').map(i => ({ ...i, label: i.title, value: i.id, mark: i.name }))
    const v3 = _.filter(fieldList, f => f.dataType === 'date').map(i => ({ ...i, label: i.title, value: i.id, mark: i.name }))

    return {
      valueFields: v1,
      dimensionFields: v2,
      timeFields: v3
    }
  }, [fieldList])

  const previousPeriodTimeOpts = [
    { value: 'mom', label: '上期环比' },
    { value: 'yoy', label: '去年同比' }
  ]

  const {
    setQueryType,
    initDataSource,
    loadDataTableItem,
    loadTableModelDetail,
    setSelected,
    loadFieldList,
    loadIndiceList
  } = useAction()

  const onCancel = () => {
    modal.hide()

    const dataSouceSelected = form.getFieldValue('dataSouceSelected')
    const timeFieldId = form.getFieldValue('timeFieldId')
    setTimeout(() => {
      form.resetFields()
      form.setFieldValue('queryType', queryType)
      form.setFieldValue('dataSouceSelected', dataSouceSelected)
      form.setFieldValue('timeFieldId', timeFieldId)
    }, 300)
  }

  const changeReset = () => {
    form.setFieldValue('followDimensions', undefined)
    form.setFieldValue('ignoreDimensions', undefined)
    form.setFieldValue('timeFieldId', undefined)
    form.setFieldValue('fieldId', undefined)
  }

  const onNodeClick = useMemoizedFn(async (node, info) => {
    info.event?.stopPropagation()

    if (node.isLeaf) {
      setSelected(node)
      changeReset()
    }
  })

  /** 异步加载内容 */
  const onLoadData = useMemoizedFn(async node => {
    if (node.type === 'tableModel' && _.isEmpty(node.children)) {
      await loadTableModelDetail(node.id)
    }
    // if (node.type === 'tag-project' && _.isEmpty(node.children)) {
    //   await loadTagGroups(node.id)
    // }
    // if (node.type === 'tag-group' && _.isEmpty(node.children)) {
    //   await loadTagList(node.id, node.projectId)
    // }
    if (node.type === 'db' && _.isEmpty(node.children)) {
      await loadDataTableItem(node.id)
    }
  })

  const onOk = async () => {

    await form.validateFields()
    const data = form.getFieldsValue()
    if (isEdit) data.key = editData.key
    onSubmit?.(data, _.keyBy(fieldList, 'id'))
    onCancel()
  }

  const renderTimeChildren = useMemoizedFn((_v, text) => (
    <Input readOnly value={text} placeholder='请选择时间' style={{ cursor: 'pointer', width: 300 }} />
  ))

  const selectQueryType = useMemoizedFn(v => {
    setQueryType(v)
    // 加载指标列表
    if (v === 'indices') loadIndiceList()
    form.setFieldValue('dataSouceSelected', undefined)
    changeReset()
  })

  const autoSelectTimeField = useMemoizedFn((val, info) => {
    info = _.isArray(info) ? info[0] : info
    if (val && info) {

      if (queryType === 'indices') {
        setSelected({ ..._.omit(info, ['categorys', 'description']), key: val })
      }

      const title = info.title || info.label
      const currentPeriodTime = form.getFieldValue('currentPeriodTime')
      if (currentPeriodTime?.direction) {
        const t = translateTimeRelativeRangeSelect(currentPeriodTime) || ''
        form.setFieldValue('fieldAlias', `${t.replace(/(.*)(（.*）)/, '$1')}_${title}`)
      }

      const time = form.getFieldValue('timeFieldId')
      if (!time && timeFields.length > 0) {
        form.setFieldValue('timeFieldId', timeFields[0].id)
      }
    }
  })

  useEffect(() => {
    if (visible && queryType) {
      initDataSource()
      form.setFieldValue('queryType', queryType)
      // form.setFieldValue('queryType', sle)
    }
  }, [visible, queryType])

  useEffect(() => {
    if (visible && editData) {
      form.setFieldValue('queryType', editData.queryType)
      form.setFieldValue('dataSouceSelected', editData.dataSouceSelected)
      form.setFieldValue('fieldId', editData.field.id)
      form.setFieldValue('fieldAlias', editData.field.alias)
      form.setFieldValue('timeFieldId', editData.timeField.id)
      form.setFieldValue('currentPeriodTime', editData.currentPeriodTime)
      form.setFieldValue('previousPeriodTime', editData.previousPeriodTime)
      form.setFieldValue('targetValue', editData.targetValue)
      form.setFieldValue('followDimensions', editData.followDimensions)
      form.setFieldValue('ignoreDimensions', editData.ignoreDimensions)

      const node = editData.tableInfo
      if (editData.queryType === 'indices') {
        loadIndiceList()
      } else {
        loadFieldList(node.id, node.dbId, node.connectId, node.connectType, editData.queryType, node)
      }
    }
  }, [visible, editData])

  return (
    <Modal
      title={isEdit ? '编辑指标字段' : '添加指标字段'}
      open={visible}
      onCancel={onCancel}
      onOk={onOk}
      width={680}
      className='causation-analysis-editor-add-value-field-modal'
      maskClosable={false}
    >
      <Form form={form} layout='vertical' initialValues={initialValues}>

        <Row>
          <Col span={8}>
            <Form.Item label='数据来源' name='queryType'>
              <Select onChange={selectQueryType}>
                {_.map(QUERY_TYPE_OPTS, opt => (
                  <Select.Option key={opt.value} value={opt.value}>
                    {opt.icon}
                    <span className='ml-2'>{opt.label}</span>
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          {queryType !== 'indices' &&
            <Col span={16}>
              <Form.Item label='库表' name='dataSouceSelected'>
                <TreeLayerSelect
                  key={queryType}
                  treeData={dataSourceTree}
                  onLoadData={onLoadData}
                  onNodeClick={onNodeClick}
                />
              </Form.Item>
            </Col>
          }
        </Row>

        <Row>
          <Col span={12}>
            <Form.Item
              label='演算指标字段'
              name='fieldId'
              rules={[{ required: true, message: '请选择字段' }]}
            >
              {queryType !== 'indices' ?
                <MultSelect
                  mode='single' options={valueFields} placeholder='请选择字段'
                  renderIcon={node => <FieldDataIcon dataType={node.dataType || 'number'} />}
                  onChange={autoSelectTimeField}
                  panelStyle={{ width: 300 }}
                /> :
                <TreeRelateSelect
                  listData={indicesList}
                  loading={indicesLoading}
                  treeData={indicesGroupTree}
                  treeDefaultKey='ALL'
                  listPlaceholder='先选中分组'
                  treeTitle='指标分类'
                  listTitle='指标列表'
                  mode='single'
                  onChange={autoSelectTimeField}
                  // value={_.keys(selectedTableMap)}
                  relateLogicFn={indiceGroupRelateLogicFn}
                  // listItemEventsFn={listItemEventsFn}
                  // onListItemClick={onListItemClick}
                  searchHistoryKey='indices-select-pro'
                  selectHistoryKey='indices-select-pro'
                  // onHistoryClick={onHistoryClick}
                  // style={{ height: 375 }}
                  isIndiceData
                  placeholder='请选择指标'
                />
              }
            </Form.Item>


          </Col>
          <Col span={12}>
            <Form.Item
              label='描述名称'
              name='fieldAlias'
              rules={[{ required: true, message: '请输入描述名称' }]}
            >
              <Input placeholder='请输入字段描述名称' />
            </Form.Item>
          </Col>
        </Row>

        <Row>
          <Col span={12}>
            <Form.Item
              label='时间字段'
              name='timeFieldId'
              rules={[{ required: true, message: '请选择时间字段' }]}
            >
              <MultSelect
                mode='single' options={timeFields} placeholder='请选择字段'
                renderIcon={node => <FieldDataIcon dataType={node.dataType || 'date'} />}
                panelStyle={{ width: 300 }}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row>
          <Col span={12}>
            <Form.Item
              label='本期时间'
              name='currentPeriodTime'
              rules={[{ required: true, message: '请选择时间' }]}
            >
              <TimeRangeSelectProPopover
                defaultActiveKey='dynamics'
                children={renderTimeChildren}
                onChange={val => {
                  const currentPeriodTime = val
                  const fid = form.getFieldValue('fieldId')
                  if (fid && currentPeriodTime?.direction) {
                    const field = _.find(valueFields, i => i.id === fid)
                    const t = translateTimeRelativeRangeSelect(currentPeriodTime) || ''
                    if (field) {
                      form.setFieldValue('fieldAlias', `${t.replace(/(.*)(（.*）)/, '$1')}_${field?.title}`)
                    }
                  }
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label='基期时间'
              name='previousPeriodTime'
              rules={[{ required: true, message: '请选择时间' }]}
            >
              <Select options={previousPeriodTimeOpts} allowClear={false} />
            </Form.Item>
          </Col>
        </Row>

        <Row>
          <Col span={12}>
            <Form.Item label='关注维度' name='followDimensions'>
              <MultSelect
                options={dimensionFields} placeholder='请选择字段'
                renderIcon={node => <FieldDataIcon dataType={node.dataType || 'string'} />}
                changeMode='change'
                panelStyle={{ width: 300 }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='忽略维度' name='ignoreDimensions'>
              <MultSelect
                options={dimensionFields} placeholder='请选择字段'
                renderIcon={node => <FieldDataIcon dataType={node.dataType || 'string'} />}
                changeMode='change'
                panelStyle={{ width: 300 }}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row>
          <Col span={12}>
            <Form.Item
              label='目标值'
              name='targetValue'
            // rules={[{ required: true, message: '请输入目标值' }]}
            >
              <Input placeholder='请输入，如：1000(固定值) 或 120%(较基期)' />
            </Form.Item>
          </Col>
        </Row>

        <Row>
          <Col span={12}>
            <Form.Item
              label='异常诊断'
              name='unusualValue'
            // rules={[{ required: true, message: '请输入目标值' }]}
            >
              <Input placeholder='请输入，如：< 50%(较基期)' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label='异常通知'
              name='unusualNotify'
            >
              <Input placeholder='请选择通知通道' disabled />
            </Form.Item>
          </Col>
        </Row>

      </Form>
    </Modal>
  )
})

export const AddValueFieldModal = fastMemo(_AddValueField)
