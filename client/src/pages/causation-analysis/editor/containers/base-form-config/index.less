
.causation-analysis-editor-base-form-config {
  margin: 0 auto;
  width: 600px;
  // border-top: none;
  // border-bottom: none;
  min-height: 80%;
  border-radius: 4px;
  padding: 16px;
  overflow-y: auto;
  background-color: #fff;
  border: 1px solid #eee;

  &:hover {
    box-shadow: 0 0 6px rgba(@primary-color, 20%);
  }

  .ant-form-item-with-help
  .ant-form-item-explain {
    transition: none !important;
    > div {
      transition: none !important;
    }
  }

  .value-field-list {

    .value-field-box {
      // display: none;
      margin-bottom: 12px;
      border: 1px solid #eee;
      border-radius: 4px;
      padding: 8px 12px;
      position: relative;

      &:hover {
        background-color: rgba(@primary-color, 4%);
        border-color: rgba(@primary-color, 50%);
      }
    }

    .query-type-tag {
      position: absolute;
      right: 8px;
      top: 8px;
      z-index: 10;
    }

    .title {
      color: #444;
    }

    .alias {
      color: #aaa;
      margin-left: 2px;
    }

    .footer {
      padding-top: 5px;
      border-top: 1px solid #f1f1f1;
      margin-top: 5px;
    }
  }

  .add-btn {
    margin-left: 8px;
  }
}
