import './index.less'

import { DeleteOutlined, EditOutlined, PlusOutlined, RiseOutlined } from '@ant-design/icons'
import { Tag } from '@sugo/design'
import { fastMemo } from '@sugo/design/functions'
import { useDeepCompareEffect } from 'ahooks'
import { Button, Divider, Form, Input, Switch, Tooltip } from 'antd'
import _ from 'lodash'
import React, { useRef, useState } from 'react'

import { QUERY_TYPE_MAP } from '@/pages/causation-analysis/editor/const'
import { useAction, useCompute, useStore } from '@/pages/causation-analysis/editor/reactive'
import { transformTimeRange } from '@/pages/causation-analysis/editor/utils'

import { AddValueFieldModal } from './add-value-field-modal'


/**
 * 自定义组件
 * @returns
 */
function _BaseFormConfig() {
  const [form] = Form.useForm()

  const [enableCron, setEnableCron] = useState(false)
  const addValueFieldModalRef = useRef<{ show: Function }>()
  const initialValues = { title: undefined }
  const valueFieldList = useCompute(s => s.valueFieldList)

  const state = useStore(s => ({
    title: s.title
  }))

  const {
    addFormValueField, setCausationTitle,
    setSelected, setQueryType, delFormValueField
  } = useAction()

  const renderItem = item => (
    <div key={item.key} className='value-field-box'>

      <Tag className='query-type-tag' text={QUERY_TYPE_MAP[item.queryType]?.label || ''} autoColor bordered={false} size='small' />

      <div>
        <RiseOutlined className='mr-2' />
        <span className='title'>{item.field.title}</span>
        <span className='alias'>（{item.field.alias}）</span>
      </div>
      <div>
        <span>本期时间：</span>
        {transformTimeRange(item.currentPeriodTime)}
        <br />
        <span>比较时间：</span>
        <Tag
          text={{ mom: '上期环比', yoy: '去年同期' }[String(item.previousPeriodTime)]}
          autoColor bordered={false}
          size='small'
        />
      </div>
      <footer className='footer'>
        <Button
          icon={<EditOutlined />} size='small' type='text' className='mr-2'
          onClick={e => {
            e.stopPropagation()
            setQueryType(item.queryType)
            setSelected(item.tableInfo)
            addValueFieldModalRef.current?.show({
              editData: item,
              onSubmit: addFormValueField
            })
          }}
        >
          编辑
        </Button>
        <Button
          icon={<DeleteOutlined />} size='small' type='text' className='mr-2'
          onClick={e => {
            e.stopPropagation()
            delFormValueField(item.key)
          }}
        >
          删除
        </Button>
      </footer>
    </div>
  )

  useDeepCompareEffect(() => {
    _.forEach(state, (v, k) => {
      form.setFieldValue(k, v)
    })
  }, [state])

  return (
    <div className='causation-analysis-editor-base-form-config'>
      <Form form={form} initialValues={initialValues}>

        <Form.Item label='分析名称' name='title' required>
          <Input
            placeholder='分析名称' allowClear showCount maxLength={30}
            onChange={e => setCausationTitle(e.target.value)}
          />
        </Form.Item>

        <Form.Item
          style={{ marginTop: -14 }}
          label='定时执行' name={['cron', 'enable']} required
          help='开启后，系统会定时执行分析，并自动更新分析结果'
        >
          <Tooltip title='未开放'>
            <Switch
              unCheckedChildren='关闭' checkedChildren='开启'
              onChange={e => setEnableCron(e)}
              disabled
            />
          </Tooltip>
        </Form.Item>

        <Form.Item
          label='增强分析' name={['aiAnswerConfig', 'enable']} required
          help={(
            <div>
              开启后，会通过 AI 大模型对数据结果进行增强分析，帮助用户更深入了解变化。
              {/* 如：
              <li>1、智能数据解读；</li>
              <li>2、智能报告生成；</li>
              <li>3、因果图谱分析；</li>
              <li>4、交叉混合归因。</li> */}
            </div>
          )}
        >
          <Tooltip title='未开放'>
            <Switch
              unCheckedChildren='关闭' checkedChildren='开启'
              onChange={e => setEnableCron(e)}
              disabled
            />
          </Tooltip>
        </Form.Item>

        <Divider type='horizontal' style={{ marginTop: 40, marginBottom: 12 }} />
        <Form.Item label='指标配置' name='title' required style={{ marginBottom: 6 }} />
        <div className='mb-4 pl-2'>添加需要分析的指标/度量字段，最多 10 项</div>

        <div className='value-field-list'>
          {_.map(valueFieldList, renderItem)}
        </div>

        {_.size(valueFieldList) < 10 &&
          <Button className='add-btn' type='primary' size='small' icon={<PlusOutlined />} onClick={e => {
            e.stopPropagation()
            addValueFieldModalRef.current?.show({
              onSubmit: addFormValueField
            })
          }}>添加 </Button>
        }
      </Form>

      <AddValueFieldModal ref={addValueFieldModalRef} />
    </div>
  )
}

export const BaseFormConfig = fastMemo(_BaseFormConfig)
