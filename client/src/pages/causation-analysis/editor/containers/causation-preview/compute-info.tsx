import { fastMemo, getFromNow } from '@sugo/design/functions'
import { Tooltip } from 'antd'
import dayjs from 'dayjs'
import _ from 'lodash'
import React from 'react'

import { useStore } from '@/pages/causation-analysis/editor/reactive'
import { msToText } from '@/utils'

/**
 * 计算信息
 * @returns
 */
function _ComputeInfo() {
  const computeTime = useStore(s => s.queryTaskState.computeTime)
  const computedAt = useStore(s => s.queryTaskState.computedAt)

  if (!computedAt) return null

  return (
    <div className='compute-info flex-row vcenter'>
      <Tooltip title={dayjs(computedAt).format('YYYY-MM-DD HH:mm:ss')} placement='left'>
        <div>数据演算于：{getFromNow(computedAt as string)}，</div>
      </Tooltip>
      <div>耗时：{msToText(computeTime || 0)}</div>
    </div>
  )
}

export const ComputeInfo = fastMemo(_ComputeInfo)
