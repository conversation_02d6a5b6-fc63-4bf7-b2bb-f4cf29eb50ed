
.causation-analysis-editor-chart-dimension-panel {
  margin: 12px 0;

  &.positive {
    .dimension-list {
      background-color: rgba(#ff6688, 12%);
    }
    .top-item-btn.active {
      background-color: darken(#ff6688, 8%);
      color: #fff !important;
    }
    .chart-mode-btn.ant-btn-primary {
      background-color: darken(#ee6688, 4%);
      border-color: darken(#ee6688, 12%);
    }
  }

  &.negative {
    .dimension-list {
      background-color: rgba(#27ceba, 12%);
    }
    .top-item-btn.active {
      background-color: darken(#27ceba, 8%);
      color: #fff !important;
    }
    .chart-mode-btn.ant-btn-primary {
      background-color: darken(#27ceba, 8%);
      border-color: darken(#27ceba, 12%);
    }
  }

  .dimension-list {
    background-color: #f1f1f1;
    display: flex;
    align-items: center;

    .max-change-dimensions {
      display: flex;
      align-items: center;
      margin-left: 10px;

      .top-item-btn {
        // border-radius: 0;
        padding: 4px 8px;
        margin-right: 6px;

        max-width: 120px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        &:hover {
          color: @primary-color;
        }
      }
    }
  }

  .dimension-chart {
    padding-top: 4px;
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;

    .chart-mode-btn {
      margin-right: 8px;
      display: flex;
      align-items: center;
      .anticon + span {
        margin-left: 5px;
      }
      &:last-of-type {
        margin: 0;
      }
    }

    .dimension-chart-view,
    .dimension-data-interpret {
      > header {
        background-color: #fafafa;
        padding: 4px 8px;
        font-weight: bold;
        color: #455;
      }
    }

    .dimension-chart-view {
      flex: 1 !important;
      border-right: 1px solid #f1f1f1;
      min-width: 600px;
      width: max-content;
      overflow-x: hidden;
    }

    .dimension-data-interpret {
      min-width: 240px;
      height: 100%;
      flex: 0.5;
      // min-height: 200px;
      // max-height: 360px;
      // overflow-y: auto;

      &.full {
        flex: 1 !important;
        max-height: none !important;
      }
    }

    .data-interpret-content {
      padding: 12px;
      b {
        color: #444;
        display: inline-block;
      }

      li {
        margin-bottom: 3px;
        line-height: 1.45;
        margin-left: 8px;
      }

      p {
        margin-bottom: 8px;
      }
      i {
        color: #444;
      }

      value {
        color: @primary-color;
      }
      trend {
        color: #dd8800;
      }
      up {
        color: #ee5566;
      }
      down {
        color: #24bdab;
      }
    }
  }

  .select-btn {
    background-color: rgba(#111, 4%);
    padding: 4px 8px;
    border-radius: 0;
    color: #444;
    max-width: 110px;
    min-width: 110px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    .anticon-caret-down {
      opacity: 0.55;
      transform: translateY(2px);
    }
    &:hover {
      background-color: rgba(#111, 8%);
    }
  }

  .design-chart-view.not-data {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.causation-analysis-editor-chart-dimension-overlay {
  .ant-dropdown-menu {
    max-height: 320px;
  }
  .ant-dropdown-menu-item {
    max-width: 240px;
  }
}

