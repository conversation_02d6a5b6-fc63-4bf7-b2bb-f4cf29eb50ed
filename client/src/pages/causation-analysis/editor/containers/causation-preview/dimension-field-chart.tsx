/* eslint-disable no-nested-ternary */
import './dimension-field-chart.less'

import Icon, { CaretDownOutlined, FieldBinaryOutlined, PercentageOutlined, TableOutlined } from '@ant-design/icons'
import { ChartView, DataPreviewTable, FieldDataIcon, MenuDropdown } from '@sugo/design'
import { fastMemo } from '@sugo/design/functions'
import { useSize } from 'ahooks'
import { Button } from 'antd'
import cn from 'classnames'
import copy from 'fast-copy'
import htmr from 'htmr'
import _ from 'lodash'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'

import { ReactComponent as Top1Svg } from '@/pages/causation-analysis/assets/icons/top1.svg'
import { ReactComponent as Top2Svg } from '@/pages/causation-analysis/assets/icons/top2.svg'
import { ReactComponent as Top3Svg } from '@/pages/causation-analysis/assets/icons/top3.svg'
import { useCompute, useStore } from '@/pages/causation-analysis/editor/reactive'
import { inventoryAnalysis } from '@/pages/causation-analysis/editor/utils'
import { formatLargeNumber } from '@/utils'

const topIcons = [
  Top1Svg,
  Top2Svg,
  Top3Svg
]

/**
 * 维度字段图表
 * @returns
 */
function _DimensionFieldChart() {
  const info = useCompute(s => s.getActiveChartValueFieldData('active'))
  const [activeKey, setActiveKey] = useState('ALL')
  const chartUpdateKey = useStore(s => s.chartUpdateKey)

  const domRef = useRef<HTMLDivElement | null>(null)
  const size = useSize(domRef)
  const width = size?.width || 0

  const dimensionDict = useMemo(() => _.keyBy(info.dimensions, i => i.field.id), [info.dimensions])
  const menuItems = useMemo(() => [{
    key: 'ALL',
    label: '全维度观测',
    icon: <FieldDataIcon dataType='none' />
  }].concat(_.map(info.dimensions, i => ({
    key: i.field.id,
    label: i.field.title,
    icon: <FieldDataIcon dataType={i.field.dataType || 'string'} />
  }))), [info.dimensions])


  const activeDimension = dimensionDict[activeKey]
  const deltaValue = _.get(info, 'data.total.deltaValue', 0)
  const direction = useMemo(() => deltaValue > 0 ? 'up' : deltaValue < 0 ? 'down' : 'none', [deltaValue])

  const [chartMode, setChartMode] = useState('changeValue')
  // 有个维度层级不一样导致数值过大，例如省份 > 城市的，因此默认显示比率比较好
  const chartModeOpts = [
    { key: 'changeValue', label: '数值', icon: <FieldBinaryOutlined /> },
    { key: 'changeRate', label: '比率', icon: <PercentageOutlined /> },
    { key: 'table', label: '明细', icon: <TableOutlined /> }
  ]

  const dimensionsDiffMap = useMemo(() => {
    const dict: Record<string, {
      name: string,
      curent: number,
      previous: number,
      rate: number | null,
      value: number,
      dimensionId: string,
      dimensionTitle: string
      direction: number
    }[]> = {}

    _.forEach(info.dimensions, dimension => {
      if (!dimension.field?.id) return

      const total = dimension?.total || {}
      const nameKeys = _.compact(_.uniq(_.concat(total.currentData, total.previousData).map(i => i?.name)))
      const currentDict = _.keyBy(total.currentData, 'name')
      const previousDict = _.keyBy(total.previousData, 'name')
      const list = _.map(nameKeys, k => {
        const obj = {
          name: k,
          curent: currentDict[k]?.value || 0,
          previous: previousDict[k]?.value || 0,
          rate: 0 as number | null,
          value: 0 as number,
          dimensionId: dimension.field.id,
          dimensionTitle: dimension.field.title,
          direction: 0
        }
        obj.rate = _.floor((obj.curent - obj.previous) / obj.previous * 100, 2)
        obj.value = _.floor((obj.curent || 0) - (obj.previous || 0), 2)

        if (!_.isFinite(obj.rate) || _.isNaN(obj.rate)) {
          obj.rate = null
        }
        if (deltaValue * obj.value > 0) obj.direction = 1
        if (deltaValue * obj.value < 0) obj.direction = -1
        if (deltaValue * obj.value === 0) obj.direction = 0

        return obj
      }).filter(i => i.value !== 0 && !_.isNil(i.value))

      dict[dimension.field.id] = _.orderBy(list, 'direction', 'desc') // 按方向排序，同向在前面
    })
    return dict
  }, [info.dimensions, deltaValue])

  const sortByList = (dire, list) => {
    const dir = dire === 'up' ? 'desc' : 'asc'
    return _.orderBy(list, ['direction', 'value', 'rate'], ['desc', dir, dir])
  }

  const trendList = useMemo(() => {
    const list = _.flatMap(_.values(dimensionsDiffMap))
    // 判断方向
    // 找出变动最大值的维成员
    return sortByList(direction, list)
  }, [direction, dimensionsDiffMap])

  // 找出同向变化最大的三个维度
  const maxChangeDimensions = useMemo(() => {
    const arr = trendList
    const arr2 = _.uniqBy(_.map(arr, i => dimensionDict[i.dimensionId]), v => v.field.id)
    return arr2.slice(0, 3)
  }, [trendList, dimensionDict])

  // console.log(dimensionDict)
  const chartData = useMemo(() => {
    if (activeKey === 'ALL') return trendList.slice(0, 10)
    // 计算出变化值，先判断方向
    return sortByList(direction, dimensionsDiffMap[activeKey])
  }, [dimensionsDiffMap, activeKey, trendList, direction])

  // const chartDataUpdateKey = useDeepCompareMemo(() => Math.random().toString(32), [chartData])

  // 数据解读
  const changeDataInterpretText = useMemo(() => {
    // const dir = direction === 'up' ? 'desc' : 'asc'
    // if (!arr[0] || !info?.field) return

    const group = _.groupBy(chartData, 'direction')
    const positives = _.slice(group['1'], 0, 3)
    const negatives = _.slice(group['-1'], -3).reverse()

    const valueTitle = _.get(info, 'field.title', '未知')
    const dimensionTitle = activeDimension?.field?.title || ''
    const dim = dimensionTitle ? `<b>${dimensionTitle}</b>的` : ''
    const title = `<p>${dim} <value>${valueTitle}</value> 变化原因是？</p>`

    return inventoryAnalysis(direction, title, valueTitle, positives, negatives)
  }, [chartData, direction, info, activeDimension])

  const onModifyConfig = useCallback(option => {
    option = { ...copy(option) }

    if (chartMode === 'changeRate') {
      option.data = _.orderBy(option.data, 'rate', direction === 'up' ? 'desc' : 'asc')
    }

    // 使用_.set统一修改配置
    _.set(option, 'xAxis.axisLabel.color', '#999')
    _.set(option, 'yAxis.axisLabel.color', '#666')

    _.set(option, 'series', _.map(option.series, s => {
      _.set(s, 'itemStyle.color', params => params.value > 0 ? '#ff6688' : '#24bdab')
      _.set(s, 'itemStyle.borderRadius', 2)
      _.set(s, 'itemStyle.position', 'insideLeft')
      _.set(s, 'itemStyle.textShadowColor', 'rgba(0, 0, 0, 0.25)')
      _.set(s, 'itemStyle.textShadowBlur', 2)
      _.set(s, 'itemStyle.textShadowOffsetX', 1)
      _.set(s, 'itemStyle.textShadowOffsetY', 1)
      // _.set(newS, 'itemStyle.borderRadius', [3, 3, 0, 0])

      s.data = _.map(option.data, d => ({
        name: d.name,
        value: chartMode === 'changeRate' ? d.rate : d.value,
        rate: chartMode === 'changeRate' ? d.value : d.rate,
        itemStyle: {
          borderRadius: d.value > 0 ? [0, 4, 4, 0] : [4, 0, 0, 4]
        },
        label: {
          show: true,
          color: '#fff',
          position: d.value < 0 ? 'insideRight' : 'insideLeft',
          // 阴影颜色为 黑色
          textShadowColor: 'rgba(0, 0, 0, 0.3)',
          textShadowBlur: 4,
          textShadowOffsetX: 2,
          textShadowOffsetY: 2,
          formatter: params => {
            const r = _.get(params, 'data.rate', 0)
            const v = formatLargeNumber(_.get(params, 'data.value', 0))
            if (_.isNil(r)) return `${v}`
            return chartMode === 'changeRate' ? `${v}%（${r}）` : `${v}（${r}%）`
          }
        }
      }))
      return s
    }))
    // option.yAxis.data = _.slice(option.yAxis.data).reverse()

    option.yAxis.inverse = true

    _.set(option, 'legend.show', false)
    _.set(option, 'xAxis.name', '')
    _.set(option, 'xAxis.splitLine.show', false)
    _.set(option, 'yAxis.splitLine.show', true)

    _.set(option, 'xAxis.axisLabel.formatter', value => formatLargeNumber(value))
    _.set(option, 'yAxis.axisLabel.formatter', value => _.truncate(value, { length: 20 }))

    _.set(option, 'grid.top', '3%')
    _.set(option, 'grid.left', '3%')
    _.set(option, 'grid.right', activeKey === 'ALL' ? '3%' : '5%')
    _.set(option, 'grid.bottom', '3%')

    _.set(option, 'opts.renderer', 'svg')


    if (activeKey === 'ALL') {
      const yAxis = copy(option.yAxis)
      _.set(option, 'yAxis', [{
        ...yAxis,
        data: _.map(option.data, 'name')
      }, {
        ...yAxis,
        data: _.map(option.data, 'dimensionTitle')
      }])
    }
    // console.log(activeKey, option)

    return option
  }, [activeKey, chartMode, direction])

  const renderChart = () => {
    if (chartMode === 'table') {
      return (
        <DataPreviewTable
          data={chartData}
          columns={[
            { key: 'name', title: '时间', dataType: 'date', width: 200 },
            { key: 'curent', title: '本期值', dataType: 'number', width: 120 },
            { key: 'previous', title: '基期值', dataType: 'number', width: 120 },
            { key: 'value', title: '变化值', dataType: 'number', width: 120 },
            { key: 'rate', title: '变化率', dataType: 'number', width: 120 }
          ].concat(activeKey === 'ALL' ? [
            { key: 'dimensionTitle', title: '维度', dataType: 'string', width: 200 }
          ] : [])}
          showFieldKey={false}
          tableBodyStyle={{ height: 300 }}
          renderItem={(_info, cell) => {
            const key: string = cell.column.id || ''
            const value = cell.getValue() // 这个获取的就是原始数据

            // if (key === 'rate' && _.isNil(value)) return 'N/A'
            if (key === 'rate') return _.isNumber(value) ? `${_.round(value, 2)}%` : value
            if (key === 'value' && value > 0) return `+${_.round(value, 2)}`
            if (key !== 'name' && _.isNumber(value)) return _.round(value, 2)
            return value
          }}
        />
      )
    }

    return (
      <ChartView
        data={chartData}
        type='barHor'
        height={360}
        key={direction}
        onModifyConfig={onModifyConfig}
        notMerge
        lazyUpdate
        width={width < 840 ? undefined : width * 0.66}
        axis={{
          x: [{ name: 'name', title: '维度', dataType: 'string', id: 'name' }],
          y: [{ name: 'value', title: '变化值', dataType: 'number' }]
        }}
      />
    )
  }

  useEffect(() => {
    setActiveKey('ALL')
  }, [info.key])

  return (
    <div
      ref={domRef}
      className={cn('causation-analysis-editor-chart-dimension-panel', {
        positive: deltaValue > 0,
        negative: deltaValue < 0
      })}
    >
      <header className='dimension-list'>
        <MenuDropdown
          menuItems={menuItems}
          trigger={['click']}
          placement='topLeft'
          overlayClassName='causation-analysis-editor-chart-dimension-overlay'
          menuActiveKey={activeKey}
          onMenuClick={e => {
            e.domEvent.stopPropagation()
            setActiveKey(e.key)
          }}
        >
          <Button type='text' className='select-btn' title={activeKey === 'ALL' ? '全维度观测' : activeDimension?.field?.title}>
            {activeKey === 'ALL' ? '全维度观测' : activeDimension?.field?.title}
            <CaretDownOutlined />
          </Button>
        </MenuDropdown>

        <div className='max-change-dimensions'>
          {_.map(maxChangeDimensions, (dimension, idx) => (
            <Button
              key={dimension.field.id}
              className={cn('top-item-btn', { active: activeKey === dimension.field.id })}
              icon={<Icon component={topIcons[idx]} className='top-icon' />}
              type='text'
              title={dimension.field.title}
              onClick={e => {
                e.stopPropagation()
                setActiveKey(dimension.field.id)
              }}
            >
              <span className='top-title'>{dimension.field.title}</span>
            </Button>
          ))}
        </div>
      </header>

      <div className='dimension-chart'>
        <div
          className='dimension-chart-view'
          key={`${chartUpdateKey}_${direction}_${activeKey}`}
        >
          <header className='flex-row vcenter'>
            <span className='flex-1'>贡献排名</span>

            <div className='flex-row vcenter'>
              {_.map(chartModeOpts, opt => (
                <Button
                  key={opt.key}
                  size='small'
                  className='chart-mode-btn'
                  type={chartMode === opt.key ? 'primary' : 'default'}
                  onClick={() => setChartMode(opt.key)}
                  icon={opt.icon}
                >
                  {opt.label}
                </Button>
              ))}
            </div>
          </header>

          {width > 0 && renderChart()}
        </div>
        <div
          className={cn('dimension-data-interpret', {
            'full': width < 840
          })}
        >
          <header>数据解读</header>
          {changeDataInterpretText &&
            <div className='data-interpret-content' >
              {htmr(changeDataInterpretText)}
            </div>
          }
        </div>
      </div>
    </div>
  )
}

export const DimensionFieldChart = fastMemo(_DimensionFieldChart)
