
.causation-analysis-editor-preview-panel {
  flex: 1;
  display: flex;
  margin-left: 3px;
  position: relative;
  background-color: #fff;
  overflow-x: hidden;

  .chart-wrap {
    flex: 1;
    transform: translate(0, 0);
    overflow-y: auto;
    position: relative;
  }

  .design-fast-data-table {
    .fast-data-table .fast-data-table-tbody td.td-border {
      border: none;
    }
    .data-table-header-item .data-title {
      // font-weight: normal;
      font-size: 14px;
    }
  }
}
