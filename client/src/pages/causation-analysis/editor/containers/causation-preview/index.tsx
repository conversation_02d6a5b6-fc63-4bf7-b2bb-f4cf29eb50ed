import './index.less'

import { fastMemo } from '@sugo/design/functions'
import React from 'react'

import { useStore } from '@/pages/causation-analysis/editor/reactive'

import { DimensionFieldChart } from './dimension-field-chart'
import { TaskProgress } from './task-progress'
import { ValueFieldChart } from './value-field-chart'
import { ValueFieldList } from './value-field-list'

/**
 * 图表显示页面
 * @returns
 */
function _CausationPreview() {
  const chartUpdateKey = useStore(s => s.chartUpdateKey)
  // 左边指标列表
  // 上面图表列表
  // 下面维度贡献

  return (
    <div className='causation-analysis-editor-preview-panel'>
      <ValueFieldList />
      <div className='chart-wrap' key={chartUpdateKey}>
        {chartUpdateKey &&
          <>
            <ValueFieldChart />
            <DimensionFieldChart />
          </>
        }
      </div>
      <TaskProgress />
    </div>
  )
}

export const CausationPreview = fastMemo(_CausationPreview)
