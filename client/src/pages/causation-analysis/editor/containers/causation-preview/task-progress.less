
.causation-analysis-editor-task-progress {
  position: fixed;
  left: 0;
  bottom: 0;
  right: 0;
  top: 0;
  background-color: rgba(#fff, 80%);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  > .content {
    width: 520px;
    padding: 12px 16px;
    border-radius: 10px;
    box-shadow: 1px 1px 6px rgba(#444, 10%);
    color: #555;
    background-color: #fff;
    position: relative;

    // 新增的
    color: #fff;
    background-color: rgba(#111, 70%);

    .ant-progress-text {
      color: #fff;
    }
    .ant-progress-inner {
      background-color: rgba(#111, 25%);
    }
    .ant-progress-bg {
      background: linear-gradient(to left, rgba(#f34, 1), rgba(@primary-color, 1) 70%);
    }
  }

  .close-icon {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 5;
  }

  .clean-btn {
    color: #fff;
    background-color: rgba(#f45, 55%);
    border-color: rgba(#f45, 80%);
  }
}
