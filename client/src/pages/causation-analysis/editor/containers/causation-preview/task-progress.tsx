
import './task-progress.less'

import { CloseOutlined, LoadingOutlined, StopOutlined } from '@ant-design/icons'
import { fastMemo } from '@sugo/design/functions'
import { Button, Progress } from 'antd'
import _ from 'lodash'
import React, { useEffect, useState } from 'react'

import { TimeClock } from '@/components/time-clock'
import { useAction, useStore } from '@/pages/causation-analysis/editor/reactive'

/**
 * 自定义组件
 * @returns
 */
function _TaskProgress() {
  const { curent, status, texts, total } = useStore(s => ({
    curent: s.queryTaskState.curent,
    total: s.queryTaskState.total,
    texts: s.queryTaskState.texts,
    status: s.queryTaskState.status
  }))
  const [show, setShow] = useState(false)

  const stopQueryTask = useAction(s => s.stopQueryTask)

  useEffect(() => {
    if (status === 'done') {
      setTimeout(() => setShow(false), 1000 * 0.75)
    }
    if (status === 'running') {
      setShow(true)
    }
  }, [status])

  if (!show) return null
  if (status !== 'running' && status !== 'done' && status !== 'stopped') return null

  return (
    <div className='causation-analysis-editor-task-progress'>
      <div className='content'>
        <div className='mb-3'>
          {status === 'stopped' ? '已经取消演算' : '正在疯狂演算中 ...'}
        </div>
        <div className='flex-row vcenter mb-1'>

          {status === 'stopped' ?
            <StopOutlined className='mr-4' /> :
            <LoadingOutlined className='mr-4' />
          }

          <div className='flex-row vcenter'>
            <span>完成 {curent} 项/总 {total} 项</span>
            <TimeClock className='ml-4' status={(status === 'done' || status === 'stopped') ? 'stopped' : 'running'} />
          </div>

          <span className='flex-1' />

          {status === 'running' &&
            <Button onClick={stopQueryTask} size='small' className='clean-btn'>取消演算</Button>
          }
        </div>

        <Progress percent={_.round(curent / total * 100, 0)} showInfo />

        <div className='mt-1'>{texts[texts.length - 1]} ...</div>

        {status === 'stopped' && (
          <CloseOutlined className='close-icon' onClick={() => setShow(false)} title='关闭' />
        )}
      </div>
    </div>
  )
}

export const TaskProgress = fastMemo(_TaskProgress)
