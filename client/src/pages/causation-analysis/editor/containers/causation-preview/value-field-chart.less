
.causation-analysis-editor-chart-value-panel {
  background-color: #fff;

  .info-panel {

    .field-panel {
      display: flex;
      align-items: center;
      background-color: #fff;
      padding: 12px 12px 5px;
      border: none;
      box-shadow: none;
      outline: none;

      .alias {
        font-size: 15px;
        font-weight: bold;
        color: #444;
      }
      .title {
        color: #999;
        font-size: 13px;
      }
      .target-value {
        margin-left: 8px;
        color: #666;
      }
    }

    .value-panel {
      padding: 6px 12px 8px;
    }
  }

  .value-panel {
    display: flex;
    align-items: center;
    padding: 12px 4px;
    line-height: 1;

    .current-value,
    .change-value,
    .previous-value {
      min-width: 220px;
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }

    .change-value {
      align-items: center;
    }

    .previous-value {
      align-items: flex-start;
    }

    .current-value {
      align-items: flex-end;
    }

    .agg-text {
      color: #bbb;
      font-size: 13px;
      font-style: italic;
      min-width: 50px;
    }

    .value {
      font-size: 24px;
      color: #444;
    }

    .rate {
      margin-right: 6px;
      font-size: 20px;
    }

    .rate,
    .value {
      &.up {
        color: #ee5566;
      }
      &.down {
        color: #24bdab;
      }
    }
  }

  .chart-type-switch-tabs {
    .ant-tabs-nav {
      margin: 0;
      padding-left: 16px;
      &::before {
        border: none;
      }
    }
    .ant-tabs-nav .ant-tabs-tab {
      padding: 5px 0 ;
    }
    .ant-tabs-tab + .ant-tabs-tab {
      margin-left: 16px !important;
    }
    .ant-tabs-tab .anticon {
      margin-right: 8px;
    }
  }

}
