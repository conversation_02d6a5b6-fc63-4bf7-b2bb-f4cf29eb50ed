import './value-field-chart.less'

import { FallOutlined, RiseOutlined, StockOutlined, TableOutlined } from '@ant-design/icons'
import { ChartView, CountUp, DataPreviewTable } from '@sugo/design'
import { fastMemo } from '@sugo/design/functions'
import { useMemoizedFn } from 'ahooks'
import { Tabs, Tooltip } from 'antd'
import cn from 'classnames'
import copy from 'fast-copy'
import _ from 'lodash'
import React, { useState } from 'react'
import { useDeepCompareMemo } from 'use-deep-compare'

import { themeColor } from '@/consts'
import { useCompute } from '@/pages/causation-analysis/editor/reactive'
import { getPreviousKey } from '@/pages/causation-analysis/editor/utils'
import { formatLargeNumber, getExaggeratedRateDescription, getInternetStyleRate } from '@/utils'

import { ComputeInfo } from './compute-info'

/**
 * 指标字段图表
 * @returns
 */
function _ValueFieldChart() {
  const info = useCompute(s => s.getActiveChartValueFieldData('active'))

  const field = info.field || {}
  const data = info.data || {}
  const config = info.config || {}

  const currentPeriodTime = _.get(data, 'currentPeriodTime', [])
  const previousPeriodTime = _.get(data, 'previousPeriodTime', [])

  const [chartType, setChartType] = useState('line')

  const total = useDeepCompareMemo(() => {
    const value = _.get(data, 'total.deltaValue', 0)
    const rate = _.floor(_.get(data, 'total.deltaRate', 0) * 100, 2)

    const currentValue = _.get(data, 'total.currentValue')
    const previousValue = _.get(data, 'total.previousValue')
    const targetValue = _.get(data, 'total.targetValue', 0)
    const targetRate = _.get(data, 'total.targetRate')

    const timeKeys = _.compact(_.uniq(_.map(data.total?.currentData, i => i?.name))).sort()
    const currentDict = _.keyBy(data.total?.currentData, 'name')
    const previousDict = _.keyBy(data.total?.previousData, 'name')

    const chartData = _.map(timeKeys, k => {
      const pKey = getPreviousKey(k, data.config.previousPeriodTime)
      const obj = {
        name: k,
        curent: currentDict[k]?.value || 0,
        previous: previousDict[pKey]?.value || 0,
        rate: 0 as number | null,
        value: 0
      }
      obj.value = (obj.curent || 0) - (obj.previous || 0)
      obj.rate = _.floor((obj.curent - obj.previous) / obj.previous * 100, 4)
      if (!_.isFinite(obj.rate) || _.isNaN(obj.rate)) {
        obj.rate = null
      }

      return obj
    })

    return {
      currentValue,
      previousValue,
      value,
      targetValue,
      targetRate,
      rate,
      chartData
    }
  }, [data.total])

  const renderValue = (value: number | undefined) => {
    if (_.isNaN(value)) return ''
    if (_.isNil(value)) return '无值'
    return <CountUp end={value} className='value' start={value * 0.5} />
  }

  const onModifyConfig = useMemoizedFn(option => {
    _.set(option.xAxis, 'axisLabel.color', '#888')
    _.set(option.yAxis, 'axisLabel.color', '#999')

    const yAxis = copy(option.yAxis)

    option.color = [themeColor, '#39f', '#e90']
    option.yAxis = [
      { ...yAxis, alignTicks: true },
      {
        ...yAxis,
        // min: -100,
        alignTicks: true, // 关键：让右轴刻度对齐左轴
        axisLabel: {
          ...yAxis.axisLabel,
          formatter: '{value}%' // 直接追加 % 符号
        }
      }
    ]
    option.series = _.map(option.series, (s, idx) => {
      const newS = {
        ...s,
        type: idx === 2 ? 'line' : 'bar',
        smooth: true,
        yAxisIndex: idx === 2 ? 1 : 0,
        barMaxWidth: 36,
        symbolSize: 0,
        symbol: 'none'
      }
      _.set(newS, 'itemStyle.borderRadius', [4, 4, 0, 0])
      return newS
    })
    option.grid = {
      ...option.grid,
      top: '10%',
      left: '3%',
      right: '3%',
      bottom: '3%'
    }

    _.set(option, 'opts.renderer', 'svg')

    return option
  })

  const renderChart = () => {
    if (chartType === 'table') {
      return (
        <DataPreviewTable
          data={total.chartData}
          columns={[
            { key: 'name', title: '时间', dataType: 'date', width: 150 },
            { key: 'curent', title: '本期值', dataType: 'number', width: 120 },
            { key: 'previous', title: '基期值', dataType: 'number', width: 120 },
            { key: 'value', title: '变化值', dataType: 'number', width: 120 },
            { key: 'rate', title: '变化率', dataType: 'number', width: 120 }
          ]}
          showFieldKey={false}
          tableBodyStyle={{ height: 300 }}
          renderItem={(_info, cell) => {
            const key: string = cell.column.id || ''
            const value = cell.getValue() // 这个获取的就是原始数据

            // if (key === 'rate' && _.isNil(value)) return 'N/A'
            if (key === 'rate') return _.isNumber(value) ? `${_.round(value, 2)}%` : value
            if (key === 'value' && value > 0) return `+${_.round(value, 2)}`
            if (key !== 'name' && _.isNumber(value)) return _.round(value, 2)
            return value
          }}
        />
      )
    }
    return (
      <ChartView
        data={total.chartData}
        height={300}
        type='line'
        themeColor={themeColor}
        onModifyConfig={onModifyConfig}
        axis={{
          x: [{ name: 'name', title: '时间', dataType: 'date', id: 'name' }],
          y: [
            { name: 'curent', title: '本期值', dataType: 'number' },
            { name: 'previous', title: '基期值', dataType: 'number' },
            { name: 'rate', title: '变化率', dataType: 'number' }
          ]
        }}
      />
    )
  }

  if (!info?.key || !field) return null

  return (
    <div className='causation-analysis-editor-chart-value-panel'>
      <header className='info-panel'>
        <div className='field-panel flex-row vcenter'>
          <span className='alias'>{field.alias}</span>
          <span className='title'>（{field.title}）</span>

          {config.targetValue &&

            <Tooltip align={{ offset: [0, 4] }} title={(
              <div>
                目标值：{config.targetValue}{/%/.test(String(config.targetValue)) && '（较基期）'}
              </div>
            )}>
              <div className='target-value'>
                目标完成度：{_.round((total.targetRate || 0) * 100, 2)}%
                （{getInternetStyleRate(_.round((total.targetRate || 0) * 100, 2))}）
              </div>
            </Tooltip>
          }
          <div className='flex-1' />
          <ComputeInfo />
        </div>

        <div className='value-panel'>
          <div className='previous-value'>
            <div className='flex-row vcenter mb-3'>
              基期值：
              {renderValue(total.previousValue)}
              <span className='agg-text'>（合计）</span>
            </div>
            <div>{previousPeriodTime.join(' ~ ')}</div>
          </div>

          <div className='change-value'>
            <div className='flex-row vcenter mb-3'>
              变化值：
              <span className={cn('value', { up: total.rate > 0, down: total.rate < 0 })}>
                {total.rate > 0 ? <RiseOutlined className='mr-1' /> : <FallOutlined className='mr-1' />}
                {total.value > 0 ? '+' : ''}
                {formatLargeNumber(total.value)}
              </span>
            </div>
            <div className='flex-row vcenter'>
              <span className={cn('rate', { up: total.rate > 0, down: total.rate < 0 })}>
                {total.rate > 0 ? '+' : ''}{total.rate}{_.isNaN(total.rate) ? '' : '%'}
              </span>
              <span>{getExaggeratedRateDescription(total.rate)}</span>
            </div>
          </div>

          <div className='current-value'>
            <div className='flex-row vcenter mb-3'>
              本期值：
              {renderValue(total.currentValue)}
              <span className='agg-text'>（合计）</span>
            </div>
            <div>{currentPeriodTime.join(' ~ ')}</div>
          </div>
        </div>
      </header>
      <div className='chart-view-wrap'>
        <Tabs
          activeKey={chartType}
          size='small'
          onChange={setChartType}
          className='chart-type-switch-tabs'
          items={[
            { key: 'line', label: '趋势', icon: <StockOutlined /> },
            { key: 'table', label: '表格', icon: <TableOutlined /> }
          ].map(i => ({
            ...i,
            label: (
              <div className='flex-row vcenter'>
                {i.icon}
                <span>{i.label}</span>
              </div>
            )
          }))}
        />
        {_.isEmpty(total.chartData) ? (
          <div className='flex-row center pd-3' style={{ height: 300 }}>
            <div className='empty-tips'>暂无数据</div>
          </div>
        ) : renderChart()}
      </div>
    </div>
  )
}

export const ValueFieldChart = fastMemo(_ValueFieldChart)
