
.causation-analysis-editor-chart-value-field-list {
  width: 440px;
  border-right: 1px solid #f4f4f4;

  .change-group {
    margin-bottom: 10px;
    border-bottom: 1px dashed #f1f1f1;

    .tt {
      color: #bbb;
    }

    .target-value {
      background-color: lighten(#a90, 5%);
      color: #fff;
      border-radius: 5px 0 0 5px;
      padding: 2px 6px;
      font-size: 13px;
      height: 28px;
      display: flex;
      align-items: center;
      text-shadow: 0 1px 0 rgba(#111, 0.16);

      + div {
        text-shadow: 0 1px 0 rgba(#111, 0.16);
        height: 28px;
        border-radius: 0 5px 5px 0;
      }
    }
  }

  .header {
    background-color: #f1f1f1;
    font-weight: bold;
    color: #444;
    padding: 8px 10px;
    margin-bottom: 4px;
  }

  .analysis-table {
    // height: 100px;
    margin-bottom: 12px;

    .ant-table-cell {
      padding: 5px !important;
    }

    .field-title {
      max-width: 130px;
    }

    .trend-chart {
      min-width: 50px;
      max-width: 50px;
      .echarts-for-react {
        transform: translate(-10px, 5px);
      }
    }

    .up {
      color: #ee5566;
    }
    .down {
      color: #24bdab;
    }
  }

  .active {
    background-color: rgba(@primary-color, 8%);

    &.down-bg {
      background-color: rgba(#27ceba, 8%) !important;
    }
    &.up-bg {
      background-color: rgba(#ff6688, 8%) !important;
    }
  }
}
