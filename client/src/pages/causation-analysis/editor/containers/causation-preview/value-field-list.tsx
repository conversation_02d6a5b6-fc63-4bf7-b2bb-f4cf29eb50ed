import './value-field-list.less'

import { ChartView } from '@sugo/design'
import { fastMemo } from '@sugo/design/functions'
import { useDebounceFn, useEventListener, useMemoizedFn } from 'ahooks'
import { Table } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React, { useCallback, useMemo } from 'react'

import { TrendUpDownBar } from '@/components/trend-up-down-bar'
import { useAction, useCompute, useStore } from '@/pages/causation-analysis/editor/reactive'
import { formatLargeNumber, getExaggeratedRateDescription, sparseArray } from '@/utils'


/**
 * 指标列表
 * @returns
 */
function _ValueFieldList() {
  const valueFieldList = useCompute(s => s.chartValueFieldList)
  const activeKey = useStore(s => s.activeChartValueFieldKey)

  const { setActiveChartValueFieldKey } = useAction()

  const changeKeys = ['上涨', '下跌', '持平']

  const changeGroup = useMemo(() => _.mapValues(_.groupBy(valueFieldList, v => {
    const diff = _.get(v, 'data.total.deltaValue', 0)
    if (diff > 0) return '上涨'
    if (diff < 0) return '下跌'
    if (diff === 0) return '持平'
    return ''
  }), v => _.size(v)), [valueFieldList])

  const [analysisList, targetValueText] = useMemo(() => {
    let targetValueTotal = 0
    let targetValueSuccess = 0

    const arr = _.values(valueFieldList).map(i => {
      const value = _.get(i, 'data.total.deltaValue')
      const rate = _.get(i, 'data.total.deltaRate')
      const targetRate = _.get(i, 'data.total.targetRate')
      const obj = {
        ...i,
        key: i.key,
        field: i.field.alias,
        value: formatLargeNumber(value || 0),
        rate: rate === 0 ? 0 : (_.round(rate! * 100, 2) || null),
        order: value || 0,
        rateDesc: getExaggeratedRateDescription((rate || 0) * 100),
        data: sparseArray(_.orderBy(_.get(i, 'data.total.currentData', []), 'name', 'asc'))
      }

      if (i.config?.targetValue) targetValueTotal += 1
      if ((targetRate || 0) > 1) targetValueSuccess += 1

      _.set(obj, 'data[0].deltaValue', value)
      return obj
    })

    return [arr, `${targetValueSuccess}/${targetValueTotal}`]
  }, [valueFieldList])

  const onModifyConfig = useCallback(option => {
    _.set(option, 'xAxis.show', false)
    _.set(option, 'yAxis.show', false)
    const min = _.minBy(option.data, 'value') as any
    const max = _.maxBy(option.data, 'value') as any

    const deltaValue = _.get(option, 'data[0].deltaValue')

    _.set(option, 'legend.show', false)
    _.set(option, 'xAxis.axisLabel.formatter', () => '')
    _.set(option, 'yAxis.axisLabel.formatter', () => '')

    _.set(option, 'yAxis.min', min?.value)
    _.set(option, 'yAxis.max', max?.value)
    _.set(option, 'grid.top', '4px')
    _.set(option, 'grid.left', '4px')
    _.set(option, 'grid.right', '4px')
    _.set(option, 'grid.bottom', '4px')
    _.set(option, 'grid.show', false)
    _.set(option, 'opts.renderer', 'svg')
    _.set(option, 'series', _.map(option.series, s => {
      s.symbolSize = 0
      s.symbol = 'none'
      s.smooth = true
      s.lineStyle.width = 2
      s.lineStyle.color = deltaValue > 0 ? '#ee5566' : '#24bdab'
      return { ...s }
    }))
    return option
  }, [])


  const renderValue = (value: any, isRate?: boolean) => {
    if (_.isNil(value) || _.isNaN(value)) return '-'
    const val = Number.parseFloat(value)
    return (
      <span className={cn({ up: val > 0, down: val < 0 })}>
        {isRate && value > 0 ? '+' : ''}{value}{isRate ? '%' : ''}
      </span>
    )
  }

  const columns = useMemo(() => [
    {
      title: '指标', dataIndex: 'field',
      render: (field: any) => <div className='field-title' title={field}>{field}</div>,
      width: 135
    },
    {
      title: '趋势', dataIndex: 'data',
      render: (currentData: any) => (
        <ChartView
          type='line'
          className='trend-chart'
          data={currentData}
          height={25}
          width={60}
          axis={{
            x: [{ name: 'name', title: '时间' }],
            y: [{ name: 'value', title: '值' }]
          }}
          lazyUpdate
          onModifyConfig={onModifyConfig}
        />
      ),
      width: 50
    },
    {
      title: '变化值', dataIndex: 'value',
      render: (rate: any) => renderValue(rate),
      width: 88
    },
    {
      title: '变化率', dataIndex: 'rate',
      render: (rate: any) => renderValue(rate, true),
      width: 70
    },
    {
      title: '晴雨表', dataIndex: 'rateDesc',
      width: 72,
      render: t => <div style={{ minWidth: 82 }}>{t}</div>
    }
  ], [onModifyConfig])

  const onRow = useMemoizedFn(row => ({
    onClick: e => {
      e.stopPropagation()
      setActiveChartValueFieldKey(row.key)
    }
  }))

  const onKeydown = useDebounceFn(e => {
    if (e.key === 'ArrowDown') {
      const index = _.findIndex(analysisList, i => i.key === activeKey)
      const next = index >= analysisList.length - 1 ? 0 : index + 1
      setActiveChartValueFieldKey(analysisList[next].key)
    }
    if (e.key === 'ArrowUp') {
      const index = _.findIndex(analysisList, i => i.key === activeKey)
      const next = index <= 0 ? analysisList.length - 1 : index - 1
      setActiveChartValueFieldKey(analysisList[next].key)
    }
  }, { wait: 300, leading: true })

  useEventListener('keydown', e => {
    onKeydown.run(e)
  })

  return (
    <div className='causation-analysis-editor-chart-value-field-list'>

      <div className='change-group'>
        <header className='header'>综合表现</header>

        <div className='mt-3 mx-2 flex-row vcenter'>
          {targetValueText !== '0/0' &&
            <div className='target-value'>目标完成：{targetValueText}</div>
          }
          <TrendUpDownBar
            up={changeGroup['上涨'] || 0}
            down={changeGroup['下跌'] || 0}
            className='flex-1'
          />
        </div>


        <div className='px-2 py-2'>
          {_.map(changeKeys, key => (
            <div key={key}>
              {key}：
              {changeGroup[key] || 0}
              {changeGroup[key] &&
                <span className='tt'>
                  （
                  {key === '上涨' ? '头牛' : ''}
                  {key === '下跌' ? '只熊' : ''}
                  ）
                </span>
              }
            </div>
          ))}
        </div>

      </div>

      {!_.isEmpty(analysisList) &&
        <Table
          columns={columns}
          dataSource={analysisList}
          size='small'
          showSorterTooltip={false}
          pagination={false}
          className='analysis-table'
          onRow={onRow}
          rowClassName={row => cn({
            'pointer': true,
            'active': row.key === activeKey,
            'up-bg': row.order > 0,
            'down-bg': row.order < 0
          })}
        />
      }
    </div>
  )
}

export const ValueFieldList = fastMemo(_ValueFieldList)
