
.causation-analysis-editor-nav-bar {
  display: flex;
  align-items: center;
  height: 50px;
  position: sticky;
  top: 1px;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(@primary-color, 0.12);
  overflow: hidden;
  z-index: 777;
  padding: 0 12px;
  transition: all 0.5s ease-in-out;
  opacity: 1;

  padding-left: 134px;

  .title {
    font-weight: bold;
    color: @primary-color;
    font-size: 15px;
  }

  .type-text {
    position: absolute;
    width: 120px;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 2;
    text-align: center;
    background-color: rgba(@primary-color, 1);
    color: #fff;
    line-height: 50px;
    font-size: 15px;
    font-weight: bold;
  }

  .title-edit-wrap {
    width: 200px;
    margin-left: 100px;

    input {
      text-align: center;
    }
  }

  .preview-btn {
    margin-right: 12px;
    color: #fff;
    background: linear-gradient(to top right, @primary-color, #e45);
    border-radius: 4px;
  }
}
