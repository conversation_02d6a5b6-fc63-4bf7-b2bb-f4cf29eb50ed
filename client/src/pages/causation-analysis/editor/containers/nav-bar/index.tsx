
import './index.less'

import {
  ArrowLeftOutlined,
  FundProjectionScreenOutlined,
  LoadingOutlined,
  SaveOutlined
} from '@ant-design/icons'
import { Input } from '@sugo/design'
import { fastMemo } from '@sugo/design/functions'
import { history } from '@umijs/max'
import { But<PERSON>, Tooltip } from 'antd'
import _ from 'lodash'
import React from 'react'

import { useNewBi } from '@/hooks/use-new-bi'
import { useAction, useCompute, useStore } from '@/pages/causation-analysis/editor/reactive'

/**
 * 导航栏
 * @returns
 */
function _NavBar() {
  const title = useStore(s => s.title)
  const themeId = useStore(s => s.id)
  const saveLoading = useStore(s => s.saveLoading)
  const status = useStore(s => s.queryTaskState.status)
  const valueFieldSize = useCompute(s => s.valueFieldSize, 'deep')

  const { isNewBi } = useNewBi()

  const { setCausationTitle, saveCausation, queryTask, openChartView } = useAction()

  const onBack = () => {
    if (isNewBi) return history.push('/framework/workspace?active=causation-analysis')
    history.push('/causation-analysis')
  }

  const onQueryTask = () => {
    openChartView()
    queryTask()
  }

  const Title = (
    <div className='title-edit-wrap'>
      <Input
        placeholder='未命名'
        maxLength={30}
        onChange={setCausationTitle}
        wait={500}
        onClick={e => e.stopPropagation()}
        value={title}
        readOnly
        bordered={false}
        className={`title-edit-input ${!title && themeId ? 'error' : ''}`}
        // onBlur={() => saveThemeTitle()}
        suffix={saveLoading ? <LoadingOutlined className='loading-icon' /> : <span className='loading-icon' />}
      />
    </div>
  )

  return (
    <div className='causation-analysis-editor-nav-bar'>
      <div className='type-text'>
        维度归因分析
      </div>
      <Button icon={<ArrowLeftOutlined />} className='back-btn' onClick={onBack}>返回</Button>
      <div className='flex-1' />
      <b className='title'>{title}</b>
      <div className='flex-1' />
      <Tooltip title={valueFieldSize === 0 ? '请先设置要分析的指标/度量再演算' : undefined}>
        <Button
          className='preview-btn'
          icon={<FundProjectionScreenOutlined />}
          disabled={saveLoading || valueFieldSize === 0}
          loading={status === 'running'}
          onClick={onQueryTask}
        >
          演算
        </Button>
      </Tooltip>
      <Button
        type='primary'
        className='save-btn'
        icon={<SaveOutlined />}
        loading={saveLoading}
        disabled={status === 'running'}
        onClick={saveCausation}
      >
        保存
      </Button>
    </div>
  )
}

export const NavBar = fastMemo(_NavBar)
