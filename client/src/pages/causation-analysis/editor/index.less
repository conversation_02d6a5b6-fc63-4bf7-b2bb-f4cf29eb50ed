
.causation-analysis-editor {
  transform: translateZ(0);
  position: relative;
  background-color: #fff;

  // background: repeating-linear-gradient(
  //   -45deg,
  //   rgba(#444, 2.5%) 0 20px,
  //   rgba(@primary-color, 0.5%) 20px 40px
  // ), repeating-linear-gradient(
  //   45deg,
  //   rgba(#444, 2.5%) 0 20px,
  //   rgba(@primary-color, 0.5%) 20px 40px
  // );

  &.show-chart-view {
    background: #fff !important;
    .causation-analysis-editor-base-form-config {
      width: 360px;
      min-width: 360px;
      border: 0;
      box-shadow: 1px 1px 5px rgba(@primary-color, 12%);
      border-radius: 0;
    }
  }

  &.readonly {
    overflow: hidden;

    .save-btn,
    .back-btn {
      display: none;
    }
  }

  .content-container {
    display: flex;
    height: calc(100vh - 100px);
  }

  .ant-spin-nested-loading,
  .ant-spin-container {
    height: 100%;
  }

  strong {
    color: #444;
  }
}

