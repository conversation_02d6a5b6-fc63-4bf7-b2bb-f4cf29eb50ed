import './index.less'

import { LoadingOutlined } from '@ant-design/icons'
import { fastMemo } from '@sugo/design/functions'
import { useParams } from '@umijs/max'
import { Spin } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React, { useEffect } from 'react'
import ResizeObserver from 'resize-observer-polyfill'

import { BaseFormConfig } from '@/pages/causation-analysis/editor/containers/base-form-config'
import { CausationPreview } from '@/pages/causation-analysis/editor/containers/causation-preview'
import { NavBar } from '@/pages/causation-analysis/editor/containers/nav-bar'
import { realmProviderMemo, useAction, useStore } from '@/pages/causation-analysis/editor/reactive'

export interface CausationAnalysisEditorProps {
  id?: string
  readOnly?: boolean
}

/**
 * 因果分析编辑器
 * @returns
 */
function _CausationAnalysisEditor(props: CausationAnalysisEditorProps) {
  const showChartView = useStore(s => s.showChartView)
  const isPreview = useStore(s => s.isPreview)
  const loading = useStore(s => s.initLoading)
  const params = useParams('id')

  const id = props.id || params.id

  const { setID, setDestroyed, loadCausation, setChartUpdateKey } = useAction()

  const readOnly = props.readOnly || _.includes(window.location.search, 'readOnly')

  useEffect(() => {
    if (!id) return
    setID(id)
    loadCausation()

    return () => {
      setDestroyed(true)
    }
  }, [id])

  useEffect(() => {
    const resizeObserver = new ResizeObserver(() => {
      setChartUpdateKey()
    })
    resizeObserver.observe(document.body)
    return () => {
      resizeObserver.unobserve(document.body)
    }
  }, [id])

  // 只显示
  if (!window.isExperiment) return (
    <h2 className='flex-row center pt-4'>
      实验性功能，不对外开放
    </h2>
  )

  return (
    <div className={cn('causation-analysis-editor', {
      'show-chart-view': showChartView,
      'is-preview': isPreview,
      'readonly': readOnly
    })}>
      <Spin spinning={loading} indicator={<LoadingOutlined />}>
        <NavBar />
        <div className='content-container'>
          {!readOnly &&
            <BaseFormConfig />
          }
          {showChartView &&
            <CausationPreview />
          }
        </div>
      </Spin>
    </div>
  )
}

export const CausationAnalysisEditor = realmProviderMemo(_CausationAnalysisEditor)
export default CausationAnalysisEditor
