import { history } from '@umijs/max'
import { message } from 'antd'
import copy from 'fast-copy'
import _ from 'lodash'

import type { CausationAnalysis, CausationAnalysisResult } from '@/pages/causation-analysis/type'
import { Cloud } from '@/services'
import { genId } from '@/utils'

import type { State } from '../init'


/**
 * 加载相关的
 * @param state
 * @param param1
 * @returns
 */
export const causationActions = (state: State, { }) => {

  const setID = (id: string) => {
    state.id = id
  }

  const loadCausation = async () => {
    const id = state.id
    state.initLoading = true

    try {
      if (id === 'new') {
        // ...
        state.title = '未命名'
      } else {
        const causation = await Cloud.CausationAnalysis.findByPk(id)
        let showChart = false

        if (causation.latestComputeInfo?.resultId) {
          const computeResult = await Cloud.CausationAnalysisResult.findByPk(causation.latestComputeInfo?.resultId)
          if (!computeResult) {
            console.log('查询结果表失败，现在重置 resultId')
            _.set(causation, 'latestComputeInfo.resultId', null)
          } else {
            state.causationResult = computeResult
            state.analysisResult = computeResult.analysisResult
            state.analysisDimensionResult = computeResult.analysisDimensionResult
            state.queryTaskState.computeTime = computeResult.computeTime
            if (computeResult.computedAt) {
              state.queryTaskState.computedAt = computeResult.computedAt
            }
            showChart = true
          }
        }

        state.causation = causation
        state.title = causation.title
        state.type = causation.type
        state.valueFieldMap = causation.valueFieldMap
        state.categorys = causation.categorys
        state.cron = causation.cron
        state.noticeConfig = causation.noticeConfig
        state.aiAnswerConfig = causation.aiAnswerConfig

        if (showChart) {
          state.activeChartValueFieldKey = _.first(_.orderBy(state.valueFieldMap, 'order', 'asc'))?.key || ''

          setTimeout(() => {
            state.showChartView = true
          }, 360)
        }
      }
    } finally {
      state.initLoading = false
    }
  }

  // 保存分析结果
  const saveCausationComputeResult = async (causation: CausationAnalysis, isUpdate?: boolean) => {

    const id = isUpdate ? state.causationResult?.id : genId()
    if (!id) return

    const record: CausationAnalysisResult = {
      analysisDimensionResult: state.analysisDimensionResult,
      analysisResult: state.analysisResult,
      causationAnalysisBackup: causation,
      computedAt: state.queryTaskState.computedAt || state.causationResult?.computedAt || new Date(),
      computeTime: state.queryTaskState.computeTime || state.causationResult?.computeTime || 0,
      computeTrigger: 'manual',
      causationAnalysisId: causation.id,
      id,
      index: 1,
      title: `${causation.title}第 1 期`,
      description: `${causation.title}第 1 期`,
      createdBy: _.get(window, 'sugo.user.id')
    }

    await Cloud.CausationAnalysis.updateByPk(state.id, {
      latestComputeInfo: {
        resultId: id,
        computedAt: record.computedAt,
        computeTrigger: record.computeTrigger,
        index: record.index,
        computeTime: record.computeTime
      }
    })

    await Cloud.CausationAnalysisResult.upsert(record, {
      where: { id }
    })
  }

  // 保存
  const saveCausation = async () => {
    const isNew = state.id === 'new'

    const record: Partial<CausationAnalysis> = {
      type: state.type,
      title: state.title,
      valueFieldMap: state.valueFieldMap,
      categorys: state.categorys,
      cron: state.cron,
      noticeConfig: state.noticeConfig,
      aiAnswerConfig: state.aiAnswerConfig,
      createdBy: _.get(window, 'sugo.user.id')
    }

    try {
      state.saveLoading = true
      if (isNew) {
        const res = await Cloud.CausationAnalysis.create(record)
        state.id = res.id
        state.causation = res
        history.replace(`/causation-analysis/editor/${res.id}`)
      } else {
        await Cloud.CausationAnalysis.updateByPk(state.id, record)
      }

      // 暂未保存过结果
      /**
       * @deprecated
       */
      if (!state.causation?.latestComputeInfo?.resultId) {
        if (!_.isEmpty(state.analysisResult)) {
          console.log('创建分析结果')
          await saveCausationComputeResult(copy(state.causation!))
        }
      } else if (state.causationResult?.id) {
        if (!_.isEqual(state.queryTaskState.computedAt, state.causationResult.computedAt)) {
          console.log('更新分析结果')
          await saveCausationComputeResult(copy(state.causation!), true)
        }
      }

      message.success('保存完成')
    } finally {
      state.saveLoading = false
    }
  }

  return {
    saveCausation,
    loadCausation,
    setID
  }
}
