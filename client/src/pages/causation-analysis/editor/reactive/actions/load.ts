
import { loadConnections, loadFields, loadTables } from '@sugo/design/dist/esm/provides/services/data-center'
import { getIndicesUnit } from '@sugo/design/dist/esm/provides/services/indices-unit/services'
import arrayToTree from 'array-to-tree'
import copy from 'fast-copy'
import _ from 'lodash'

import { Cloud as AbiCloud, MutCloud, umiRequest } from '@/services'

import type { State } from '../init'

const fieldPick = ['id', 'name', 'title', 'dataType', 'dataFormater', 'formulaSchema', 'dataSourcePath']

/** 获取字段类型 */
const getDataType = type => {
  const mysqlNumberTypes = ['INT', 'DECIMAL', 'DOUBLE', 'TINY', 'BIGINT', 'FLOAT', 'SMALLINT', 'MEDIUMINT']
  if (mysqlNumberTypes.includes(_.toUpper(type))) return 'number'
  if (/DATE|TIME/i.test(type)) return 'date'
  return 'string'
}

/**
 * 加载相关的
 * @param state
 * @param param1
 * @returns
 */
export const loadActions = (state: State, { markRaw }) => {

  // 加载指标列表
  const loadIndiceList = async (forceLoad?: boolean, indiceIds?: string[]) => {

    if (!forceLoad && state.isPreview) return
    if (state.withoutQueryType) return
    if (!state.contains.includes('indices')) return

    if (!forceLoad && !_.isEmpty(state.indicesMap)) return

    // 加载分组
    if (state.indicesLoading) return
    state.indicesLoading = true

    const where: any = {
      analyticsVisible: true
    }
    if (indiceIds) {
      where.id = indiceIds
      if (_.isEmpty(indiceIds)) return
    }

    try {
      const [indicesUnits, res, metaRes, indicesSpec] = await Promise.all([
        getIndicesUnit(),
        umiRequest.get('/app/indices-base-category/list?cCache=600&sCache=3600', {
          headers: { 'micro-apps': 'sugo-total-mut' }
        }),
        MutCloud.$fn.indiceMetaFind({
          limit: -1, where,
          attributes: {
            include: [
              'categorys',
              'code',
              'name',
              'id',
              'themeId',
              'unit',
              'type',
              'versions',
              'granularitys',
              'dimensions',
              'description',
              'dataDepartment'
            ] as any
          }
        }),
        MutCloud.IndicesSpec.findAll({ attributes: ['id', 'name'], order: [['createdAt', 'DESC']] })
      ])

      const groups = _.get(res, 'result', []).map(i => ({ ...i, key: i.id }))
      const groupsTree = arrayToTree(groups, {
        parentProperty: 'parentId',
        childrenProperty: 'children'
      })

      const indicesDimensionGroup = {}

      state.indicesUnits = markRaw(_.orderBy(_.map(indicesUnits, i => ({ id: i.id, title: i.name })), ['title']))
      state.indicesGroupTree = markRaw(groupsTree)
      state.indicesSpecMap = markRaw(_.keyBy(indicesSpec, 'id'))

      const indicesList = _.map(metaRes?.data, i => {
        _.forEach(i.dimensions, dim => {
          if (!indicesDimensionGroup[dim.id]) {
            indicesDimensionGroup[dim.id] = []
          }
          indicesDimensionGroup[dim.id].push(i)
        })

        return {
          ...i,
          granularitys: _.map(i.granularitys, g => g.key),
          groupIds: _.map(i.categorys, c => c.id),
          value: i.id,
          name: _.trim(i.name),
          label: _.trim(i.name),
          title: _.trim(i.name),
          key: i.id
        }
      })



      // 把指标塞进去，类型是 number
      const indiceFieldMap = {}
      const dimensionMap = {}
      const dimensionFieldMap = {}

      _.forEach(indicesList, item => {
        indiceFieldMap[item.id] = {
          id: item.id,
          name: item.code,
          title: item.name,
          type: 'indicesSpec',
          dataType: 'number',
          dataSourcePath: `indices/${item.id}/${item.code}/${item.type}`
        }
        _.forEach(item.dimensions, _dim => {
          const dim = { ..._dim, enName: _.toLower(_dim.enName) }

          dimensionMap[dim.id] = dim
          dimensionFieldMap[dim.id] = {
            id: dim.id,
            name: dim.enName,
            title: dim.cnName,
            dataType: 'string',
            dataSourcePath: 'indices/dimension' // 这里不知道是在哪个指标里面
          }
        })
      })

      state.indicesMap = markRaw(_.keyBy(indicesList, 'id'))
      state.indicesSize = _.size(indicesList)
      state.indicesDimensionGroup = markRaw(indicesDimensionGroup)
      state.dimensionMap = markRaw(dimensionMap)
      state.dimensionSize = _.size(dimensionMap)

      // 记录每个指标有哪些维度
      const fieldGroup: Record<string, any[]> = {}
      _.forEach(indicesList, item => {
        fieldGroup[item.id] = _.map(item.dimensions, dim => ({
          id: dim.id,
          name: dim.enName,
          title: dim.cnName,
          dataType: 'string',
          dataSourcePath: `indices/dimension/${item.id}` // 这里不知道是在哪个指标里面
        }))
      })

      state.fieldGroup = {
        ...state.fieldGroup,
        ...fieldGroup
      }

    } finally {
      state.indicesLoading = false
    }
  }

  /** 加载数据视图 */
  const loadDataViewList = async () => {
    if (_.size(state.datasetList) > 0) return

    const [groups, list] = await Promise.all([
      AbiCloud.DatasetGroup.findAll({ attributes: ['id', 'title'], order: [['createdAt', 'ASC']] }),
      AbiCloud.Dataset.findAll({
        where: { deleteAt: { $eq: null } },
        attributes: ['id', 'groupId', 'title', 'type', 'dbType', 'dbId', 'sqlVariable']
      })
    ])

    state.datasetGroup = groups
    state.datasetList = _.map(list, d => ({
      ...d,
      groupId: d.groupId,
      dbId: d.dbId,
      tableId: d.id,
      tableName: d.title,
      datasetId: d.id,
      datasetName: d.title
    }))
    state.datasetMap = _.keyBy(state.datasetList, 'id')
  }

  const loadDataViewDetail = async (id: string | string[]) => {
    id = _.castArray(id).filter(i => !state.datasetMap?.[i]?.sqlContent)

    if (_.isEmpty(id)) return

    const res = await AbiCloud.Dataset.findAll({
      where: { deleteAt: { $eq: null }, id: { $in: id } },
      attributes: ['id', 'groupId', 'title', 'type', 'dbType', 'dbId', 'sqlVariable', 'sqlContent']
    })
    const datasetMap = _.keyBy(res, 'id')
    state.datasetMap = {
      ...state.datasetMap,
      ...datasetMap
    }
  }

  /** 加载我的数据集列表 */
  const loadMyDatasetList = async () => {
    if (_.size(state.mydatasetList) > 0) return

    try {
      const userId = _.get(window, 'sugo.user.id')
      const isAdmin = _.get(window, 'sugo.user.type') === 'built-in'
      const res: any[] = await MutCloud.IndicesDocumentDataset.findAll({
        attributes: ['id', 'createdBy', 'name', 'templateConfig', 'tableId', 'dbId', 'connectId', 'physicalTableName'],
        where: isAdmin ? {} : { createdBy: userId }
      })
      state.mydatasetList = _.map(res, i => ({
        ...i,
        tableName: i.physicalTableName
      })) as any[]

      const fieldMap = {}
      const fieldDict = {}
      _.forEach(res, item => {
        fieldMap[item.id] = _.map(item.templateConfig, t => {
          const dataType = getDataType(t.type)
          const name = t.field
          const newItem = { ...t, dataType, name }
          newItem.dataSourcePath = `mydatasets/${item.connectId}/${item.dbId}/${item.physicalTableName}`
          fieldDict[t.id] = _.pick(newItem, fieldPick) as any
          return newItem
        })
      })
      state.fieldGroup = {
        ...state.fieldGroup,
        ...fieldMap
      }
    } catch (err) {
      console.error(err)
    }
  }

  // 这个只加载到连接和库
  const loadDataTableList = async () => {
    if (_.size(state.connectMap) > 0) return

    const connect = await loadConnections()
    state.connectMap = _.keyBy(connect, 'id')
    const dbMap = {}
    _.forEach(connect, c => {
      _.forEach(c.databaseAndSchemaList, t => {
        dbMap[t.id] = { ...t, connectId: c.id, connectType: c.type }
      })
    })
    state.dbMap = dbMap
  }

  /** 加载数据模型 */
  const loadTableModelList = async () => {
    if (_.size(state.tableModelList) > 0) return

    const [groups, list] = await Promise.all([
      AbiCloud.TableModelGroup.findAll({
        attributes: ['id', 'title'], order: [['createdAt', 'ASC']],
        usePermission: 'new'
      }),
      AbiCloud.TableModel.findAll({
        attributes: ['id', 'groupId', 'title', 'tableJoinMap'],
        order: [['createdAt', 'ASC']],
        usePermission: 'new'
      })
    ])

    state.tableModelGroup = groups
    state.tableModelList = list

    const dict = _.keyBy(list, 'id')
    const dict2 = {}
    _.forEach(dict, (v, k) => {
      dict2[k] = { ...state.tableModelMap[k], ...v }
    })
    state.tableModelMap = dict2
  }


  const initDataSource = async (queryType?: string) => {
    queryType = queryType || state.queryType

    if (state.isPreview) return
    if (queryType === 'dataview') return loadDataViewList()
    if (queryType === 'datatable') return loadDataTableList()
    if (queryType === 'mydataset') return loadMyDatasetList()
    if (queryType === 'tablemodel') return loadTableModelList()
  }

  const loadDataViewField = async (id: string) => {
    if (state.fieldGroup[id]) return // 加载过

    let fields: any[] = await AbiCloud.DatasetDetail.findAll({
      where: { datasetId: id, status: 'show', deleteAt: { $eq: null } },
      attributes: ['id', 'name', 'dataType', 'dataFormater', 'title'],
      order: [['seq', 'DESC']]
    })
    fields = _.uniqBy(fields, 'name')
    if (fields) {
      const fieldMap = {}
      _.forEach(fields, f => {
        f.dataSourcePath = `dataview/${id}`
        fieldMap[f.id] = _.pick(f, fieldPick)
      })
      state.fieldGroup[id] = _.values(fieldMap)
    }
  }

  /** 加载表字段 */
  const loadTableField = async (id: string, dbId: string, connectId: string, connectType?: string) => {
    if (state.fieldGroup[id]) return


    const connect = state.connectMap[connectId]
    const _connectType = connectType || connect?.type

    // 对应 数据源管理/数据表/元数据 -> 字段用途
    const useFieldTypeMap = {
      1: 'number',
      2: 'string',
      3: 'date'
    }

    const fields = await loadFields(id, dbId, connectId)
    const fieldMap = {}
    _.forEach(fields, f => {
      const dataType = useFieldTypeMap[(f as any).columnUsage] || getDataType(f.columnType)

      const newItem = { ...f, title: f.columnAlias || f.columnName, name: f.columnName, dataType } as any
      newItem.dataSourcePath = `datatable/${_connectType}/${connectId}/${dbId}/${id}`
      newItem.id = String(newItem.id)
      fieldMap[String(f.id)] = _.pick(newItem, fieldPick)
      return newItem
    }).filter(i => !i.isDeleted)

    state.fieldGroup[id] = _.values(fieldMap)
  }

  /** 加载我的数据集列表 */
  const loadMyDatasetFieldList = async (id: string) => {
    if (state.fieldGroup[id]) return
    try {
      const item: any = await MutCloud.IndicesDocumentDataset.findOne({
        where: { id },
        attributes: ['id', 'name', 'templateConfig', 'tableId', 'dbId', 'connectId', 'physicalTableName']
      })
      if (!item) return

      const fieldMap = {}
      _.forEach(item.templateConfig, t => {
        const dataType = getDataType(t.type)
        const name = t.field
        const newItem = { ...t, dataType, name }
        newItem.dataSourcePath = `mydatasets/${item.connectId}/${item.dbId}/${item.physicalTableName}`
        fieldMap[t.id] = _.pick(newItem, fieldPick) as any
        return newItem
      })

      state.fieldGroup[id] = _.values(fieldMap)
    } catch (err) {
      console.error(err)
    }
  }

  /** 加载数据模型的表字段。如果是组合表则去详情拿 */
  const loadTableModelField = async (id: string, dbId?: string, connectId?: string, connectType?: string) => {
    const [k = '', tableModelId] = _.split(id, '|')
    if (_.startsWith(id, 'subQuery_')) {
      const tId = tableModelId
      const tableModel = state.tableModelMap[tId] || _.find(state.tableModelMap, t => !!t.tableParamsMap[k])
      if (tableModel) {
        const fieldMap = {}
        const arr: any[] = []
        _.forEach(_.get(tableModel, ['tableParamsMap', k, 'subFieldMap']), item => {
          fieldMap[item.id] = item
          arr.push(item)
        })
        // todo: field.id 有前缀，为了防止覆盖
        state.fieldGroup[k] = _.values(fieldMap)
      } else {
        console.log('tableModel 未初始化请检查')
      }
    } else if (k && k.indexOf('_t') === -1) {
      await loadTableField(k, dbId || '', connectId || '', connectType)
    }
  }


  /** 加载某表的字段列表 */
  const loadFieldList = async (id: string, dbId?: string, connectId?: string, connectType?: string, queryType?: string, info?: any) => {
    queryType = queryType || state.queryType

    // 加载数据模型表主题的
    if ((info?.key?.indexOf('_tableRealm_') > -1 || String(id).indexOf('_tableRealm_') > -1) && queryType === 'tablemodel') {
      // id 可能是一个 tableId，需要拿到真正的 tableModel ID
      const rawKey = String(id).indexOf('_tableRealm_') > -1 ? id : info.key
      const [tableModelId, tableParamsKey] = _.split(rawKey, '_tableRealm_')

      const tableParams = state.tableModelMap[tableModelId || '']

      if (tableParams) {
        const theme = tableParams.tableThemeMap?.[tableParamsKey]
        let fields = _.filter(tableParams.fieldMap, v => (v.dataSourcePath || '').indexOf(tableParamsKey) > -1)

        if (!_.isEmpty(theme?.ignores)) {
          fields = _.filter(fields, f => !_.includes(theme?.ignores, f.id)) // 排除忽略的
        }

        fields = _.map(fields, f => {
          const [, , pKey, ...rest] = _.split(f.dataSourcePath, '/')
          const tableId = _.last(rest)

          return {
            ...f,
            title: theme?.fieldConfigMap[f.id]?.alias || f.title,
            col: `t_${f.name.replace(/^([A-Z])_/, '$1.')}`,
            tableId,
            tableCode: `t_${f.name.replace(/^([A-Z])_.*/, '$1')}`,
            tableParamsKey: pKey,
            tableModelId
          }
        })

        state.fieldGroup[rawKey] = _.orderBy(fields, 'title')
      }

      return
    }

    // 例如 abc,dab 变成批量查询
    if (String(id).indexOf(',') > -1) {
      const ids = _.split(id, ',')
      await Promise.all(ids.map(i => loadFieldList(i, dbId, connectId, connectType, queryType)))
      if (queryType === 'tablemodel' || queryType === 'datatable') {
        let tableFields: (typeof state.fieldGroup)[] = []
        _.forEach(ids, i => {
          tableFields = tableFields.concat(state.fieldGroup[i])
        })
        // 两个表的字段合并
        state.fieldGroup[id] = tableFields
      }

      return
    }

    try {
      if (queryType === 'dataview') await loadDataViewField(id)
      if (queryType === 'datatable') await loadTableField(id, dbId || '', connectId || '', connectType)
      if (queryType === 'mydataset') await loadMyDatasetFieldList(id)
      if (queryType === 'tablemodel') await loadTableModelField(id, dbId || '', connectId || '', connectType)
    } catch (err) {
      console.error(err)
    } finally {
      // ...
    }

    return true
  }

  /** 加载数据表 */
  const loadDataTableItem = async (dbId: string) => {
    const tables = await loadTables('', dbId)
    const groups = _.groupBy(tables, 'dbId')
    const db = state.dbMap[dbId] || {}
    const dbTableGroup = {}

    if (state.dbTableLoadedMap[dbId]) return

    _.forEach(groups, (list, k) => {
      dbTableGroup[k] = _.map(list, i => ({
        ...i,
        title: i.tableAlias || i.tableName,
        connectType: db.connectType,
        connectId: db.connectId,
        dbId: db.id,
        tableId: i.id,
        tableName: i.tableName,
        dbName: db.dbName,
        dbType: db.dbType
      }))
    })

    state.dbTableGroup = {
      ...state.dbTableGroup,
      ...dbTableGroup
    }

    if (!_.isEmpty(dbTableGroup)) {
      state.dbTableLoadedMap[dbId] = true
    }
  }

  const loadTableModelDetail = async (id: string) => {
    if (state.tableModelMap[id]?.tableParamsMap) return copy(state.tableModelMap[id])
    const res = await AbiCloud.TableModel.findByPk(id)
    state.tableModelMap[id] = {
      ...state.tableModelMap[id],
      ...res
    }
    return res
  }

  return {
    loadIndiceList,
    initDataSource,
    loadFieldList,
    loadDataTableItem,
    loadTableModelDetail,
    loadDataViewDetail
  }
}
