/* eslint-disable max-len */
/* eslint-disable no-nested-ternary */
/* eslint-disable guard-for-in */
import { getTimeRelativeRangeSelectTime } from '@sugo/design/dist/esm/components/time-relative-range-select/utils'
import { KnexQueryBuilder } from '@sugo/sql-builder'
import { message } from 'antd'
import Async from 'async'
import dayjs from 'dayjs'
import copy from 'fast-copy'
import _ from 'lodash'

import type { CausationAnalysisResult } from '@/pages/causation-analysis/type'
import { Cloud } from '@/services'
import { querySql } from '@/utils/sql'

import type { State } from '../init'

type QueryArgs = {
  valueFieldName: string,
  timeFieldName: string,
  timeRange: string[],
  context: {
    tableModelId?: string
    dbType?: string
    dbId?: string
    tableName: string
    queryType: string
    valueField: any
    [key: string]: any
  },
  dimensionName?: string,
  dir?: 'top' | 'bottom'
}

/**
 * 查询
 * @param state
 * @param param1
 * @returns
 */
export const queryActions = (state: State, { getAction }) => {

  // 1. 按度量去分任务
  // 2. 查询字段列表
  // 3. 计算任务总量
  // 4. 开始执行任务
  // 5. 输出进度

  // 查询指标的
  const queryTotalByIndice = async (args: QueryArgs) => {
    const { context, timeRange, dimensionName, dir } = args
    const isTotal = !dimensionName

    const query: Record<string, any> = {
      themeId: state.id,
      // orderBy: [{ field: 'name', dir: 'desc' }],
      orderBy: (() => {
        if (dimensionName && dir) {
          return dir === 'top' ? [{ field: 'value', dir: 'desc' }] : [{ field: 'value', dir: 'asc' }]
        }
        return [{ field: 'name', dir: 'asc' }]
      })(),
      queryTotal: false,
      roleIds: ['auto'],
      type: 'indicesTable',
      queryMode: 'groupBy',
      filters: [{
        eq: _.map(timeRange, t => dayjs(t).toISOString()),
        col: 'time_date',
        op: 'in-ranges'
      }],
      limit: dir ? 10 : 1000,
      offset: 0,
      // 设置缓存
      // 'PT4H' as string | number, // 实时的数据就不要设置
      // staleAfter: !cacheConfig.enableCache ? 0 : cacheConfig.cacheTime,
      /** sql 变量 */
      timeBucket: 'MONTH' as string | undefined,
      tableName: 'index_data_20221024',
      fieldsBinding: {
        value: {
          dataType: 'number',
          id: `${context.valueField.id}:default_spec`,
          name: `_tempMetric_${context.valueField.name}`,
          title: context.valueField.title,
          type: 'indicesSpec'
        },
        name: {
          dataType: 'date',
          id: isTotal ? 'time_date' : dimensionName,
          name: isTotal ? 'time_date' : dimensionName,
          title: '统计时间',
          type: 'indicesDims'
        }
      }
    }

    const res = await Cloud.$fn.queryData({ query })
    const list = _.get(res, '[0].resultSet') || res || []
    return _.map(list, i => _.mapValues(i, f => {
      if (_.isNumber(f)) return _.round(f, 4)
      return f
    }))
  }


  // 查询 sql 的
  const queryTotal = async (args: QueryArgs) => {
    // eslint-disable-next-line prefer-const
    let { context, timeFieldName, timeRange, valueFieldName, dimensionName, dir } = args

    if (state.queryTaskState.status === 'stopped' || state.destroyed) {
      throw new Error('暂停了')
    }

    const fixCol = col => col?.replace(/^([A-Z])_(.*)/, '$1.$2') || ''
    // 对数据模型字段进行修复
    if (context.tableModelId) {
      valueFieldName = fixCol(valueFieldName)
      timeFieldName = fixCol(timeFieldName)
      dimensionName = fixCol(dimensionName)
    }

    const isTotal = !dimensionName
    const builder = new KnexQueryBuilder({ dbType: context.dbType })
    builder.schema({
      select: _.compact([
        { sum: `${valueFieldName} as value` },
        [isTotal ? timeFieldName : dimensionName!, 'name']
      ]),
      groupBy: [isTotal ? timeFieldName : dimensionName!],
      from: { raw: context.tableName },
      filters: [{
        col: timeFieldName,
        eq: timeRange, // TODO: 时间格式不正确
        op: 'in-ranges'
      }],
      orderBy: (() => {
        if (dimensionName && dir) {
          return dir === 'top' ? [[valueFieldName, 'desc']] : [[valueFieldName, 'asc']]
        }
        return [[timeFieldName, 'asc']]
      })(),
      limit: dir ? 10 : 1000
    })
    const sql = builder.toSql()
    // await new Promise(rs => setTimeout(rs, 500))
    let res

    try {
      if (context.queryType === 'indices') {
        res = {
          data: await queryTotalByIndice(args)
        }
      } else {
        res = await querySql({
          dbId: context.dbId,
          dbType: context.dbType,
          cacheRefresh: true,
          sql,
          themeId: state.id
        })
      }
    } catch (err) {
      // console.log(context)
      console.error(err)
    }

    const data = _.get(res, 'data', []).map(i => ({
      ...i,
      value: i.value ? Number(i.value) : null
    }))
    const total = _.isEmpty(data) ? null : _.reduce(data, (o, v) => {
      const val = Number(v.value)
      if (_.isNaN(val) || _.isNil(val)) return o
      if (_.isNumber(val)) return o + val
      return o
    }, 0)

    return [total, data]
  }

  // 获取数据视图的表 sql
  const getDatasetTableSql = async tableInfo => {
    const { loadDataViewDetail } = getAction()
    await loadDataViewDetail(tableInfo.datasetId)
    const dataset = state.datasetMap[tableInfo.datasetId!]
    return `(${dataset?.sqlContent}) ttt`
    // _.set(tableInfo, 'tableName', `(${dataset?.sqlContent}) ttt`)
  }

  // 获取数据模型的表 sql
  const getTableModelSql = async (field, tableInfo) => {
    const { loadTableModelDetail } = getAction()

    await loadTableModelDetail(tableInfo.tableModelId)
    const tableModel = state.tableModelMap[tableInfo.tableModelId!] || {}
    const tableJoinMap = tableModel?.tableJoinMap || {}
    const tableParamsMap = tableModel?.tableParamsMap || {}

    const [, , tableParamsKey] = _.split(field.dataSourcePath, '/')

    const joins = _.filter(tableJoinMap, (_v, k) => _.startsWith(k, tableParamsKey))
    const builder = new KnexQueryBuilder({ dbType: tableInfo.dbType })

    const wrap = str => builder.getTableName(str, true)
    const fixSubSql = tableParams => tableParams.subSql || ''

    // 先生成主表的
    const mt = tableParamsMap[tableParamsKey]
    const sql1 = mt?.isCombination ? `(${fixSubSql(mt)}) ${wrap(mt?.indexCode)}` : `${wrap(mt?.tableName)} ${wrap(mt?.indexCode)}`

    // 关联表
    const sql2 = _.map(joins, i => {
      const t = tableParamsMap[i.right.key]
      const l = tableParamsMap[i.left.key]
      const w = i.on[0]
      const op = w.op === 'equal' ? '=' : '!='

      if (!t) return ''

      return `${i.type} ${wrap(t?.tableName)} ${wrap(t?.indexCode)} ON
    ${wrap(`${l?.indexCode}.${w.left?.name}`)} ${op} ${wrap(`${t?.indexCode}.${w.right?.name}`)}`
    })

    const sql = [sql1].concat(_.compact(sql2)).join('\n  ')
    return `(${sql})`
  }

  const computeTotal = (curentInfo, previousInfo, targetValue?: number | string) => {
    const v0 = previousInfo[0] || null
    const v1 = curentInfo[0] || null
    const isPercentage = /%/.test(String(targetValue))
    const _targetValue = isPercentage ? _.round(Number.parseFloat(String(targetValue)) * 0.01 * v0, 2) : _.toNumber(targetValue)
    return {
      currentValue: v1,
      previousValue: v0,
      currentData: curentInfo[1],
      previousData: previousInfo[1],
      deltaValue: (v1 || 0) - (v0 || 0),
      deltaRate: (v1 === 0 && v0 === 0) ? 0 : _.round(((v1 || 0) - (v0 || 0)) / (v0 || 0), 4),
      targetValue: _targetValue,
      targetRate: _.round(v1 / _targetValue * 1, 2)
    }
  }

  const queryTask = async (valueFieldKey?: string) => {
    const { loadFieldList, loadIndiceList } = getAction()
    const valueFieldMap = copy(valueFieldKey ? _.pick(state.valueFieldMap, valueFieldKey) : state.valueFieldMap)

    if (_.isEmpty(valueFieldMap)) {
      message.warning('请先添加度量，再进行预演')
      return
    }

    const s = Date.now()

    state.queryTaskState.status = 'running'
    state.queryTaskState.computedAt = new Date().toISOString()
    state.queryTaskState.texts = []
    state.queryTaskState.curent = 0
    state.queryTaskState.total = 0
    state.destroyed = false

    const texts = state.queryTaskState.texts

    // 加载指标信息
    const indiceIds = _.uniq(_.values(valueFieldMap).filter(i => i.queryType === 'indices').map(i => i.field.id))

    if (!_.isEmpty(indiceIds)) {
      await loadIndiceList(true, indiceIds)
    }

    // 要想加载详情，才能加载字段
    await Promise.all(
      _.map(valueFieldMap, async item => {
        const tableInfo = item.tableInfo
        // 预加载
        if (/dataview/.test(item.queryType)) {
          _.set(tableInfo, 'tableName', await getDatasetTableSql(item.tableInfo))
        }
        // 数据模型 join
        if (/tablemodel/.test(item.queryType)) {
          _.set(tableInfo, 'tableName', await getTableModelSql(item.field, item.tableInfo))
        }
        return true
      })
    )

    // 1. 先把字段查询处理，才能确定任务大小
    // 假设这个阶段就已经知道了所有的字段了
    await Promise.all(
      _.map(valueFieldMap, v => loadFieldList(
        v.tableInfo.id,
        v.tableInfo.dbId, v.tableInfo.connectId,
        v.tableInfo.connectType, v.queryType, v.tableInfo
      ))
    )

    // 2. 计算总量
    const fieldGroup: Record<string, any[]> = {}
    let total = 0
    _.forEach(valueFieldMap, v => {
      total += 1 * 2

      const rawFields = state.fieldGroup[v.tableInfo.id] || state.fieldGroup[v.tableInfo.key] || []
      let fields = _.filter(rawFields, f => f.dataType === 'string' && f.id !== v.timeField.id)

      if (!_.isEmpty(v.followDimensions)) fields = fields.filter(i => v.followDimensions!.includes(i.id))
      if (!_.isEmpty(v.ignoreDimensions)) fields = fields.filter(i => !v.ignoreDimensions!.includes(i.id))

      fieldGroup[v.key] = copy(fields)
      total += fields.length * 4
    })

    state.queryTaskState.total = total

    let curent = 0 // 当前已经执行完成的

    const stepAdd = () => {
      curent += 1
      state.queryTaskState.curent = curent
      state.queryTaskState.texts = texts
    }

    const analysisResult: CausationAnalysisResult['analysisResult'] = {}
    const analysisDimensionResult: CausationAnalysisResult['analysisDimensionResult'] = {}
    const list = _.orderBy(_.values(valueFieldMap), 'order', 'asc')

    // 3. 开始执行任务
    for (const index in list) {
      const item = list[index]
      const fieldList = fieldGroup[item.key] || []
      const context = { ...item.tableInfo, queryType: item.queryType, valueField: item.field }

      // 1. 先查询指标总数
      const field = item.field
      const timeField = item.timeField
      const valueFieldName = field.name
      const timeFieldName = timeField.name

      const curentTime = _.isArray(item.currentPeriodTime) ?
        item.currentPeriodTime :
        getTimeRelativeRangeSelectTime(item.currentPeriodTime as any).split(' ~ ')

      const previousTime = (() => {
        if (_.isArray(item.previousPeriodTime)) return item.previousPeriodTime
        if (item.previousPeriodTime === 'mom') return _.map(curentTime, t => dayjs(t).add(-1, 'month').format('YYYY-MM-DD'))
        if (item.previousPeriodTime === 'yoy') return _.map(curentTime, t => dayjs(t).add(-1, 'year').format('YYYY-MM-DD'))
        return []
      })()

      // 查询本期的
      const start = Date.now()
      let curentInfo: any[] = []
      texts.push(`分析 “${field.alias || field.title}” 的本期值`)
      curentInfo = await queryTotal({ valueFieldName, timeFieldName, timeRange: curentTime, context })
      stepAdd()

      // 查询基期的
      let previousInfo: any[] = []
      texts.push(`分析 “${field.alias || field.title}” 的基期值`)
      previousInfo = await queryTotal({ valueFieldName, timeFieldName, timeRange: previousTime, context })
      stepAdd()

      analysisResult[item.key] = {
        valueFieldId: field.id,
        field: copy(field),
        currentPeriodTime: curentTime,
        previousPeriodTime: previousTime,
        computedAt: new Date().toISOString(),
        computeTime: Date.now() - start,
        total: computeTotal(curentInfo, previousInfo, item.targetValue),
        dimensions: fieldList,
        config: _.pick(item, ['currentPeriodTime', 'previousPeriodTime'])
      }

      state.analysisResult[item.key] = analysisResult[item.key]

      // 2. 再查询各个维度的数量
      await Async.timesLimit(fieldList.length, 2, async (idx, done) => {
        const dimension = fieldList[idx]
        const dimensionName = dimension.name

        const start2 = Date.now()
        // 查询本期维度 TOP10
        let dimCurentTopInfo: any[] = []
        texts.push(`分析 “${field.alias || field.title}/${dimension.title}” 的本期值`)
        dimCurentTopInfo = await queryTotal({ valueFieldName, timeFieldName, timeRange: curentTime, context, dimensionName, dir: 'top' })
        stepAdd()

        // 查询本期维度 BOTTOM10
        let dimCurentBottomInfo: any[] = []
        texts.push(`分析 “${field.alias || field.title}/${dimension.title}” 的本期值`)
        dimCurentBottomInfo = await queryTotal({ valueFieldName, timeFieldName, timeRange: curentTime, context, dimensionName, dir: 'bottom' })
        stepAdd()

        // 查询基期维度 TOP10
        let dimPreviousTopInfo: any[] = []
        texts.push(`分析 “${field.alias || field.title}/${dimension.title}” 的基期值`)
        dimPreviousTopInfo = await queryTotal({ valueFieldName, timeFieldName, timeRange: previousTime, context, dimensionName, dir: 'top' })
        stepAdd()

        // 查询基期维度 BOTTOM10
        let dimPreviousBottomInfo: any[] = []
        texts.push(`分析 “${field.alias || field.title}/${dimension.title}” 的基期值`)
        dimPreviousBottomInfo = await queryTotal({ valueFieldName, timeFieldName, timeRange: previousTime, context, dimensionName, dir: 'bottom' })
        stepAdd()

        // 维度成员
        const members = _.uniq(_.concat(dimCurentTopInfo[1], dimCurentBottomInfo[1], dimPreviousTopInfo[1], dimPreviousBottomInfo[1]).map(i => i.name))
        const curentInfoList = _.uniqBy(_.concat(dimCurentTopInfo[1], dimCurentBottomInfo[1]), 'name')
        const previousInfoList = _.uniqBy(_.concat(dimPreviousTopInfo[1], dimPreviousBottomInfo[1]), 'name')
        const curentInfoDict = _.keyBy(curentInfoList, 'name')
        const previousInfoDict = _.keyBy(previousInfoList, 'name')

        const dimCurentTotal = _.reduce(curentInfoList, (o, v) => {
          const val = Number(v.value)
          if (_.isNaN(val) || _.isNil(val)) return o
          if (_.isNumber(val)) return o + val
          return o
        }, 0)

        const dimPreviousTotal = _.reduce(previousInfoList, (o, v) => {
          const val = Number(v.value)
          if (_.isNaN(val) || _.isNil(val)) return o
          if (_.isNumber(val)) return o + val
          return o
        }, 0)

        const dimTotalChange = dimCurentTotal - dimPreviousTotal

        // ....
        analysisDimensionResult[dimension.id] = {
          valueFieldId: field.id,
          valueFieldKey: item.key,
          field: copy(dimension),
          computeTime: Date.now() - start2,
          computedAt: new Date().toISOString(),
          total: {
            currentValue: dimCurentTotal,
            previousValue: dimPreviousTotal,
            deltaValue: dimTotalChange,
            deltaRate: _.round(dimTotalChange / dimPreviousTotal, 4),
            currentData: curentInfoList,
            previousData: previousInfoList
          },
          members: members.map(name => {
            const v1 = curentInfoDict[name]
            const v2 = previousInfoDict[name]
            const _total = computeTotal([v1?.value], [v2?.value])
            const mu = _total.deltaValue * (analysisResult[item.key].total.deltaValue || 0)
            const isNew = !v2 && v1
            const isChange = v1?.value !== v2?.value
            const isRemoved = v2 && !v1

            const direction = (() => {
              if (_.isNaN(mu)) return 'none'
              if (mu < 0) return 'negative'
              if (mu > 0) return 'positive'
              if (mu === 0) return 'invariable'
              return 'none'
            })()

            const obj: CausationAnalysisResult['analysisDimensionResult'][string]['members'][number] = {
              dimension: dimension.title,
              direction,
              member: name,
              totalContribution: _.round(_total.deltaValue / dimTotalChange, 2),
              total: _total,
              isNew,
              isChange,
              isRemoved
            }

            return obj
          })
        }

        done()
      })
    }

    const valueFieldKeys = _.keys(state.valueFieldMap)

    state.queryTaskState.status = 'done'
    state.queryTaskState.computeTime = Date.now() - s
    state.analysisResult = _.pick({
      ...state.analysisResult,
      ...analysisResult
    }, valueFieldKeys)

    state.analysisDimensionResult = _.pickBy({
      ...state.analysisDimensionResult,
      ...analysisDimensionResult
    }, d => valueFieldKeys.includes(String(d.valueFieldKey)))

    const active = _.orderBy(state.valueFieldMap, 'order', 'asc').find(i => !_.isNaN(state.analysisResult[i.key].total?.deltaRate))
    // 默认激活
    if (active && (!state.activeChartValueFieldKey || !state.valueFieldMap[state.activeChartValueFieldKey])) {
      state.activeChartValueFieldKey = active?.key
    }
  }

  return {
    queryTask
  }
}
