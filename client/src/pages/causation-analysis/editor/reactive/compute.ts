import { modifyTreeNode } from '@sugo/design/functions'
import arrayToTree from 'array-to-tree'
import _ from 'lodash'

import type { State, StateRef } from './init'

export const causationAnalysisEditorCompute = (state: State, ref: StateRef) => ({

  /**
 * 界面渲染的树结构
 * 界面渲染只要 key, title, children
 */
  dataSourceTree: () => {
    if (state.queryType === 'datatable') {
      // 数据源 - 库 - 表
      const list = _.orderBy(_.map(state.dataSourceList, item => ({
        id: item.id,
        key: `type_${item.id}`,
        title: _.upperFirst(_.camelCase(item.title)),
        selectable: false,
        children: _.filter(state.connectMap, v => v.type === item.title).map(conn => ({
          key: `connect_${conn.id}`,
          id: conn.id,
          title: conn.name,
          selectable: false,
          children: _.map(conn.databaseAndSchemaList, db => ({
            id: db.id, key: `db_${db.id}`, title: db.dbAlias || db.dbName || '未知库',
            type: 'db',
            isLeaf: false,
            selectable: false,
            children: _.map(state.dbTableGroup[db.id], t => ({
              id: String(t.id),
              key: String(t.id),
              title: t.tableAlias || t.tableName,
              connectType: item.id,
              connectName: conn.name,
              connectId: t.connectId,
              dbId: t.dbId,
              tableId: t.tableId,
              tableName: t.tableName,
              dbName: t.dbName,
              dbType: t.dbType,
              dbCatalog: db.catalog,
              isLeaf: true
            }))
          }))
        }))
      })), 'title')
      return list.filter(i => !_.isEmpty(i.children))
    }
    // 分组 - 数据模型 - 表
    if (state.queryType === 'tablemodel') {
      const groups = _.groupBy(state.tableModelList, v => v.groupId || 'ALL')

      const list = [{
        id: 'ALL', title: '全部', key: 'ALL', selectable: false,
        type: 'tableModelGroup'
      }].concat(_.map(state.tableModelGroup, group => ({
        id: group.id,
        title: group.title,
        key: group.id,
        selectable: false,
        type: 'tableModelGroup',
        parentId: group.parentId
      })))

      const getTables = tableModel => {
        if (!tableModel?.tableThemeMap) return []

        const theme = _.values(tableModel.tableThemeMap).map(i => ({
          ...i,
          id: i.key,
          key: `${tableModel.id}_tableRealm_${i.key}`,
          title: i.title,
          tableId: i.tableId,
          tableModelName: tableModel.title,
          tableModelId: tableModel.id,
          groupId: tableModel.groupId,
          type: 'tableModel-tableTheme',
          isLeaf: true
        }))

        return _.concat(theme)
      }

      const pickTableModel = d => {
        const c = getTables(state.tableModelMap[d.id])
        return {
          id: d.id,
          key: d.id,
          title: d.title,
          type: 'tableModel',
          isLeaf: false,
          children: c // 模型里面的表
        }
      }

      let tree = arrayToTree(list, { parentProperty: 'parentId' })
      tree = modifyTreeNode(tree, 0, [], node => {
        if (_.isObject(node) as any && node.id && node.type === 'tableModelGroup') {
          node.children = _.map(groups[node.id], pickTableModel)
        }
      })

      return tree
    }
    if (state.queryType === 'dataview') {
      // 数据视图分组 - 数据视图
      const groups = _.groupBy(state.datasetList, 'groupId')
      const pickDataset = d => ({
        ...d,
        id: d.id,
        key: d.id,
        title: d.title,
        isLeaf: true
      })
      const all = [{
        id: 'ALL', title: '全部', key: 'ALL', selectable: false,
        children: _.map(state.datasetList, d => pickDataset(d))
      }]

      const list = _.map(state.datasetGroup, group => ({
        id: group.id,
        title: group.title,
        key: group.id,
        selectable: false,
        children: _.map(groups[group.id], d => pickDataset(d))
      })).filter(i => i.children.length > 0)

      return all.concat(list)
    }

    // 我的数据集 - 待去掉
    if (state.queryType === 'mydataset') {
      return _.map(state.mydatasetList, i => ({
        ...i, id: i.id, key: i.id, title: i.name, isLeaf: true
      }))
    }
    // 项目 - 分组 - 子分组 - 标签
    // if (state.queryType === 'usertags') {
    //   const tagMap = _.groupBy(state.userTagMap, v => `${v.groupId},${v.projectId}`)
    //   const groupMap = _.mapValues(_.groupBy(state.userTagGroup, 'projectId'), list => {
    //     const groups = _.map(_.orderBy(list, 'order', 'desc'), item => ({
    //       id: item.id,
    //       title: item.title,
    //       key: item.id,
    //       type: 'tag-group',
    //       parentId: item.pid,
    //       projectId: item.projectId,
    //       projectName: item.project?.name,
    //       isLeaf: false
    //     }))

    //     const nodeChildRefMap: Record<string, any> = {}
    //     const nodeRefMap: Record<string, any> = {}
    //     _.forEach(groups, node => {
    //       const tagChilds = _.map(_.orderBy(tagMap[`${node.id},${node.projectId}`], 'order', 'asc'), i => ({
    //         id: i.id,
    //         title: i.title,
    //         key: i.id,
    //         type: 'tag-item',
    //         name: i.name,
    //         groupId: node.id,
    //         groupName: node.title,
    //         projectId: node.projectId,
    //         projectName: node.projectName,
    //         isLeaf: true
    //       }))
    //       nodeChildRefMap[node.id] = tagChilds
    //     })

    //     const groupTree = copy(arrayToTree(groups, { parentProperty: 'parentId' }))

    //     // 每个分组都有子节点
    //     modifyTreeNode(groupTree, 0, [], node => {
    //       if (node) nodeRefMap[node.id] = node
    //     })

    //     _.forEach(nodeRefMap, node => {
    //       if (node) {
    //         const tagChilds = _.map(nodeChildRefMap[node.id], tag => ({
    //           ...tag,
    //           groupName: _.map(node.keyPaths, k => nodeRefMap[k]?.title).filter(i => i).join('/')
    //         }))
    //         node.children = _.concat(node.children, tagChilds).filter(i => i)
    //       }
    //     })

    //     return _.filter(nodeRefMap, i => !i.parentId)
    //   })

    //   const tree = state.userTagProjects.map(i => ({
    //     title: i.name,
    //     key: i.id,
    //     id: i.id,
    //     datasourceId: i.datasourceId,
    //     type: 'tag-project',
    //     children: groupMap[i.id],
    //     isLeaf: false
    //   }))

    //   return tree
    // }
    return []
  },

  /** 获取字段列表 */
  fieldList: () => {
    // const queryType = state.queryType
    const id = String(state.selected.key).replace(/^table_/, '')
    let list: any[] = []

    // if (queryType === 'dataview') {
    //   list = state.fieldGroup[id] || []
    // }
    // if (queryType === 'datatable') {
    //   list = state.fieldGroup[id] || []
    // }
    // if (queryType === 'tablemodel') {
    //   list = state.fieldGroup[id] || []
    // }
    // if (queryType === 'mydataset') {
    // }
    list = state.fieldGroup[id] || []

    if (state.queryType === 'indices') {
      const timeField = {
        id: 'time_date',
        name: 'time_date',
        title: '统计时间',
        dataType: 'date',
        dataSourcePath: 'indices/time_date'
      }
      list.push(timeField)
    }

    return list
  },

  /** 获取指标字段列表 */
  valueFieldList: () => _.orderBy(_.values(state.valueFieldMap), 'order'),
  valueFieldSize: () => _.size(state.valueFieldMap),

  /** 获取图表的指标列表 */
  chartValueFieldList: () => {
    const fields = _.mapValues(state.valueFieldMap, v => ({
      key: v.key,
      field: v.field,
      data: state.analysisResult[v.key],
      order: v.order,
      config: v
    }))
    return _.orderBy(fields, 'order', 'asc')
  },
  /** 获取激活的图表数据 */
  getActiveChartValueFieldData: (key: (string & {}) | 'active') => {
    let item = state.valueFieldMap[key === 'active' ? state.activeChartValueFieldKey : key]
    if (!item) item = _.first(_.values(state.valueFieldMap))!
    return {
      key: item?.key,
      field: item?.field,
      data: state.analysisResult[item?.key],
      config: item,
      dimensions: _.pick(state.analysisDimensionResult, _.map(state.analysisResult[item?.key]?.dimensions, d => d.id))
    }
  }
})
