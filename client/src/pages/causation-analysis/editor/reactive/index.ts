import { defineRealm } from '@sugo/reactive'
import _ from 'lodash'

import type { CausationAnalysis } from '../../type'
import { causationActions } from './actions/causation'
import { loadActions } from './actions/load'
import { queryActions } from './actions/query'
import { causationAnalysisEditorCompute } from './compute'
import { initRef, initState, State } from './init'


const store = defineRealm({
  namespace: 'causation-analysis-editor',
  state: initState,
  ref: initRef,
  compute: causationAnalysisEditorCompute,
  setup: (state, { markRaw, ref, stores }) => {
    const getAction = () => stores.self().action

    const resetState = () => {
      _.forEach(initState, (v, k) => {
        state[k] = v
      })
    }

    const loadAction = loadActions(state, { markRaw })
    const causationAction = causationActions(state, {})
    const queryAction = queryActions(state, { getAction })

    const setCausationTitle = (title: string) => {
      state.title = title
    }

    const setQueryType = (queryType: State['queryType']) => {
      state.queryType = queryType
    }

    const setSelected = async (node: Partial<State['selected']>, queryType?: string) => {
      const { loadFieldList, loadTableModelDetail, loadDataViewDetail, loadDataTableItem } = loadAction
      queryType = queryType || state.queryType
      if (node.tableModelId) {
        await loadTableModelDetail(node.tableModelId)
      }
      else if (node.datasetId) {
        await loadDataViewDetail(node.datasetId)
      }
      else if (node.tableId) {
        await loadDataTableItem(node.dbId)
      }

      await loadFieldList(node.id, node.dbId, node.connectId, node.connectType, queryType, node)
      state.selected = node
    }

    // 添加字段
    const addFormValueField = (data: any, fieldDict: Record<string, any>) => {
      const prev = state.valueFieldMap[data.key]

      const valueField: CausationAnalysis['valueFieldMap'][string] = {
        key: prev ? prev.key : Math.random().toString(36).slice(2),
        chartType: 'line',
        previousPeriodTime: data.previousPeriodTime || 'mom',
        currentPeriodTime: data.currentPeriodTime,
        field: {
          ...fieldDict[data.fieldId],
          alias: data.fieldAlias
        },
        queryType: data.queryType,
        tableInfo: state.selected as any,
        timeField: fieldDict[data.timeFieldId],
        createdAt: new Date().toISOString(),
        filters: [],
        dataSouceSelected: data.dataSouceSelected,
        targetType: String(data.targetValue).indexOf('%') !== -1 ? 'rate' : 'value',
        targetValue: data.targetValue,
        order: prev?.order ? prev.order : _.size(state.valueFieldMap) + 1,
        followDimensions: data.followDimensions,
        ignoreDimensions: data.ignoreDimensions
      }

      if (data.queryType === 'indices') {
        valueField.field = {
          alias: valueField.field.alias,
          id: state.selected?.id,
          name: state.selected?.code,
          title: state.selected?.name,
          dataType: 'number',
          dataSourcePath: `indices/${state.selected?.id}`
        }
        valueField.timeField = {
          id: 'time_date',
          name: 'time_date',
          title: '统计时间',
          dataType: 'date',
          dataSourcePath: 'indices/time_date'
        }
      }

      state.valueFieldMap[valueField.key] = valueField
      state.valueFieldMap = { ...state.valueFieldMap }
    }

    const openChartView = (flag?: undefined) => {
      state.showChartView = true
      // if (!state.activeChartValueFieldKey) {
      //   state.activeChartValueFieldKey = _.first(compute.chartValueFieldList)?.key || ''
      // }
    }

    const stopQueryTask = () => {
      state.queryTaskState.status = 'stopped'
    }

    const delFormValueField = (key: string) => {
      state.valueFieldMap = _.omit(state.valueFieldMap, key)
    }

    const setActiveChartValueFieldKey = (key: string) => {
      state.activeChartValueFieldKey = key
    }

    const setChartUpdateKey = _.debounce(() => {
      state.chartUpdateKey = Math.random().toString(32)
    }, 500, {
      maxWait: 500
    })

    const setDestroyed = flag => {
      state.destroyed = flag
    }

    return {
      ...loadAction,
      ...causationAction,
      ...queryAction,

      resetState,
      openChartView,

      // ...
      setSelected,
      setQueryType,
      setCausationTitle,

      // 表单操作
      addFormValueField,
      delFormValueField,

      stopQueryTask,

      // 图表操作
      setActiveChartValueFieldKey,

      setChartUpdateKey,
      setDestroyed
    }
  }
})

export const {
  useRealmAction: useAction,
  useRealmCompute: useCompute,
  useRealmStore: useStore,
  realmProviderMemo
} = store
