import { VALID_DB_TYPES } from '@sugo/design/presets'
import _ from 'lodash'

import { CausationAnalysis, CausationAnalysisResult } from '../../type'

export const initState = {
  id: 'new',
  title: '未命名',
  type: 'dimensionCausation' as CausationAnalysis['type'],

  causation: undefined as CausationAnalysis | undefined,
  causationResult: undefined as CausationAnalysisResult | undefined,

  destroyed: false,

  isPreview: false,
  showChartView: false,
  initLoading: false,
  saveLoading: false,

  // 跟权限相关
  withoutQueryType: false,
  contains: ['indices', 'datatable', 'mydataset', 'dataview', 'tablemodel'] as string[],

  queryType: 'datatable' as 'indices' | 'datatable' | 'mydataset' | 'dataview' | 'tablemodel',
  selected: {
    id: '',
    tableId: '',
    dbId: '',
    connectId: '',
    tableModelId: '',
    datasetId: ''
  } as Record<string, any>,

  /** 激活的 */
  activeChartValueFieldKey: '',

  chartUpdateKey: '',

  // 指标
  indicesMap: {} as Record<string, any>,
  indicesLoading: false,
  indicesUnits: {} as Record<string, any>,
  indicesGroupTree: [] as any[],
  indicesSpecMap: {} as Record<string, any>,
  indicesSize: 0,
  indicesDimensionGroup: {} as Record<string, any>,
  dimensionMap: {} as Record<string, any>,
  dimensionSize: 0,

  // 数据视图
  datasetList: [] as any[],
  datasetGroup: {} as Record<string, any>,
  datasetMap: {} as Record<string, any>,

  // 库表
  /** 数据源 */
  dataSourceList: VALID_DB_TYPES.map(connType => ({
    id: connType,
    value: connType,
    title: connType,
    loadType: 'connection'
  })).filter(i => !['HIVE', 'FTP', 'KUDU'].includes(_.toUpper(i.title))) as any[],

  connectMap: {} as Record<string, any>,
  dbMap: {} as Record<string, any>,
  dbTableLoadedMap: {} as Record<string, boolean>,
  dbTableGroup: {} as Record<string, any>,

  // 我的数据集
  mydatasetList: [] as any[],

  // 数据模型
  tableModelGroup: {} as Record<string, any>,
  tableModelList: [] as any[],
  tableModelMap: {} as Record<string, any>,

  // 放字段分组的
  fieldGroup: {} as Record<string, any[]>,

  // 字段列表
  valueFieldMap: {} as CausationAnalysis['valueFieldMap'],
  cron: undefined as CausationAnalysis['cron'],
  aiAnswerConfig: undefined as CausationAnalysis['aiAnswerConfig'],
  categorys: [] as CausationAnalysis['categorys'],
  noticeConfig: undefined as CausationAnalysis['noticeConfig'],

  // 计算结果
  analysisResult: {} as CausationAnalysisResult['analysisResult'],
  analysisDimensionResult: {} as CausationAnalysisResult['analysisDimensionResult'],

  /** 查询任务 state */
  queryTaskState: {
    texts: [] as string[],
    total: 0,
    curent: 0,
    status: 'default' as 'default' | 'running' | 'stopped' | 'done' | 'error',
    computedAt: '' as string | Date,
    computeTime: 0
  }
}

export type State = typeof initState

export const initRef = {
  fieldMap: {} as Record<string, any>
}

export type StateRef = typeof initRef
