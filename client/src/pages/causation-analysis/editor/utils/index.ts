/* eslint-disable max-len */

import { getTimeRelativeRangeSelectTime } from '@sugo/design/dist/esm/components/time-relative-range-select/utils'
import dayjs from 'dayjs'
import _ from 'lodash'

export function transformTimeRange(time: string | string[] | object): string {
  if (_.isString(time)) dayjs(time).format('YYYY-MM-DD')
  if (_.isArray(time)) return _.map(time, t => dayjs(t).format('YYYY-MM-DD')).join(' ~ ')
  return getTimeRelativeRangeSelectTime(time as any)
}

/** 获取上期的 key */
export const getPreviousKey = (key: string, type: 'yoy' | 'mom'): string => {
  // 尝试解析日期（兼容多种格式）
  let date = dayjs(key)

  if (!date.isValid() && /^\d{4}$/.test(key)) date = dayjs(key, 'YYYY')
  if (!date.isValid() && /^\d{6}$/.test(key)) date = dayjs(key, 'YYYYMM')

  // 如果解析失败，尝试处理 YYYYMMDD 格式
  if (!date.isValid() && /^\d{8}$/.test(key)) date = dayjs(key, 'YYYYMMDD')

  // 如果仍然无效，返回原始 key 或抛出错误（根据需求调整）
  if (!date.isValid()) {
    return key // 或 throw new Error('Invalid date format');
  }

  // 计算前一年或前一个月
  if (type === 'yoy') {
    date = date.subtract(1, 'year')
  } else {
    date = date.subtract(1, 'month')
  }

  // 返回与输入格式匹配的结果
  if (/^\d{6}$/.test(key)) {
    return date.format('YYYYMM')
  }
  if (/^\d{8}$/.test(key)) {
    return date.format('YYYYMMDD')
  }
  if (key.includes('/')) {
    return date.format('YYYY/MM/DD')
  }
  return date.format('YYYY-MM-DD') // 默认返回 ISO 格式
}

/** 数据解读 */
export const inventoryAnalysis = (
  type: 'up' | 'down' | (string & {}),
  title: string,
  valueFieldTitle: string,
  // 同向
  positives: { name: string, value: number, rate: number | null }[],
  // 逆向
  negatives: { name: string, value: number, rate: number | null }[]
) => {
  // **经分析发现：**
  // - 主要： 由于 **天然薰香洗衣液2kg每瓶加量600g特惠装** 较上期下跌了 -1622048（降幅达 -55.62%），从而导致了 **库存变动金额** 下降。
  // - 次要： 由于 **贝贝健定时调量加热器+30毫升无香电蚊香液×2瓶** 较上期下跌了-724561（降幅达 -28.95%），进一步加剧了 **库存变动金额** 的下降趋势。

  // **此外，值得关注的是：**
  // - **尊宝洁厕宝50g*1** 和 **亮深层洁净型牙刷4支8.5折装** 在整体 **库存变动金额** 下降的环境下，存在**逆市增长**趋势，分别较上期上涨了 11408 和 5408（升幅达 5.01% 和 8.01%），建议重点关注。

  let template = `${title}<p class="mt-3"><i>经分析发现：</i></p>`
  const marks = ['主要原因', '次要原因', '接着']

  const downTails = ['直观的导致了', '进一步的加剧了', '间接的导致了']
  const downTrends = ['下跌', '下降趋势', '下降趋势']

  const upTails = ['侧面的促进了', '进一步的促进了', '间接的导致了']
  const upTrends = ['上涨', '上升趋势', '上升趋势']

  const field = valueFieldTitle

  const r1 = _.map(negatives, 'rate').map(i => _.isNil(i) ? 'N/A' : `${i}%`)
  const v1 = _.map(negatives, 'value').map(i => _.isNil(i) ? 'N/A' : i.toLocaleString())

  if (type === 'up') {
    const arr = _.map(positives, (i, idx) => `<li><b>${marks[idx]}</b>是由于<b>“${i.name}”</b>较上期上涨了 ${Number(i.value).toLocaleString()}（升幅达 ${_.isNil(i.rate) ? 'N/A' : i.rate}%），从而<trend>${upTails[idx]}</trend>“<value>${field}</value>”的<up>${upTrends[idx]}</up>。</li>`)
    template += `${arr.join('')}\n（以上是导致“${field}”变化的主要原因 ...）`

    if (!_.isEmpty(negatives)) {
      template += '<p class="mt-3"><i>此外，值得关注的是：</i></p>'
      const n = _.map(negatives, i => `<b>${i.name}</b>`).join('、')
      template += `<li>发现了 ${n} 在整体“<value>${field}</value>”上涨的环境下：<p class='mt-2'><b>“存在<up>逆市下跌</up>的趋势”</b>，</p>分别较上期<down>下跌了</down> ${v1.join('、')}（升幅达 ${r1.join('、')}），对整体存在拖累发展情况，建议请重点关注逆向下跌的。</li>`
    }
  }

  if (type === 'down') {
    const arr = _.map(positives, (i, idx) => `<li><b>${marks[idx]}</b>是由于<b>“${i.name}”</b>较上期<down>下跌了</down> ${Number(i.value).toLocaleString()}（降幅达 ${_.isNil(i.rate) ? 'N/A' : i.rate}%），从而<trend>${downTails[idx]}</trend>“<value>${field}</value>”的<down>${downTrends[idx]}</down>。</li>`)
    template += `${arr.join('')}\n（以上是导致“${field}”变化的主要原因 ...）`

    if (!_.isEmpty(negatives)) {
      template += '<p class="mt-3"><i>此外，值得关注的是：</i></p>'
      const n = _.map(negatives, i => `<b>${i.name}</b>`).join('、')
      template += `<li>我们发现了 ${n} 在整体“<value>${field}</value>”下降的环境下：<p class='mt-2'><b>“存在<up>逆市增长</up>的趋势”</b>，</p>分别较上期<up>上涨了</up> ${v1.join('、')}（升幅达 ${r1.map(i => _.isNil(i) ? 'N/A' : i).join('、')}），对整体存在促进增长情况，建议请重点关注逆向增长的。</li>`
    }
  }

  return template
}
