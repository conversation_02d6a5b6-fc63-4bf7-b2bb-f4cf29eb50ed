
.causation-analysis-page-card-item {
  min-width: 200px;
  width: max-content;
  min-height: 160px;
  height: max-content;
  background-color: #fff;
  margin: 8px;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  box-shadow: 1px 2px 6px rgba(@primary-color, 12%);
  float: left;

  .header {
    display: flex;
    align-items: center;
    padding: 8px 10px;

    .title {
      font-size: 15px;
      font-weight: 480;
      color: #444;
      cursor: pointer;
      &:hover {
        font-weight: bold;
        color: rgba(@primary-color, 1);
      }
    }
  }

  .content {
    padding: 4px 10px;
    padding-top: 0;
    position: relative;

    .analysis-table {
      // height: 100px;
      margin-bottom: 12px;

      .ant-table-content {
        height: 141px;
        width: 432px;
      }

      .ant-table-cell {
        padding: 5px !important;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;

        &:nth-child(2) {
          overflow: visible;
          white-space: normal;
          text-overflow: clip;
        }
      }

      .field-title {
        max-width: 130px;
        white-space: nowrap;      /* 禁止换行 */
        overflow: hidden;         /* 隐藏溢出内容 */
        text-overflow: ellipsis;  /* 显示省略号 */
      }

      .ant-table-thead .ant-table-cell {
        font-weight: normal;
      }

      .trend-chart {
        min-width: 50px;
        max-width: 45px;
        .echarts-for-react {
          transform: translate(-15px, 5px);
        }
      }

      .up {
        color: #ee5566;
      }
      .down {
        color: #24bdab;
      }
    }

    .list-more-btn {
      position: absolute;
      bottom: 25px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 11;
      display: none;
      opacity: 0.8;
    }

    .target-value {
      background-color: lighten(#a90, 5%);
      color: #fff;
      border-radius: 5px 0 0 5px;
      padding: 2px 6px;
      font-size: 13px;
      height: 28px;
      display: flex;
      align-items: center;
      text-shadow: 0 1px 0 rgba(#111, 0.16);

      + div {
        text-shadow: 0 1px 0 rgba(#111, 0.16);
        height: 28px;
        border-radius: 0 5px 5px 0;
      }
    }

    &:hover {
      .list-more-btn {
        display: block;
      }
    }
  }

  .footer {
    margin-top: 8px;
    border-top: 1px solid #f1f1f1;

    button {
      margin-right: 6px;
      padding: 4px 10px;
      flex: 1;
      height: auto;
      max-height: none;
      &:last-of-type {
        margin: 0;
      }
      &:hover {
        color: rgba(@primary-color, 1);
      }
    }
  }
}
