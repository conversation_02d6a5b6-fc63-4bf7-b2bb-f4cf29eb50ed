
import './card-item.less'

import { DeleteOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons'
import { ChartView, Tag } from '@sugo/design'
import { fastMemo, getFromNow } from '@sugo/design/functions'
import { history } from '@umijs/max'
import { Button, Popconfirm, Table, Tooltip, Typography } from 'antd'
import cn from 'classnames'
import dayjs from 'dayjs'
import _ from 'lodash'
import React, { useCallback, useMemo } from 'react'

import { TrendUpDownBar } from '@/components/trend-up-down-bar'
import { useNewBi } from '@/hooks/use-new-bi'
import { useAction, useCompute } from '@/pages/causation-analysis/list/reactive'
import type { CausationAnalysis } from '@/pages/causation-analysis/type'
import { formatLargeNumber, getExaggeratedRateDescription, msToText, sparseArray } from '@/utils'

const { Text } = Typography

export interface CardItemProps {
  data: CausationAnalysis
  onPreview?: (data: any) => any
}

/**
 * 卡片
 * @returns
 */
function _CardItem(props: CardItemProps) {
  const { data, onPreview } = props
  const { markNewBi } = useNewBi()

  const latest = data.latestComputeInfo
  const analysisResult = useCompute(s => s.getAnalysisResult(data.id))

  const [analysisList, trendGroup, targetValueText] = useMemo(() => {
    let targetValueTotal = 0
    let targetValueSuccess = 0

    let list = _.values(analysisResult).map(i => {
      const value = _.get(i, 'total.deltaValue')
      const rate = _.get(i, 'total.deltaRate')

      const obj = {
        key: i.valueFieldId,
        field: i.field.alias,
        value: formatLargeNumber(value || 0),
        rate: rate === 0 ? 0 : (_.round(rate! * 100, 2) || null),
        order: value,
        rateDesc: getExaggeratedRateDescription((rate || 0) * 100),
        data: sparseArray(_.orderBy(i.total?.currentData || [], 'name', 'asc'))
      }

      _.set(obj, 'data[0].deltaValue', value)
      return obj
    })

    if (_.isEmpty(analysisResult)) {
      list = _.values(data.valueFieldMap).map(i => ({
        key: i.key,
        field: i.field.alias,
        value: null,
        rate: null,
        rateDesc: '-',
        order: i.order,
        data: []
      }))
    }

    if (_.isEmpty(list)) {
      list = [{ key: 'a', field: '暂无指标', rateDesc: '-' }] as any
    }

    // 计算目标
    _.forEach(analysisResult, (v, k) => {
      const targetRate = _.get(v, 'total.targetRate')
      if (data.valueFieldMap[k]?.targetValue) targetValueTotal += 1
      if ((targetRate || 0) > 1) targetValueSuccess += 1
    })

    const _trendGroup = _.values(analysisResult).reduce((o, v) => {
      const value = _.get(v, 'total.deltaValue', 0)
      if (!o.up) o.up = 0
      if (!o.down) o.down = 0
      if (value > 0) o.up += 1
      if (value < 0) o.down += 1
      return o
    }, {} as Record<string, number>)

    return [
      _.orderBy(list, ['order'], ['asc']).slice(0, 3),
      _trendGroup,
      `${targetValueSuccess}/${targetValueTotal}`
    ]
  }, [analysisResult, data.valueFieldMap])

  const delCausation = useAction(s => s.delCausation)

  const renderValue = (value: any, isRate?: boolean) => {
    if (_.isNil(value) || _.isNaN(value)) return '-'
    const val = Number.parseFloat(value)
    return (
      <span className={cn({ up: val > 0, down: val < 0 })}>
        {isRate && value > 0 ? '+' : ''}{value}{isRate ? '%' : ''}
      </span>
    )
  }

  const onModifyConfig = useCallback(option => {
    _.set(option, 'xAxis.show', false)
    _.set(option, 'yAxis.show', false)
    const min = _.minBy(option.data, 'value') as any
    const max = _.maxBy(option.data, 'value') as any

    const deltaValue = _.get(option, 'data[0].deltaValue')

    _.set(option, 'legend.show', false)
    _.set(option, 'xAxis.axisLabel.formatter', () => '')
    _.set(option, 'yAxis.axisLabel.formatter', () => '')

    _.set(option, 'yAxis.min', min?.value)
    _.set(option, 'yAxis.max', max?.value)
    _.set(option, 'grid.top', '4px')
    _.set(option, 'grid.left', '4px')
    _.set(option, 'grid.right', '4px')
    _.set(option, 'grid.bottom', '4px')
    _.set(option, 'grid.show', false)
    _.set(option, 'opts.renderer', 'svg')
    _.set(option, 'series', _.map(option.series, s => {
      s.symbolSize = 0
      s.symbol = 'none'
      s.smooth = true
      s.lineStyle.width = 2
      s.lineStyle.color = deltaValue > 0 ? '#ee5566' : '#24bdab'
      return { ...s }
    }))
    return option
  }, [])

  const columns = useMemo(() => [
    {
      title: '指标（Top3）', dataIndex: 'field',
      render: (field: any) => <div className='field-title' title={field}>{field}</div>
    },
    {
      title: '趋势', dataIndex: 'data',
      render: (currentData: any) => (
        <ChartView
          type='line'
          className='trend-chart'
          data={currentData}
          height={25}
          width={60}
          axis={{
            x: [{ name: 'name', title: '时间' }],
            y: [{ name: 'value', title: '值' }]
          }}
          lazyUpdate
          onModifyConfig={onModifyConfig}
        />
      )
    },
    {
      title: '变化值', dataIndex: 'value',
      render: (rate: any) => renderValue(rate)
    },
    {
      title: '变化率', dataIndex: 'rate',
      render: (rate: any) => renderValue(rate, true)
    },
    {
      title: '晴雨表', dataIndex: 'rateDesc'
    }
  ], [onModifyConfig])

  // 标题
  // 上次计算时间，计算耗时，触发方式
  // 期数
  // 状态
  // 显示两个指标的趋势

  const triggerMap = { auto: '自动', manual: '手动' }

  const onReadView = e => {
    e.stopPropagation()
    onPreview?.(data)
  }

  return (
    <div className='causation-analysis-page-card-item'>
      <header className='header flex-row vcenter'>
        <Text className='title' ellipsis title={data.title} onClick={onReadView}>
          {data.title || '未命名'}
        </Text>
        <div className='flex-1' />
        {latest &&
          <Tag text={`第 ${latest?.index || 1} 期`} bordered={false} size='small' />
        }
      </header>

      <div className='flex-1 content'>
        {!_.isEmpty(analysisList) &&
          <div>
            <div className='mb-1 flex-row vcenter'>
              {targetValueText !== '0/0' &&
                <div className='target-value'>目标完成：{targetValueText}</div>
              }
              <TrendUpDownBar
                up={trendGroup.up}
                down={trendGroup.down}
                className='flex-1'
              />
            </div>
            <Table
              columns={columns}
              dataSource={analysisList}
              size='small'
              showSorterTooltip={false}
              pagination={false}
              className='analysis-table'
            />
            {/* {isMoreList && (
              <Button type='primary' size='small' className='list-more-btn'>
                查看更多
              </Button>
            )} */}
          </div>
        }
        {latest ?
          <div className='compute-info flex-row vcenter'>
            <Tag autoColor size='small' bordered={false}>
              {triggerMap[latest.computeTrigger]}触发
            </Tag>
            <div className='flex-1' />
            <Tooltip title={dayjs(latest.computedAt).format('YYYY-MM-DD HH:mm:ss')} align={{ offset: [0, 2] }}>
              <div>演算于：{getFromNow(latest.computedAt as string)}，</div>
            </Tooltip>
            <div>耗时：{msToText(latest.computeTime || 0)}</div>
          </div> :
          <div style={{ textAlign: 'center', opacity: 0.9 }}>无计算记录，未进行演算</div>
        }
      </div>

      <footer className='footer flex-row vcenter space-between'>
        <Button
          size='small' type='text' onClick={onReadView}
          icon={<EyeOutlined />}
          disabled={_.isEmpty(analysisResult)}
        >
          查看
        </Button>
        <Button
          size='small' type='text' onClick={() => history.push(`/causation-analysis/editor/${data.id}?${markNewBi}`)}
          icon={<EditOutlined />}
        >
          编辑
        </Button>
        <Popconfirm
          title='确定要删除吗？'
          onConfirm={() => delCausation(data.id)}
        >
          <Button size='small' type='text'
            icon={<DeleteOutlined />}
          >
            删除
          </Button>
        </Popconfirm>
      </footer>
    </div>
  )
}

export const CardItem = fastMemo(_CardItem)
