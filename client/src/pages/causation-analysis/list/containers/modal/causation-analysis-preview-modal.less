
.causation-analysis-page-preview-modal {
  .ant-drawer-header {
    display: none;
  }
  .ant-drawer-body {
    padding: 0;
    overflow: hidden;
    position: relative;
  }


  .causation-analysis-editor-nav-bar {
    top: 0;
    padding-right: 55px;
  }

  .causation-analysis-editor-preview-panel {
    width: calc(100% - 5px);
    .chart-wrap {
      overflow-x: hidden;
    }
  }

  .close-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1111;
    cursor: pointer;
    user-select: none;
  }
}
