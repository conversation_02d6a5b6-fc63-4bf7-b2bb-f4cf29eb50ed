import './causation-analysis-preview-modal.less'

import { CloseOutlined } from '@ant-design/icons'
import { fastMemo, withRefModal } from '@sugo/design/functions'
import { But<PERSON>, Drawer } from 'antd'
import React from 'react'

import { CausationAnalysisEditor } from '@/pages/causation-analysis/editor'

/**
 * 详情弹窗
 * @returns
 */
const _CausationAnalysisPreviewModal = withRefModal(props => {
  const { visible, modal, id } = props
  const onCancel = () => {
    modal.hide()
  }

  return (
    <Drawer
      open={visible}
      title={null}
      width='100%'
      placement='bottom'
      height='calc(100% - 48px)'
      destroyOnClose

      onClose={onCancel}
      className='causation-analysis-page-preview-modal'
      zIndex={888}
    >
      <Button
        className='close-icon'
        title='关闭'
        onClick={e => {
          e.stopPropagation()
          onCancel()
        }}
      >
        <CloseOutlined />
      </Button>
      {visible && id &&
        <CausationAnalysisEditor
          key={id}
          id={id}
          readOnly
        />
      }
    </Drawer>
  )
})

export const CausationAnalysisPreviewModal = fastMemo(_CausationAnalysisPreviewModal)
