import { TreePanelSelect } from '@sugo/design'
import { fastMemo, withRefModal } from '@sugo/design/functions'
import { Form, Input, message, Modal } from 'antd'
import _ from 'lodash'
import React, { useEffect, useMemo, useState } from 'react'

import { useAction, useStore } from '@/pages/causation-analysis/list/reactive'

// 写一个新建分组的弹窗
const _CreateGroupModal = withRefModal(props => {
  const { visible, modal, editData, groupId } = props

  const groupTree = useStore(s => s.groupTree)
  const createGroup = useAction(s => s.createGroup)

  const isEdit = !!editData
  const [loading, setLoading] = useState(false)
  const [form] = Form.useForm()

  const treeData = useMemo(() => groupTree.filter(i => !/all/i.test(i.id)), [groupTree])
  const defaultExpandedKeys = useMemo(() => groupTree.filter(i => !_.isEmpty(i.children)).map(i => i.id).slice(0, 5), [groupTree])

  const onCancel = () => {
    modal.hide()
    form.resetFields()
  }

  const onOk = async () => {
    try {
      setLoading(true)
      await form.validateFields()
      const data = form.getFieldsValue()
      if (isEdit) data.id = editData.id
      await createGroup(data, isEdit)
      onCancel()
      message.success(isEdit ? '编辑成功' : '创建成功')
    } catch (err) {
      message.error(isEdit ? '编辑失败' : '创建失败')
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (visible && groupId && !/all/i.test(groupId)) {
      form.setFieldValue('parentId', groupId)
    }
  }, [visible, groupId])

  useEffect(() => {
    if (visible && isEdit) {
      form.setFieldValue('title', editData.title)
      form.setFieldValue('parentId', editData.parentId || undefined)
    }
  }, [visible, isEdit])

  return (
    <Modal
      title={isEdit ? '编辑分类' : '创建分类'}
      open={visible}
      onCancel={onCancel}
      onOk={onOk}
      confirmLoading={loading}
      width={520}
    >
      <Form form={form} labelAlign='right' labelCol={{ span: 5 }}>
        <Form.Item label='分类名称' name='title' rules={[{ required: true, message: '请输入分类名称' }, {
          validator(_rule, value) {
            if (value === '全部') return Promise.reject(new Error('你不能创建一个“全部”的分类'))
            return Promise.resolve(true)
          }
        }]}>
          <Input placeholder='输入分类名称' maxLength={30} showCount />
        </Form.Item>
        <Form.Item label='上级分类' name='parentId'>
          <TreePanelSelect
            placeholder='请选择上级分类'
            treeData={treeData}
            title='上级分类'
            enableQuickSearch
            treePanelStyle={{ width: 348, maxHeight: 360, overflowY: 'auto' }}
            allowClear
            treeProps={{
              defaultExpandedKeys
            }}
          />
        </Form.Item>
      </Form>
    </Modal>
  )
})

export const CreateGroupModal = fastMemo(_CreateGroupModal)
