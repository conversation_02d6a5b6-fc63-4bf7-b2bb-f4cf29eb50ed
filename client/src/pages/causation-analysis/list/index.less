
.causation-analysis-page {
  // background-color: #fff;
  height: calc(100vh - 58px);
  display: flex;
  overflow: hidden;

  .causation-intro {
    text-align: center;
    border-radius: 4px;
    margin-bottom: 12px;
    // background-color: rgba(@primary-color, 2%);
    padding-bottom: 0;
    padding-top: 20px;

    > div:first-child {
      font-size: 16px;
      font-weight: bold;
      color: #555;
    }
  }

  > .group-panel {
    width: 230px;
    // border-right: 1px solid #f1f1f1;
    height: 100%;
    float: left;
    background-color: #fff;
    box-shadow: 1px 2px 4px rgba(@primary-color, 0.08);
    border-radius: 4px;

    .fix-only-search {
      .ant-input-affix-wrapper {
        margin-bottom: 3px;
        padding: 2px 8px;
      }
      .ant-input-suffix {
        transform: translate(1px, -2px);
      }
    }

    .titlt-panel {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px;
      padding-bottom: 3px;
    }

    h3 {
      font-weight: bold;
      font-size: 15px;
      margin: 0;
    }

    .group-action {
      display: flex;
      align-items: center;
      .anticon {
        margin: 0 3px;
        &:hover {
          color: @primary-color;
        }
      }
    }

    .ant-tree-treenode-active {
      .tree-panel-node-icon {
        color: @primary-color;
        .path  {
          fill: @primary-color;
        }
      }
    }

    .only-top-node {
      .ant-tree-switcher {
        width: 12px;
      }
    }
  }

  > .list-panel {
    flex: 1;

    .design-filter-action-bar {
      border-radius: 0;
      box-shadow: 1px 2px 4px rgba(108, 94, 243, 0.04);
      border-bottom: 1px solid #fafafa;
      min-height: 56px;

      .app-title {
        font-weight: bold;
        color: #444;
        font-size: 16px;
        margin: 0 4px;
        margin-left: 6px;
        min-width: 64px;
        display: inline-block;
      }
    }

    .causation-list {
      min-height: calc(100% - 200px);
      padding: 2px 12px;
      overflow-y: auto;
      max-height: calc(100vh - 195px);

      &::after {
        content: '';
        display: block;
        clear: both;
      }
    }

    .causation-pagination {
      display: flex;
      justify-content: flex-end;
      padding-right: 16px;
      position: sticky;
      z-index: 10;
      bottom: 10px;
      right: 0;
      width: max-content;
      background-color: #fff;
      float: right;
    }

  }
}
