
import './index.less'

import { DeleteOutlined, EditOutlined, HolderOutlined, PlusOutlined } from '@ant-design/icons'
import { FilterActionBar, TreePanel } from '@sugo/design'
import { fastMemo } from '@sugo/design/functions'
import { history } from '@umijs/max'
import { useMemoizedFn } from 'ahooks'
import { Pagination, Popconfirm, Tooltip } from 'antd'
import _ from 'lodash'
import React, { useEffect, useRef } from 'react'

import { CardItem } from '@/pages/causation-analysis/list/containers/card-item'
import { CausationAnalysisPreviewModal } from '@/pages/causation-analysis/list/containers/modal/causation-analysis-preview-modal'
import { CreateGroupModal } from '@/pages/causation-analysis/list/containers/modal/create-group-modal'
import { useAction, useCompute, useStore } from '@/pages/causation-analysis/list/reactive'

/**
 * 分析列表
 * @returns
 */
function _CausationAnalysisList() {
  const [total, list, filter] = useStore(s => [s.total, s.list, s.filter])
  const groupTree = useStore(s => s.groupTree)

  const createGroupModalRef = useRef<{ show: Function }>()
  const causationAnalysisPreviewModalRef = useRef<{ show: Function }>()

  const { loadList, setFilter, delGroup, loadGroups } = useAction()
  const groupOnlyTopNode = useCompute(s => s.groupOnlyTopNode)
  const isAllEmpty = false

  const renderAction = useMemoizedFn(node => (
    node?.id !== 'ALL' &&
    <div className='group-action'>
      <EditOutlined onClick={() => createGroupModalRef.current?.show({ editData: node })} />
      <Popconfirm title='确定要删除吗？' onConfirm={() => delGroup(node)}>
        <DeleteOutlined />
      </Popconfirm>
      <HolderOutlined />
    </div>
  ))

  const onTreePanelChange = useMemoizedFn(value => {
    setFilter({ groupId: value, page: 1 })
  })

  const onPreview = useMemoizedFn(item => {
    causationAnalysisPreviewModalRef.current?.show({ id: item.id })
  })

  useEffect(() => {
    loadGroups()
    loadList()
  }, [])

  // 只显示
  if (!window.isExperiment) return (
    <h2 className='flex-row center pt-4'>
      实验性功能，不对外开放
    </h2>
  )

  return (
    <div className='causation-analysis-page'>
      <div className='group-panel' style={{ display: isAllEmpty ? 'none' : undefined }}>
        <div className='titlt-panel'>
          <h3>分类</h3>
          <Tooltip title='添加分类'>
            <PlusOutlined onClick={() => createGroupModalRef.current?.show({ groupId: filter.groupId })} />
          </Tooltip>
        </div>
        <TreePanel
          treeData={groupTree}
          enableQuickSearch
          value={filter.groupId || 'ALL'}
          className={groupOnlyTopNode ? 'only-top-node' : ''}
          onChange={onTreePanelChange}
          renderAction={renderAction}
          preset='directory-icon'
          // onTreeDroped={onTreeDroped}
          treeProps={{ draggable: true }}
        />
      </div>

      <div className='list-panel'>
        <FilterActionBar
          schemas={[
            { key: 'text', type: 'Text', text: <span className='app-title'>成因分析</span> as any, position: 'left' },
            { key: 'title', type: 'Search', wait: 600, trigger: 'change', placeholder: '请输入名称', position: 'left' },
            { key: 'reset', type: 'ResetButton', text: '' },
            { key: 'create', type: 'Button', preset: 'create', text: '开始分析', position: 'right' }
          ]}
          onClick={(e, key) => {
            e.stopPropagation()
            if (key === 'create') history.push('/causation-analysis/editor/new')
          }}
          value={filter}
          onChange={val => {
            setFilter(val)
            loadList(false)
          }}
        />
        <div className='causation-intro'>
          <div>有因必有果，有果才有因</div>
          <div>成因分析就是根据数据的变化结果，来判断原因和结果的关系</div>
        </div>
        <div className='causation-list'>
          {_.map(list, item => (
            <CardItem
              key={item.id}
              data={item}
              onPreview={onPreview}
            />
          ))}
        </div>

        <Pagination
          className='causation-pagination'
          current={filter.page}
          pageSize={filter.pageSize}
          total={total}
          onChange={page => {
            setFilter({ page })
            loadList(false)
          }}
        />
      </div>

      <CreateGroupModal ref={createGroupModalRef} />
      <CausationAnalysisPreviewModal ref={causationAnalysisPreviewModalRef} />
    </div>
  )
}

export const CausationAnalysisList = fastMemo(_CausationAnalysisList)
export default CausationAnalysisList
