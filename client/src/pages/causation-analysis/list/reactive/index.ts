import { defineStore } from '@sugo/reactive'
import arrayToTree from 'array-to-tree'
import _ from 'lodash'

import type { CausationAnalysis, CausationAnalysisGroup, CausationAnalysisResult } from '@/pages/causation-analysis/type'
import { Cloud } from '@/services'

const initState = {
  list: [] as CausationAnalysis[],
  total: 0,
  loading: false,
  groupTree: [] as (CausationAnalysisGroup & { key: string, children?: any[] })[],
  groups: [] as CausationAnalysisGroup[],
  groupDict: {} as Record<string, CausationAnalysisGroup>,
  filter: {
    page: 1,
    pageSize: 10,
    groupId: undefined as string | undefined,
    title: undefined as string | undefined
  },
  // 分析结果，key 是 ID
  analysisResultMap: {} as Record<string, CausationAnalysisResult['analysisResult']>
}

type State = typeof initState

const stores = defineStore({
  namespace: 'causation-analysis-list',
  state: initState,
  compute: state => ({
    groupOnlyTopNode: () => _.every(state.groupTree, node => _.isEmpty(node.children)),
    getAnalysisResult: (id: string) => state.analysisResultMap[id]
  }),
  setup: state => {
    const getSelfId = () => _.get(window, 'sugo.user.id') || null as string | null
    const isAdmin = () => _.get(window, 'sugo.user.type') === 'built-in'

    const loadList = async (init?: boolean) => {
      // ..
      state.loading = true
      const where: Record<string, any> = {
        createdBy: isAdmin() ? undefined : getSelfId()
      }
      if (!init && state.filter.title) {
        where.title = { $like: `%${state.filter.title}%` }
      }
      if (!init && state.filter.groupId) {
        where.groupId = state.filter.groupId
      }
      try {
        const { page, pageSize } = state.filter
        const res = await Cloud.CausationAnalysis.findAndCountAll({
          limit: pageSize,
          offset: init ? 0 : (page - 1) * pageSize,
          where,
          attributes: [
            'id', 'title', 'description', 'groupId', 'type', 'createdAt',
            'updatedAt', 'latestComputeInfo', 'latestComputeData', 'valueFieldMap'
          ],
          order: [['updatedAt', 'DESC']]
        })

        // 加载计算信息
        const res2 = await Cloud.CausationAnalysisResult.findAll({
          where: {
            id: { $in: _.compact(_.map(res.list, i => i.latestComputeInfo?.resultId)) }
          },
          attributes: ['id', 'analysisResult', 'causationAnalysisId']
        })

        state.analysisResultMap = _.reduce(res2, (o, v) => ({
          ...o,
          [v.causationAnalysisId]: v.analysisResult
        }), {})

        state.list = res.list
        state.total = res.total
      } finally {
        state.loading = false
      }
    }

    const setFilter = (filter: Partial<State['filter']>) => {
      state.filter = { ...state.filter, ...filter }
    }


    const loadGroups = async () => {
      let list = await Cloud.CausationAnalysisGroup.findAll({})
      // 包含全部
      list = [{ id: 'ALL', title: '全部', order: -1, draggable: false } as any, ...list]

      const tree = arrayToTree(
        _.orderBy(list.map(i => ({ ...i, key: i.id })), 'order', 'asc'),
        { parentProperty: 'parentId', childrenProperty: 'children' }
      )

      state.groups = list
      state.groupDict = _.keyBy(list, 'id')
      state.groupTree = [...tree]
    }

    /** 删除子级，并且卡片变成了无分组 */
    const delGroup = async (data: any) => {
      await Cloud.$batch(s => [
        s.CausationAnalysisGroup.deleteByPk(data.id),
        s.CausationAnalysisGroup.delete({ where: { parentId: data.id } }),
        s.CausationAnalysis.update({ groupId: null }, { where: { groupId: data.id } }),
        s.CausationAnalysisResult.update({ groupId: null }, { where: { groupId: data.id } })
      ])
      await loadGroups()
    }


    const createGroup = async (data: any, isEdit?: boolean) => {
      if (isEdit) {
        await Cloud.ThemeGroup.updateByPk(data.id, {
          ..._.omit(data, 'id'),
          updatedBy: getSelfId()
        })
      } else {
        await Cloud.ThemeGroup.create({
          ...data,
          createdBy: getSelfId()
        })
      }
      await loadGroups()
    }

    const delCausation = async (id: string) => {
      await Cloud.CausationAnalysis.deleteByPk(id)
      await loadList()
    }

    return {
      loadList,
      setFilter,
      delGroup,
      loadGroups,
      createGroup,
      delCausation
    }
  }
})

export const {
  useAction,
  useCompute,
  useStore
} = stores
