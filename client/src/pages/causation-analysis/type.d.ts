
type Total = Partial<{
  previousValue: number       // 上期总值
  currentValue: number        // 本期总值
  deltaValue: number          // 变化值（current - previous）
  deltaRate: number           // 变化率（(deltaValue - previous) / previous），0.01 => 1%
  currentData: { // 本期数据
    value: number
    name: string
  }[]
  previousData: { // 上期数据
    value: number
    name: string
  }[],
  targetValue: number
  targetRate: number // 目标率，0.01 => 1%
}>

// 主表
export interface CausationAnalysis {
  id: string
  title: string
  description?: string
  groupId?: string | null
  type: 'dimensionCausation' | 'individualityCausation'

  // key 是随机 id
  valueFieldMap: Record<string, {
    key: string

    queryType: 'dataset' | 'datatable' | 'mydatasets' | 'indices' | 'tablemodel' | 'usertags' | 'expressionField' | 'computedField' | 'custom'
    chartType: 'bar' | 'line' | 'lineArea' | 'pie' | 'table'

    field: {
      id: string
      name: string
      title: string
      alias: string // 界面就是显示别名
      order?: number
      dataType?: 'number'
      formulaSchema?: any[]
      /**
       * 字段的来源
       *
       * 类似的：
       * - MYSQL/193/398/26203
       * - datasets/xxxx 数据视图的
       * - datatable/xxxx 数据表
       * - mydatasets/xxx/xxx 数据集的
       * - usertags/:projectId/:groupId/:tagId 用户标签
       * - :queryType/computedField/xxx 计算字段
       * - :queryType/expressionField/xxx （前端）表达式字段，不要把这个字段发去后端
       * - indices/xxx/code/type 指标的
       * - indices/dimension/[指标id]/[维度id] 指标的维度
       * - tablemodel/xxx/yyy/:tableId 数据模型的，最后一个为 tableId
       */
      dataSourcePath?: string
    }

    // 绑定的时间字段，不能为空
    timeField: {
      id: string
      name: string
      title: string
      dataType?: 'date'
      dataSourcePath?: string
    }

    /**
     * 计算字段才有这个属性
     *
     * 放一些字段配置，例如格式化，单位等等
    */
    config?: {
      /** 字段单位 */
      // 用 config.valueUnit
      unit?: string // 如果是指标，那么在 createCard 时会自动赋值这个，后序显示优先拿 config.valueUnit || field.unit
      format?: 'f' | 'd' | '%' | (string & {}) // 数值格式
      precision?: number // 数值精度
      [key: string]: any
    }

    createdAt?: string // 可用来排序判断是否新建的

    // TODO: 时间是一个范围，计算时查询出来后，应该再计算总和

    // 本期时间
    currentPeriodTime: string[] | {
      direction: string       // 时间方向
      count: number           // 数值（如 3）
      unit: string            // 时间单位
    }
    // 上期时间
    previousPeriodTime: string[] | 'yoy' | 'mom' | {
      direction: string       // 时间方向
      count: number           // 数值（如 3）
      unit: string            // 时间单位
    }

    // 目标值
    targetValue?: number
    targetType?: 'value' | 'rate'

    // 筛选条件
    filters?: any[]

    // 绑定分析维度，默认是全部，也可以手动调整分析维度
    // dimensions?: string[]

    /** 分类 id */
    categoryId?: string

    // 关注维度，默认所有都分析，存 field.id
    followDimensions?: string[]

    // 忽略维度，不参与分析，存 field.id
    ignoreDimensions?: string[]

    tableInfo: {
      id: string
      key: string
      tableId: string | number
      tableName: string
      tableAlias?: string
      dbId?: string
      dbName?: string
      dbAlias?: string
      dbType?: string // mysql, db2 等
      dbCatalog?: string
      connectId?: string
      connectName?: string
      connectType?: string
      connectUser?: string

      datasetId?: string
      tableModelId?: string
    },

    order: number,
    dataSouceSelected?: string[]
  }>,

  /** 定时任务配置 */
  cron?: {
    enable: boolean
    value: string[]
    expression: string
    description?: string
  }

  // 计算触发
  computeTrigger: 'manual' | 'auto'
  // 计算状态
  computeStatus?: 'default' | 'running' | 'stopped'

  // 列表显示用到
  // key 是 valueFieldMap 的 key
  latestComputeData?: Record<string, {
    field: {
      id: string
      name: string
      title: string
      alias: string
      dataType?: 'number'
    }
    // 总变化
    total: Total,
    // 本期时间（计算好的）
    currentPeriodTime: string[]
    // 上期时间（计算好的）
    previousPeriodTime: string[]
    // 目标值
    targetValue?: number
    targetType?: 'value' | 'rate'
  }>
  // 最新计算信息
  latestComputeInfo?: {
    resultId: string // 最新计算结果 id
    index: number // 期数
    computeTime: number // 最新计算时间
    computedAt: Date | string
    computeTrigger: string
  }

  /** 计算的上下文 */
  computeContext?: {
    // key 是 valueField.id 或 dimensionField.id
    queryParmasMap: Record<string, {
      valueFieldName: string,
      timeFieldName: string,
      dimensionName?: string,
      timeRange: string[],
      tableInfo: any,
    }>
  }

  /** 推送通知配置 */
  noticeConfig?: any

  // 分类
  categorys?: {
    id: string
    title: string
  }[]

  /** 是否开启 AI 解读 */
  aiAnswerConfig?: {
    enable: boolean
    model?: string
    prompt?: string
  }

  createdAt?: Date
  updatedAt?: Date
  createdBy?: string
}

export interface CausationAnalysisGroup {
  id: string
  title: string
  parentId?: string
  order: number
  createdAt?: Date
  updatedAt?: Date
}

// 计算表
export interface CausationAnalysisResult {
  id: string
  // 绑定的分析
  causationAnalysisId: string

  index: number // 期数，从 1 开始
  title: string // 标题，如 第 xxx 期
  description?: string // 描述
  groupId?: string | null // 分组 id

  // 维度分析备份数据
  causationAnalysisBackup: CausationAnalysis // 绑定的分析

  // 分析结果，key 是 valueFieldMap 的 key
  analysisResult: Record<string, {
    valueFieldId: string // 绑定的度量字段 id

    field: {
      id: string
      name: string
      title: string
      alias: string
      dataType?: 'number'
    }

    config: {
      currentPeriodTime: any
      previousPeriodTime: any
    }

    // 时间范围定义
    // 本期时间
    currentPeriodTime: string[]
    // 上期时间
    previousPeriodTime: string[]

    // 总变化
    total: Total

    // 花费时间
    computeTime: number      // 计算耗时（毫秒）
    computedAt: Date | string         // 计算时间
    // 元信息
    dataSource?: string       // 数据来源标识
    notes?: string           // 备注（如异常处理说明）

    /** 参与计算的维度 */
    dimensions: {
      id: string
      name: string
      title: string
      alias: string
      dataType?: 'number'
    }[]
  }>

  // 维度分析结果，key 是 维度的 field.id
  analysisDimensionResult: Record<string, {
    valueFieldId: string // 绑定的度量字段 id
    valueFieldKey: string // valueFieldMap.key

    // 维度字段
    field: {
      id: string
      name: string
      title: string
      alias: string
      dataType?: 'number'
    }

    total: Total // 总变化
    // totalContribution: number   // 总贡献度（deltaValue / 总变化值）
    // totalChange: number

    // direction: "positive" | "negative" // 维度整体方向

    // 为了防止数据量过大，需要查两次，一次是 Top10（limit: 10），另外一次是 Bottom10（limit: 10, order: desc）
    // TODO: 只取 Top10 和 Bottom10
    // 维度成员明细
    members: {
      dimension: string          // 维度名称（如 "供应商"）
      member: string             // 维度成员值（如 "供应商A"）

      total: Total               // 总变化
      totalContribution: number   // 总贡献度（deltaValue / 总变化值）
      // “正面”|“负面”
      direction: 'positive' | 'negative' | 'invariable' | 'none' // 贡献方向
      // directionText?: '同向' | '负向' | 'None'

      isNew?: boolean            // 是否新增成员（基期不存在）
      isChange?: boolean         // 是否变化成员（本期存在，上期存在，本期和上期值不同）
      isRemoved?: boolean        // 是否消失成员（本期不存在）
    }[]

    // 花费时间
    computeTime: number          // 计算耗时（毫秒）
    computedAt: Date | string    // 计算时间
    // 元信息
    dataSource?: string       // 数据来源标识
    notes?: string           // 备注（如异常处理说明）
    queryError?: string      // 查询错误
  }>

  // 触发方式
  computeTrigger: 'auto' | 'manual'
  // 花费时间
  computeTime: number      // 计算耗时（毫秒）
  computedAt: Date | string        // 计算时间

  // ...
  createdAt?: Date
  updatedAt?: Date
  createdBy?: string
}
