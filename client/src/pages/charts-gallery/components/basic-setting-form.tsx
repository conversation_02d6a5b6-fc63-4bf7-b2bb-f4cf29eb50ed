import { LoadingOutlined, UploadOutlined } from '@ant-design/icons'
import { useReactive } from 'ahooks'
import { Col, Form, Input, message, Radio, Row, Select, TreeSelect, Upload } from 'antd'
import type { UploadChangeParam, UploadFile } from 'antd/es/upload/interface'
import _ from 'lodash'
import React from 'react'

import CodeEditor from '@/components/code-editor'
import IconfontIcon from '@/components/icons/iconfont-icon'
import { ICON_MAP } from '@/components/icons/iconfont-icon-const'
import { COMPONENT_TYPE_MAP, DEVICE_TYPE_MAP, ELEMENT_GROUP_MAP } from '@/consts/screen'
import { useFormFieldsAdapter } from '@/hooks/use-form-fields-adapter'
import { Service } from '@/services'
import { FileService } from '@/services/files'
import type { ComponentDefine } from '@/types/editor-core/component'
import { enableSelectSearch } from '@/utils'

export interface ChartsBasicSettingFormProps {
  value: Partial<ComponentDefine> | null | undefined
  onChange: (next: Partial<ComponentDefine>) => any
}

/** 上传功能 */
function useUploadCustomChartBtn(
  value: Partial<ComponentDefine> | null | undefined,
  onChange: (next: Partial<ComponentDefine>) => any
) {
  const reactiveState = useReactive({
    uploading: false
  })
  const beforeUpload = file => {
    if (1024 * 1024 * 10 < file.size) {
      message.warn('文件不能大于 10 Mb')
      return false
    }
    return true
  }
  const onChange1 = ({ file }: UploadChangeParam<UploadFile>) => {
    if (file.status !== 'done') {
      return
    }
    const res = file.response
    if (!res.success) {
      message.warn('上传文件无效')
      reactiveState.uploading = false
      return
    }
    // set notebook js url
    onChange({
      ...value,
      extraConfig: { ...(value?.extraConfig || {}), moduleAddress: res.path }
    })
    reactiveState.uploading = false
  }
  const customRequest = async options => {
    const { onSuccess, onError, file } = options

    try {
      reactiveState.uploading = true
      const res = await FileService.upload(file)
      if (!res) {
        return onError(new Error('上传失败'))
      }
      const pkgInfo = await Service.$execute('parseNotebookPkg', { uploadRes: res })
      message.info('上传成功')
      onSuccess(pkgInfo)
    } catch (err) {
      onError(err)
    }
  }

  return (
    <Upload
      accept='*.tar,application/tar,*.tgz,application/gzip'
      multiple={false}
      customRequest={customRequest}
      showUploadList={false}
      beforeUpload={beforeUpload}
      onChange={onChange1}
      disabled={reactiveState.uploading}
    >
      {reactiveState.uploading ? <LoadingOutlined /> : <UploadOutlined />}
    </Upload>
  )
}

/**
 * 自定义图表基础配置
 * @param props
 * @constructor
 */
export default function ChartsBasicSettingForm(props: ChartsBasicSettingFormProps) {
  const { value, onChange } = props

  const { fields, onFieldsChange } = useFormFieldsAdapter(value, onChange, 'extraConfig')

  const uploadCustomChartBtn = useUploadCustomChartBtn(value, onChange)
  return (
    <Form className='!px-8 !py-4' layout='vertical' autoComplete='off' fields={fields} onFieldsChange={onFieldsChange}>
      <Form.Item
        label='observable notebook js 代码地址'
        name={['extraConfig', 'moduleAddress']}
        rules={[{ required: true, message: '此项必填' }]}
        tooltip={
          <div>
            获取代码地址请参考此
            <a href='https://observablehq.com/@observablehq/introduction-to-embedding' target='_blank' rel='noreferrer'>
              说明
            </a>
            ，最终用到的是 Runtime with JavaScript 里面的 js 脚本地址
          </div>
        }
      >
        <Input placeholder='请填写 js 脚本地址' addonAfter={uploadCustomChartBtn} />
      </Form.Item>

      <Row gutter={12}>
        <Col span={8}>
          <Form.Item
            label='显示模式'
            name={['extraConfig', 'displayMode']}
            tooltip='dom 模式（默认）时会变量的值应为 dom，react 模式变量的值应为函数组件，react-dom 模式变量的值应为 react 虚拟 dom'
          >
            <Radio.Group>
              <Radio value='dom'>Dom</Radio>
              <Radio value='react'>React 组件</Radio>
              <Radio value='react-dom'>React V-DOM</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label='展示变量'
            tooltip='用于展示的 Notebook 单元的变量的名称'
            name={['extraConfig', 'displayVariableName']}
            rules={[{ required: true, message: '此项必填' }]}
          >
            <Input placeholder='请填写变量名称' />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label='图表类别' name='type'>
            <Select
              getPopupContainer={triggerNode => triggerNode.parentNode}
              placeholder='可自定义图表类别'
              {...enableSelectSearch}
              allowClear
              onChange={nextType => {
                const categoryTypeMapping = { element: 'base', layout: 'component' }
                onChange({ ...(value || {}), type: nextType, category: categoryTypeMapping[nextType] || nextType })
              }}
            >
              {_.map(COMPONENT_TYPE_MAP, (v, k) => (
                <Select.Option value={k} key={k}>
                  {v.title || k}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={12}>
        <Col span={8}>
          <Form.Item label='图表组别' name='group'>
            <TreeSelect
              getPopupContainer={triggerNode => triggerNode.parentNode}
              placeholder='可自定义图表类别'
              {...enableSelectSearch}
              allowClear
              treeData={_.map(ELEMENT_GROUP_MAP, (v, k) => ({ title: v.title || k, value: k, id: k, pId: v.parent }))}
              treeDataSimpleMode
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label='展示图标' name='icon'>
            <Select
              getPopupContainer={triggerNode => triggerNode.parentNode}
              placeholder='可自定义图表图标'
              {...enableSelectSearch}
              value={value?.icon}
              onChange={val => onChange({ ...(value || {}), icon: val })}
              allowClear
            >
              {_.map(ICON_MAP, (v, k) => (
                <Select.Option value={k} key={k}>
                  <IconfontIcon type={v} className='mr-3' />
                  {k}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label='适用设备' name='device'>
            <Select
              getPopupContainer={triggerNode => triggerNode.parentNode}
              placeholder='不限'
              {...enableSelectSearch}
              allowClear
            >
              {_.map(DEVICE_TYPE_MAP, (v, k) => (
                <Select.Option value={k} key={k}>
                  {v.title || k}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Form.Item label='描述' name='description'>
        <Input.TextArea placeholder='未填写' autoSize={{ minRows: 2, maxRows: 6 }} maxLength={300} showCount />
      </Form.Item>

      <Form.Item label='传参控制' name={['extraConfig', 'variableOverridingMethod']} tooltip='用于覆盖 notebook 变量'>
        <CodeEditor className='!w-full !h-[400px] border border-gray-100 border-solid' />
      </Form.Item>
    </Form>
  )
}
