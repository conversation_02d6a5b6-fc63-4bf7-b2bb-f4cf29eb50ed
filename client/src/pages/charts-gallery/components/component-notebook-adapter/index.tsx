// import '@sugo/design/dist/esm/components/abi-notebook-adapter/index.less'

import { ErrorBoundary } from '@sugo/design'
import { OriginalAbiNotebookAdapter, OriginalAbiNotebookAdapterProps } from '@sugo/design/dist/esm/components/abi-notebook-adapter/original'
import React from 'react'

import { BUILD_IN_UTILS } from './utils'


/**
 * 带有异常处理的自定义图表展示组件
 * @param props
 * @constructor
 */
export default function SafeComponentNotebookAdapter(props: OriginalAbiNotebookAdapterProps) {
  return (
    <ErrorBoundary>
      <OriginalAbiNotebookAdapter {...props} utilsInject={BUILD_IN_UTILS} />
    </ErrorBoundary>
  )
}
