import { Pagination } from 'antd'
import { format as d3Format } from 'd3-format'
import _ from 'lodash'
import React from 'react'
import ReactDOM from 'react-dom'

function cachePromiseGetter<T extends object>(origin: T) {
  const memoFn = _.memoize(k => origin[k])
  return new Proxy<T>(origin, {
    get(_target, propKey) {
      return memoFn(propKey)
    }
  })
}

/** 自定义图表里可用的工具库 */
export const BUILD_IN_UTILS = cachePromiseGetter({
  _,
  React,
  ReactDOM,
  d3Format,
  get ReactMarkdown() {
    return import('react-markdown').then(def => def.default)
  },
  get echarts() {
    const win: any = document.defaultView || window
    return win.echarts || import('echarts')
  },
  get htm() {
    return import('htm').then(def => def.default)
  },
  get dayjs() {
    return import('dayjs').then(def => def.default)
  },
  // 这个动态加载会报错，不知道为啥，只能用到再加
  // antd: new Proxy({}, {
  //   get: function(target, propKey, receiver) {
  //     return import(`antd/lib/${_.kebabCase(String(propKey))}`).then(def => def.default)
  //   }
  // }),
  antd: cachePromiseGetter({
    /** 日期选择器 */
    get DatePicker() {
      return import('@/components/customs/date-picker').then(r => r.default)
    },
    /** 表格 */
    get Table() {
      return import('antd/lib/table/style/index.less').then(() => import('antd/lib/table')).then(r => r.default)
    },
    /** 树选择 */
    get TreeSelect() {
      return import('antd/lib/tree-select/style/index.less')
        .then(() => import('antd/lib/tree-select'))
        .then(r => r.default)
    },
    /** 树 */
    get Tree() {
      return import('antd/lib/tree/style/index.less').then(() => import('antd/lib/tree')).then(r => r.default)
    },
    /** 按钮 */
    get Button() {
      return import('antd/lib/button/style/index.less').then(() => import('antd/lib/button')).then(r => r.default)
    },
    /** 输入框 */
    get Input() {
      return import('antd/lib/input/style/index.less').then(() => import('antd/lib/input')).then(r => r.default)
    },
    /** 下拉选择 */
    get Select() {
      return import('antd/lib/select/style/index.less').then(() => import('antd/lib/select')).then(r => r.default)
    },
    /** 多选按钮 */
    get Checkbox() {
      return import('antd/lib/checkbox/style/index.less').then(() => import('antd/lib/checkbox')).then(r => r.default)
    },
    /** 单选按钮 */
    get Radio() {
      return import('antd/lib/radio/style/index.less').then(() => import('antd/lib/radio')).then(r => r.default)
    },
    /** 跑马灯 */
    get Carousel() {
      return import('antd/lib/carousel/style/index.less').then(() => import('antd/lib/carousel')).then(r => r.default)
    },
    /** 菜单 */
    get Menu() {
      return import('antd/lib/menu/style/index.less').then(() => import('antd/lib/menu')).then(r => r.default)
    },
    /** 分页器 */
    Pagination, // 直接引入，可解决英文的问题
    /** 评分控件 */
    get Rate() {
      return import('antd/lib/rate/style/index.less').then(() => import('antd/lib/rate')).then(r => r.default)
    },
    /** 开关 */
    get Switch() {
      return import('antd/lib/switch/style/index.less').then(() => import('antd/lib/switch')).then(r => r.default)
    },
    /** 消息 */
    get message() {
      return import('antd/lib/message').then(r => r.default)
    },
    /** 进度控制器 */
    get Slider() {
      return import('antd/lib/slider/style/index.less').then(() => import('antd/lib/slider')).then(r => r.default)
    },
    /** 进度条 */
    get Progress() {
      return import('antd/lib/progress/style/index.less').then(() => import('antd/lib/progress')).then(r => r.default)
    },
    /** 锚点 */
    get Anchor() {
      return import('antd/lib/anchor/style/index.less').then(() => import('antd/lib/anchor')).then(r => r.default)
    },
    /** 统计数值 */
    get Statistic() {
      return import('antd/lib/statistic/style/index.less').then(() => import('antd/lib/statistic')).then(r => r.default)
    },
    /** 固钉 */
    get Affix() {
      return import('antd/lib/affix/style/index.less').then(() => import('antd/lib/affix')).then(r => r.default)
    },
    /** 下拉菜单 */
    get Dropdown() {
      return import('antd/lib/dropdown/style/index.less').then(() => import('antd/lib/dropdown')).then(r => r.default)
    },
    /** 悬浮提示 */
    get Tooltip() {
      return import('antd/lib/tooltip/style/index.less').then(() => import('antd/lib/tooltip')).then(r => r.default)
    },
    /** 标签 */
    get Tag() {
      return import('antd/lib/tag/style/index.less').then(() => import('antd/lib/tag')).then(r => r.default)
    },
    /** 折叠面板 */
    get Collapse() {
      return import('antd/lib/collapse/style/index.less').then(() => import('antd/lib/collapse')).then(r => r.default)
    }
  })
})
