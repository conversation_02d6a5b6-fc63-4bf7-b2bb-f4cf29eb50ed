import { importCompat, interceptFuncCall } from '@sugo/design/functions'
import { useReactive, useRequest } from 'ahooks'
import { Button, Input, Select } from 'antd'
import classnames from 'classnames'
import _ from 'lodash'
import React, { useEffect } from 'react'

import CodeEditor from '@/components/code-editor'
import { enableSelectSearch } from '@/utils'

export interface NotebookVariableOverridingProps {
  notebookScriptUrl: string
  preferVarName?: string
  value: Record<string, string>
  onChange: (definition: Record<string, string>) => any
  onEditorMount?: () => any
  onRefreshChart: () => any
}

/** notebook 变量定义读取函数 */
function readNotebookDefinition(defineFn: Function) {
  const safeMock = new Proxy(() => false, {
    get() {
      return safeMock
    },
    apply() {
      return safeMock
    }
  })

  const defDict = {}
  const runtimeMockForRead = interceptFuncCall(safeMock, 'module', (_mockRuntime, ..._args0) =>
    interceptFuncCall(safeMock, 'variable', (_mockModule, ..._args1) =>
      interceptFuncCall(safeMock, 'define', (_mockVar, ...args2) => {
        const fn = _.findLast(args2, _.isFunction)
        const fnName = _.find(args2, _.isString) // || fn?.name  没有变量名的定义无法覆盖，所以读出来也没用
        const arr = _.find(args2, _.isArray)
        if (fnName && !_.startsWith(fnName, 'mutable') && !_.startsWith(arr?.[0], 'mutable')) {
          defDict[fnName] = fn!.toString()
        }
      })
    )
  )
  defineFn(runtimeMockForRead, _.identity)
  return defDict

  // const main = runtime.module();
  // const fileAttachments = new Map([["....png","https://..."]]);
  // main.builtin("FileAttachment", runtime.fileAttachments(name => fileAttachments.get(name)));
  // main.variable(observer()).define(["md"], _1);
  // main.variable(observer("main")).define("main", ["FileAttachment","html","width"], _main);
  // return main;
}

/**
 * 读取 notebook 脚本里其中一个变量的定义
 * @param url
 */
export async function loadVariableContent(url: string | null | undefined) {
  if (!url) {
    return {}
  }

  try {
    const defineFn = (await importCompat(url)).default
    return readNotebookDefinition(defineFn)
  } catch (err) {
    console.error('readDefinition', err)
  }
}

type NotebookVariableOverridingState = {
  varName: string | null | undefined
  hideSwitchVarCtrl: boolean
}

/**
 * 渲染变量切换器
 * @param reactiveState
 * @param definition
 * @param savedDict
 * @param onReset
 */
function useVarNameSwitcher(
  reactiveState: NotebookVariableOverridingState,
  definition: Record<string, string> | undefined,
  savedDict: Record<string, string> | undefined,
  onReset: () => any
) {
  return (
    <Input.Group
      compact
      className={classnames('!inline-block align-top !absolute top-6 right-4 !w-[150px]', {
        '!hidden': reactiveState.hideSwitchVarCtrl
      })}
      size='small'
    >
      <Select
        getPopupContainer={triggerNode => triggerNode.parentNode}
        size='small'
        className='!w-[calc(100%-50px)]'
        dropdownMatchSelectWidth={false}
        {...enableSelectSearch}
        value={reactiveState.varName}
        onChange={next => (reactiveState.varName = next)}
      >
        {_.orderBy(_.keys(definition), n => (savedDict?.[n] ? 0 : 1)).map(varName0 => (
          <Select.Option key={varName0}>
            {savedDict?.[varName0] ? <span className='font-bold'>{varName0}</span> : varName0}
          </Select.Option>
        ))}
      </Select>

      <Button className='!w-[50px]' onClick={onReset} size='small'>
        重置
      </Button>
    </Input.Group>
  )
}

/**
 * notebook 变量内容的读取与改写面板
 * @param props
 * @constructor
 */
export default function NotebookVariableOverriding(props: NotebookVariableOverridingProps) {
  const { notebookScriptUrl, value, onChange, preferVarName = 'option', onEditorMount, onRefreshChart } = props

  const reactiveState = useReactive<NotebookVariableOverridingState>({
    varName: preferVarName,
    hideSwitchVarCtrl: false
  })

  const { data: definition } = useRequest(() => loadVariableContent(notebookScriptUrl), {
    refreshDeps: [notebookScriptUrl]
  })

  useEffect(() => {
    const vars = _.keys(definition)
    reactiveState.varName = _.includes(vars, preferVarName) ? preferVarName : _.first(vars)
  }, [definition])

  const varNameSwitcher = useVarNameSwitcher(reactiveState, definition, value, () => {
    onRefreshChart() // 修正点重置没有刷新到最新的代码的问题
    onChange({})
  })
  const varName = reactiveState.varName || ''
  const onCodeChange = nextCode => {
    const code = nextCode === definition?.[varName] ? undefined : nextCode
    const next = _.pickBy({ ...(value || {}), [varName]: code! }, (v, k) => v && k) // 避免保存无用数据
    onChange(next)
  }
  const onEditorFocus = () => {
    reactiveState.hideSwitchVarCtrl = true
  }
  const onEditorBlur = () => {
    reactiveState.hideSwitchVarCtrl = false
  }
  return (
    <div className='relative h-full'>
      <CodeEditor
        key={varName || '-'} // 避免撤销的时候变成了上一个变量的内容
        className='!h-full !w-full'
        value={value?.[varName] || (definition as any)?.[varName] || ''}
        onChange={onCodeChange}
        onFocus={onEditorFocus}
        onBlur={onEditorBlur}
        onMount={onEditorMount}
      />

      {varNameSwitcher}
    </div>
  )
}
