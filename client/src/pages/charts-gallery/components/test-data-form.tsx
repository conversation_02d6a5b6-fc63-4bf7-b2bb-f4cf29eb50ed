import './test-data-form.less'

import { Form, message, Select } from 'antd'
import _ from 'lodash'
import React from 'react'

import CodeEditor from '@/components/code-editor'
import { useFormFieldsAdapter } from '@/hooks/use-form-fields-adapter'
import { ChartField } from '@/types/editor-core/component'
import { enableSelectSearch } from '@/utils'
import { tryJsObjParse } from '@/utils/json-utils'


export interface TestDataParam {
  name: string
  fields: ChartField[]
  demoData: Record<string, any>[]
  default?: boolean
}

// 自定义图表测试数据
export const TestDataPreset: TestDataParam[] = [
  {
    name: '无维度，多指标查询，如总计图表',
    fields: [
      { name: 'total', title: '指标1', type: 'number', required: true },
      { name: 'total2', title: '指标2', type: 'number', required: true },
      { name: 'total3', title: '指标3', type: 'number', required: true, dynamic: true }
    ],
    demoData: [{ total: 2754, total2: 190, total3: 0.3377 }]
  },
  {
    name: '单维度，单指标查询，如饼图',
    default: true,
    fields: [
      { name: 'dim0', title: 'X轴', type: 'string', required: true },
      { name: 'total', title: 'Y轴', type: 'number', required: true }
    ],
    demoData: [
      { total: 1792, dim0: '联通' },
      { total: 909, dim0: '电信' },
      { total: 27, dim0: '未知' },
      { dim0: '移动', total: 26 }
    ]
  },
  {
    name: '单维度，多指标查询，如一维柱图',
    fields: [
      { name: 'dim0', title: 'X轴', type: 'string', required: true },
      { name: 'total', title: '数值1', type: 'number', required: true },
      { name: 'total2', title: '数值2', type: 'number', dynamic: true }
    ],
    demoData: [
      { total: 909, dim0: '电信', total2: 168 },
      { total: 1792, dim0: '联通', total2: 25 },
      { total: 27, dim0: '未知', total2: 4 },
      { total: 26, dim0: '移动', total2: 1 }
    ]
  },
  {
    name: '多维度，单指标查询，如二维柱图',
    fields: [
      { name: 'dim0', title: '维度1', type: 'string', required: true },
      { name: 'dim1', title: '维度2', type: 'string', required: true },
      { name: 'total', title: 'Y轴', type: 'number', required: true }
    ],
    demoData: [
      { total: 33, dim1: '1080*2175', dim0: '联通' },
      { dim1: '未知', total: 32, dim0: '联通' },
      { total: 22, dim1: '1080*2160', dim0: '联通' },
      { dim1: '320*568', total: 44, dim0: '电信' },
      { dim1: '1280*800', total: 40, dim0: '电信' },
      { total: 29, dim1: '720*1280', dim0: '电信' },
      { total: 20, dim1: '1080*2030', dim0: '电信' },
      { total: 19, dim1: '1920*1080', dim0: '电信' },
      { dim1: '1080*1920', total: 1, dim0: '电信' },
      { dim1: '1080*2029', total: 1, dim0: '电信' },
      { dim1: '1080*2160', total: 26, dim0: '移动' }
    ]
  },
  {
    name: '多维度，多指标查询，如气泡图',
    fields: [
      { name: 'dim0', title: '维度1', type: 'string', required: true },
      { name: 'dim1', title: '维度2', type: 'string', required: true },
      { name: 'total', title: '数值1', type: 'number', required: true },
      { name: 'total2', title: '数值2', type: 'number', required: true }
    ],
    demoData: [
      { total: 164, dim1: '启动', total2: 160, dim0: '电信' },
      { total: 283, dim1: '窗口停留', total2: 109, dim0: '电信' },
      { total: 251, dim1: '浏览', total2: 84, dim0: '电信' },
      { total: 50, dim1: '后台', total2: 35, dim0: '电信' },
      { total: 47, dim1: 'APP停留', total2: 32, dim0: '电信' },
      { total: 20, dim1: '唤醒', total2: 14, dim0: '电信' },
      { total: 11, dim1: 'APP安装', total2: 11, dim0: '电信' },
      { total: 33, dim1: '停留', total2: 6, dim0: '电信' },
      { total: 4, dim1: '首次访问', total2: 4, dim0: '电信' },
      { total: 36, dim1: '点击', total2: 4, dim0: '电信' },

      { total: 22, dim1: '启动', total2: 22, dim0: '联通' },
      { total: 858, dim1: '窗口停留', total2: 22, dim0: '联通' },
      { total: 843, dim1: '浏览', total2: 14, dim0: '联通' },
      { total: 19, dim1: 'APP停留', total2: 10, dim0: '联通' },
      { total: 17, dim1: '后台', total2: 9, dim0: '联通' },
      { total: 11, dim1: '唤醒', total2: 6, dim0: '联通' },
      { dim1: '退出', total: 4, total2: 3, dim0: '联通' },
      { dim1: '点击', total: 15, total2: 2, dim0: '联通' },
      { dim1: '聚焦', total: 3, total2: 1, dim0: '联通' },

      { total: 4, dim1: '启动', total2: 4, dim0: '未知' },
      { total: 10, dim1: '浏览', total2: 2, dim0: '未知' },
      { total: 4, dim1: '点击', total2: 1, dim0: '未知' },
      { total: 9, dim1: '窗口停留', total2: 1, dim0: '未知' },

      { dim1: 'APP停留', total: 4, total2: 1, dim0: '移动' },
      { dim1: '唤醒', total: 1, total2: 1, dim0: '移动' },
      { dim1: '退出', total: 4, total2: 1, dim0: '移动' },
      { dim1: '窗口停留', total: 3, total2: 1, dim0: '移动' },
      { dim1: '点击', total: 9, total2: 1, dim0: '移动' },
      { dim1: '浏览', total: 2, total2: 1, dim0: '移动' },
      { dim1: '启动', total: 1, total2: 1, dim0: '移动' },
      { dim1: '后台', total: 2, total2: 1, dim0: '移动' }
    ]
  }
]

/**
 * 测试数据设置表单
 * @param props
 * @constructor
 */
export default function TestDataForm(props) {
  const { value, onChange } = props

  const { fields, onFieldsChange } = useFormFieldsAdapter(value, onChange)

  return (
    <Form className='!px-8 !py-4' layout='vertical' autoComplete='off' fields={fields} onFieldsChange={onFieldsChange}>
      <Form.Item label='预设' name='name'>
        <Select
          getPopupContainer={triggerNode => triggerNode.parentNode}
          placeholder='未选择预设'
          {...enableSelectSearch}
          onChange={name => {
            const obj = _.find(TestDataPreset, p => p.name === name)
            const next = _.mapValues(obj, v => (!_.isObject(v) ? v : JSON.stringify(v, null, 2)))
            onChange(next)
          }}
        >
          {_.map(TestDataPreset, (v, idx) => (
            <Select.Option value={v.name} key={idx}>
              {v.name}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>

      <div className='test-data-form-row-layout'>
        <Form.Item label='字段描述（fields）' name='fields' required>
          <CodeEditor
            className='!w-full !h-[600px] border border-gray-100 border-solid'
            onBlur={editor => {
              // 不应设置多个 "dynamic": true
              const fieldsPreSave: ChartField[] = tryJsObjParse(editor.getValue(), [])
              const [strFields, numFields] = _.partition(fieldsPreSave, f => f.type === 'string')
              if (_.filter(strFields, f => f.dynamic).length > 1 || _.filter(numFields, f => f.dynamic).length > 1) {
                message.error('维度或指标最多只能设置一个动态字段，一般建议设置为最后一个维度或指标')
              }
            }}
          />
        </Form.Item>

        <Form.Item label='图表数据（data）' name='demoData'>
          <CodeEditor className='!w-full !h-[600px] border border-gray-100 border-solid' />
        </Form.Item>
      </div>
    </Form>
  )
}
