import { useReactive } from 'ahooks'
import { Button, Tabs } from 'antd'
import { produce } from 'immer'
import _ from 'lodash'
import React, { useEffect } from 'react'

import CodeEditor from '@/components/code-editor'
import { DEFAULT_CUSTOM_ACTION_HANDLER_DEFINES } from '@/consts/define'
import { ChartsGalleryEditState } from '@/pages/charts-gallery/containers/editor/index'
import { ComponentDefine } from '@/types/editor-core/component'
import { tryJsObjParse } from '@/utils/json-utils'

const { TabPane } = Tabs

interface ActionHandlerDefinePaneState {
  pendingEditorContent: string | null
}

/** 将对象转换为可编辑的字符串 */
function objToEditorContent(obj) {
  const json = JSON.stringify(obj, (k, v) => (k in obj ? `unwrap#${k}` : v), 2)
  const s = json.replace(/"unwrap#(\w+)"/g, (_m, fnName) => `${obj[fnName]}`)
  return `(${s})` // 包一层是因为编辑器不支持 json 模式，解决报错
}

/** 解析编辑器内容到 js 对象 */
function editorContentToObj(str: string) {
  const obj = tryJsObjParse(str || '{}') || {}
  return _.mapValues(obj, v => (_.isFunction(v) ? `(${v})` : v))
}

/** actionName 内容的读取与改写面板 */
function useActionNameSwitcher(reactiveState: ActionHandlerDefinePaneState, onSave: () => any) {
  const onReset = () => {
    reactiveState.pendingEditorContent = objToEditorContent(DEFAULT_CUSTOM_ACTION_HANDLER_DEFINES)
    onSave()
  }
  return (
    <Button className='!inline-block align-top !absolute top-6 right-4 !w-[50px]' onClick={onReset} size='small'>
      重置
    </Button>
  )
}

/** 渲染动作执行逻辑定义面板 */
export function useActionHandlerDefinePane(
  pendingChart: Partial<ComponentDefine> | null,
  setEditorStatePatch: (patch: Partial<ChartsGalleryEditState> | ((prev: ChartsGalleryEditState) => any)) => void
) {
  const reactiveState = useReactive<ActionHandlerDefinePaneState>({
    pendingEditorContent: null
  })

  useEffect(() => {
    const pendingDefineObj = pendingChart?.eventActionDefine?.actionHandler || {}
    reactiveState.pendingEditorContent = objToEditorContent(pendingDefineObj)
  }, [reactiveState, pendingChart?.eventActionDefine?.actionHandler])

  const onCodeChange = nextCode => {
    reactiveState.pendingEditorContent = nextCode
  }

  const onSave = () => {
    setEditorStatePatch(prev => ({
      ...prev,
      pendingChart: produce(prev.pendingChart, draft => {
        if (!draft) return
        if (!draft.eventActionDefine) {
          draft.eventActionDefine = {}
        }

        draft.eventActionDefine.actionHandler = editorContentToObj(reactiveState.pendingEditorContent || '{}')
      })
    }))
  }

  const actionNameSwitcher = useActionNameSwitcher(reactiveState, onSave)

  const onEditorMount = () => setEditorStatePatch({ codeEditorMounted: true })
  return (
    <TabPane tab='动作定义' key='action'>
      <div className='relative h-full'>
        <CodeEditor
          className='!h-full !w-full'
          // language='json' // 没效果
          // options={{ language: 'json' }}
          value={reactiveState.pendingEditorContent || ''}
          onChange={onCodeChange}
          onBlur={onSave}
          onMount={onEditorMount}
        />

        {actionNameSwitcher}
      </div>
    </TabPane>
  )
}
