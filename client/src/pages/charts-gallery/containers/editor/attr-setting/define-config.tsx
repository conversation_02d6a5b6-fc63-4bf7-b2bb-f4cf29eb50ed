import { useReactive, useWhyDidYouUpdate } from 'ahooks'
import { message, Radio } from 'antd'
import isEqual from 'fast-deep-equal/react'
import { klona as cloneDeep } from 'klona/lite'
import _ from 'lodash'
import React, { memo, useEffect } from 'react'

import CodeEditor from '@/components/code-editor'
import MarkdownView from '@/components/markdown-view'
import { getCodeStringByData, getDataByCodeString } from '@/utils'

const exp = `
### 说明
1. 配置定义（configDefine、styleDefine）用于设置**创建组件时的默认值**。
2. 配置定义部分的值由模型配置生成，模型配置改变时会做**对象的 merge 处理**。
3. 配置定义不要设置过多的默认值，也不要有冗余数据。
`

export interface DefineConfigProps {
  data?: Partial<{
    configDefine: Record<string, any>
    styleDefine: Record<string, any>
    configSchema: Record<string, any>
    styleSchema: Record<string, any>
    baseSchema: Record<string, any>
  }>
  onSubmit?: (val: any) => any
  tab?: string
  onTabChange?: (tab: string) => any
}

function DefineConfig(props: DefineConfigProps) {
  const { data = {}, tab, onTabChange } = props
  const type = tab

  const state = useReactive({
    configDefine: {} as any,
    baseSchema: {} as any,
    configSchema: {} as any,
    styleSchema: {} as any,
    styleDefine: {} as any,
    source: ''
  })

  const options = [
    { value: 'config', label: '配置定义' },
    { value: 'style', label: '样式定义' }
  ]

  const onSubmit = _.debounce(() => {
    try {
      const result = getDataByCodeString(state.source, type === 'config' ? 'configDefine' : 'styleDefine')
      const params: any = {}
      if (type === 'config') params.configDefine = result
      if (type === 'style') params.styleDefine = result
      props.onSubmit?.(params)
    } catch (err) {
      message.error({
        content: '编辑有错误，请检查',
        key: '编辑有错误，请检查'
      })
    }
  }, 500)

  const onCodeChange = (val: string) => {
    state.source = val
    onSubmit()
  }

  useEffect(() => {
    if (type === 'config') {
      state.source = getCodeStringByData('配置定义', 'configDefine', data.configDefine || {})
      state.configDefine = cloneDeep(data.configDefine)
    }
    if (type === 'style') {
      state.source = getCodeStringByData('样式定义', 'styleDefine', data.styleDefine || {})
      state.styleDefine = cloneDeep(data.styleDefine)
    }
    state.baseSchema = cloneDeep(data.baseSchema)
    state.configSchema = cloneDeep(data.configSchema)
    state.styleSchema = cloneDeep(data.styleSchema)
  }, [type])

  return (
    <div className='config-define-editor'>
      <header className='header'>
        <div>
          <Radio.Group
            options={options}
            value={type}
            optionType='button'
            size='small'
            onChange={e => onTabChange?.(e.target.value)}
          />
        </div>
      </header>

      <div className='config-define-editor-content'>
        <div className='exmplate'>
          <MarkdownView content={exp} />
        </div>

        <CodeEditor
          className='h-full code-editor-view'
          value={state.source}
          onChange={onCodeChange}
          options={{
            minimap: { enabled: !true }
          }}
        />
      </div>
    </div>
  )
}

export default memo(DefineConfig, isEqual)
