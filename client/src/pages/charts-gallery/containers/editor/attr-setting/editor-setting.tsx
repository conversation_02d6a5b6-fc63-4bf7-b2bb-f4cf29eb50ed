import { useMemoizedFn } from 'ahooks'
import { Radio } from 'antd'
import isEqual from 'fast-deep-equal/react'
import _ from 'lodash'
import React, { memo, useEffect, useState } from 'react'

import { FormSchemaEditor } from '@/components/form-schema'
import { ELEMENT_OMIT_FIELD } from '@/consts/screen'
import { getCodeStringByData, getDataByCodeString } from '@/utils'

const configOmitField = ['visible', 'lock', 'i18n']
const styleOmitField = ELEMENT_OMIT_FIELD

export interface FormSchemaModalProps {
  data?: Partial<{
    configDefine: Record<string, any>
    styleDefine: Record<string, any>
    configSchema: Record<string, any>
    styleSchema: Record<string, any>
    baseSchema: Record<string, any>
  }>
  onSubmit?: (val: any) => any
  tab?: string
  onTabChange?: (tab: string) => any
}

/**
 * 进入表单模型设计器
 */
function FormSchemaModal(props: FormSchemaModalProps) {
  const { data = {}, tab, onTabChange } = props
  const type = tab

  const options = [
    { value: 'base', label: '基础模型' },
    { value: 'config', label: '属性模型' },
    { value: 'style', label: '样式模型' }
  ]

  const [state, setState] = useState({
    schema: {},
    define: {},
    schemaCode: getCodeStringByData('模型', 'schema', {}),
    resultCode: getCodeStringByData('输出', 'config', {})
  })
  const update = newState => setState(s => ({ ...s, ...newState }))
  const resultKey = type === 'style' ? 'style' : 'config'

  const onSubmit = newValue => {
    const { schema } = newValue

    // const isError = ''
    // onRefresh(err => isError === err)
    // if (isError) {
    //   message.error('错误：', isError)
    //   return
    // }

    const params: any = {}

    if (type === 'config') {
      params.configSchema = _.omit(schema, configOmitField)
      params.configDefine = _.merge({}, data?.configDefine, state.define)
    }
    if (type === 'style') {
      params.styleSchema = _.omit(schema, styleOmitField)
      params.styleDefine = _.merge({}, data?.styleDefine, state.define)
    }
    if (type === 'base') {
      params.baseSchema = _.omit(schema, configOmitField)
      params.configDefine = _.merge({}, data?.configDefine, state.define)
    }

    props.onSubmit?.(params)
  }

  const onSchemaChange = _.debounce(val => update({ schemaCode: val }), 160)
  const onRefresh = async (cb?: Function) => {
    try {
      const schema = getDataByCodeString(state.schemaCode, 'schema')
      // const define = getInitDefaultValue(schema)
      const schemaCode = getCodeStringByData('模型', 'schema', schema)
      // const resultCode = getCodeStringByData('输出', resultKey, define)
      update({ schema, schemaCode, resultCode: '' })
      await new Promise(rs => setTimeout(rs, 360))
      onSubmit({ schema })
    } catch (err) {
      cb?.(err)
    }
  }

  const onConfigChange = useMemoizedFn(val => {
    const resultCode = getCodeStringByData('输出', resultKey, val)
    update({ define: val, resultCode })
  })

  // 获取初始化的数据
  const getInitData = (val: any, mode: string) => {
    let schema = {}
    let define = {}
    if (mode === 'config') {
      const configDefine = _.merge({}, val?.configDefine)
      schema = val?.configSchema || {}
      define = _.omit(configDefine, configOmitField)
    }
    if (mode === 'style') {
      const styleDefine = _.merge({}, val?.styleDefine)
      schema = val?.styleSchema || {}
      define = _.omit(styleDefine, styleOmitField)
    }
    if (mode === 'base') {
      const configDefine = _.merge({}, val?.configDefine)
      schema = val?.baseSchema || {}
      define = _.omit(configDefine, configOmitField)
    }
    const schemaCode = getCodeStringByData('模型', 'schema', schema)
    // const resultCode = getCodeStringByData('输出（不可编辑）', resultKey, define)
    return { schema, define, schemaCode, resultCode: '' }
  }

  // 初始化
  useEffect(() => {
    if (!data) return
    update(getInitData(data, type!))
  }, [data, type])

  return (
    <div className='attr-setting-editor'>
      <header className='header'>
        <div>
          <Radio.Group
            options={options}
            value={type}
            optionType='button'
            size='small'
            onChange={e => onTabChange?.(e.target.value)}
          />
        </div>
      </header>
      <div className='modal-content form-schema-page'>
        <FormSchemaEditor
          key={type}
          config={state.define}
          configCode={state.resultCode}
          schema={state.schema}
          schemaCode={state.schemaCode}
          onConfigChange={onConfigChange}
          onRefresh={onRefresh}
          onSchemaChange={onSchemaChange}
        // verifyMessage={verifyMessage}
        />
      </div>
    </div>
  )
}

export default memo(FormSchemaModal, isEqual)
