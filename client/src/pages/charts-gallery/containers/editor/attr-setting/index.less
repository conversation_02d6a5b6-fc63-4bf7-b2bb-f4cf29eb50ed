.charts-gallery-attr-setting {
  position: relative;
  height: 100%;

  > div {
    height: 100%;
    width: 100%;
  }

  .group-type-panel {
    height: 30px;
    position: absolute;
    top: 8px;
    left: 10px;
    z-index: 10;
    width: 200px;
  }

  .attr-setting-editor,
  .config-define-editor {
    height: 100%;
    > .header {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      justify-content: flex-end;
    }

    .message-panel {
      display: flex;
      align-items: center;
      .ant-alert {
        padding: 3px 8px;
        margin-right: 6px;
        font-size: 13px;
        display: flex;
        align-items: center;
        .anticon-exclamation-circle {
          font-size: 20px;
        }
      }
    }
  }

  .attr-setting-editor {
    .form-schema-page {
      height: 94%;
      .form-schema-editor-view {
        height: 100%;
      }
    }
    .config-code {
      max-width: 420px;
      width: auto;
      flex: 1;
    }
  }

  .config-define-editor-content {
    height: 96%;
    display: flex;
    flex-direction: column;

    .exmplate {
      display: flex;
      flex: 1;
      justify-content: space-around;
      padding: 6px;
      padding-bottom: 0;
      min-width: 350px;

      > .abi-form-schema-main,
      > .form-schema-main {
        margin: 12px auto;
        border: 1px solid var(--border-color-base);
        border-radius: 3px;
        // box-shadow: 0 0 6px rgba(#111, 0.08);
        max-height: 600px;
        overflow-y: auto;
      }
    }

    .code-editor-view {
      max-width: 50vw;
    }
  }
}
