import './index.less'

import { useMemoizedFn, useReactive } from 'ahooks'
import { Radio, Tooltip } from 'antd'
import React from 'react'

import DefineConfigEditor from './define-config'
import FormSchemaEditor from './editor-setting'

export interface AttrSettingProps {
  styleSchema: Record<string, any>
  styleDefine: Record<string, any>
  configSchema: Record<string, any>
  configDefine: Record<string, any>
  baseSchema: Record<string, any>
  onChange: (data: Omit<AttrSettingProps, 'onChange'>) => any
}

/**
 * 属性设置
 */
export default function AttrSetting(props: AttrSettingProps) {
  const state = useReactive({
    mode: 'schema' as 'schema' | 'define',
    defineTab: 'config',
    schemaTab: 'base'
  })

  const data = {
    configDefine: props.configDefine || {},
    configSchema: props.configSchema || {},

    styleDefine: props.styleDefine || {},
    styleSchema: props.styleSchema || {},
    baseSchema: props.baseSchema || {}
  }

  const defineOptions = [
    { value: 'schema', label: '模型配置', describe: '模型配置用于生成组件的表单输入项' },
    { value: 'define', label: '定义编辑', describe: '定义编辑用于设置组件的默认值信息' }
  ]

  const onChange = useMemoizedFn(val => props.onChange?.(val))
  const onTabChange1 = useMemoizedFn(val => state.defineTab = val)
  const onTabChange2 = useMemoizedFn(val => state.schemaTab = val)

  const renderLeftExpand = useMemoizedFn(() => (
    <div className='group-type-panel'>
      <Radio.Group
        // options={defineOptions}
        optionType='button'
        size='small'
        value={state.mode}
        onChange={e => (state.mode = e.target.value)}
      >
        {defineOptions.map(item => (
          <Tooltip title={item.describe} key={item.value}>
            <Radio.Button value={item.value} key={item.value}>
              {item.label}
            </Radio.Button>
          </Tooltip>
        ))}
      </Radio.Group>
      {/* <span className='text-gray-600 ml-2 mr-2'>可编辑样式与属性定义表单模型，用于生产样式与属性配置</span> */}
    </div>
  ))

  return (
    <div className='charts-gallery-attr-setting'>
      {renderLeftExpand()}
      <div style={{ display: state.mode === 'define' ? 'block' : 'none' }}>
        <DefineConfigEditor
          data={data}
          onSubmit={onChange}
          tab={state.defineTab}
          onTabChange={onTabChange1}
        />
      </div>
      <div style={{ display: state.mode === 'schema' ? 'block' : 'none' }}>
        <FormSchemaEditor
          data={data}
          onSubmit={onChange}
          tab={state.schemaTab}
          onTabChange={onTabChange2}
        />
      </div>
    </div>
  )
}
