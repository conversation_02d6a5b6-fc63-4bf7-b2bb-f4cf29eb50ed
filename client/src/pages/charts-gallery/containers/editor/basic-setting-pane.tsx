import { Tabs } from 'antd'
import React from 'react'

import ChartsBasicSettingForm from '@/pages/charts-gallery/components/basic-setting-form'
import { ChartsGalleryEditState } from '@/pages/charts-gallery/containers/editor/index'
import { ComponentDefine } from '@/types/editor-core/component'

const { TabPane } = Tabs

/**
 * 渲染底图配置面板
 * @param pendingChart
 * @param setEditorStatePatch
 */
export function useBasicSettingPane(
  pendingChart: Partial<ComponentDefine> | null | undefined,
  setEditorStatePatch: (value: Partial<ChartsGalleryEditState>) => void
) {
  return (
    <TabPane tab='底图配置' key='base' className='overflow-y-auto' forceRender>
      {/* 使用内置底图/设置 notebook 地址(可上传代码包生产)；设置展示的变量和启用条件 */}
      <ChartsBasicSettingForm
        value={pendingChart}
        onChange={next => {
          setEditorStatePatch({
            pendingChart: { ...pendingChart, ...next }
          })
        }}
      />
    </TabPane>
  )
}
