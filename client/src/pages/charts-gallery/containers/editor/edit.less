// pc 端
.custom-chart-renderer {
  // transform: scale(1.15);
  box-shadow: 0 0 4px rgba(1, 11, 21, 0.14);
  min-height: 270px;
  height: min-content;
  max-height: calc(100vh - 200px);

  overflow: auto;

  > div {
    min-height: 270px;
  }
}

// 移动端
.custom-chart-renderer-mobile {
  width: 360px;
  border: 1px solid #f1f1f1;
  border-radius: 10px;
  margin: 24px auto;
  min-height: 600px;
  max-height: 700px;
  overflow-x: hidden;
  overflow-y: auto;
  box-shadow: 0 0 4px rgba(1, 11, 21, 0.13);

  .mobile-header {
    height: 35px;
    background-size: 106%;
    background-position: -10px -10px;
    border-bottom: 1px solid #f1f1f1;
    display: flex;
    align-items: center;
    padding: 0 16px;

    > .flex-1 {
      flex: 1;
    }

    > .five-g {
      font-size: 15px;
      margin-right: 4px;
      line-height: 1;
      padding-top: 4px;
    }
    > .dot {
      position: relative;
      width: 20px;
      height: 20px;
      border-radius: 100%;
      background-color: #444;
      margin-right: 10px;
      &::after {
        position: absolute;
        width: 10px;
        height: 10px;
        content: '';
        left: 5px;
        top: 5px;
        z-index: 1;
        border: 1px dashed rgba(#fff, 0.6);
        background-color: rgba(#fff, 0.12);
        border-radius: 100%;
      }
    }
    > .anticon {
      margin-right: 12px;
      color: #555;
      &:last-of-type {
        margin-right: 0;
      }
    }
  }
  .custom-chart-renderer {
    width: 360px !important;
    box-shadow: none;
    transform: none;
    margin: 0;
    margin-top: 4px;
  }
}
