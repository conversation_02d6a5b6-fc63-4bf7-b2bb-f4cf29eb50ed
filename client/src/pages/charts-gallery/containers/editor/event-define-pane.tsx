import { useReactive } from 'ahooks'
import { Button, message, Tabs } from 'antd'
import { produce } from 'immer'
import _ from 'lodash'
import React, { useEffect } from 'react'

import CodeEditor from '@/components/code-editor'
import { DEFAULT_CUSTOM_EVENT_DEFINES } from '@/consts/define'
import { ChartsGalleryEditState } from '@/pages/charts-gallery/containers/editor/index'
import { ComponentDefine } from '@/types/editor-core/component'

const { TabPane } = Tabs

interface EventDefinePaneState {
  hideResetDefaultBtn: boolean
  pendingEditorContent: string | null
}

/**
 * 渲染事件响应定义配置面板
 */
export function useEventActionDefinePane(
  pendingChart: Partial<ComponentDefine> | null | undefined,
  setEditorStatePatch: (value: Partial<ChartsGalleryEditState> | ((prev: ChartsGalleryEditState) => any)) => any
) {
  const reactiveState = useReactive<EventDefinePaneState>({
    hideResetDefaultBtn: false,
    pendingEditorContent: null
  })

  const pendingDefineObj = pendingChart?.eventActionDefine || DEFAULT_CUSTOM_EVENT_DEFINES
  useEffect(() => {
    const json = JSON.stringify(_.omit(pendingDefineObj, 'actionHandler'), null, 2)
    reactiveState.pendingEditorContent = `(${json})`
  }, [pendingDefineObj])

  const onCodeChange = nextCode => {
    reactiveState.pendingEditorContent = nextCode
  }
  const onEditorFocus = () => {
    reactiveState.hideResetDefaultBtn = true
  }

  const onSave = () => {
    let res: any
    try {
      // eslint-disable-next-line no-eval
      res = eval(`(${reactiveState.pendingEditorContent})`)
    } catch (e0) {
      console.error(e0)
      message.error((e0 as any).message)
    }

    setEditorStatePatch(prev => ({
      ...prev,
      pendingChart: produce(prev.pendingChart, draft => {
        if (!draft) return
        if (!draft.eventActionDefine) {
          draft.eventActionDefine = {}
        }
        _.assign(draft.eventActionDefine, res)
      })
    }))
  }

  const onReset = () => {
    const json = JSON.stringify(DEFAULT_CUSTOM_EVENT_DEFINES, null, 2)
    reactiveState.pendingEditorContent = `(${json})`
    onSave()
  }

  const onEditorBlur = () => {
    reactiveState.hideResetDefaultBtn = false
    onSave()
  }
  const onEditorMount = () => setEditorStatePatch({ codeEditorMounted: true })
  return (
    <TabPane tab='事件响应' key='event'>
      <div className='relative h-full'>
        <CodeEditor
          className='!h-full !w-full'
          // language='json' // 没效果
          // options={{ language: 'json' }}
          value={reactiveState.pendingEditorContent || ''}
          onChange={onCodeChange}
          onFocus={onEditorFocus}
          onBlur={onEditorBlur}
          onMount={onEditorMount}
        />

        <Button className='!inline-block align-top !absolute top-6 right-4 !w-[50px]' onClick={onReset} size='small'>
          重置
        </Button>
      </div>
    </TabPane>
  )
}
