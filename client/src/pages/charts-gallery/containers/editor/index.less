.charts-gallery-editor-warp {
  display: flex;
  max-height: 100%;
  background-color: #fff;

  .dashed-btn {
    border-color: var(--primary-color-80);
    color: var(--primary-color-90);
  }

  // 导航栏
  .nav-bar {
    position: absolute;
    height: 45px;
    z-index: 30;
    top: 0;
    width: 100%;
    background-color: #fff;
    border-bottom: 1px solid #f1f1f1;

    display: flex;
    align-items: center;
    padding: 0 12px;

    .nav-bar-center {
      flex: 1;
    }

    .anticon-arrow-left {
      font-size: 18px;
      cursor: pointer;
      user-select: none;
    }

    .updated-at {
      color: #999;
      margin-left: 12px;
      font-size: 12px;
    }
  }

  > .charts-gallery-editor {
    flex: 1.2;
    margin-top: 50px;

    .ant-tabs-nav {
      margin-bottom: 1px;
    }
    .ant-tabs-content,
    .ant-tabs-tabpane {
      height: 100%;
    }
    .ant-upload-list {
      display: none;
    }
  }

  > .charts-gallery-view {
    width: 36%;
    min-width: 500px;
    border: none;
    margin-top: 50px;
    border-left: 1px solid #eee;

    .preview-nav-bar {
      height: 35px;
      padding: 0 8px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #eee;
      justify-content: space-between;

      .refresh-time {
        font-size: 13px;
        color: #999;
        float: right;
      }
    }
  }
}
