import './edit.less'
import './index.less'

import { useParams } from '@umijs/max'
import { useAsyncEffect, useTitle } from 'ahooks'
import { Button, message, Tabs } from 'antd'
import dayjs from 'dayjs'
import isEqual from 'fast-deep-equal'
import _ from 'lodash'
import React, { useEffect, useState } from 'react'

import { CUSTOM_CHART_INIT_PROPS } from '@/consts/define'
import { TestDataParam, TestDataPreset } from '@/pages/charts-gallery/components/test-data-form'
import { useActionHandlerDefinePane } from '@/pages/charts-gallery/containers/editor/action-define-pane'
import { useBasicSettingPane } from '@/pages/charts-gallery/containers/editor/basic-setting-pane'
import { useEventActionDefinePane } from '@/pages/charts-gallery/containers/editor/event-define-pane'
import { useOptionEditPane } from '@/pages/charts-gallery/containers/editor/option-edit-pane'
import { usePreviewPanel } from '@/pages/charts-gallery/containers/editor/preview-panel'
import { useStyleDefinePane } from '@/pages/charts-gallery/containers/editor/style-define-pane'
import { useTestDataSettingPane } from '@/pages/charts-gallery/containers/editor/test-data-setting-pane'
import { useSaveBar } from '@/pages/charts-gallery/containers/editor/use-save-bar'
import { Service } from '@/services'
import { ComponentDefine } from '@/types/editor-core/component'
import { compareVersion } from '@/utils'
import { tryJsObjParse } from '@/utils/json-utils'

import MenuPanel from './menu-panel'

/**
 * 自定义图表编辑器响应式状态
 */
export interface ChartsGalleryEditState {
  // 弹出框的 key
  visiblePopoverKey: string
  // 当前 tab 的 key
  selectedTabKey: string
  // 正在输入的测试数据 json 文本
  pendingTestDataStr: Record<string, any> | null
  // 正在预览的图表数据
  previewingChart: Partial<ComponentDefine> | null
  // 正在修改的图表数据
  pendingChart: Partial<ComponentDefine> | null
  // 编辑器是否已经加载，加载完了再加载图表，否则编辑器会报错
  codeEditorMounted: boolean
}

/** 封装运行按钮相关逻辑 */
function useRunBtn(
  editorState: ChartsGalleryEditState,
  setEditorStatePatch: (patch: Partial<ChartsGalleryEditState> | ((next: ChartsGalleryEditState) => any)) => void,
  editingChart: Partial<ComponentDefine> | null,
  onRefresh: any
) {
  const pendingTestDataStr = editorState.pendingTestDataStr
  const previewingChart = editorState.previewingChart
  const pendingChart = editorState.pendingChart

  // 运行，即把 pendingChart 设置到 previewingChart
  const onRun = () => {
    // 判断版本号是否变小
    try {
      if (compareVersion(editingChart?.version || '0', pendingChart?.version || '0') > 0) {
        message.warn('版本号不能变小')
        setEditorStatePatch({ visiblePopoverKey: '' })
        return
      }
    } catch (e) {
      console.error(e)
      return
    } finally {
      onRefresh()
    }

    const obj = _.mapValues(pendingTestDataStr, (v, k) => (k === 'name' || !_.isString(v) ? v : tryJsObjParse(v)))
    const nextPreviewingChart: Partial<ComponentDefine> = {
      ...previewingChart,
      ...pendingChart,
      // 尽量不保存 demoData，根据 name 来取
      dataSourceDefine: _.omit(obj, 'default')
    }
    setEditorStatePatch({
      previewingChart: nextPreviewingChart
    })

    // 返回的是准备保存的信息
    const originalTestDataPreset = _.find(TestDataPreset, d => d.name === obj.name)
    const nextDataSourceDefine = isEqual(originalTestDataPreset?.demoData, obj.demoData) ? _.omit(obj, 'demoData') : obj
    return {
      ...nextPreviewingChart,
      dataSourceDefine: _.omit(nextDataSourceDefine, 'default')
    }
  }
  const runBtn = (
    <Button size='small' className='mr-4 dashed-btn' onClick={onRun}>
      运行
    </Button>
  )
  return { onRun, runBtn }
}

/**
 * 自定义图表编辑器（详情）
 * @constructor
 */
export default function ChartsGalleryEdit() {
  const params: { key?: string } = useParams()
  const key = params?.key === 'new' ? undefined : params?.key

  const [refreshTime, setRefreshTime] = useState(0)
  const [editorState, setEditorState] = useState<ChartsGalleryEditState>(() => ({
    visiblePopoverKey: '',
    selectedTabKey: key === 'new' ? 'code' : 'base',
    pendingTestDataStr: null,
    previewingChart: null,
    pendingChart: null,
    codeEditorMounted: false
  }))

  // 图表详情
  const [detail, setDetail] = useState<any>({})

  const pendingTestDataStr = editorState.pendingTestDataStr
  const previewingChart = editorState.previewingChart
  const pendingChart = editorState.pendingChart

  // 准备保存的图表数据，预览时也会用到
  const editingChart = key ? detail : CUSTOM_CHART_INIT_PROPS

  // 加载自定义图表
  useAsyncEffect(async () => {
    if (key) {
      const res = await Service.ComponentDefine.findOne({ where: { key } })
      setDetail(res || CUSTOM_CHART_INIT_PROPS)
    }
  }, [key])

  // setState 支持 patch
  const setEditorStatePatch = (patch: Partial<ChartsGalleryEditState> | ((prev: ChartsGalleryEditState) => any)) => {
    setEditorState(_.isFunction(patch) ? patch : prev => ({ ...prev, ...patch }))
  }

  const onRefresh = () => setRefreshTime(Date.now())
  const { onRun, runBtn } = useRunBtn(editorState, setEditorStatePatch, editingChart, onRefresh)

  const saveBar = useSaveBar(key, detail, editorState, setEditorStatePatch, onRun)
  const eventDefinePane = useEventActionDefinePane(pendingChart, setEditorStatePatch)

  const actionDefinePane = useActionHandlerDefinePane(pendingChart, setEditorStatePatch)
  const testDataSettingPane = useTestDataSettingPane(pendingTestDataStr, setEditorStatePatch)

  const styleDefinePane = useStyleDefinePane(pendingChart, setEditorStatePatch)
  const optionEditPane = useOptionEditPane(pendingChart, setEditorStatePatch)
  const basicSettingPane = useBasicSettingPane(pendingChart, setEditorStatePatch)
  const previewPanel = usePreviewPanel(previewingChart, editorState.codeEditorMounted)

  useTitle(`${editingChart?.title}-自定义图表`)

  // 将数据定义转换为字符串
  useEffect(() => {
    const next = _.mapValues(pendingChart?.dataSourceDefine, v => (!_.isObject(v) ? v : JSON.stringify(v, null, 2)))
    setEditorStatePatch({ pendingTestDataStr: next })
  }, [pendingChart?.dataSourceDefine])

  // 打开自定义图表，会初始化 previewingChart
  useEffect(() => {
    const dataSourceDefine = !_.isEmpty(editingChart?.dataSourceDefine)
      ? editingChart!.dataSourceDefine!
      : (_.find(TestDataPreset, p => p.default) as TestDataParam)
    const withData = _.isEmpty(dataSourceDefine.demoData)
      ? {
        ...dataSourceDefine,
        demoData: _.find(TestDataPreset, p => p.name === dataSourceDefine?.name)?.demoData
      }
      : dataSourceDefine
    setEditorStatePatch({ previewingChart: { ...editingChart, dataSourceDefine: withData } })
  }, [editingChart])

  // previewingChart 变化，会修改 pendingChart
  useEffect(() => {
    setEditorStatePatch({ pendingChart: previewingChart })
  }, [previewingChart])

  return (
    <div className='h-full charts-gallery-editor-warp'>
      {saveBar}

      <MenuPanel active={editorState.selectedTabKey} onActiveChange={k => setEditorStatePatch({ selectedTabKey: k })} />

      <div className='h-full charts-gallery-editor'>
        <Tabs
          activeKey={editorState.selectedTabKey}
          onChange={k => setEditorStatePatch({ selectedTabKey: k })}
          className='h-full'
          tabBarGutter={24}
          animated={false}
          tabBarStyle={{ display: 'none' }}
        // tabBarExtraContent={runBtn}
        >
          {basicSettingPane}
          {optionEditPane}
          {styleDefinePane}
          {eventDefinePane}
          {actionDefinePane}
          {testDataSettingPane}
        </Tabs>
      </div>

      {/* 属性编辑时不要显示出来 */}
      <div className='h-full charts-gallery-view'>
        <div className='preview-nav-bar'>
          {runBtn}
          {refreshTime > 0 && <span className='refresh-time'>刷新于：{dayjs(refreshTime).format('HH:mm:ss')}</span>}
        </div>

        {previewPanel}
      </div>
    </div>
  )
}
