.meun-panel {
  position: relative;
  width: 240px;
  padding: 12px;
  min-width: 200px;
  margin-top: 45px;
  // box-shadow: 1px 2px 3px rgba(1, 1, 1, 0.12);
  border-right: 1px solid #e8e8e8;
  z-index: 10;
  padding-top: 30px;

  .meun-panel-item {
    cursor: pointer;
    padding: 10px 8px;
    margin-bottom: 12px;
    user-select: none;
    border: 1px solid transparent;

    &-summary {
      font-size: 14px;
      color: #999;
      margin-bottom: 0;
      margin-left: 30px;
      padding: 0 12px;
    }

    &-top {
      display: flex;
      align-items: center;

      &-title {
        font-size: 15px;
        margin: 0;
        color: #222;
        opacity: 0.75;
      }

      &-icon {
        width: 30px;
        height: 30px;
        line-height: 30px;
        border-radius: 50%;
        border: 1px solid #eee;
        text-align: center;
        margin-right: 12px;
      }
    }

    &:hover {
      .meun-panel-item-top-title {
        opacity: 1;
        color: var(--primary-color);
      }
    }
  }

  .meun-panel-item-active {
    border-radius: 20px;
    // box-shadow: 0 0 3px rgba(1, 1, 1, 0.15);
    background-color: var(--tint-color-98);
    border: 1px solid #eee;

    .meun-panel-item-top-title {
      opacity: 1;
      color: var(--primary-color);
      font-weight: bold;
    }
    .meun-panel-item-top-icon {
      color: var(--primary-color);
      border-color: var(--primary-color-30);
    }
  }
}
