import './index.less'

import {
  AlertOutlined,
  ApiOutlined,
  CodeOutlined,
  DatabaseOutlined,
  EditOutlined,
  ScheduleOutlined} from '@ant-design/icons'
import cn from 'classnames'
import React from 'react'

const OPTIONS = [
  { key: 'base', title: '基础配置', icon: EditOutlined, summary: '设置组件的最基础的配置' },
  { key: 'code', title: '图表代码编辑', icon: CodeOutlined, summary: '实现组件的渲染逻辑' },
  {
    key: 'config',
    title: '属性模型配置',
    icon: ScheduleOutlined,
    summary: '配置组件的表单模型，用于在组件样式、属性上进行展现'
  },
  { key: 'datascoure', title: '数据字段设置', icon: DatabaseOutlined, summary: '配置组件数据源的字段维度要求' },
  { key: 'event', title: '自定义事件', icon: ApiOutlined, summary: '可以进行自定义的事件实现' },
  { key: 'action', title: '自定义动作', icon: AlertOutlined, summary: '可以进行自定义的动作实现' }
]

export interface MenuPanelProps {
  active: string
  onActiveChange: (active: string) => any
}

/**
 * 菜单栏
 */
export default function MenuPanel(props: MenuPanelProps) {
  const { active, onActiveChange } = props

  return (
    <div className='meun-panel'>
      {OPTIONS.map(item => (
        <div
          key={item.key}
          className={cn({
            'meun-panel-item': true,
            'meun-panel-item-active': item.key === active
          })}
          onClick={() => onActiveChange(item.key)}
        >
          <div className='meun-panel-item-top'>
            <div className='meun-panel-item-top-icon'>
              <item.icon />
            </div>
            <h3 className='meun-panel-item-top-title'>{item.title}</h3>
          </div>
          <p className='meun-panel-item-summary'>{item.summary}</p>
        </div>
      ))}
    </div>
  )
}
