import { Tabs } from 'antd'
import { produce } from 'immer'
import _ from 'lodash'
import { nanoid } from 'nanoid'
import React from 'react'

import NotebookVariableOverriding from '@/pages/charts-gallery/components/notebook-variable-overriding'
import { ChartsGalleryEditState } from '@/pages/charts-gallery/containers/editor/index'
import { ComponentDefine } from '@/types/editor-core/component'
import { withExtraQuery } from '@/utils/query'

const { TabPane } = Tabs

/** 渲染示例编辑面板 */
export function useOptionEditPane(
  pendingChart: Partial<ComponentDefine> | null | undefined,
  setEditorStatePatch: (value: Partial<ChartsGalleryEditState> | ((next: ChartsGalleryEditState) => any)) => any
) {
  return (
    <TabPane tab='示例编辑' key='code' forceRender>
      {!pendingChart?.extraConfig?.moduleAddress ? (
        <div className='text-center px-8 py-4 text-gray-400 text-xl'>
          请先填写底图配置里的【observable notebook js 代码地址】和
        </div>
      ) : (
        <NotebookVariableOverriding
          notebookScriptUrl={pendingChart.extraConfig?.moduleAddress}
          preferVarName={_.startsWith(pendingChart.extraConfig?.displayMode, 'react') ? 'main' : 'option'}
          value={pendingChart.extraConfig?.variableDefineOverriding || {}}
          onChange={(next: Record<string, string>) => {
            setEditorStatePatch(prev => ({
              ...prev,
              pendingChart: {
                ...(prev.pendingChart || {}),
                extraConfig: { ...prev.pendingChart?.extraConfig, variableDefineOverriding: next }
              }
            }))
          }}
          onEditorMount={() => setEditorStatePatch({ codeEditorMounted: true })}
          onRefreshChart={() =>
            // 直接往 url 上加随机参数才能刷新
            setEditorStatePatch(prev => ({
              ...prev,
              pendingChart: produce(prev.pendingChart, draft => {
                if (!draft?.extraConfig?.moduleAddress) return
                draft.extraConfig.moduleAddress = withExtraQuery(
                  draft.extraConfig.moduleAddress.replace(/&?t=[^&]+/, ''),
                  `t=${nanoid(5)}`
                )
              })
            }))
          }
        />
      )}
    </TabPane>
  )
}
