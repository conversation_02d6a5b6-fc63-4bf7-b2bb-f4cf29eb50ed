import { EnvironmentOutlined, WifiOutlined } from '@ant-design/icons'
import React from 'react'

import Icon from '@/components/icons/iconfont-icon'
import ComponentNotebookAdapter from '@/pages/charts-gallery/components/component-notebook-adapter'
import { ComponentDefine } from '@/types/editor-core/component'

/**
 * 图表预览区域
 * @param previewingChart
 * @param codeEditorMounted
 */
export function usePreviewPanel(previewingChart: Partial<ComponentDefine> | null, codeEditorMounted: boolean) {
  if (!codeEditorMounted) {
    return null
  }
  if (!previewingChart?.extraConfig?.moduleAddress || !previewingChart?.extraConfig?.displayVariableName) {
    return (
      <div className='text-center p-8 text-gray-400 text-xl'>
        请先填写【observable notebook js 代码地址】和【用于展示的单元的变量的名称】
      </div>
    )
  }

  const renderNotebook = () => (
    <ComponentNotebookAdapter
      className='chart-preview custom-chart-renderer w-[480px] mx-auto my-4'
      define={previewingChart || {}}
      data={previewingChart?.dataSourceDefine?.demoData || []}
      config={previewingChart?.configDefine}
      meta={{}}
    />
  )

  if (previewingChart.device === 'mobile') {
    return (
      <div className='custom-chart-renderer-mobile'>
        <div className='mobile-header'>
          <span className='dot' title='摄像头' />
          <span className='five-g'>6G</span>
          <Icon name='5G信道' size={20} />
          <div className='flex-1' />
          <WifiOutlined className='wifi' />
          <EnvironmentOutlined className='location' />
          <Icon name='80%电量' size={20} />
        </div>
        {renderNotebook()}
      </div>
    )
  }

  // 右边的图表
  return renderNotebook()
}
