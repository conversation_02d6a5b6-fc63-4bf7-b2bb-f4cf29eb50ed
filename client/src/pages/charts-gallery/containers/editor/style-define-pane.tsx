import { Tabs } from 'antd'
import React from 'react'

import type { ChartsGalleryEditState } from '@/pages/charts-gallery/containers/editor'
import AttrSetting from '@/pages/charts-gallery/containers/editor/attr-setting'

const { TabPane } = Tabs

/**
 * 渲染样式定义面板
 */
export function useStyleDefinePane(
  detail: ChartsGalleryEditState['pendingChart'],
  setEditorStatePatch: (value: Partial<ChartsGalleryEditState>) => any
) {
  return (
    <TabPane tab='属性定义' key='config'>
      <AttrSetting
        styleSchema={detail?.styleSchema || {}}
        styleDefine={detail?.styleDefine || {}}
        configSchema={detail?.configSchema || {}}
        configDefine={detail?.configDefine || {}}
        baseSchema={detail?.baseSchema || {}}
        onChange={val => {
          setEditorStatePatch({ pendingChart: { ...detail, ...val } })
        }}
      />
      {/* <div className='px-8 py-4'>配置需要支持哪些样式</div> */}
    </TabPane>
  )
}
