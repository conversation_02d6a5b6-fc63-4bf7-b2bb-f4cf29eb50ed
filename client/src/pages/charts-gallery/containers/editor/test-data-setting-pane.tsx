import { Tabs } from 'antd'
import React from 'react'

import TestDataForm from '@/pages/charts-gallery/components/test-data-form'
import type { ChartsGalleryEditState } from '@/pages/charts-gallery/containers/editor'

const { TabPane } = Tabs

/**
 * 渲染测试数据设置面板
 * @param pendingTestDataStr
 * @param setEditorStatePatch
 */
export function useTestDataSettingPane(
  pendingTestDataStr: Record<string, any> | null | undefined,
  setEditorStatePatch: (value: Partial<ChartsGalleryEditState>) => void
) {
  return (
    <TabPane tab='字段配置' key='datascoure' className='overflow-y-auto'>
      <TestDataForm
        value={pendingTestDataStr}
        onChange={next => {
          setEditorStatePatch({ pendingTestDataStr: next })
        }}
      />
    </TabPane>
  )
}
