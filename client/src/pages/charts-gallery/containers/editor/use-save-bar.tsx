import { ArrowLeftOutlined } from '@ant-design/icons'
import { history } from '@umijs/max'
import { Button, Input, message, Popconfirm as PopConfirm } from 'antd'
import dayjs from 'dayjs'
import _ from 'lodash'
import React from 'react'

import { useNewBi } from '@/hooks/use-new-bi'
import type { ChartsGalleryEditState } from '@/pages/charts-gallery/containers/editor'
import { useCommit as useChartGalleryCommit } from '@/stores/models/charts-gallery'
import { ComponentDefine } from '@/types/editor-core/component'
import { takeSnapshot } from '@/utils'
import { delayPromised } from '@/utils/promise-utils'

/**
 * 渲染保存功能所在顶部横条
 * @param key
 * @param editorState
 * @param setEditorStatePatch
 * @param onRun
 */
export function useSaveBar(
  key: string | undefined,
  detail: any,
  editorState: ChartsGalleryEditState,
  setEditorStatePatch: (patch: Partial<ChartsGalleryEditState>) => void,
  onRun: Function
) {
  const chartsGalleryCommit = useChartGalleryCommit()
  const { isNewBi, markNewBi } = useNewBi()

  const pendingChart = editorState.pendingChart

  const onSave = async (forceCreate = false) => {
    // 保存截图
    const previewingChart0: Partial<ComponentDefine> = onRun()
    if (!previewingChart0) return

    await delayPromised(300)
    setEditorStatePatch({ visiblePopoverKey: 'saving-pending' })

    let res

    if (!previewingChart0?.id || forceCreate) {
      res = await chartsGalleryCommit(
        'createInDB',
        forceCreate ? _.omit(previewingChart0, ['id', 'objectId', 'name', 'versionCode']) : (previewingChart0 as any)
      )
    } else {
      res = await chartsGalleryCommit('updateInDB', {
        prev: detail,
        next: previewingChart0 as any
      })
    }

    setEditorStatePatch({ visiblePopoverKey: '' })

    if (!res || _.isEmpty(res)) {
      return // 保存报错
    }
    const isCreated = !key || forceCreate || (res.key && res.key !== previewingChart0.key) // 由于版本号变化引起的创建
    message.success(
      <span>
        {isCreated ? '创建' : '修改'}
        图表内容成功
        <a href={`${window.routerBase || ''}/charts/gallery`}>，返回列表页</a>
      </span>
    )
    if (isCreated) {
      history.push(`/charts/gallery/${res.key}`)
    }
  }

  const visiblePopoverKey = editorState.visiblePopoverKey
  const onNameChange = ev => {
    const { value } = ev.target
    setEditorStatePatch({
      pendingChart: { ...pendingChart, title: value } as ComponentDefine
    })
  }
  const onVersionChange = ev => {
    const { value } = ev.target
    setEditorStatePatch({
      pendingChart: { ...pendingChart, version: value } as ComponentDefine
    })
  }
  const onClickSave = async () => {
    if (visiblePopoverKey === 'saving') {
      setEditorStatePatch({ visiblePopoverKey: '' })
      return
    }
    setEditorStatePatch({ visiblePopoverKey: 'saving-snapshot' })
    // 更新预览
    onRun()
    await delayPromised(1000)
    const snapshotData = await takeSnapshot('.chart-preview', {
      message: '图表渲染不正常，无法截图',
      scaleBase: 168 * 2
    })
    setEditorStatePatch({
      pendingChart: { ...pendingChart, thumb: snapshotData } as ComponentDefine,
      visiblePopoverKey: 'saving'
    })
  }
  const onClickSaveAs = () => {
    if (key === 'new') {
      setEditorStatePatch({ visiblePopoverKey: '' })
    } else {
      onSave(true)
    }
  }
  const onSaveConfirmVisibleChange = visible => {
    if (!visible && visiblePopoverKey === 'saving') {
      setEditorStatePatch({ visiblePopoverKey: '' })
    }
  }
  return (
    <div className='nav-bar' style={isNewBi ? { top: 50 } : {}}>
      <div className='nav-bar-left'>
        <ArrowLeftOutlined title='退出' onClick={() => history.push(`/charts/gallery?${markNewBi}`)} />
        <span className='ml-4'>当前编辑：{pendingChart?.title || '未命名'}</span>
        {pendingChart?.updatedAt && (
          <span className='updated-at'>上次更新于：{dayjs(pendingChart?.updatedAt).format('YYYY-MM-DD HH:mm:ss')}</span>
        )}
      </div>

      <div className='nav-bar-center' />
      <div className='nav-bar-right'>
        <Input.Group compact className='!w-[250px] !mr-4 !inline-block'>
          <Input
            className='!w-3/5'
            size='small'
            value={pendingChart?.title}
            onChange={onNameChange}
            placeholder='请为图表命名'
          />
          <Input
            className='!w-2/5'
            size='small'
            defaultValue=''
            prefix='@'
            value={pendingChart?.version}
            onChange={onVersionChange}
          />
        </Input.Group>
        <PopConfirm
          open={_.startsWith(visiblePopoverKey, 'saving')}
          title={`确认保存为 ${pendingChart?.title} ？`}
          okText={visiblePopoverKey === 'saving-snapshot' ? '正在生成封面…' : '保存'}
          okButtonProps={{ loading: _.startsWith(visiblePopoverKey, 'saving-') }}
          cancelButtonProps={{ loading: _.startsWith(visiblePopoverKey, 'saving-') }}
          cancelText={key === 'new' ? '取消' : '另存为'}
          onConfirm={() => onSave()}
          onCancel={onClickSaveAs}
          onOpenChange={onSaveConfirmVisibleChange}
          overlayClassName='[&_.ant-popover-buttons]:whitespace-nowrap'
        >
          <Button className='mr-2' size='small' type='primary' onClick={onClickSave}>
            保存
          </Button>
        </PopConfirm>
      </div>
    </div>
  )
}
