import { DeleteOutlined, Pie<PERSON>hartOutlined } from '@ant-design/icons'
import { Link } from '@umijs/max'
import { useMemoizedFn } from 'ahooks'
import { Col, Dropdown, Popconfirm, Row, Typography } from 'antd'
import cn from 'classnames'
import isEqual from 'fast-deep-equal'
import _ from 'lodash'
import React, { memo } from 'react'

import Icon from '@/components/icons/iconfont-icon'
import Rect from '@/components/rectangle'
import { useNewBi } from '@/hooks/use-new-bi'

const { Text } = Typography

const DEVICE_ICON_MAP = {
  pc: <Icon name='电脑' />,
  screen: <Icon name='大屏' />,
  mobile: <Icon name='移动端' />
}

export interface ChartsGridProps {
  charts: {
    id: string
    title: string
    versionList: any[]
    [key: string]: any
  }[]
  visiblePopoverKey: string
  onVisiblePopoverKeyChange: (value: ((prevState: string) => string) | string) => void
  onDeleteItem: (key: string) => any
}

/** 优化，单个项 */
function ChartItem(props: any) {
  const { data, onDeleteItem, visiblePopoverKey, onVisibleChange } = props
  const { id, thumb, key, versionList = [], title, device } = data

  const { isNewBi, markNewBi } = useNewBi()

  return (
    <Col
      span={4}
      className={cn('mb-4 relative', {
        'charts-list-item': true,
        group: visiblePopoverKey !== `confirm-delete:${id}`
      })}
    >
      <div>
        <Link to={`/charts/gallery/${key}?${markNewBi}`} target={isNewBi ? '' : '_blank'}>
          <Rect aspectRatio={1920 / 1080}>
            {thumb ? (
              <img src={thumb} alt='缩略图' className='w-full h-full rounded object-contain' />
            ) : (
              <div className='shadow-lg rounded w-full h-full'>
                <PieChartOutlined className='center-of-relative text-[100px] text-gray-400' />
              </div>
            )}
          </Rect>
          <Text className='chart-item-link-text' ellipsis>
            <span>
              {DEVICE_ICON_MAP[device]}
              <span className='ml-1'>{title}</span>
            </span>
          </Text>
        </Link>

        {versionList?.length > 1 && (
          <Dropdown
            trigger={['click']}
            overlay={
              <div className='charts-list-item-version-list-overlay'>
                {_.sortBy(versionList, 'versionCode').map(i => (
                  <Link key={i.key} className='version-list-item' to={`/charts/gallery/${i.key}`} target='_blank'>
                    {i.title}@{i.versionCode}
                  </Link>
                ))}
              </div>
            }
          >
            <div className='version-list'>有 {versionList.length} 个版本</div>
          </Dropdown>
        )}

        <Popconfirm
          title='确认删除此自定义图表？'
          onConfirm={() => onDeleteItem(key)}
          onOpenChange={visible => onVisibleChange(visible ? `confirm-delete:${id}` : '')}
        >
          <DeleteOutlined
            className={cn('text-danger-700 absolute right-0 top-0 mt-6 mr-6', {
              'opacity-0 group-hover:opacity-100': visiblePopoverKey !== `confirm-delete:${id}`
            })}
          />
        </Popconfirm>
      </div>
    </Col>
  )
}

const ChartItemMemo = memo(ChartItem, isEqual)
/**
 * 用流式布局渲染图表
 */
export default function ChartsGrid(props: ChartsGridProps) {
  const { charts, onDeleteItem, onVisiblePopoverKeyChange, visiblePopoverKey } = props

  const $onDeleteItem = useMemoizedFn(onDeleteItem)
  const $onVisibleChange = useMemoizedFn(onVisiblePopoverKeyChange)

  return (
    <Row gutter={20}>
      {_.map(charts, data => (
        <ChartItemMemo
          key={data.id}
          data={data}
          onDeleteItem={$onDeleteItem}
          visiblePopoverKey={visiblePopoverKey}
          onVisibleChange={$onVisibleChange}
        />
      ))}
    </Row>
  )
}
