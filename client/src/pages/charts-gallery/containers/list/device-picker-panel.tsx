import { SearchOutlined } from '@ant-design/icons'
import cn from 'classnames'
import isEqual from 'fast-deep-equal'
import _ from 'lodash'
import React, { memo } from 'react'

import DebounceInput from '@/components/debounce-input'
import { DEVICE_TYPE_MAP } from '@/consts/screen'

export interface ChartDevicePickerPanelProps {
  device: string | null
  onDeviceChange: (val: string | null) => any
  keyword: string
  onSearch: (val: string) => any
}

/**
 * 设备分类
 */
function DevicePickerPanel(props: ChartDevicePickerPanelProps) {
  const { device, onDeviceChange, keyword, onSearch } = props

  const list = [
    { key: 'ALL', title: '全部', value: -1 },
    { key: null, title: '未设置', value: -2 },
    ..._.values(DEVICE_TYPE_MAP)
  ]
  return (
    <div className='group-list'>
      <h3>设备类型：</h3>
      {list.map(item => (
        <span
          className={cn('group-list-item', {
            'active text-white': device === item.key
          })}
          key={item.key}
          onClick={e => {
            e.stopPropagation()
            onDeviceChange(device === item.key ? '' : item.key)
          }}
        >
          {item.title}
        </span>
      ))}

      <div className='search-panel'>
        <DebounceInput
          placeholder='搜索图表'
          allowClear
          suffix={<SearchOutlined />}
          value={keyword}
          onChange={e => onSearch(e.target.value || '')}
        />
      </div>
    </div>
  )
}

export default memo(DevicePickerPanel, isEqual)
