import { DownOutlined } from '@ant-design/icons'
import { Dropdown } from 'antd'
import cn from 'classnames'
import isEqual from 'fast-deep-equal'
import _ from 'lodash'
import React, { memo } from 'react'

import { ELEMENT_GROUP_MAP } from '@/consts/screen'
import { scrollTo } from '@/utils/dom'

export interface GroupPickerPanelProps {
  chartGroups: any[]
  selectedGroup: string | null
  onSelectGroup: (value: ((prevState: string | null) => string | null) | string | null) => any
}

/**
 * 分组选择
 */
function GroupPickerPanel(props: GroupPickerPanelProps) {
  const { chartGroups, onSelectGroup, selectedGroup } = props

  const MAX_COUNT = 12 // 最大显示个数

  const renderItem = (groupId, className) => (
    <div
      className={cn(className, { 'active': selectedGroup === groupId })}
      key={groupId}
      onClick={() => {
        scrollTo('.charts-gallery-list-content', `#${groupId}`, -12)
        onSelectGroup(groupId)
      }}
    >
      {ELEMENT_GROUP_MAP[groupId]?.title || groupId || '其他'}
    </div>
  )

  const renderOverlay = () => (
    <div className='charts-gallery-list-group-overlay'>
      {_.slice(chartGroups, MAX_COUNT).map(groupId => renderItem(groupId, 'group-list-item'))}
    </div>
  )

  return (
    <div className='group-list group-bar'>
      <h3>分类：</h3>
      {/* {_.isEmpty(chartGroups) ? <div className='p-8 text-xl text-gray-400 text-center'>暂无内容</div> : undefined} */}
      {_.slice(chartGroups, 0, MAX_COUNT).map(groupId => renderItem(groupId, 'group-list-item'))}

      {chartGroups.length === 0 && <span>无</span>}

      {chartGroups.length > MAX_COUNT && (
        <Dropdown placement='bottomLeft' trigger={['click']} overlay={renderOverlay()}>
          <div className='more-group group-list-item'>
            <span>更多</span>
            <DownOutlined />
          </div>
        </Dropdown>
      )}
    </div>
  )
}

export default memo(GroupPickerPanel, isEqual)
