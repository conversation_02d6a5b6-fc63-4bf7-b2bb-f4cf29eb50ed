.charts-gallery-list {
  background-color: #fff;
  height: calc(100vh - 50px);
  overflow: hidden;

  .charts-gallery-toolbar {
    box-shadow: 0px 2px 3px rgba(1, 1, 1, 0.05);
    border-radius: 4px;
    position: relative;
    left: 0;
    top: 0;
    z-index: 22;
    background-color: #fff;

    .actions {
      position: absolute;
      top: 10px;
      right: 5px;
    }
  }

  .toolbox {
    position: absolute;
    min-height: 20px;
    background-color: #fff;
    right: 0;
    left: 0;
    top: 8px;
    padding-top: 75px;
    bottom: 10px;
  }

  // 分组列表
  .group-list {
    height: 40px;
    display: flex;
    align-items: center;
    padding: 0 16px;

    > h3 {
      margin: 0;
      font-weight: 499;
      margin-right: -4px;
      font-size: 16px;
    }

    .group-list-item {
      border-radius: 3px;
      font-size: 14px;
      margin: 0 10px;
      padding: 1px 6px;
      cursor: pointer;

      &:hover {
        background-color: var(--tint-color-80);
        color: #333;
      }

      &.active {
        background-color: var(--primary-color);
        color: #fff;
      }
    }

    .more-group {
      background-color: var(--tint-color-90);
    }
  }

  .charts-gallery-list-content {
    padding-top: 20px;
    overflow-x: hidden;
    overflow-y: auto;
    max-height: calc(100vh - 110px);
    background-color: @app-bg-color;
    padding: 12px 20px;
  }

  .fix-charts-gallery-list-content {
    max-height: calc(100vh - 200px);
  }

  .fix-charts-gallery-list-content-new-bi {
    max-height: calc(100vh - 150px);
  }

  .search-panel {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);

    .ant-input-group-addon {
      background-color: #fff;
    }
  }

  .charts-gallery-list-item-title {
    border-bottom: 1px solid #f1f1f1;
    padding-bottom: 8px;
    margin-bottom: 20px;
    font-size: 20px;
    color: #444;
  }

  .charts-list-item {
    position: relative;
    padding: 4px 12px;

    > div {
      border: 1px solid #f1f1f1;
      border-radius: 4px;
      background-color: #fff;

      &:hover {
        box-shadow: 1px 0 6px rgba(#111, 0.08);
      }
    }

    .chart-item-link-text {
      margin: 0;
      padding: 6px;
      text-align: center;
      font-size: 15px;
      .anticon {
        opacity: 0.65;
      }
    }

    &:hover {
      .chart-item-link-text {
        cursor: pointer;
        color: var(--primary-color);
      }
    }

    img {
      border-bottom: 1px solid #f1f1f1;
    }

    .version-list {
      position: absolute;
      top: 5px;
      right: 10px;
      z-index: 1;
      font-size: 13px;
      background-color: var(--tint-color-30);
      padding: 1px 3px;
      border-radius: 3px;
      color: #fff;
      cursor: pointer;
    }
  }
}

.charts-gallery-list-group-overlay {
  background-color: #fff;

  width: 120px;
  border-radius: 3px;
  box-shadow: 0 0 12px rgba(#111, 0.12);
  padding: 4px 0;
  max-height: 360px;
  overflow-y: auto;

  .group-list-item {
    padding: 4px 12px;
    cursor: pointer;
    &:hover {
      background-color: var(--tint-color-80);
      color: #333;
    }
  }
}

.charts-list-item-version-list-overlay {
  background-color: #fff;
  width: 150px;
  max-height: 260px;
  overflow-y: auto;

  border-radius: 3px;
  padding: 5px 0;
  box-shadow: 1px 3px 6px rgba(#111, 0.1);

  .version-list-item {
    padding: 2px 5px;
    border-bottom: 1px solid #f1f1f1;
    font-size: 13px;
    display: block;
    color: #333;

    &:last-of-type {
      border: none;
    }
    &:hover {
      cursor: pointer;
      color: var(--primary-color);
    }
  }
}
