import './index.less'

import { <PERSON> } from '@umijs/max'
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd'
import cn from 'classnames'
import type { Dictionary } from 'lodash'
import _ from 'lodash'
import React, { useEffect, useMemo, useState } from 'react'

import Div from '@/components/div-wrap'
import Markdown from '@/components/markdown-view'
// import { ComponentDefine } from '@/types/editor-core/component'
import { ELEMENT_GROUP_MAP } from '@/consts/screen'
import { useNewBi } from '@/hooks/use-new-bi'
import { useTitleFromRouter } from '@/hooks/use-title'
import ExportButton from '@/pages/admins/import-data/comp-define/button'
import {
  useCommit as useChartGalleryCommit,
  useModelState as useChartGalleryModelState
} from '@/stores/models/charts-gallery'

import ChartsGrid from './charts-grid'
import DevicePickerPanel from './device-picker-panel'
import GroupPickerPanel from './group-picker-panel'
import ReadmeContent from './readme.md'

/**
 * 自定义图表列表展示页
 * @constructor
 */
export default function ChartsGallery() {
  useTitleFromRouter()
  const chartsGalleryCommit = useChartGalleryCommit()
  const customChartsDefineMap = useChartGalleryModelState(s => s.customChartDefineMap)
  const loading = useChartGalleryModelState(s => s.loading)

  const [selectedGroup, setSelectedGroup] = useState<string | null>(null)
  const [device, setDevice] = useState<string | null>('ALL')
  const [keyword, setKeyword] = useState('')

  const { isNewBi } = useNewBi()

  const chartTypeGroup: Dictionary<any[]> = useMemo(() => {
    const kw = (keyword || '').toLocaleLowerCase()
    const list = _.values(customChartsDefineMap.entities).filter(i => {
      const title = (i.title || '').toLocaleLowerCase()
      if (device === 'ALL' || i.device === device) {
        if (keyword && title.indexOf(kw) === -1) return false
        return true
      }
      return false
    })
    return _.groupBy(list, 'group')
  }, [customChartsDefineMap, device, keyword])

  const [visiblePopoverKey, setVisiblePopoverKey] = useState('')

  useEffect(() => {
    const firstGroup = _.keys(chartTypeGroup)[0]
    if (!selectedGroup && firstGroup) setSelectedGroup(firstGroup)
  }, [chartTypeGroup, selectedGroup])

  return (
    <Div className='h-full relative charts-gallery-list'>

      <div className='charts-gallery-toolbar'>
        <DevicePickerPanel device={device} onDeviceChange={setDevice} keyword={keyword} onSearch={setKeyword} />

        <GroupPickerPanel
          chartGroups={_.keys(chartTypeGroup)}
          selectedGroup={selectedGroup}
          onSelectGroup={setSelectedGroup}
        />

        <div className='actions'>
          <ExportButton type='define' className='mr-6' />
          <Button size='small' onClick={() => setVisiblePopoverKey('help')} className='mr-4'>
            使用说明
          </Button>
          <Link to='/charts/gallery/new' className='mr-2'>
            <Button size='small' type='primary'>
              添加图表
            </Button>
          </Link>
        </div>
      </div>

      <div className='absolute toolbox'>
        <Spin spinning={loading}>
          <div
            className={cn('h-full relative p-8', {
              'charts-gallery-list-content': true,
              'fix-charts-gallery-list-content': window.__POWERED_BY_QIANKUN__,
              'fix-charts-gallery-list-content-new-bi': isNewBi
            })}
          >
            {_.isEmpty(chartTypeGroup) && !loading && (
              <div className='p-8 text text-gray-400 text-center'>
                暂无内容，请先前往
                <a href={`${window.routerBase}/charts/gallery/new`}>添加</a>
              </div>
            )}

            {_.map(chartTypeGroup, (charts, g) => (
              <React.Fragment key={g}>
                <div id={g} className='charts-gallery-list-item-title'>
                  {ELEMENT_GROUP_MAP[g]?.title || g || '其他'}
                </div>

                <ChartsGrid
                  charts={charts}
                  onDeleteItem={key => chartsGalleryCommit('deleteInDB', key)}
                  onVisiblePopoverKeyChange={setVisiblePopoverKey}
                  visiblePopoverKey={visiblePopoverKey}
                />
                {/* {renderChartsGrid(charts, visiblePopoverKey, setVisiblePopoverKey, chartsGalleryCommit)} */}
              </React.Fragment>
            ))}
          </div>
        </Spin>
      </div>

      <Drawer
        open={visiblePopoverKey === 'help'}
        width='40%'
        onClose={() => setVisiblePopoverKey('')}
        getContainer={() => document.getElementById('abi-app-modal') || document.body}
      >
        <Markdown>{ReadmeContent}</Markdown>
      </Drawer>
    </Div>
  )
}
