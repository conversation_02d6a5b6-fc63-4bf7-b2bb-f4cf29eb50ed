# 自定义图表简单使用说明

### 使用须知

1. 使用者应该懂得如何编写 js 代码
2. 图表容器只会取一个 observable notebook 变量进行渲染
3. 支持两种渲染模式，dom 或 react 模式，dom 模式需要的变量类型是 dom，react 则是函数组件
4. observable 里写 jsx 语法需要用到 [htm](https://www.npmjs.com/package/htm) 库，模版已经内置
5. 暂不支持完整的 observable notebook 功能，例如 `mutable` 关键字，需要的话请提需求
6. 按照模版里提供的说明，一步步操作即可，编辑前建议阅读[内嵌组件编写规范](https://observablehq.com/d/5c6afe5939d3046f)

### 准备工作

1. 注册一个 [Observablehq](https://observablehq.com/) 账号
2. 查看 [Observablehq 用户手册](https://observablehq.com/@observablehq/user-manual) 了解基础使用方法

### 从 Dom 模版创建

1. [echarts](https://observablehq.com/d/94ac81ea79285cf9)
2. [Antv 地图](https://observablehq.com/d/af211c2e631d1858)
3. [Antv/f2](https://observablehq.com/d/2348ebe5f4cb9e3d)

### 从 React 模版创建

1. [Antv 表格](https://observablehq.com/d/ae6f319759cf7a67)
2. [统计数值](https://observablehq.com/d/7f4a37f37806ffa6)
3. [Antd 按钮](https://observablehq.com/d/1304ccdba3af7dee)
4. [Antd 日期选择](https://observablehq.com/d/1368a19c034ccb37)

## 如何快捷使用线上模版

1. 点击右上角的 `...`，`Export`，`Embed cells`，弹窗里选择 `Runtime with Javascript`
2. 找到下方 `import define from "..."`，复制 双引号内的 url
3. 在我们的系统进入`自定义图表`，`添加图表`，`更换底图`；
4. 在 `observable notebook js 代码地址` 里粘贴刚刚的 url
5. 在`字段配置` Tab 切换到合适的`预设`，点击`运行`预览
6. 效果无误后，修改`图表名称`，然后点击`保存`

## 如何离线使用模版

1. 从浏览器地址栏复制图表 id，例如：`observablehq.com/d/94ac81ea79285cf9` 的 id 为 `d/94ac81ea79285cf9`
2. 打开 [Notebook 代码打包导出工具](https://observablehq.com/d/62788f0850769dd3)
3. 在 Notebook 输入框里填入 id
4. 点击`下载压缩包`
5. 在我们的系统进入`自定义图表`，`添加图表`，`更换底图`；
6. 在 `observable notebook js 代码地址` 点击右方的上传按钮，上传第 1 步下载的代码压缩包
7. 在`字段配置` Tab 切换到合适的`预设`，点击`运行`预览
8. 效果无误后，修改`图表名称`，然后点击`保存`

## 常用资源

1. [Observablehq 用户手册](https://observablehq.com/@observablehq/user-manual)
2. [Observablehq 官方文档](https://observablehq.com/documentation)
3. [Echarts 官方示例](https://echarts.apache.org/examples/zh/index.html)
4. [Ant Design Charts 官方示例](https://charts.ant.design/zh/examples/gallery)
5. [d3 图表示例](https://observablehq.com/@d3/gallery)
6. [htm jsx 介绍](https://www.npmjs.com/package/htm)
7. [abi 内嵌组件编写规范](https://observablehq.com/d/5c6afe5939d3046f)
