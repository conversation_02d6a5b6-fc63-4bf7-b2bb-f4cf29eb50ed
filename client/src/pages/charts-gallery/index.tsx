import { fastMemo } from '@sugo/design/functions'
import { message } from 'antd'
import _ from 'lodash'
import React, { useEffect } from 'react'

import ChartsGalleryList from '@/pages/charts-gallery/containers/list'
import { useCommit, useModelState } from '@/stores/models/charts-gallery'

export const ChartsGallery = fastMemo(() => {
  // 加载自定义图表
  const commit = useCommit()
  const customCharts = useModelState(s => s.customChartDefineMap)

  useEffect(() => {
    message.warn({ content: '过时功能，不再维护，请注意使用！' })

    if (_.isEmpty(customCharts.keys)) {
      commit('loadMore')
    }
  }, [])

  return <ChartsGalleryList />
})

export default ChartsGallery
