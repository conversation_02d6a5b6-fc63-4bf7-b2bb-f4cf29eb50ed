export const FLOW_ROUND_NODE_COLOR = {
  FIELDS: '#a16928',
  TABLE: '#7856fd',
  SUBTABLE: '#9a17a5'
}
export const FLOW_ROUND_NODE_TYPE = {
  FIELDS: 'FIELDS',
  TABLE: 'TABLE',
  SUBTABLE: 'SUBTABLE'
}
export const FLOW_CONNECTION_COLOR = {
  select: '#000',
  default: '#ababab'
}
export const FLOW_TABLE_TYPE = {
  indices: 'indices',
  dataset: 'dataset',
  hive: 'hive',
  default: 'default',
  guass: 'guass',
  mysql: 'mysql',
  postgre: 'postgre'
}
export const FLOW_TABLE_TYPE_MAP = {
  [FLOW_TABLE_TYPE.hive]: { style: 'table-title-table', name: '数据表' },
  [FLOW_TABLE_TYPE.default]: { style: 'table-title-table', name: '数据表' },
  [FLOW_TABLE_TYPE.guass]: { style: 'table-title-table', name: '数据表' },
  [FLOW_TABLE_TYPE.mysql]: { style: 'table-title-table', name: '数据表' },
  [FLOW_TABLE_TYPE.postgre]: { style: 'table-title-table', name: '数据表' },
  [FLOW_TABLE_TYPE.indices]: { style: 'table-title-indices', name: '指标' },
  [FLOW_TABLE_TYPE.dataset]: { style: 'table-title-dataset', name: '数据视图' }
}

export const FLOW_TABLE_TYPE_ICON_MAP = {
  [FLOW_TABLE_TYPE.indices]: 'table-title-icon-indices',
  [FLOW_TABLE_TYPE.dataset]: 'table-title-icon-dataset',
  [FLOW_TABLE_TYPE.hive]: 'table-title-icon-hive',
  [FLOW_TABLE_TYPE.default]: 'table-title-icon-database',
  [FLOW_TABLE_TYPE.guass]: 'table-title-icon-guass',
  [FLOW_TABLE_TYPE.mysql]: 'table-title-icon-mysql',
  [FLOW_TABLE_TYPE.postgre]: 'table-title-icon-hive'
}
