.flow-design {
  .table-result {
    border-color: #d26b58 !important;
    .table-title {
      background-color: #d26b58 !important;
    }
  }
  .table-select {
    border-color: #0fb264 !important;
    .table-title {
      background-color: #0fb264 !important;
    }
  }
  .field-hover {
    background-color: #faebd7;
  }
  .field-select {
    background-color: #fca12b;
  }
  .table-panel {
    width: 200px;
    border-radius: 5px 0px 0px;
    border: #91c051 1px solid;
    // box-shadow: 0px 0px 4px 1px #666666;
    text-align: center;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    align-content: center;
    justify-content: center;
    background-color: #fff;
    cursor: pointer;
    .table-title {
      color: #fff;
      font-weight: 600;
      height: 25px;
      line-height: 25px;
      background-color: #91c051;
    }
    .field {
      height: 20px;
      line-height: 20px;
      flex: 1;
      text-align: left;
    }
    .table-input,
    .table-output {
      width: 10px;
      margin-top: 2px;
    }
    .table-output > div {
      width: 1px;
      height: 1px;
      margin-right: -3px;
    }
    .table-input > div {
      width: 1px;
      height: 1px;
      margin-left: -5px;
    }
  }
  .dataflow {
    border: 0px !important;
  }
  .main-path {
    fill: none;
    stroke-width: 1px !important;
    stroke: #ababab !important;
  }
  .main-path-select {
    stroke: #000 !important;
  }
  .refresh-btn {
    position: absolute;
    right: 10px;
    top: 0px;
  }
}
