import './index.less'

import { Button } from 'antd'
import React,{ useEffect, useRef, useState } from 'react'
import { NodeEditor } from 'rete'

import { IDiagramData } from '../types'
import { createEditor } from './edit-create'
import { EditorRigger } from './editor-rigger'

type Props = {
  indicesNameMap: { [key: string]: string }
  isLoading?: boolean
  data?: IDiagramData
  onRefresh?: () => void
}

export default function DataFlowPanel(props: Props) {
  const { data, onRefresh,  isLoading = false, indicesNameMap } = props
  const rootDomRef = useRef()
  const editorRef = useRef<NodeEditor | null>(null)
  const [chartData, setChartData] = useState<IDiagramData>({})

  useEffect(() => {
    const rootDom = rootDomRef.current
    const { editor } = createEditor(rootDom)
    editorRef.current = editor
  }, [])

  useEffect(() => {
    if (!data) {
      return
    }
    setChartData(data)
  }, [data])

  const handleRefresh = () => {
    editorRef.current?.clear()
    onRefresh?.()
  }

  return (
    <div className='flex-1 relative width-100 h-[100%] flow-design '>
      <EditorRigger editor={editorRef.current} chartState={chartData} indicesNameMap={indicesNameMap} />
      <div
        ref={rootDomRef}
        // style={
        //   readonly
        //     ? {}
        //     : {
        //       backgroundImage: `url(${bggrid})`,
        //       backgroundRepeat: 'repeat',
        //       backgroundPosition: `${pos.x} ${pos.y}`, // 'left top',
        //       backgroundSize
        //     }
        // }
        className='data-flow-panel'
      />
      {/* {!_.isEmpty(data) ? null : (
        <div className='absolute left-0 top-0 mt-12 ml-4 text-red-600 leading-8'>
          <Spin spinning />
          <span className='ml-2'>正在加载…</span>
        </div>
      )} */}

      <div className='refresh-btn'>
        <Button type='primary' className='' loading={isLoading} onClick={handleRefresh}>
          刷新
        </Button>
      </div>
    </div>
  )
}
