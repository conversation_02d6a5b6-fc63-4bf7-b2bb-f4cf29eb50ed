import { FieldNumberOutlined, FieldStringOutlined } from '@ant-design/icons'
import _ from 'lodash'
import React from 'react'
import Rete from 'rete'
import { Control,Node, Socket } from 'rete-react-render-plugin'

export const DATA_FLOW_SOCKET = new Rete.Socket('Dataflow')

class TableComponent<PERSON><PERSON><PERSON> extends Node {
  renderFieldItem(list: any[], type: 'dimension' | 'indices' | '') {
    const { node, bindSocket, bindControl } = this.props
    const { outputs, inputs } = this.state
    const indicesNameMap = node?.data?.indicesNameMap
    return list.map((item, idx) => {
      const output = _.find(outputs, p => p.key === `out_${item.fieldName}`)
      const input = _.find(inputs, p => p.key === `in_${item.fieldName}`)
      const alias = _.get(indicesNameMap, item.fieldName)
      let icon: any = null
      if (type === 'dimension') {
        icon = <FieldStringOutlined className='mg1r' />
      } else if (type === 'indices') {
        icon = <FieldNumberOutlined className='mg1r' />
      }
      return (
        <div
          id={`${node?.data?.name}_${item.fieldName}`}
          key={`${node?.data?.name}_${item.fieldName}`}
          className={`flex ${item.isSelect ? 'field-select' : ''} ${item.isHover ? 'field-hover' : ''}`}
        >
          <div className='table-input' key={input.key}>
            <Socket type='input' socket={input.socket} io={input} innerRef={bindSocket} />
            {input.showControl() && (
              <Control className='input-control' control={input.control} innerRef={bindControl} />
            )}
          </div>
          <div
            className='field'
            onFocus={()=>0}
            onClick={() => node?.data?.onNodeItemSelect({ nodeName: node?.data?.name, itemName: item.fieldName })}
            onMouseOver={() => node?.data?.onNodeItemHover({ nodeName: node?.data?.name, itemName: item.fieldName })}
          >
            {icon} {item.fieldName}
            {alias ? `[${alias}]` : ''}
          </div>
          <div className='table-output' key={`out_${item.fieldName}`}>
            <Socket type='output' socket={output.socket} io={output} innerRef={bindSocket} />
          </div>
        </div>
      )
    })
  }

  render() {
    const { node } = this.props
    const { selected } = this.state
    const fields = node?.data?.fields || []
    if (node?.data?.name === 'result') {
      const dimension = fields.filter(p => p.type === 'dimension')
      const indices = fields.filter(p => p.type === 'indices')
      return (
        <div className={`${selected ? 'table-select' : ''} table-result table-panel`}>
          <div className='table-title'>{node?.data?.name === 'result' ? '结果表': node?.data?.name}</div>
          {dimension?.length ? (
            <>
              {this.renderFieldItem(dimension, 'dimension')}
            </>
          ) : null}
          {indices?.length ? (
            <>
              {this.renderFieldItem(indices, 'indices')}
            </>
          ) : null}
        </div>
      )
    }

    return (
      <div
        className={`${selected ? 'table-select' : ''} ${
          node?.data?.name === 'result' ? 'table-result' : ''
        } table-panel`}
      >
        <div className='table-title'>{node?.data?.name}</div>
        {this.renderFieldItem(fields, '')}
      </div>
    )
  }
}

class TableComponent extends Rete.Component {
  constructor() {
    super('Table')
    this.data.component = TableComponentRenderer
  }

  builder(node) {
    const filelds = node?.data?.fields
    filelds.forEach(p => {
      const inp1 = new Rete.Input(`in_${p.fieldName}`, '', DATA_FLOW_SOCKET, true)
      const out = new Rete.Output(`out_${p.fieldName}`, '', DATA_FLOW_SOCKET, true)
      node.addInput(inp1).addOutput(out)
    })

    return node
  }

  worker(node, inputs, outputs) {
    console.log('work') 
  }
}
export const TableComponentInst = new TableComponent()
