
export function getAngle({ x: x1, y: y1 }, { x: x2, y: y2 }) {
  const dx = x1 - x2
  const dy = y1 - y2

  return (180 * Math.atan2(dy, dx)) / Math.PI
}

export function getTransformAlong(path, offset, delta = 1, needRotate = true) {
  const length = path.getTotalLength() * delta
  const p1 = path.getPointAtLength(length + offset)
  const p2 = path.getPointAtLength(length)
  const angle = 180 + (needRotate ? getAngle(p1, p2) : 0)

  return `translate(${p1.x}, ${p1.y}) rotate(${angle})`
}

export function getTransformAlong2(path, offset, delta = 1, needRotate = true) {
  const length = path.getTotalLength() * delta
  const p1 = path.getPointAtLength(length)
  const p2 = path.getPointAtLength(0)
  const dx = (p1.x - p2.x) / 2
  const dy = (p1.y - p2.y) / 2
  const angle = 180 + (needRotate ? getAngle(p1, p2) : 0)

  return `translate(${p1.x - dx}, ${p1.y - dy + 5}) rotate(${angle})`
}
