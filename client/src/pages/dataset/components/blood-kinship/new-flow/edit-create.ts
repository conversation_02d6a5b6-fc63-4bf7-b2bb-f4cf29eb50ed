import _ from 'lodash'
import Rete from 'rete'
import ConnectionPlugin from 'rete-connection-plugin'
import ReactRenderPlugin from 'rete-react-render-plugin'
import AutoArrangePlugin from './auto-arrange/index'
import { TableComponentInst } from './node/table-component'

let isAltKeyDown = false
document.addEventListener('keydown', zEvent => {
  isAltKeyDown = zEvent.altKey
})
document.addEventListener('keyup', zEvent => {
  isAltKeyDown = zEvent.altKey
})

export function createEditor(container) {
  const components = [TableComponentInst]

  const editor = new Rete.NodeEditor('demo@0.1.0', container)
  editor.use(ReactRenderPlugin)
  editor.use(ConnectionPlugin)
  editor.use(AutoArrangePlugin, { margin: { x: 100, y: 50 }, depth: 100 })

  const engine = new Rete.Engine('demo@0.1.0')

  components.forEach(c => {
    editor.register(c)
    engine.register(c)
  })

  editor.on('zoom', ev => {
    if (ev.source === 'dblclick') {
      return false
    }
    if (ev.source === 'wheel') {
      return isAltKeyDown
    }
  })

  editor.on('rendernode', ({ el, node }) => {
    const wrapperCls = `node-${node.id}`
    if (!el.classList.contains(wrapperCls)) {
      el.classList.add(wrapperCls)
    }
  })

  editor.view.resize()
  editor.trigger('process')
  return { editor, engine }
}
