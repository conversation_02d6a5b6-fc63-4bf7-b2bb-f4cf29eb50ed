// 利用 react 的虚拟 dom 更新机制来对 editor 进行操纵
import _ from 'lodash'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { NodeEditor } from 'rete'

import { FLOW_CONNECTION_COLOR } from '../const'
import { IDiagramData } from '../types'
import { VNode } from './node'
import { getTransformAlong, getTransformAlong2, replace<PERSON>eyCharacter } from './utils'

export function EditorRigger(props: {
  editor: NodeEditor | null | undefined
  chartState: IDiagramData
  indicesNameMap: { [key: string]: string }
  setLoadding: (v: boolean) => void
}) {
  const { editor, chartState, indicesNameMap, setLoadding } = props
  const { inputsDict, sourceRelationDict, targetRelationDict } = useMemo(() => ({
      inputsDict: _.groupBy(chartState?.links, l => l.target),
      sourceRelationDict: _.groupBy(chartState?.links, l => `${l.source}_${l.sourceKey}`),
      targetRelationDict: _.groupBy(chartState?.links, l => `${l.target}_${l.targetKey}`)
    }), [chartState?.links])
  const [updateCnt, triggerUpdate] = useState<number>(0)
  const lastSelectNodeItemRef = useRef<{ nodeName: string; itemName: string }[]>([])
  // const lastHoverNodeItemRef = useRef<{ nodeName: string; itemName: string }[]>([])

  useEffect(() => {
    // setLoadding(true)
    editor?.clear()
    // 当 socket 渲染后，再强制触发渲染，避免线不显示
    const onRenderSocket = _.debounce(() => triggerUpdate(prev => prev + 1), 150)
    editor?.on('rendersocket', onRenderSocket)
    editor?.on('renderconnection', onRenderConnection)
    editor?.on('updateconnection', onUpdateConnection)
    editor?.on('nodecreated', onNodeCreated)

    return () => {
      if (!editor) {
        return
      }
      editor.events.rendersocket = _.filter(editor.events.rendersocket, fn => fn !== onRenderSocket)
      editor.events.renderconnection = _.filter(editor.events.renderconnection, fn => fn !== onRenderConnection)
      editor.events.updateconnection = _.filter(editor.events.updateconnection, fn => fn !== onUpdateConnection)
      editor.events.nodecreate = _.filter(editor.events.nodecreate, fn => fn !== onNodeCreated)
    }
  }, [chartState?.links, chartState?.tables])

  const onNodeCreated = useCallback(() => {
    if (editor?.nodes.length === chartState?.tables?.length) {
      setTimeout(() => {
        setLoadding(false)
        editor?.arrange(_.last(editor?.nodes))
      }, 500)
    }
  }, [chartState?.tables])

  const onRenderConnection = ({ el, connection }) => {
    const path = el.querySelector('path')
    const key = replaceKeyCharacter(`${connection?.data?.source}_${connection?.data?.sourceKey}`)
    path.setAttribute('stroke', FLOW_CONNECTION_COLOR.default)
    path.setAttribute('id', `link_${key}`)
    const marker = document.createElementNS('http://www.w3.org/2000/svg', 'path')
    el.querySelector('svg').appendChild(marker)
    marker.setAttribute('id', `marker_${key}`)
    marker.classList.add('marker')
    marker.setAttribute('fill', FLOW_CONNECTION_COLOR.default)
    marker.setAttribute('d', 'M24,-3 L24,3 L30,0 z')
    marker.setAttribute('transform', getTransformAlong(path, -25))
    if (connection?.data?.title) {
      const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle')
      el.querySelector('svg').appendChild(circle)
      circle.setAttribute('id', `circle_${key}`)
      circle.classList.add('circle')
      circle.setAttribute('fill', FLOW_CONNECTION_COLOR.default)
      circle.setAttribute('cx', '5')
      circle.setAttribute('cy', '5')
      circle.setAttribute('r', '4')
      circle.setAttribute('transform', getTransformAlong2(path, 0))
      const title = document.createElementNS('http://www.w3.org/2000/svg', 'title')
      title.innerHTML = connection?.data?.title
      circle.appendChild(title)
    }
  }

  const onUpdateConnection = ({ el }) => {
    const [path, marker] = el.querySelectorAll('path')
    const [circle] = el.querySelectorAll('circle')
    marker.setAttribute('transform', getTransformAlong(path, -25))
    circle?.setAttribute('transform', getTransformAlong2(path, 0))
  }

  useEffect(() => {
    if (!editor) {
      return
    }
    // 同步位置
    const { transform, scale } = chartState
    const area = editor.view.area
    if (area.transform.x !== transform?.x || area.transform.y !== transform.y || area.transform.k !== scale) {
      area.transform.x = transform?.x ?? 0
      area.transform.y = transform?.y ?? 150
      area.zoom(scale || 1)
      area.update()
    }
  }, [editor, chartState.transform?.x, chartState.transform?.y, chartState.scale])

  const getRelationNode = (params: { nodeName: string; itemName: string }) => {
    const value: { nodeName: string; itemName: string }[] = []
    const getInRelationItem = (nodeName: string, itemName: string) => {
      value.push({ nodeName, itemName })
      const sources = _.get(sourceRelationDict, [`${nodeName}_out_${itemName}`])
      if (sources?.length) {
        sources.forEach(p => {
          getInRelationItem(p.target, p.targetKey?.replace('in_', ''))
        })
      }
    }
    const getOutRelationItem = (nodeName: string, itemName: string) => {
      value.push({ nodeName, itemName })
      const targets = _.get(targetRelationDict, [`${nodeName}_in_${itemName}`])
      if (targets?.length) {
        targets
          .filter(p => p.target === nodeName)
          .forEach(p => {
            getOutRelationItem(p.source, p.sourceKey?.replace('out_', ''))
          })
      }
    }
    let { nodeName, itemName } = params
    nodeName = replaceKeyCharacter(nodeName)
    itemName = replaceKeyCharacter(itemName)
    getInRelationItem(nodeName, itemName)
    getOutRelationItem(nodeName, itemName)
    return _.unionBy(value, p => p.nodeName + p.itemName)
  }

  const onNodeItemSelect = (params: { nodeName: string; itemName: string }) => {
    updateClass(lastSelectNodeItemRef.current, 'select', 'D')
    const value = getRelationNode(params)
    lastSelectNodeItemRef.current = value
    updateClass(value, 'select', 'A')
  }

  const onNodeItemHover = (params: { nodeName: string; itemName: string }) => {
    // updateClass(
    //   _.differenceBy(lastHoverNodeItemRef.current, lastSelectNodeItemRef.current, p => p.nodeName + p.itemName),
    //   'hover',
    //   'D'
    // )
    // const value = getRelationNode(params)
    // lastHoverNodeItemRef.current = value
    // updateClass(value, 'hover', 'A')
  }

  // const updateClass = (
  //   value: { nodeName: string; itemName: string }[],
  //   handleType: 'select' | 'hover',
  //   type: 'A' | 'D'
  // ) => {
  //   if (type === 'D') {
  //     value.forEach(p => {
  //       const key = replaceKeyCharacter(`${p.nodeName}_out_${p.itemName}`)
  //       document
  //         .querySelector(`#${replaceKeyCharacter(p.nodeName)}_${replaceKeyCharacter(p.itemName)}`)
  //         ?.classList.remove(`field-${handleType}`)
  //       document.querySelector(`#link_${key}`)?.classList.remove('main-path-select')
  //       document.querySelector(`#marker_${key}`)?.setAttribute('fill', FLOW_CONNECTION_COLOR.default)
  //       document.querySelector(`#circle_${key}`)?.setAttribute('fill', FLOW_CONNECTION_COLOR.default)
  //     })
  //     return
  //   }
  //   value.forEach(p => {
  //     const key = replaceKeyCharacter(`${p.nodeName}_out_${p.itemName}`)
  //     document
  //       .querySelector(`#${replaceKeyCharacter(p.nodeName)}_${replaceKeyCharacter(p.itemName)}`)
  //       ?.classList.add(`field-${handleType}`)
  //     document.querySelector(`#link_${key}`)?.classList.add('main-path-select')
  //     document.querySelector(`#marker_${key}`)?.setAttribute('fill', FLOW_CONNECTION_COLOR.select)
  //     document.querySelector(`#circle_${key}`)?.setAttribute('fill', FLOW_CONNECTION_COLOR.select)
  //   })
  // }

  const updateClass = (
    value: { nodeName: string; itemName: string }[],
    handleType: 'select' | 'hover',
    type: 'A' | 'D'
  ) => {
    if (type === 'D') {
      value.forEach(p => {
        const key = replaceKeyCharacter(`${p.nodeName}_out_${p.itemName}`)
        document
          .querySelectorAll(`#${replaceKeyCharacter(p.nodeName)}_${replaceKeyCharacter(p.itemName)}`)
          ?.forEach(p => p.classList.remove(`field-${handleType}`))

        document.querySelectorAll(`#link_${key}`)?.forEach(p => p.classList.remove('main-path-select'))
        document.querySelectorAll(`#marker_${key}`)?.forEach(p => p.setAttribute('fill', FLOW_CONNECTION_COLOR.default))
        document.querySelectorAll(`#circle_${key}`)?.forEach(p => p.setAttribute('fill', FLOW_CONNECTION_COLOR.default))
      })
      return
    }
    value.forEach(p => {
      const key = replaceKeyCharacter(`${p.nodeName}_out_${p.itemName}`)
      document
        .querySelectorAll(`#${replaceKeyCharacter(p.nodeName)}_${replaceKeyCharacter(p.itemName)}`)
        ?.forEach(p => p.classList.add(`field-${handleType}`))
      document.querySelectorAll(`#link_${key}`)?.forEach(p => p.classList.add('main-path-select'))
      document.querySelectorAll(`#marker_${key}`)?.forEach(p => p.setAttribute('fill', FLOW_CONNECTION_COLOR.select))
      document.querySelectorAll(`#circle_${key}`)?.forEach(p => p.setAttribute('fill', FLOW_CONNECTION_COLOR.select))
    })
  }

  useEffect(() => {
    if (!editor) {
      return
    }

    return () => {
      editor.clear()
    }
  }, [editor])

  if (!editor) {
    return null
  }
  return (
    <>
      {_.map(chartState?.tables, (t, i) => (
          <VNode
            key={`${t.name}${t.fields.map(p => p.fieldName).join('')}`}
            editor={editor}
            nodeState={t}
            index={i}
            inputs={inputsDict[t.id]}
            onNodeItemSelect={onNodeItemSelect}
            onNodeItemHover={onNodeItemHover}
            indicesNameMap={t.order === 0 ? indicesNameMap : {}}
          />
        ))}
    </>
  )
}
