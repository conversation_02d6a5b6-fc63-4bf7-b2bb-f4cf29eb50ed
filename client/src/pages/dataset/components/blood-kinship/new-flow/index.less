.flow-design {
  .table-select {
    border-color: #20aeff !important;
  }

  .scale-btn {
    margin-top: 5px;
    display: inline-block;
    vertical-align: top;
    font-size: 16px;
    cursor: pointer;
  }

  .color-999 {
    color: #999;
  }

  .disabled {
    cursor: not-allowed !important;
  }

  .color-666 {
    color: #666;
  }

  .field-hover {
    background-color: #eee;
  }

  .field-select {
    background-color: #eee;
  }

  .mg1l {
    margin-left: 5px;
  }

  .scale-text {
    display: inline-block;
    vertical-align: top;
    margin: 3px 16px 0;
    color: #666;
    width: 40px;
    font-size: 14px;
    text-align: center;
  }

  .table-title-type {
    padding: 2px 8px;
    font-size: 11px;
    font-weight: 800;
    border-radius: 4px;
    line-height: 18px;
  }

  .table-title-table {
    color: #6c5ef3;
    background-color: #e8e5ff;
  }

  .table-title-indices {
    color: #0065ee;
    background-color: #e5f0ff;
  }

  .table-title-dataset {
    color: #20aeff;
    background-color: #e5f6ff;
  }

  .table-title-icon-hive {
    background-image: url('./assets/hive.png');
  }

  .table-title-icon-guass {
    background-image: url('./assets/gaussdb.png');
  }

  .table-title-icon-mysql {
    background-image: url('./assets/mysql.png');
  }

  .table-title-icon-postgre {
    background-image: url('./assets/postgre.png');
  }

  .table-title-icon-indices {
    background-image: url('./assets/indices.png');
  }

  .table-title-icon-dataset {
    background-image: url('./assets/dataset.png');
  }

  .table-title-icon-database {
    background-image: url('./assets/database.png');
  }

  .base-indices {
    font-weight: 800;
    color: #ffffff;
    background-color: #ffac28;
    border-radius: 3px 3px 3px 0px;
    width: 60px;
    line-height: 23px;
    text-align: center;
    height: 23px;
    font-size: 13px;
    position: absolute;
    top: -10px;
    left: -10px;

    & > div {
      height: 1px;
      width: 1px;
      border-top: 3px solid #d2850b;
      border-right: 4px solid #d2850b;
      border-bottom: 3px solid #fff;
      border-left: 6px solid #fff;
    }
  }

  .field-icon {
    width: 30px;
    height: 100%;
    background-repeat: no-repeat;
    background-position: center;
  }

  .field-number {
    background-image: url('./assets/number.png');
  }

  .field-string {
    background-image: url('./assets/string.png');
  }

  .table-panel {
    width: 300px;
    border-radius: 5px 5px;
    border: #e4e4e4 1px solid;
    // box-shadow: 0px 0px 4px 1px #666666;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    align-content: center;
    justify-content: center;
    background-color: #fff;

    .table-title-icon {
      width: 50px;
      background-repeat: no-repeat;
      background-position: center;
    }

    .table-title-text {
      flex: 1;

      .table-title-ellipsis {
        white-space: nowrap;
        max-width: 170px;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      & > div {
        height: 23px;
        line-height: 23px;
        font-weight: 800;
        color: #333333;
      }
    }

    .table-title {
      height: 50px;
      display: flex;
      border-bottom: #e4e4e4 1px solid;
    }

    .table-filed {
      height: 40px;
      line-height: 40px;
    }

    .field {
      flex: 1;
      text-align: left;
      color: #666666;
      font-weight: 800;
      display: flex;

      .text-ellipsis {
        display: inline-block;
        white-space: nowrap;
        max-width: 250px;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .table-input,
    .table-output {
      width: 10px;
      margin-top: 2px;
    }

    .table-output > div {
      width: 1px;
      height: 1px;
      margin-right: -3px;
    }

    .table-input > div {
      width: 1px;
      height: 1px;
      margin-left: -5px;
    }
  }

  .dataflow {
    border: 0px !important;
  }

  .main-path {
    fill: none;
    stroke-width: 1px !important;
    stroke: #ababab !important;
  }

  .main-path-select {
    stroke: #000 !important;
  }

  .refresh-btn {
    position: absolute;
    right: 10px;
    top: 0px;
  }

  .ant-spin-container {
    height: 100%;
    width: 100%;
  }
}
