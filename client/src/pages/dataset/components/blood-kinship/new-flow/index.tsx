import './index.less'

import { MinusCircleFilled, PlusCircleFilled } from '@ant-design/icons'
import { Button, Spin } from 'antd'
import _ from 'lodash'
import * as React from 'react'
import { useEffect, useRef, useState } from 'react'
import { NodeEditor } from 'rete'

import { IDiagramData } from '../types'
import { createEditor } from './edit-create'
import { EditorRigger } from './editor-rigger'

type Props = {
  indicesNameMap: { [key: string]: string }
  isLoading?: boolean
  data?: IDiagramData
  onRefresh?: () => void
  visible?: boolean
}

export default function DataFlowPanel(props: Props) {
  const { data, onRefresh, isLoading = false, indicesNameMap, visible = true } = props
  const rootDomRef = useRef()
  const editorRef = useRef<NodeEditor | null>(null)
  const [chartData, setChartData] = useState<IDiagramData>({})
  const [flowScale, setFlowScale] = useState(1)
  const [loadding, setLoadding] = useState(false)

  useEffect(() => {
    const rootDom = rootDomRef.current
    const { editor } = createEditor(rootDom)
    editorRef.current = editor
  }, [])

  useEffect(() => {
    if (!visible) {
      editorRef.current?.clear()
    }
  }, [visible])

  useEffect(() => {
    if (!data || _.isEmpty(data)) {
      editorRef.current?.clear()
      setChartData({})
    } else {
      setChartData(data)
    }
  }, [data])

  const handleRefresh = () => {
    editorRef.current?.clear()
    onRefresh?.()
  }

  function getScaleCtrl() {
    const scale = flowScale

    return (
      <>
        <MinusCircleFilled
          className={`scale-btn ${scale <= 0.5 ? 'color-999 disabled' : 'color-666'}`}
          onClick={() => setFlowScale(Math.max(0.5, scale - 0.1))}
        />
        <div className='scale-text' onClick={() => setFlowScale(1)}>
          {Math.round(scale * 100)}%
        </div>
        <PlusCircleFilled
          className={`scale-btn ${scale >= 2 ? 'color-999 disabled' : 'color-666'}`}
          onClick={() => setFlowScale(Math.min(2, scale + 0.1))}
        />
      </>
    )
  }

  return (
    <div className='flex-1 relative w-full h-full width-100 height-100 flow-design '>
      <Spin spinning={loadding} wrapperClassName='height-100 width-100 w-full h-full ' className='height-100 width-100 w-full h-full '>
        <EditorRigger
          editor={editorRef.current}
          chartState={{ ...chartData, scale: flowScale }}
          indicesNameMap={indicesNameMap}
          setLoadding={setLoadding}
        />
        <div
          ref={rootDomRef}
          // style={
          //   readonly
          //     ? {}
          //     : {
          //       backgroundImage: `url(${bggrid})`,
          //       backgroundRepeat: 'repeat',
          //       backgroundPosition: `${pos.x} ${pos.y}`, // 'left top',
          //       backgroundSize
          //     }
          // }
          className='data-flow-panel'
        />
      </Spin>
      {/* {!_.isEmpty(data) ? null : (
        <div className='absolute left-0 top-0 mt-12 ml-4 text-red-600 leading-8'>
          <Spin spinning />
          <span className='ml-2'>正在加载…</span>
        </div>
      )} */}

      <div className='refresh-btn'>
        {getScaleCtrl()}
        <Button type='primary' size='small' className='mg1l' loading={isLoading} onClick={handleRefresh}>
          刷新
        </Button>
      </div>
    </div>
  )
}
