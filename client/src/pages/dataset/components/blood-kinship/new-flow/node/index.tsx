import { useAsyncEffect } from 'ahooks'
import _ from 'lodash'
import React, { useEffect, useState } from 'react'
import { Node, NodeEditor } from 'rete'

import { IDiagramLink, IDiagramTableNode } from '../../types'
import { VLink } from './link'
import { TableComponentInst } from './table-component'

export function VNode(props: {
  editor: NodeEditor
  nodeState: IDiagramTableNode
  inputs: IDiagramLink[]
  index: number
  onNodeItemSelect: (item: any) => any
  onNodeItemHover: (item: any) => any
  indicesNameMap: { [key: string]: string }
}) {
  const { editor, nodeState, inputs, index, onNodeItemSelect, indicesNameMap, onNodeItemHover } = props
  const [node, setNode] = useState<Node | null>()

  useAsyncEffect(async () => {
    const targetNode: Node | null = await TableComponentInst.createNode({
      ...nodeState,
      onNodeItemSelect,
      indicesNameMap,
      onNodeItemHover
    })
    targetNode.position = [nodeState.position?.x ?? 0 + 250 * index, nodeState.position?.y || 0]
    editor.addNode(targetNode)
    setNode(targetNode)
  }, [nodeState.name, nodeState.fields])

  useEffect(() => {
    if (!node) {
      return
    }
    Object.assign(node.data, nodeState)
    node.position = [nodeState.position?.x ?? 0 + 250 * index, nodeState.position?.y || 0]
    editor.view.nodes.get(node)?.translate(...node.position)
    node.update()
  }, [nodeState, node])

  useEffect(() => {
    if (!node) {
      return
    }
    Object.assign(node.data, nodeState)
    // const tableSelect = selectNodeItem.find(p => p.nodeName === node?.data?.name)
    // const oldTableSelect = lastSelectNodeItemRef.current.find(p => p.nodeName === node?.data?.name)
    // if (_.isEmpty(oldTableSelect) && _.isEmpty(tableSelect)) {
    //   return
    // }
    // if (!_.isEmpty(oldTableSelect)) {
    //   node.data = immutateUpdate(node.data, 'fields', p => {
    //     return p.map(item => ({ ...item, isSelect: false }))
    //   })
    // }
    // if (!_.isEmpty(tableSelect)) {
    //   node.data = immutateUpdate(node.data, 'fields', p => {
    //     return p.map(item => ({ ...item, isSelect: item.fieldName === tableSelect?.itemName }))
    //   })
    // }
    node.update()
    // lastSelectNodeItemRef.current = selectNodeItem
  }, [nodeState, node])

  // useEffect(() => {
  //   if (!node) {
  //     return
  //   }
  //   const tableSelect = hoverNodeItem.find(p => p.nodeName === node?.data?.name)
  //   const oldTableSelect = lastHoverNodeItemRef.current.find(p => p.nodeName === node?.data?.name)
  //   if (_.isEmpty(oldTableSelect) && _.isEmpty(tableSelect)) {
  //     return
  //   }
  //   if (!_.isEmpty(oldTableSelect)) {
  //     node.data = immutateUpdate(node.data, 'fields', p => {
  //       return p.map(item => ({ ...item, isHover: false }))
  //     })
  //   }
  //   if (!_.isEmpty(tableSelect)) {
  //     node.data = immutateUpdate(node.data, 'fields', p => {
  //       return p.map(item => ({ ...item, isHover: item.fieldName === tableSelect?.itemName }))
  //     })
  //   }
  //   node.update()
  //   lastHoverNodeItemRef.current = hoverNodeItem
  // }, [hoverNodeItem])

  if (!node) {
    return null
  }

  return (
    <>
      {_.map(inputs, inp => {
        const targetNode = node
        const sourceNode = _.find(editor.nodes, n => n.data.id === inp.source)
        const out = sourceNode?.outputs.get(inp.sourceKey)
        const inPort = targetNode?.inputs.get(inp.targetKey)
        const sourceNodeView = sourceNode && editor.view.nodes.get(sourceNode)
        const targetNodeView = targetNode && editor.view.nodes.get(targetNode)
        if (!out || !inPort || !sourceNodeView?.sockets.size || !targetNodeView?.sockets?.size) {
          return
        }
        return (
          <VLink
            key={`${inp.source}_${inp.sourceKey}`}
            editor={editor}
            linkState={inp}
            sourceNode={sourceNode as Node}
            targetNode={targetNode as Node}
            targetKey={inp.targetKey}
            sourceKey={inp.sourceKey}
          />
        )
      })}
    </>
  )
}
