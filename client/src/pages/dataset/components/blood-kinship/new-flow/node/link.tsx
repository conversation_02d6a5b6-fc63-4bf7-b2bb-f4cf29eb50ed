import { Connection, Node, NodeEditor } from 'rete'
import React, { useEffect } from 'react'
import _ from 'lodash'
import { IDiagramLink } from '../../types'

export function VLink(props: {
  editor: NodeEditor
  linkState: IDiagramLink
  targetNode: Node
  sourceNode: Node
  targetKey: string
  sourceKey: string
}) {
  const { editor, linkState, targetNode, sourceNode, sourceKey, targetKey } = props

  useEffect(() => {
    const out = sourceNode.outputs.get(sourceKey)
    const inPort = targetNode.inputs.get(targetKey)
    editor.connect(out, inPort, _.cloneDeep(linkState)) // 因为 reroute 那边是直接赋值的写法，所以传之前拷贝一份
    const conn: Connection = editor.view.connections.findKey((v, k) => k.data._id === linkState._id)
    return () => {
      if (conn) {
        editor.removeConnection(conn)
      }
    }
  }, [sourceNode, targetNode])

  return null
}
