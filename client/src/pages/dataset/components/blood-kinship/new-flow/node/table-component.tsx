/* eslint-disable jsx-a11y/mouse-events-have-key-events */
/* eslint-disable class-methods-use-this */
/* eslint-disable @typescript-eslint/no-explicit-any */
// eslint-disable-next-line max-classes-per-file
import { Select, Tooltip } from 'antd'
import _ from 'lodash'
import React from 'react'
import Rete, { Input, Output } from 'rete'
import { Control,Node, Socket } from 'rete-react-render-plugin'

import { FLOW_TABLE_TYPE_ICON_MAP, FLOW_TABLE_TYPE_MAP } from '../../const'
import { replaceKey<PERSON>haracter } from '../utils'

export const DATA_FLOW_SOCKET = new Rete.Socket('Dataflow')

const getToolTipContent = item => {
  if (item?.type || !item?.fieldAlias) return item?.fieldName
  return (
    <div>
      {item?.fieldName}
      <br />
      {item?.fieldAlias}
    </div>
  )
}

class TableComponent extends Rete.Component {
  constructor() {
    super('Table')
    this.data.component = TableComponentRenderer
  }

  builder(node: {
    data: { fields: any }
    addInput: (arg0: Input) => { (): any; new (): any; addOutput: { (arg0: Output): void; new (): any } }
  }) {
    const filelds = node?.data?.fields
    filelds.forEach(p => {
      const inp1 = new Rete.Input(`in_${replaceKeyCharacter(p.fieldName)}`, '', DATA_FLOW_SOCKET, true)
      const out = new Rete.Output(`out_${replaceKeyCharacter(p.fieldName)}`, '', DATA_FLOW_SOCKET, true)
      node.addInput(inp1).addOutput(out)
    })

    return node
  }

  worker(_node: any, inputs: any, outputs: any) {}
}

class TableComponentRenderer extends Node {
  [x: string]: { node: any }

  renderFieldItem(list: any[]) {
    const { node, bindSocket, bindControl } = this.props
    const { outputs, inputs } = this.state
    const { indicesNameMap } = node?.data
    return list.map((item, idx) => {
      const fiedlKey = replaceKeyCharacter(item.fieldName)
      const tableKey = replaceKeyCharacter(node?.data?.name)
      const output = _.find(outputs, p => p.key === `out_${fiedlKey}`)
      const input = _.find(inputs, p => p.key === `in_${fiedlKey}`)
      const alias = _.get(indicesNameMap, item.fieldName)
      return (
        <div
          id={`${tableKey}_${fiedlKey}`}
          key={`${tableKey}_${fiedlKey}`}
          className={`flex table-filed ${item.isSelect ? 'field-select' : ''} ${item.isHover ? 'field-hover' : ''}`}
        >
          <div className='table-input' key={input.key}>
            <Socket type='input' socket={input.socket} io={input} innerRef={bindSocket} />
            {input.showControl() && (
              <Control className='input-control' control={input.control} innerRef={bindControl} />
            )}
          </div>
          <div
            className='field elli'
            onClick={() => node?.data?.onNodeItemSelect({ nodeName: node?.data?.name, itemName: item.fieldName })}
            onMouseOver={() => node?.data?.onNodeItemHover({ nodeName: node?.data?.name, itemName: item.fieldName })}
          >
            <div className={item.type === 'dimension' ? 'field-icon field-string' : 'field-icon field-number '} />
            <Tooltip  title={getToolTipContent(item)}>
              <span className='text-ellipsis'>{item.fieldAlias || item.fieldName}</span>
            </Tooltip>
            {/* {alias ? `[${alias}]` : ''} */}
          </div>
          <div className='table-output' key={`out_${fiedlKey}`}>
            <Socket type='output' socket={output.socket} io={output} innerRef={bindSocket} />
          </div>
        </div>
      )
    })
  }

  render() {
    const { node } = this.props
    const { selected } = this.state
    const fields = node?.data?.fields || []
    // if (node?.data?.name === 'result') {
    //   const dimension = fields.filter(p => p.type === 'dimension')
    //   const indices = fields.filter(p => p.type === 'indices')
    //   return (
    //     <div className={`${selected ? 'table-select' : ''} table-result table-panel`}>
    //       <div className='table-title'>{node?.data?.name}</div>
    //       {dimension?.length ? (
    //         <>
    //           <div className='aligncenter bg-main'>维度</div>
    //           {this.renderFieldItem(dimension, 'dimension')}
    //         </>
    //       ) : null}
    //       {indices?.length ? (
    //         <>
    //           <div className='aligncenter bg-main'>指标</div>
    //           {this.renderFieldItem(indices, 'indices')}
    //         </>
    //       ) : null}
    //     </div>
    //   )
    // }
    const { name, style } = _.get(FLOW_TABLE_TYPE_MAP, node.data?.type, FLOW_TABLE_TYPE_MAP.default)
    const { versionList, isBase, onChangeVersion, versionInfo } = node?.data || {}
    return (
      <div
        className={`${selected ? 'table-select' : ''} ${
          node?.data?.name === 'result' ? 'table-result' : ''
        } table-panel`}
      >
        {isBase ? (
          <div className='base-indices'>
            当前指标
            <div />
          </div>
        ) : null}
        <div className='table-title'>
          <div
            className={`table-title-icon ${_.get(
              FLOW_TABLE_TYPE_ICON_MAP,
              node.data?.type,
              FLOW_TABLE_TYPE_ICON_MAP.default
            )}`}
          />
          <div className='table-title-text'>
            <Tooltip title={node?.data?.name}>
              <div className='table-title-ellipsis'>{node?.data?.name}</div>
            </Tooltip>
            <div>
              <span className={`${style} table-title-type`}>{name}</span>
            </div>
          </div>
          <div className='mg2r font12 line-height50'>
            {isBase ? (
              <Select
                bordered={false}
                size='small'
                value={versionInfo?.id}
                options={versionList?.map(p => ({
                  value: p.id,
                  label: `${p.name}${p.alias ? `(${p.alias})` : ''}`,
                  key: p.id
                }))}
                onChange={e => onChangeVersion(e)}
              />
            ) : (
              `${versionInfo?.name || ''}${versionInfo?.alias ? `(${versionInfo?.alias})` : ''}`
            )}
          </div>
        </div>
        {this.renderFieldItem(fields, '')}
      </div>
    )
  }
}

export const TableComponentInst = new TableComponent()
