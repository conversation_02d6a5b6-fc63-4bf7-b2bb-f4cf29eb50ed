export interface IDiagramTableNode {
  id: string
  name: string
  order?: number
  position: { x: number; y: number }
  type?: string
  fields: { fieldName: string; fieldAlias?: string, type?: string, aggs?: string; isSelect?: boolean }[]
}

export interface IDiagramLink {
  source: string
  target: string
  sourceKey: string
  targetKey: string
  title: string
  _id?: string
  pins?: { x: number; y: number }[]
  extraClassName?: string // 只适用于不变的状态
}

export interface IDiagramData {
  id?: string
  transform?: { x: number; y: number }
  scale?: number
  tables?: IDiagramTableNode[]
  links?: IDiagramLink[]
}
