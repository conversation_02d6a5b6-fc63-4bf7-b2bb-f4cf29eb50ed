.abi-dataset-card-list {
  padding: 16px;
  height: 100%;
  position: relative;
  padding-top: 8px;
  overflow-y: auto;
  max-height: calc(100vh - 120px);
  padding-right: 4px;

  &::after {
    clear: both;
    display: block;
    content: '';
  }

  .list-item {
    height: 200px;
    width: 230px;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    border: 1px solid #f1f1f1;
    margin-bottom: 12px;
    position: relative;
    background-color: #fff;
    margin: 6px;
    margin-left: 0;
    margin-right: 12px;
    float: left;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 4px 8px;

      .space {
        display: flex;
        align-items: center;
        color: #999;
      }

    }

    .dbtype {
      margin: 8px auto;
      text-align: center;

      .anticon {
        font-size: 42px;
      }
    }

    .content {
      flex: 1;
      padding: 0 12px;
    }

    .title {
      flex: 1;
      margin: 8px 0;
      color: #444;
      font-size: 15px;
      margin-top: 2px;
      cursor: pointer;
      text-align: center;

      .anticon-table {
        color: #333;
        margin-right: 4px;
        font-size: 14px;
      }
    }

    .time {
      color: #aaa;
      padding: 0 8px;
      font-size: 13px;
    }

    .actions {
      display: flex;
      align-items: center;

      >button,
      >span {
        flex: 1;
        padding: 0;

        &:hover {
          color: @primary-color;
        }
      }
    }

    &:hover {
      box-shadow: 0 1px 6px rgba(@primary-color, 0.16);

      .title,
      .anticon-table {
        color: @primary-color !important;
        text-decoration: underline;
      }

      .file-name {
        opacity: 1;
      }
    }
  }
}
