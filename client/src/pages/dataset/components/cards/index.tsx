import './index.less'

import { LoadingOutlined } from '@ant-design/icons'
import { CardPagination } from '@sugo/design'
import { fastMemo } from '@sugo/design/functions'
import { Empty, Spin } from 'antd'
import _ from 'lodash'
import React from 'react'

import { EmptyData } from '@/components/empty-data'

import { CardItem } from './item'

export interface CardListProps {
  data: any[]
  pagination: any
  loading?: boolean
  groupMap?: Record<string, any>
  filter?: any

  onCreate: () => any
  onGroup: (item: any) => any
  onEdit: (item: any) => any
  onDel: (item: any) => any
  canFn: (item: any) => {
    canCategory: boolean
    canEdit: boolean
    cadDel: boolean
  }
  loaded?: boolean
}

/**
 * 自定义组件
 * @returns
 */
function _CardList(props: CardListProps) {
  const { data = [], pagination, loading = false, onGroup, onDel, onEdit, canFn, onCreate } = props
  const { filter, groupMap = {}, loaded } = props

  const getGroupName = id => groupMap[id]?.title

  return (
    <>
      <Spin spinning={loading} indicator={<LoadingOutlined />}>
        <div className='abi-dataset-card-list'>
          {_.map(data, item => (
            <CardItem
              key={item.id}
              data={item}
              onEdit={onEdit}
              onGroup={onGroup}
              onDel={onDel}
              canFn={canFn}
              getGroupName={getGroupName}
            />
          ))}
          {loaded && _.size(data) === 0 && (
            <div className='not-list-data'>
              <EmptyData
                title='此分类暂无数据视图，您可以立即开始创建'
                buttonText='创建数据视图'
                onlyEmpty={!!(filter.name || !_.isEmpty(filter.rangeDate))}
                onCreate={e => {
                  e.stopPropagation()
                  onCreate()
                }}
              />
            </div>
          )}
        </div>
      </Spin>

      <CardPagination
        {...pagination}
        showSizeChanger={false}
        isFixed
      />
    </>
  )
}

export const CardList = fastMemo(_CardList)
