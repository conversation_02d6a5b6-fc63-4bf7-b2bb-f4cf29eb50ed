import Icon from '@ant-design/icons'
import { Tag } from '@sugo/design'
import { fastMemo, getFromNow } from '@sugo/design/functions'
import { <PERSON><PERSON>, Popconfirm, Tooltip, Typography } from 'antd'
import _ from 'lodash'
import dayjs from 'moment'
import React from 'react'

import { ReactComponent as DorisAvg } from './db-icon/Doris.svg'
import { ReactComponent as HiveSvg } from './db-icon/Hive.svg'
import { ReactComponent as MysqlSvg } from './db-icon/MYSQL.svg'
import { ReactComponent as OracleSvg } from './db-icon/Oracle.svg'
import { ReactComponent as PostgresqlSvg } from './db-icon/postgresql.svg'
import { ReactComponent as SQLserverSvg } from './db-icon/SQLserver.svg'

const iconMap = {
  mysql: MysqlSvg,
  sqlserver: SQLserverSvg,
  oracle: OracleSvg,
  postgresql: PostgresqlSvg,
  hive: HiveSvg,
  doris: DorisAvg
}

const { Text } = Typography

export interface CardItemProps {
  data: any

  onGroup: (item: any) => any
  onEdit: (item: any) => any
  onDel: (item: any) => any
  canFn: (item: any) => {
    canCategory: boolean
    canEdit: boolean
    cadDel: boolean
  }

  getGroupName: (id: string) => string
}

/**
 * 自定义组件
 * @returns
 */
function _CardItem(props: CardItemProps) {
  const { data = {}, onDel, onEdit, onGroup, canFn, getGroupName } = props

  const can = canFn(data)

  return (
    <div className='list-item'>
      <header className='header'>
        <Tag text={getGroupName(data.groupId)} bordered={false} size='samll' autoColor />
        <div className='space'>
          <span>#</span>
          <span style={{ transform: 'translateY(-1px)', marginLeft: 1 }}>{data.dbType}</span>
        </div>
      </header>

      <div className='content'>
        <div className='dbtype'>
          <Icon component={iconMap[_.toLower(data.dbType)] || MysqlSvg} />
        </div>
        <div className='title' onClick={() => can.canEdit && onEdit(data)}>
          {data.title}
        </div>
      </div>

      <Tooltip title={dayjs(data.updatedAt).format('YYYY-MM-DD HH:mm:ss')} placement='topLeft' align={{ offset: [0, 10] }}>
        <Text className='time' ellipsis>
          由 {data.updatedByName} 更新于{getFromNow(data.updatedAt)}
        </Text>
      </Tooltip>

      <div className='actions'>
        {can.canEdit &&
          <Button type='text' onClick={() => onEdit(data)}>编辑</Button>
        }
        {can.canCategory &&
          <Button type='text' onClick={() => onGroup(data)}>分组</Button>
        }
        {can.cadDel &&
          <Popconfirm
            title='确定删除吗？如果有被使用到数据视图不建议删除'
            okType='danger'
            okButtonProps={{ type: 'primary' }}
            onConfirm={() => onDel(data)}
          >
            <Button type='text'>删除</Button>
          </Popconfirm>
        }
      </div>
    </div>
  )
}

export const CardItem = fastMemo(_CardItem)
