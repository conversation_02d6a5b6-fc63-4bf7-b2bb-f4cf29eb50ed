import { Button, Form, Input, Popconfirm, Select, Table } from 'antd'
import type { FormInstance } from 'antd/es/form'
import _ from 'lodash'
import { nanoid } from 'nanoid'
import React, { useEffect, useState } from 'react'

import {
  DATA_TYPE_OPTIONS,
  DATASET_DECIMAL_OPTIONS,
  SHOW_FORMAT_DEFAULT_VALUE,
  SHOW_FORMAT_OPTIONS
} from '@/consts/dataset'
import { useEditTableHeight } from '@/pages/dataset/hooks/use-edit-table-height'
import type { ColumnType } from '@/types/dataset'

import { formatToConfigFormValue } from './utils'

export interface DatasetConfigFormValue {
  title: string
  name: string
  dataType: ColumnType
  dataFormater: string
  showFormat?: null | string
  decimal?: null | number
  key?: string
  id?: string
  isShowPermil?: boolean
  description: string
  [key: string]: any
}

export interface DatasetConfigFormProps {
  form?: FormInstance
  formValues: DatasetConfigFormValue[]
  onConfigValueChange: (data: any) => any
}

const dateFormats = [
  'YYYY-MM-DD', 'YYYY-MM', 'YYYY-MM-DD HH:mm:ss'
]

const getColumns = ({ onCopy, onDelete, deletetDisabled = false }) => [
  {
    title: '字段中文名',
    align: 'center',
    dataIndex: 'title',
    with: 100,
    render(text, row, idx) {
      return (
        <Form.Item name={[idx, 'title']} noStyle>
          <Input style={{ width: 100 }} placeholder='请输入' />
        </Form.Item>
      )
    }
  },
  {
    title: '物理字段',
    align: 'center',
    with: 100,
    dataIndex: 'name',
    render(text, row, idx) {
      return (
        <Form.Item name={[idx, 'name']} noStyle>
          <Input readOnly style={{ width: 100 }} placeholder='请输入' />
        </Form.Item>
      )
    }
  },
  {
    title: '字段类型',
    align: 'center',
    with: 100,
    dataIndex: 'dataType',
    render(text, row, idx) {
      return (
        <Form.Item name={[idx, 'dataType']} noStyle>
          <Select
            style={{ minWidth: 100 }}
            options={DATA_TYPE_OPTIONS}
            placeholder='请选择'
            getPopupContainer={() => document.getElementById('abi-app-modal') || document.body}
          />
        </Form.Item>
      )
    }
  },
  {
    title: '显示格式',
    align: 'center',
    with: 150,
    dataIndex: 'dataFormater',
    render(text, row, idx) {
      return (
        <Form.Item noStyle shouldUpdate={(pre, cur) => pre.dataType !== cur.dataType}>
          {
            ({ getFieldValue }) => {
              const item = getFieldValue(['datasetConfig', idx])
              return (
                <Form.Item name={[idx, 'showFormat']} noStyle>
                  <Select
                    disabled={item?.dataType === 'string'}
                    getPopupContainer={() => document.getElementById('abi-app-modal') || document.body}
                    style={{ minWidth: 150 }}
                    placeholder='请选择'
                    dropdownMatchSelectWidth={false}
                    options={SHOW_FORMAT_OPTIONS.filter(
                      v =>
                        (item?.dataType === 'date' && v.type === 'time') ||
                        (item?.dataType === 'number' && v.type === 'number')
                    )}
                  />
                </Form.Item>
              )
            }
          }
        </Form.Item>
      )
    }
  },
  {
    title: '精度',
    align: 'center',
    with: 100,
    dataIndex: 'dataFormater',
    render(text, row, idx) {
      return (
        <Form.Item noStyle shouldUpdate={(pre, cur) => pre.dataType !== cur.dataType}>
          {
            ({ getFieldValue }) => {
              const item = getFieldValue(['datasetConfig', idx])
              return (
                <Form.Item name={[idx, 'decimal']} noStyle>
                  <Select
                    disabled={item?.dataType !== 'number'}
                    style={{ minWidth: 100 }}
                    options={DATASET_DECIMAL_OPTIONS}
                    placeholder='请选择'
                  />
                </Form.Item>
              )
            }
          }
        </Form.Item>
      )
    }
  },
  {
    title: '使用千分号',
    align: 'center',
    with: 100,
    dataIndex: 'dataFormater',
    render(text, row, idx) {
      return (
        <Form.Item noStyle shouldUpdate={(pre, cur) => pre.dataType !== cur.dataType}>
          {
            ({ getFieldValue }) => {
              const item = getFieldValue(['datasetConfig', idx])
              return (
                <Form.Item name={[idx, 'isShowPermil']} noStyle>
                  <Select
                    disabled={item?.dataType !== 'number'}
                    style={{ minWidth: 100 }}
                    getPopupContainer={() => document.getElementById('abi-app-modal') || document.body}
                    placeholder='请选择'
                    options={[
                      { label: '是', value: true },
                      { label: '否', value: false }
                    ]}
                  />
                </Form.Item>
              )
            }
          }
        </Form.Item>
      )
    }
  },
  {
    title: '描述',
    align: 'center',
    with: 100,
    dataIndex: 'description',
    render(text, row, idx) {
      return (
        <Form.Item name={[idx, 'description']} noStyle>
          <Input maxLength={500} placeholder='请输入' />
        </Form.Item>
      )
    }
  },
  {
    title: '操作',
    align: 'center',
    render(text, row, idx) {
      return (
        <>
          <Button type='link' onClick={() => onCopy?.(idx)}>
            复制
          </Button>

          <Form.Item noStyle shouldUpdate={(pre, cur) => pre.status !== cur.status}>
            {
              ({ getFieldValue }) => {
                const item = getFieldValue(['datasetConfig', idx])
                return (
                  <Button type='link' onClick={() => onDelete?.(item, idx)} disabled={deletetDisabled}>
                    {item.status === 'show' ? '隐藏' : '显示'}
                  </Button>
                )
              }
            }
          </Form.Item>

          <Form.Item name={[idx, 'status']} style={{ display: 'none' }}>
            <Input readOnly />
          </Form.Item>
          <Form.Item name={[idx, 'id']} style={{ display: 'none' }} >
            <Input readOnly placeholder='请输入' />
          </Form.Item>
        </>
      )
    }
  }
]

export default function DatasetConfigForm(props: DatasetConfigFormProps) {
  const { form, formValues = [], onConfigValueChange } = props
  const [dataSource, setDataSource] = useState<DatasetConfigFormValue[]>([])
  const height = useEditTableHeight({})

  const initData = () => {
    const list = formatToConfigFormValue(formValues, dataSource)
    setDataSource(list)
    form?.setFieldsValue({ datasetConfig: list })
  }

  useEffect(() => {
    initData()
  }, [formValues])

  const onCopy = idx => {
    const allValues = form?.getFieldsValue()
    setDataSource([...dataSource, { ...allValues.datasetConfig[idx], key: nanoid(6) }])
    form?.setFieldValue(['datasetConfig', dataSource.length], _.omit(allValues.datasetConfig[idx], ['id']))
  }

  const onDelete = (row, idx) => {
    const res = row.status === 'show' ? 'hide' : 'show'
    dataSource[idx].status = res
    setDataSource([...dataSource])
    form?.setFieldValue(['datasetConfig', idx, 'status'], res)
    setTimeout(() => {
      onConfigValueChange?.(form?.getFieldsValue())
    }, 300)
  }

  const handleValuesChange = (changedValues, allValues) => {
    const idx = _.chain(changedValues.datasetConfig).keys().last().value()
    const val = _.get(changedValues.datasetConfig, `[${idx}]`)
    const dataType = val?.dataType
    /**
     * 只需要更新dataSource每项的dataType，用于判断可选格式
     */
    if (dataType) {
      form?.setFieldValue(['datasetConfig', idx], {
        ...allValues.datasetConfig[idx],
        showFormat: SHOW_FORMAT_DEFAULT_VALUE[dataType],
        decimal: dataType === 'number' ? 0 : null,
        isShowPermil: dataType === 'number'
      })
      dataSource[idx].dataType = changedValues.datasetConfig[idx].dataType
      setDataSource([...dataSource])
    }
    onConfigValueChange?.(form?.getFieldsValue())
  }

  return (
    <Form
      form={form}
      name='datasetConfig'
      className='dataset-config-form'
      initialValues={{ datasetConfig: dataSource }}
      onValuesChange={handleValuesChange}
    >
      <Form.List name='datasetConfig'>
        {fields => (
          <Table
            size='small'
            scroll={{ x: 'max-content', y: height - 50 }}
            columns={getColumns({ onCopy, onDelete, deletetDisabled: fields.length === 1 }) as any[]}
            dataSource={dataSource}
            rowKey='key'
            pagination={false}
          />
        )}
      </Form.List>

    </Form>
  )
}
