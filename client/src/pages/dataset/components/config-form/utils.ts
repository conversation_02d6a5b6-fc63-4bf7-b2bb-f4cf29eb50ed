import { formatSpecifier } from 'd3-format'
import _ from 'lodash'

import type { ColumnType } from '@/types/dataset'

const d3formatToConfigType = (formaterStr: string, type: ColumnType) => {
  if (type === 'string' || !formaterStr)
    return {
      showFormat: null,
      decimal: null,
      isShowPermil: null
    }
  if (type === 'date') {
    return {
      showFormat: formaterStr,
      decimal: null,
      isShowPermil: null
    }
  }
  const formatObj = formatSpecifier(formaterStr)
  return {
    showFormat: formatObj.type,
    decimal: formatObj?.precision,
    isShowPermil: formaterStr.indexOf(',') !== -1
  }
}

/**
 * 将数据源转化为config form的value值
 * @param data
 */
export const formatToConfigFormValue = (data, cacheData) => {
  const dict = _.keyBy(cacheData, 'name')
  const res = _.map(data, v => {
    const config = _.get(dict, v.name)
    if (config) return config
    return {
      ...v,
      ...d3formatToConfigType(v?.dataFormater, v?.dataType)
    }
  })
  return res
}
