import './index.less'

import { Select, TreeSelect } from 'antd'
import React, { useMemo } from 'react'

import type { DataSourceInfo } from '@/types/data-source'

import type { DraggableTreeData } from '../draggale-tree'
import DraggableTree from '../draggale-tree'
import { toDbdata, toSimpleConnectData, toTableData } from './utils'

export interface DatasetDataOriginProps {
  treeData: DataSourceInfo
  connectId?: null | number
  dbId: null | number
  onLoadColumnData?: (data: any) => Promise<any>
  onSelectConnectId?: (id: number) => any
  onSelectDb?: (id: number) => any
  onDoubleClick?: (data: DraggableTreeData) => any
}

/**
 * 数据源
 * @returns
 */
export default function DatasetDataOrigin(props) {
  const { connectId, dbId, treeData, onLoadColumnData, onSelectConnectId, onSelectDb, onDoubleClick } = props

  const connectionData = useMemo(() => toSimpleConnectData(treeData?.connectionMap), [treeData?.connectionMap])

  const databaseList = useMemo(
    () => toDbdata(treeData?.databaseMap).filter(v => v?.connectId === connectId),
    [treeData?.databaseMap, connectId]
  )

  const tableList = useMemo(
    () => toTableData(treeData?.tableMap, treeData?.fieldMap).filter(v => v?.dbId === dbId),
    [treeData?.tableMap, treeData?.fieldMap, dbId]
  )

  return (
    <div className='dataset-data-origin'>
      <h4>数据源</h4>
      <TreeSelect
        showSearch
        filterTreeNode={(input, treeNode) => {
          if (!input) return true
          return new RegExp(input, 'i').test((treeNode?.title || '') as string)
        }}
        className='dataset-data-origin-select'
        treeNodeFilterProp='title'
        dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
        treeData={connectionData as any[]}
        value={connectId}
        placeholder='选择数据源连接名'
        treeDataSimpleMode
        treeIcon
        autoClearSearchValue={false}
        onSelect={onSelectConnectId}
      />
      <h4>数据库</h4>
      <Select
        showSearch
        placeholder='选择数据库'
        options={databaseList}
        getPopupContainer={triggerNode => triggerNode.parentNode}
        value={dbId}
        filterOption={(input, option) => {
          if (!input) return true
          return new RegExp(input, 'i').test((option?.label || '') as string)
        }}
        className='dataset-data-origin-select'
        onSelect={onSelectDb}
      />
      <h4>数据目录
        <span style={{ color: '#999' }}>（可拖拽表名或字段名）</span>
      </h4>
      <DraggableTree
        className='dataset-data-origin-tables'
        treeData={tableList}
        draggable
        onDoubleClick={onDoubleClick}
        loadData={onLoadColumnData}
      />
    </div>
  )
}
