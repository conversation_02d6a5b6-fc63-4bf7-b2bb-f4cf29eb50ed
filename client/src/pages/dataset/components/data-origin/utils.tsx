import { ApiOutlined, DatabaseOutlined, TableOutlined } from '@ant-design/icons'
import arrayToTree from 'array-to-tree'
import _ from 'lodash'
import React from 'react'

import type { ConnectionMap, DatabaseMap, FieldMap, TableMap } from '@/types/data-source'

const icoApiOutlined = <ApiOutlined />
const icoDbOutlined = <DatabaseOutlined />
const icoTableOutlined = <TableOutlined />
const connIcon = () => icoApiOutlined
const dbIcon = () => icoDbOutlined
const tableIcon = () => icoTableOutlined

export const toSimpleConnectData = (connectionMap: ConnectionMap = { entities: {}, keys: [] }) => {
  const connDict = connectionMap.entities
  if (_.isEmpty(connDict)) return []

  const typeGroup = _.chain(connDict)
    // 兼容旧版本没有showType的情况
    .map(v => v?.showType ? v?.showType : _.toUpper(v.type))
    .filter(Boolean)
    .uniq()
    .orderBy()
    .value()
  const validTypesSet = new Set(typeGroup)

  return [
    ..._.map(typeGroup, connType => ({
      id: connType,
      value: connType,
      title: connType,
      loadType: 'connection',
      selectable: false
    })),
    // {
    //   id: 'static',
    //   value: 'static',
    //   title: '静态数据',
    //   selectable: false
    // },
    // // 静态数据
    // {
    //   id: 'static_csv',
    //   value: 'static_csv',
    //   pId: 'static',
    //   title: 'csv',
    //   isLeaf: true,
    //   selectable: true,
    //   icon: tableIcon
    // },
    // 数据开发中心的连接
    ..._.map(connectionMap.keys, connId => {
      const conn = connDict[connId]
      // 兼容旧版本没有showType的情况
      const type = conn?.showType || conn.type
      if (!validTypesSet.has(type)) {
        return null
      }
      return {
        id: conn.id,
        value: conn.id,
        pId: type,
        title: conn.name,
        loadType: 'dataBase',
        selectable: true,
        icon: connIcon
      }
    }).filter(Boolean)
  ]
}

export const toDbdata = (databaseMap: DatabaseMap = { entities: {}, keys: [] }) => {
  if (_.isEmpty(databaseMap?.keys)) return []

  return _.map(databaseMap.keys, v => {
    const obj = databaseMap?.entities[v]
    return _.merge({}, obj, {
      label: obj?.dbAlias || obj?.dbName || obj?.schemaName,
      value: obj?.id
    })
  })
}

export const toTableData = (
  tableMap: TableMap = { entities: {}, keys: [] },
  fieldMap: FieldMap = { entities: {}, keys: [] }
) => {
  const tableDict = tableMap?.entities
  if (_.isEmpty(tableDict)) return []
  const list = _.map(tableMap.keys, tableId => {
    const table = tableDict[tableId]
    return {
      ...table,
      realName: table.tableName,
      id: tableId,
      value: tableId,
      loadType: 'field',
      title: table?.tableAlias || table.tableName,
      selectable: true,
      children: [],
      icon: tableIcon
    }
  })
  const fieldDict = fieldMap?.entities
  const fields = _.map(fieldMap?.keys, fieldId => {
    const field = fieldDict[fieldId]
    return {
      realName: field.columnName,
      id: field.id,
      value: field.id,
      pId: field.tableId,
      title: field?.columnAlias || field.columnName,
      isLeaf: true,
      selectable: false
    }
  })
  const tree = arrayToTree(_.concat(list, fields as any[]), { parentProperty: 'pId' })
  return tree
}
