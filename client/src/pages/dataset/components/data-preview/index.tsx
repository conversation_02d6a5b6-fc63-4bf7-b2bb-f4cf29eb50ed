import { DataPreviewTable } from '@sugo/design'
import { Empty, Table } from 'antd'
import _ from 'lodash'
import { nanoid } from 'nanoid'
import React from 'react'

import { getDatasetFormatFunc } from '../../containers/editor/utils'

export interface DatasetDataPreviewProps {
  dataSource?: object[]
  columns: any[]
  loading?: boolean
}

const toTableColumns = data => _.map(data, item => ({
  title: item.title,
  dataIndex: item.name,
  ellipsis: true,
  width: 100,
  align: 'center',
  render(text) {
    return getDatasetFormatFunc(item.dataType)(text, item.dataFormater)
  }
}))


export default function DatasetDataPreview(props: DatasetDataPreviewProps) {
  const { dataSource = [], loading = false, columns } = props

  const column = toTableColumns(columns)

  if (_.isEmpty(column)) return <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />

  return (
    <DataPreviewTable
      loading={loading}
      columns={column.map(i => ({ key: i.dataIndex, title: i.title, name: i.dataIndex }))}
      data={dataSource}
    />
  )
}
