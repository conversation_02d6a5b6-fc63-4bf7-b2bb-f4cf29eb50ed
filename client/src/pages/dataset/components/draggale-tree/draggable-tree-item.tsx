import classNames from 'classnames'
import _ from 'lodash'
import React, { useState } from 'react'

import type { DraggableTreeData } from './draggable-tree-label'
import DraggableTreeLabel from './draggable-tree-label'

export interface DraggableTreeItemProps {
  data: DraggableTreeData
  level?: number
  draggable?: boolean
  activeKey?: string | number
  onClickArrow?: (data: DraggableTreeData) => Promise<any>
  onSelect?: (data: DraggableTreeData) => any
  onDoubleClick?: (data: DraggableTreeData) => any
}

/**
 * 树型项组件
 * @returns
 */
function DraggableTreeItem(props: DraggableTreeItemProps) {
  const { data, level = 0, draggable, activeKey = '', onSelect, onClickArrow, onDoubleClick } = props
  const [showChildren, setShowChildren] = useState(false)

  const handleClickArrow = async (val: DraggableTreeData) => {
    setShowChildren(v => !v)
    onClickArrow?.(val)
  }

  return (
    <div className={classNames('draggable-tree-item', activeKey === data?.value ? 'draggable-tree-item-active' : '')}>
      <DraggableTreeLabel
        arrowDown={showChildren}
        data={data}
        level={level}
        draggable={draggable}
        onSelect={onSelect}
        onDoubleClick={onDoubleClick}
        onClickArrow={handleClickArrow}
      />
      {showChildren && (
        <div className={classNames('draggable-tree-subitem')}>
          {_.map(data?.children || [], v => (
            <DraggableTreeItem
              draggable={draggable}
              onDoubleClick={onDoubleClick}
              key={v.value}
              data={v}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  )
}
export default DraggableTreeItem
