import { CaretRightOutlined } from '@ant-design/icons'
import _ from 'lodash'
import React, { memo } from 'react'

export interface DraggableTreeData {
  key: string | number
  title: string
  value: string | number
  icon: () => React.ReactElement
  [k: string]: any
  children?: {
    key: string | number
    title: string
    value: string | number
    icon: () => React.ReactElement
    [key: string]: any
  }[]
}

export interface DraggableTreeLabelProps {
  data: DraggableTreeData
  level?: number
  arrowDown: boolean
  draggable?: boolean
  onClickArrow?: (data: DraggableTreeData) => Promise<any>
  onSelect?: (data: DraggableTreeData) => any
  onDoubleClick?: (data: DraggableTreeData) => any
}

/**
 * 树型label组件
 * @returns
 */
function DraggableTreeLabel(props: DraggableTreeLabelProps) {
  const { data, arrowDown, draggable, onClickArrow, onSelect, onDoubleClick } = props

  const onDragStart = e => {
    e.dataTransfer.setData('text/plain', JSON.stringify(_.omit(data, ['children'])))
    e.dataTransfer.dropEffect = 'copy'
  }

  const handleDoubleClick = () => {
    onDoubleClick?.(data)
  }

  return (
    <div className='draggable-tree-item-label' onDragStart={onDragStart} draggable={draggable}>
      <div className='draggable-tree-arrow' onClick={() => onClickArrow?.(data)}>
        {data?.children && <CaretRightOutlined rotate={arrowDown ? 90 : 0} />}
      </div>
      <div className='draggable-tree-item-content' onClick={() => onSelect?.(data)} onDoubleClick={handleDoubleClick}>
        {data?.icon ? <div className='draggable-tree-icon'>{data?.icon?.()}</div> : null}
        <span>{data.title}</span>
      </div>
    </div>
  )
}

export default memo(DraggableTreeLabel)
