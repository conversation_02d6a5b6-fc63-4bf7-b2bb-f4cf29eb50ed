import './index.less'

import { Empty } from 'antd'
import classNames from 'classnames'
import _ from 'lodash'
import React, { useState } from 'react'

import DraggableTreeItem from './draggable-tree-item'
import type { DraggableTreeData } from './draggable-tree-label'

export interface DraggableTreeProps {
  treeData: DraggableTreeData[]
  draggable?: boolean
  className?: string
  loadData: (data: DraggableTreeData) => Promise<any>
  onDoubleClick?: (data: DraggableTreeData) => any
}

/**
 * 可拖拽树形组件
 * @param props
 * @returns
 */
function DraggableTree(props) {
  const { treeData = [], className, loadData, draggable, onDoubleClick } = props
  const [activeKey, setActiveKey] = useState<string | number>('')

  const handleClickArrow = async (data: DraggableTreeData) => {
    if (loadData) return loadData(data)
  }

  const handleSelect = (data: DraggableTreeData) => {
    setActiveKey(v => (data?.value === v ? '' : data?.value))
  }

  return (
    <div className={classNames('draggable-tree', className)}>
      {treeData.map(v => (
        <DraggableTreeItem
          key={v.value}
          draggable={draggable}
          activeKey={activeKey}
          onSelect={handleSelect}
          onDoubleClick={onDoubleClick}
          data={v}
          onClickArrow={handleClickArrow}
        />
      ))}
      {_.isEmpty(treeData) && <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
    </div>
  )
}

export default DraggableTree
