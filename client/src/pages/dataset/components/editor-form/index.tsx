import { LoadingOutlined } from '@ant-design/icons'
import { Flex } from '@sugo/design'
import { fastMemo } from '@sugo/design/functions'
import { Button, Form, Input } from 'antd'
import type { FormInstance } from 'antd/es/form'
import React, { lazy, Suspense } from 'react'

import type { SqlEditorInstance } from '@/components/code-editor/sql-editor'
// import SqlEditor from '@/components/code-editor/sql-editor'
import { isTieke } from '@/consts/dataset'

const SqlEditor = lazy(() => import('@/components/code-editor/sql-editor'))

const MySqlEditor = fastMemo(props => (
  <Suspense fallback={(
    <div style={{ height: 240, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
      <LoadingOutlined />
    </div>
  )}>
    <SqlEditor {...props} />
  </Suspense>
))

export interface DatasetEditorFormProps {
  form?: FormInstance
  onRunSql?: React.MouseEventHandler<HTMLElement>
  sqlEditorRef?: React.Ref<SqlEditorInstance | null>
  onValuesChange?: (changedValues: any, values: any) => void
  onChange?: () => void
  sqlLoading?: boolean
  onClean?: () => void
}

export default function DatasetEditorForm(props: DatasetEditorFormProps) {
  const { form, sqlEditorRef, onValuesChange, onRunSql, onChange, sqlLoading, onClean } = props
  return (
    <Form form={form} name='dataset' layout='vertical' onValuesChange={onValuesChange}>
      <Form.Item
        label={`${isTieke ? '数据集' : '视图'}名称`}
        name='title'
        rules={[{ required: true, message: `${isTieke ? '数据集' : '视图'}名称不能为空` }]}
      >
        <Input placeholder={`请输入${isTieke ? '数据集' : '视图'}名称`} showCount maxLength={50} />
      </Form.Item>

      <Form.Item
        label={`${isTieke ? '数据集' : '视图'}脚本`}
        name='sqlContent'
        extra={(
          <Flex row vcenter className='mt-[16px]'>
            <Button className='mr-3' type='primary' size='small' onClick={onRunSql} loading={sqlLoading}>运行</Button>
            <Button size='small' onClick={onClean} disabled={!sqlLoading}>取消</Button>
          </Flex>
        )}
        rules={[{ required: true, message: `${isTieke ? '数据集' : '视图'}脚本不能为空` }]}
      >
        <MySqlEditor loading={sqlLoading} ref={sqlEditorRef} onChange={onChange} style={{ height: 'calc(95vh - 100px - 500px)' }} />
      </Form.Item>

    </Form>
  )
}
