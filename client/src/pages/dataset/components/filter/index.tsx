import 'dayjs/locale/zh-cn'
import './index.less'

import { SearchOutlined, SyncOutlined } from '@ant-design/icons'
import { useDebounceFn, useMemoizedFn } from 'ahooks'
import { Button, Col, DatePicker, Form, Input, Row, Space } from 'antd'
import type { RangePickerProps } from 'antd/es/date-picker'
import locale from 'antd/es/date-picker/locale/zh_CN'
import classNames from 'classnames'
import dayjs from 'dayjs'
import _ from 'lodash'
import React, { memo, useImperativeHandle } from 'react'

import { DS_PAGE_NAME } from '@/consts/dataset'

export interface DatasetFilterProps {
  className?: string
  renderExtra?: React.ReactNode
  onSearch: (data: { name: string; rangeDate: Date[] | null }) => any
  onReset: () => any
}

const disabledDate: RangePickerProps['disabledDate'] = current =>
  // Can not select days before today and today
  current && current > dayjs().endOf('day')

/**
 * 数据集筛选器组件
 * @param props
 * @returns
 */
function DatasetFilter(props, ref) {
  const { className, renderExtra, onSearch, onReset } = props
  const [form] = Form.useForm()

  useImperativeHandle(ref, () => ({
    reset: () => {
      form.resetFields()
    }
  }))

  const handleSearch = useMemoizedFn(() => {
    const data = _.cloneDeep(form.getFieldsValue())
    if (!_.isEmpty(data?.rangeDate)) {
      data.rangeDate[0] = dayjs(`${data.rangeDate[0]?.format('YYYY-MM-DD')} 00:00:00`).toDate()
      data.rangeDate[1] = dayjs(`${data.rangeDate[1]?.format('YYYY-MM-DD')} 23:59:59`).toDate()
    }
    onSearch?.(data)
  })

  const handleReset = useMemoizedFn(() => {
    form.resetFields()
    onReset?.()
  })

  const { run: debounceHandleSearch } = useDebounceFn(handleSearch, {
    wait: 1000
  })
  return (
    <div className={classNames('dataset-manage-filter', className)}>
      <Row gutter={32}>
        <Col flex='auto'>
          <Form form={form} layout='inline'>
            <Form.Item name='name'>
              <Input
                placeholder={`搜索${DS_PAGE_NAME}名称`}
                onChange={debounceHandleSearch}
                autoComplete='off'
                suffix={
                  <SearchOutlined style={{ color: 'rgba(0,0,0,.45)' }} />
                }
              />
            </Form.Item>
            <Form.Item name='rangeDate'>
              <DatePicker.RangePicker locale={locale} onChange={handleSearch} disabledDate={disabledDate} />
            </Form.Item>
            <Form.Item>
              <Space size='middle'>
                {/*  icon={<SearchOutlined />} */}
                {/* <Button type='primary' onClick={handleSearch}>
                  搜索
                </Button> */}
                {/* icon={<ReloadOutlined />} */}
                <Button onClick={handleReset} icon={<SyncOutlined />} style={{ color: 'rgba(0,0,0,.45)' }}>
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Col>

        <Col>
          {renderExtra || null}
        </Col>
      </Row>
    </div>
  )
}

export default memo(React.forwardRef(DatasetFilter))
