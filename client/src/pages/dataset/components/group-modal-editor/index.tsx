import { Form, Input } from 'antd'
import React, { useEffect, useState } from 'react'

import Modal from '@/components/customs/custom-modal'
import withRefModal from '@/components/with-ref-modal'

import type { DatasetGroupDataSourceItem } from '../group'

export interface DatasetGroupModalEditorProps {
  visible: boolean
  title?: string
  value: DatasetGroupDataSourceItem
  onClose: () => any
  onSave: (newTitle: string) => Promise<any>
}

/**
 * 分组编辑框
 * @param props
 * @returns
 */
function DatasetGroupModalEditor(props) {
  const { title, value, visible, onClose, onSave } = props
  const [form] = Form.useForm()
  const [confirmLoading, setConfirmLoading] = useState(false)

  useEffect(() => {
    if (visible && value) form.setFieldsValue({ title: value?.title })
    else form.resetFields()
  }, [visible, value])

  const handleClose = () => {
    setConfirmLoading(false)
    onClose?.()
  }

  const handleSave = async () => {
    try {
      const newTitle = await form.validateFields()
      setConfirmLoading(true)
      let res: any = null
      if (onSave) res = await onSave(newTitle.title)
      if (res) handleClose()
    } finally {
      setConfirmLoading(false)
    }
  }

  return (
    <Modal
      title={title}
      confirmLoading={confirmLoading}
      open={visible}
      okText='保存'
      centered
      onOk={handleSave}
      onCancel={handleClose}
    >
      <Form form={form}>
        <Form.Item
          label='分组名称'
          name='title'
          validateFirst
          rules={[
            {
              required: true,
              message: '名称不能为空',
              whitespace: true
            }
          ]}
        >
          <Input maxLength={30} placeholder='请输入' showCount />
        </Form.Item>
      </Form>
    </Modal >
  )
}

export default withRefModal(DatasetGroupModalEditor)
