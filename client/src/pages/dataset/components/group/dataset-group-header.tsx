import { PlusOutlined } from '@ant-design/icons'
import { But<PERSON> } from 'antd'
import React from 'react'

import { DS_PAGE_NAME } from '@/consts/dataset'

export interface DatasetGroupHeaderProps {
  title?: string
  onClick?: () => any
}

/**
 * 数据集分组头部
 * @returns
 */
function DatasetGroupHeader(props) {
  const { onClick, title = `${DS_PAGE_NAME}分组` } = props

  return (
    <h3 className='dataset-manage-group-header'>
      <span className='dataset-manage-group-title'><b>分类</b></span>
      <Button type='text' size='small' icon={<PlusOutlined />} onClick={onClick} />
    </h3>
  )
}

export default DatasetGroupHeader
