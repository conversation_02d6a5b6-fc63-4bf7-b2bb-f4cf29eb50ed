import { EllipsisOutlined } from '@ant-design/icons'
import { IconFont } from '@sugo/iconfont'
import { Button, Dropdown, Empty, Menu, Tooltip } from 'antd'
import classNames from 'classnames'
import _ from 'lodash'
import React, { useMemo } from 'react'

import { GROUP_OPERATIONS } from '@/consts/dataset'
import { GroupOperationType } from '@/types/dataset'

export interface DatasetGroupListDataSourceItem {
  title: string
  id: string
  [key: string]: unknown
}

const disabledList = ['默认分组', '全部']

/**
 * @param dataSource [] 数据源
 * @param valueKey string 列表项value属性
 * @param titleKey string 列表项title属性
 * @param selected striong | number 列表选中值
 * @param onClick 点击事件
 */
export interface DatasetGroupListProps {
  dataSource: DatasetGroupListDataSourceItem[]
  selected: number | string
  className?: string
  onEditGroup?: (row: DatasetGroupListDataSourceItem, type: GroupOperationType) => any
  onClick?: (data: DatasetGroupListDataSourceItem, index?: number) => any
}

/**
 * 数据集分组列表
 * @param props
 * @returns
 */
function DatasetGroupList(props: DatasetGroupListProps) {
  const { dataSource = [], selected = '', className, onEditGroup, onClick } = props

  const list = useMemo(() => _.uniqBy(dataSource, 'id').filter(i => i), [dataSource])

  const handleClick = (val, idx) => {
    if (selected === val.id) return
    onClick?.(val, idx)
  }

  const handleSelect = (data: DatasetGroupListDataSourceItem, key: GroupOperationType) => {
    onEditGroup?.(data, key)
  }

  return (
    <ul className={classNames('dataset-manage-group-list', className)}>
      {_.map(list, (val, idx) => (
        <li
          key={val?.id}
          className={classNames('dataset-manage-group-item', selected === val?.id ? 'group-active' : '')}
        >
          {/* TODO: 添加tooltips */}
          <Tooltip title={val?.title} placement='right' align={{ offset: [20, 0] }}>
            <div className='dataset-group-item-content' onClick={() => handleClick(val, idx)}>
              <IconFont type='sugo-label-group' className='dataset-manage-group-item-icon' />
              <span className='dataset-manage-group-text'>{val?.title || '--'}</span>
            </div>
          </Tooltip>

          {!disabledList.includes(val?.title) &&

            (
              <Dropdown
                overlay={
                  <Menu onClick={data => handleSelect(val, data?.key as GroupOperationType)} items={GROUP_OPERATIONS} />
                }
                disabled={disabledList.includes(val?.title)}
                placement='bottomLeft'
              >
                <Button
                  type={selected === val?.id ? 'link' : 'text'}
                  icon={<EllipsisOutlined />}
                  className='dataset-manage-group-dropdown'
                />
              </Dropdown>
            )}
        </li>
      ))}
      {_.isEmpty(dataSource) && (
        <li>
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </li>
      )}
    </ul>
  )
}

export default DatasetGroupList
