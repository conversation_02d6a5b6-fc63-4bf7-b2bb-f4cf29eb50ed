.dataset-manage-group {
  padding: 10px;
  height: 100%;
  width: 230px;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  box-shadow: 1px 2px 4px rgba(@primary-color, 0.08);
  z-index: 2;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
    padding: 12px 0;
    padding-top: 0;

    .ant-btn-icon-only {
      transform: translateX(4px);
    }

    .anticon-plus {
      opacity: 0.65;
    }
  }
  &-title {
    font-size: 15px;
  }

  &-list {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    font-size: 14px;
    list-style: none;
    overflow-y: auto;
  }

  &-item {
    // margin-bottom: 6px;
    padding: 8px 28px 8px 10px;
    position: relative;
    border-radius: 4px;
    cursor: pointer;
    margin-bottom: 1px;

    .dataset-manage-group-item-icon {
      color: #9e9e9e;
    }

    &:hover {
      background-color: tint(@primary-color, 94%);
      color: #555;
      .dataset-manage-group-item-icon {
        color: @primary-color;
      }
    }

    .dataset-group-item-content {
      display: flex;
      align-items: center;
    }
  }

  .group-active {
    color: @primary-color;
    background-color: tint(@primary-color, 92%);

    .dataset-manage-group-item-icon {
      color: @primary-color;
    }
  }

  &-item-icon {
    flex-shrink: 0;
    color: #bbb;
    font-size: 13px;
  }

  &-text {
    margin-left: 8px;
    white-space: nowrap;
    line-height: 1.2em;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  &-dropdown {
    position: absolute;
    right: 0;
    top: -2px;
    z-index: 1;
    border-radius: 100%;
  }
}
