import './index.less'

import React from 'react'

import { DS_PAGE_NAME } from '@/consts/dataset'
import { GroupOperationType } from '@/types/dataset'

import DatasetGroupHeader from './dataset-group-header'
import type { DatasetGroupListDataSourceItem } from './dataset-group-list'
import DatasetGroupList from './dataset-group-list'

export type DatasetGroupDataSourceItem = DatasetGroupListDataSourceItem

export interface DatasetGroupProps {
  dataSource: DatasetGroupDataSourceItem[]
  selected: string
  title?: string
  onSelect: (data: DatasetGroupDataSourceItem, index?: number) => any
  onAddGroup: () => any
  onEditGroup: (row: DatasetGroupListDataSourceItem, type: GroupOperationType) => any
}

/**
 * 数据集分组
 * @returns
 */
function DatasetGroup(props: DatasetGroupProps) {
  const { dataSource, selected, title = `${DS_PAGE_NAME}分组`, onEditGroup, onSelect, onAddGroup } = props
  return (
    <div className='dataset-manage-group'>
      <DatasetGroupHeader title={title} onClick={onAddGroup} />
      <DatasetGroupList onEditGroup={onEditGroup} dataSource={dataSource} selected={selected} onClick={onSelect} />
    </div>
  )
}

export default DatasetGroup
