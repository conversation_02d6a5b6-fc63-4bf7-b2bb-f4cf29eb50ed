import { useReactive } from 'ahooks'
import { Col, message, Modal, Row, Select } from 'antd'
import React, { forwardRef, useImperativeHandle } from 'react'

import { ALL_GROUP } from '@/consts/dataset'

import type { DatasetGroupDataSourceItem } from '../group'


export interface DatasetMoveGroupModalProps {
  /** 分组选择项 */
  options: Array<{ label?: string, value?: any }>
  /** 保存 */
  onSave: (data:any) => Promise<any>
}

interface DatasetMoveGroupModalState {
  visible: boolean,
  targetGroupId: string | null,
  saveLoading: boolean,
  currentDataset: DatasetGroupDataSourceItem | null
}

/**
 * 修改分组弹窗组件
 * @param props
 * @returns
 */
function DatasetMoveGroupModal(props:DatasetMoveGroupModalProps, ref) {
  const { options, onSave } = props
  const state = useReactive<DatasetMoveGroupModalState>({
    visible: false,
    targetGroupId: null,
    saveLoading: false,
    currentDataset: null
  })

  const reset = () => {
    state.visible = false
    state.targetGroupId = null
    state.saveLoading = false
    state.currentDataset = null
  }

  useImperativeHandle(ref, () => ({
    show: (data: DatasetGroupDataSourceItem) => {
      state.currentDataset = data
      state.visible = true
    },
    hide: () => {
      reset()
    }
  }))

  const handleCancel = () => {
    reset()
  }

  const handleSave = async () => {
    if (!state.targetGroupId) return message.warning({ content: '请先选择分组' })
    state.saveLoading = true
    const submitData = { targetGroupId: state.targetGroupId, datasetId: state.currentDataset?.id  }
    try {
      await onSave?.(submitData)
    } finally {
      handleCancel()
    }
  }

  const handleChange = (gId: string) => {
    state.targetGroupId = gId
  }

  return (
    <Modal
      title={`修改${state.currentDataset?.title}分组`}
      open={state.visible}
      centered
      confirmLoading={state.saveLoading}
      onCancel={handleCancel}
      onOk={handleSave}
    >
      <Row gutter={20} align='middle'>
        <Col>选择分组</Col>
        <Col >
          <Select
            showSearch
            placeholder='请选择分组'
            options={options.filter(v => v.value !== state.currentDataset?.groupId && v.value !== ALL_GROUP.id)}
            value={state.targetGroupId}
            onChange={handleChange}
            className='w-[200px]'
            filterOption={
              (input, option) => new RegExp(input, 'ig').test(option?.label as string)
            }
          />
        </Col>
      </Row>
    </Modal>
  )
}

export default forwardRef(DatasetMoveGroupModal)
