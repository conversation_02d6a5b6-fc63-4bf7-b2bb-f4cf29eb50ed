import './index.less'

import { PlusOutlined } from '@ant-design/icons'
import { usePermissions } from '@sugo/design/hooks'
import { Button } from 'antd'
import classNames from 'classnames'
import React from 'react'

import { DATASET_TYPE_DISABLEDS, DATASET_TYPE_MAP, DS_PAGE_NAME } from '@/consts/dataset'
import type { DatesetType } from '@/types/dataset'

/** 数据集类型 */
export interface DatasetOperationProps {
  className?: string
  onClick: (key: DatesetType) => any
}

const items = DATASET_TYPE_MAP.keys.map(v => ({
  key: v,
  label: DATASET_TYPE_MAP.entities[v],
  disabled: DATASET_TYPE_DISABLEDS.includes(v)
}))

/**
 * 数据集操作区
 * @param props
 * @returns
 */
function DatasetOperation(props) {
  const { className, onClick } = props


  const [canAdd] = usePermissions(['/app/indices/dataset/add'])
  // const handleSelect = data => {
  //   onClick?.(data.key)
  // }

  return (
    <div className={classNames('dataset-manage-option', className)}>
      {/* <Dropdown trigger={['click']} overlay={<Menu onClick={handleSelect} items={items} />} placement='bottom'> */}
      {canAdd && <Button type='primary' onClick={() => onClick?.('sql')} icon={<PlusOutlined />}>新建{DS_PAGE_NAME}</Button>}
      {/* </Dropdown> */}
    </div>
  )
}

export default DatasetOperation
