import './index.less'

import { LoadingOutlined } from '@ant-design/icons'
import type { FormInstance } from 'antd'
import { Button, Form, Input, Modal, Select, Table } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import React, { lazy, Suspense } from 'react'

// import SqlEditor from '@/components/code-editor/sql-editor'
import WithDynamicTableSize from '@/components/with-dynamic-table-size'
import { selectSqlType } from '@/consts/dataset'

import { SqlVariableStateType } from '../../containers/editor'
import type { SqlVariableDataType } from '../../containers/editor/index'

const SqlEditor = lazy(() => import('@/components/code-editor/sql-editor'))

interface SqlVariableProps {
  key: React.Key
  datasetVariableTable: SqlVariableDataType[] // 获取表格渲染数据
  form: FormInstance
  getVariableSql: () => string
  onRefresh: () => void
  status: SqlVariableStateType
  className?: string
}

/**
 * 渲染sql字符串数据表格
 * @param props
 * @returns
 */
export default function SqlVariable(props: SqlVariableProps) {
  const {
    datasetVariableTable,
    form,
    getVariableSql,
    onRefresh,
    status,
    className,
    key
  } = props

  const columns: ColumnsType<SqlVariableDataType> = [
    {
      title: '序号',
      dataIndex: 'key',
      key: 'key'
    },
    {
      title: '变量名称',
      dataIndex: 'varitableName',
      key: 'varitableName'
    }, {
      title: '变量类型',
      dataIndex: 'varitableType',
      key: 'varitableType',
      render: ($, record) => (
        <Form.Item name={[record.varitableName, 'type']}>
          <Select defaultValue='string' placeholder='选择类型' options={selectSqlType} />
        </Form.Item>
      )
    },
    {
      title: '默认值',
      dataIndex: 'defaultValue',
      key: 'defaultValue',
      render: ($, record) => (
        <Form.Item name={[record.varitableName, 'value']}>
          <Input defaultValue='' />
        </Form.Item>
      )
    }
  ]

  const renderSqlTable = (dataSource: SqlVariableDataType[]) => (
    <WithDynamicTableSize standardDefaultHeight={50} >
      {({ containerHeight, scroll, ref }) => (
        <div ref={ref} style={{ height: containerHeight - 50 }} className='drawer-container'>
          <Table
            dataSource={dataSource}
            columns={columns}
            size='middle'
            scroll={scroll}
            pagination={{
              size: 'default',
              showSizeChanger: true,
              total: dataSource.length,
              showTotal: total => `共 ${total} 条`
            }}
          />
          <div className='variable-container-btn'>
            <Button onClick={() => {
              status.sqlContentVariable = getVariableSql()
              status.opeDrawer = true
            }}>
              解析SQL
            </Button>
            <Button onClick={onRefresh}>
              刷新
            </Button>
          </div>
          <Modal
            width='80%'
            title='SQL解析结果'
            footer={null}
            centered
            closable
            onCancel={() => status.opeDrawer = false}
            open={status.opeDrawer}
            getContainer={false}
          >
            <Suspense fallback={<LoadingOutlined />}>
              <SqlEditor value={status.sqlContentVariable} options={{ readOnly: true }} />
            </Suspense>
          </Modal>
        </div>
      )
      }
    </WithDynamicTableSize >
  )
  return (
    <div className={className} key={key}>
      <Form form={form}>
        {renderSqlTable(datasetVariableTable)}
      </Form>
    </div>
  )
}
