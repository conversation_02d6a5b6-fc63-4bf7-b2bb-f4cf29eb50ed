@base-padding: 16px;

.dataset-editor {
  // position: relative;

  .ant-drawer-body {
    padding: 12px 15px;
    padding-top: 0;
  }
  .ant-drawer-header {
    padding: 12px;
    background-color: lighten(#444, 2%);
    box-shadow: 1px 2px 4px rgba(#333, 12%);
    border-radius: 0 0 4px 4px;
    .title,
    .anticon-close {
      color: #fff;
      text-shadow: 0 0 2px rgba(#333, 12%);
    }
  }

  .dataset-editor-hint-box {
    opacity: 0.75;
    font-size: 13px;
    color: rgba(#fff, 0.75);
  }

  .ant-modal-footer {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 8px 16px;
  }

  .ant-drawer-close {
    position: relative !important;
    left: 0;
    right: auto;
    padding: 1px 6px;
  }

  .ant-drawer-extra {
    text-align: right;
  }

  .ant-modal-body {
    padding-top: 9px;
    padding-bottom: 9px;
    overflow: hidden;
  }

  &-hint-box {
    color: #666;
  }

  &-content {
    display: flex;
    height: calc(95vh - 90px);
    // overflow-y: auto;
    // overflow-x: hidden;
  }

  &-leftside {
    padding-top: 5px;
    padding-right: 16px;
    width: 300px;
    flex-shrink: 0;
    // overflow: auto;
    border-right: 1px solid var(--border-color-base);
  }

  &-rightside {
    padding-top: 5px;
    padding-left: 16px;
    width: calc(100% - 100px);
    overflow-x: hidden;
    overflow-y: auto;
    .ant-form-item {
      margin-bottom: 12px;
    }
  }

  &-btn-new-config-box {
    margin-bottom: 16px;
  }

  .preview-edit-rows {
    display: inline-flex;
    align-items: center;
    margin: 4px;
    color: #666;
  }
  .preview-edit-rows-btn {
    margin-left: 6px;
    cursor: pointer;
    color: @primary-color;
  }

  .preveiw-rows-tips {
    margin-left: 8px;
    font-size: 12px;
  }

  .dataset-data-origin-tables {
    height: calc(95vh - 260px);
  }

  .data-flow-panel {
    height: 400px;
    cursor: move;
  }

  .dataset-editor-field-tabs {
    .ant-tabs-nav {
      margin: 0;
    }
    .ant-tabs-tab {
      margin-left: 12px !important;
      padding: 4px 10px !important;
      &:first-of-type {
        margin: 0 !important;
      }
    }
  }

  .dataset-editor-footer {
    position: fixed;
    bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    left: 0;
    right: 0;
    z-index: 999;
  }
}

.editor-confirm-modal {
  .ant-modal-body {
    padding-top: 32px;
    padding-bottom: 32px;
  }
}
