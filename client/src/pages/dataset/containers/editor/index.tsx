/* eslint-disable no-template-curly-in-string */
import './index.less'

import { EditOutlined, LoadingOutlined } from '@ant-design/icons'
import { useDebounceFn, useMemoizedFn, useReactive, useSafeState } from 'ahooks'
import { <PERSON><PERSON>, But<PERSON>, Drawer, Form, Input, message, Modal, Popover, Space, Spin, Tabs, Tooltip, Typography } from 'antd'
import classNames from 'classnames'
import isEqual from 'fast-deep-equal'
import _ from 'lodash'
import React, { useEffect, useRef, useState } from 'react'

import withRefModal from '@/components/with-ref-modal'
import { DS_PAGE_NAME, getDbType, isTieke, LIMIT_MAX } from '@/consts/dataset'
import { Cloud } from '@/services'
import { useCommit, useModelState } from '@/stores/models/dataset'
import type { DatasetDetaiInfo, DatasetInfo, DatesetType } from '@/types/dataset'
import { claenSqlRun, previewSqlData } from '@/utils/query'

import type { DatasetConfigFormValue, SqlEditorInstance } from '../../components'
import { DatasetConfigForm, DatasetDataOrigin, DatasetDataPreview, DatasetEditorForm } from '../../components'
import DataFlow from '../../components/blood-kinship/new-flow'
import SqlVariable from '../../components/sql-variable'
import { useBloodKinship } from '../../hooks/use-blood-kinship'
import { checkIsExistCN, formatNewConfigs, getFormaterStr } from './utils'

// 保存row行数key值
const PREVIEW_ROW_KEY = 'PREVIEW_ROWS'

const { Text } = Typography

const defaultPreviewRows = Number(window.localStorage.getItem(PREVIEW_ROW_KEY)) || 10

export type SqlVariableStateType = {
  sqlContentVariable: string
  opeDrawer: boolean
}

export interface SqlVariableDataType {
  key: React.Key // 唯一id
  varitableName: string // 解析出来的变量名
}

export interface DatasetEditorProps {
  title?: string
  visible?: boolean
  groupId?: string
  type?: DatesetType
  value?: DatasetInfo | null
  onClose: () => any
}

/** 解析sql字符串 生成正常sql */
export const getSqlContent = (sql, compiled) => {
  const sqlContentTemplate = _.template(sql)
  const sqlContentStr = sqlContentTemplate(compiled)
  return sqlContentStr
}

/** 处理模板变量数据 */
export const handleTemplateVariable = variables => {
  const cloneVariables = _.cloneDeep(variables)

  _.forEach(cloneVariables, (item, index) => {
    const { type: varType, value: varValue } = item
    const stringValue = _.toString(varValue)

    if (varType === 'string' || varType === 'date') {
      // 去掉所有的单引号和双引号重新添加
      cloneVariables[index].value = stringValue.replace(/['"]/g, '')
    }
    if (varType === 'number') {
      cloneVariables[index].value = _.toNumber(stringValue)
    }
    if (varType === 'listString') {
      cloneVariables[index].value = stringValue.split(',').map(v => `'${v.replace(/['"]/g, '')}'`)
    }
    if (varType === 'listNumber') {
      cloneVariables[index].value = stringValue.split(',').map(v => v.replace(/['"]/g, ''))
    }
  })

  return _.mapValues(cloneVariables, item => item.value)
}

/**
 * 数据集编辑器
 * @param props
 * @returns
 */
function DatasetEditor(props) {
  const { title = `编辑${DS_PAGE_NAME}`, visible, type, groupId, value, onClose } = props
  const [datasetForm] = Form.useForm()
  const [configForm] = Form.useForm()
  const [rowForm] = Form.useForm()
  const [sqlVariableFrom] = Form.useForm()
  const datasetCommit = useCommit()
  const datsetDataInfo = useModelState(s => s, isEqual)
  const [activeTabKey, setActiveTabKey] = useSafeState('datasetConfig')
  // 数据源选中项
  const dataOriginConfig = useReactive<{ connectId: null | number; dbId: null | number; dbType: string }>({
    connectId: null,
    dbId: null,
    dbType: ''
  })

  // sql变量状态
  const sqlVariableState = useReactive<SqlVariableStateType>({
    sqlContentVariable: datasetForm.getFieldValue('sqlContent'),
    opeDrawer: false
  })

  const { state: bloodKinshipState, onRefresh } = useBloodKinship()

  // sql编辑器实例
  const sqlEditorRef = useRef<SqlEditorInstance | null>(null)
  // 缓存sql脚本，sql编辑值变更时判断是否需要先执行运行
  const cacheSqlContentRef = useRef<string>('')

  const isAlreadyRunRef = useRef(false)
  // 切换tabs时，是否获取数据预览数据
  const isGetPreviewDataRef = useRef(true)

  // 预览数据
  const previewData = useReactive({ openPopover: false, loading: false, rows: defaultPreviewRows, dataSource: [] })

  // 用于初始化配置列表单数据
  const [configFormValues, setConfigFormValues] = useSafeState<DatasetConfigFormValue[]>([])

  // 用于保存table表格配置列
  const [tableColumns, setTableColumns] = useSafeState<DatasetConfigFormValue[]>([])

  // 是否变更数据
  const isChangeDataRef = useRef(false)

  // 编辑时缓存初始化的列配置
  const cacheInitColumns = useRef<DatasetConfigFormValue[]>([])
  const [saveLoading, setSaveLoading] = useSafeState(false)
  const [loading, setLoading] = useSafeState(false)
  const [sqlLoading, setSqlLoading] = useSafeState(false)
  const [sqlVariableKey, setSqlVariableKey] = useSafeState(Math.random())
  const [sqlError, setSqlError] = useState<string | undefined>(undefined)

  const cancelRef = useRef<any>()

  const { run: forceRefresh } = useDebounceFn(() => {
    setSqlVariableKey(Math.random())
  }, {
    wait: 1000
  })

  const isAutoVariable = useMemoizedFn(() => _.isEmpty(sqlVariableFrom.getFieldsValue()))

  useEffect(() => {
    if (visible) {
      sqlVariableFrom.setFieldsValue(value?.sqlVariable)
    }
  }, [visible, sqlVariableFrom, value?.sqlVariable, setActiveTabKey])

  useEffect(() => {
    if (!visible) {
      setActiveTabKey('datasetConfig')
      setSqlError(undefined)
      setSqlLoading(false)
    }
  }, [visible])

  const queryDatasetSql = useMemoizedFn(async ({ dbId, sqlContent, rows }) => {
    try {
      previewData.loading = true
      const maxRow = Number(rows) > LIMIT_MAX ? LIMIT_MAX : Number(rows)
      const variables = await sqlVariableFrom.validateFields()
      if (!_.isEmpty(variables)) {
        const templateVariable = handleTemplateVariable(variables)
        sqlContent = getSqlContent(sqlContent, templateVariable)
      }
      setSqlError(undefined)
      const params = {
        sql: sqlContent,
        dbId,
        execId: Math.random().toString(32).slice(2),
        maxRow,
        isReturnFields: 1
      }
      cancelRef.current = params
      const res = await previewSqlData(params, cancelRef.current.execId)
      // console.log('res', res)
      cancelRef.current = undefined
      if (res?.code !== 200) {
        message.error(res?.message)
        return []
      }
      previewData.dataSource = res?.data?.data || []
      setSqlError(undefined)

      return res?.data?.fields || []
    } catch (error: any) {
      console.error(error)
      setSqlError(_.replace(error.data?.message || '', '接口请求失败', ''))
      // message.error(error?.message || 'SQL 解释失败')
      return []
    } finally {
      previewData.loading = false
      isGetPreviewDataRef.current = false
      cancelRef.current = undefined
    }
  })

  /* 获取解析后的SQL */
  const getVariableSql = useMemoizedFn(() => {
    const sqlVariable = sqlVariableFrom.getFieldsValue()
    const sqlContentStr = datasetForm.getFieldsValue()?.sqlContent
    const variables = _.isEmpty(sqlVariable) ? value?.variable : sqlVariable
    return getSqlContent(sqlContentStr, handleTemplateVariable(variables))
  })

  const updatedInit = useMemoizedFn(async () => {
    setLoading(true)
    try {
      const connectDict = datsetDataInfo.connectionMap.entities
      const keys = datsetDataInfo.connectionMap.keys
      datasetForm?.setFieldsValue({ title: value?.title, sqlContent: value?.sqlContent })
      cacheSqlContentRef.current = value?.sqlContent
      _.forEach(keys, async k => {
        const connectItem = connectDict[k]
        const findDb = _.find(connectItem.databaseAndSchemaList, dbItem => dbItem.id === value.dbId)
        if (!findDb) return
        await datasetCommit('dataBase', { id: k })
        await datasetCommit('table', { id: findDb?.id })
        dataOriginConfig.connectId = Number(k)
        dataOriginConfig.dbId = value?.dbId
        dataOriginConfig.dbType = value?.showType
      })

      /** 获取数据集列配置 */
      const res = await datasetCommit('asyncGetDatasetDetails', { datasetId: value?.id, groupId: value?.groupId })
      const configList = _.map(_.uniqBy(res || [], 'name'), (v: any) => ({ ...v, key: v.id })) || []
      cacheInitColumns.current = configList as DatasetConfigFormValue[]
      isAlreadyRunRef.current = true
      setConfigFormValues(configList)
      setTableColumns(configList)
    } catch (error: any) {
      message.error(error.message || '初始化配置失败')
    } finally {
      setLoading(false)
    }
  })

  useEffect(() => {
    if (!value) return
    setTimeout(() => {
      // 延迟加载，动画卡
      updatedInit()
    }, 360)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value])

  const resetForm = useMemoizedFn(() => {
    datasetForm?.resetFields()
    configForm?.resetFields()
    setConfigFormValues([])
    setTableColumns([])
    previewData.dataSource = []
    cacheInitColumns.current = []
    cacheSqlContentRef.current = ''
    setLoading(false)
    setSaveLoading(false)
    dataOriginConfig.connectId = null
    dataOriginConfig.dbId = null
    dataOriginConfig.dbType = ''
    isGetPreviewDataRef.current = true
    isChangeDataRef.current = false
    sqlVariableState.opeDrawer = false
  })

  /** 关闭 */
  const handleClose = useMemoizedFn(() => {
    const data = datasetForm.getFieldsValue()
    if (!isChangeDataRef.current || !data?.sqlContent) {
      resetForm()
      onClose?.()
      return
    }

    Modal.confirm({
      title: '提示',
      content: '你填写的信息未保存，确认退出吗？',
      okText: '确定',
      centered: true,
      cancelText: '取消',
      className: 'editor-confirm-modal',
      onOk() {
        resetForm()
        onClose?.()
      }
    })
  })

  /** 运行操作 */
  const handleRunSql = useMemoizedFn(async () => {
    try {
      const datasetData = datasetForm.getFieldsValue()
      const { sqlContent } = datasetData
      if (!sqlContent) return message.warning('请输入sql代码')
      if (!dataOriginConfig?.dbId) return message.warning('请选择数据库')
      setSqlLoading(true)
      const sqlRes = await queryDatasetSql({
        dbId: dataOriginConfig.dbId,
        sqlContent,
        rows: previewData.rows
      })

      const hasCn = _.some(sqlRes, v => checkIsExistCN(v?.columnName))
      if (hasCn) {
        message.warning({ content: '解释的物理字段不应存在中文，请检查脚本中的别名或者表中的字段名否使用了中文' })
      }

      const list = formatNewConfigs(sqlRes, cacheInitColumns.current)
      setConfigFormValues(list)
      setTableColumns(list)
      isAlreadyRunRef.current = true

    } catch (error: any) {
      console.error(error)
      message.error(error.message)
    } finally {
      setSqlLoading(false)
    }
  })

  // 取消
  const handleClaenSql = useMemoizedFn(async () => {
    if (cancelRef.current) {
      Cloud.$cancel(cancelRef.current.execId)
      setSqlLoading(false)
      claenSqlRun(cancelRef.current)
      message.info('已取消 SQL 执行')
    }
  })

  /** 保存操作 */
  const handleSave = useMemoizedFn(async () => {
    try {
      const datasetRes = await datasetForm.validateFields()
      if (_.isEmpty(datasetRes) || _.isNull(datasetRes)) {
        return
      }
      const configRes = await configForm.validateFields()
      let variables = await sqlVariableFrom.validateFields()
      if (_.isEmpty(variables)) variables = null
      const hasCn = _.some(configRes?.datasetConfig || [], v => checkIsExistCN(v?.name))

      if (hasCn) {
        message.warning({ content: ` ${DS_PAGE_NAME}配置中的物理字段不支持中文，请检查脚本中的别名或者表中的字段名否使用了中文` })
        return
      }
      if (_.isEmpty(configRes?.datasetConfig)) {
        message.warning({ content: '请先运行sql代码，再点击保存' })
        return
      }
      // if (!isAlreadyRunRef.current) {
      //   message.warning({ content: '请先运行sql代码，再点击保存' })
      //   return
      // }
      setSaveLoading(true)

      const connectDict = datsetDataInfo.connectionMap.entities
      const databaseMap = datsetDataInfo.databaseMap.entities
      // 还有把 connectId 存起来
      const connectId = databaseMap[dataOriginConfig.dbId || '']?.connectId || ''
      // 保存时修正 dbType
      const dbType = connectDict[connectId] ? getDbType(connectDict[connectId]?.type as any) : dataOriginConfig.dbType

      // 是否新增
      const isAdd = !value
      const dataset = {
        ...datasetRes,
        updatedAt: new Date(),
        groupId: value?.groupId || groupId,
        type,
        dbId: dataOriginConfig.dbId,
        dbType: dbType || dataOriginConfig.dbType,
        sqlVariable: variables
      }

      // 新增或修改
      await datasetCommit(isAdd ? 'asyncAddDatasetAndConfig' : 'asyncUpdatedDatasetAndConfig', {
        dataset,
        newConfigs: _.map(configRes.datasetConfig, v => _.omit(v, ['key'])) as DatasetDetaiInfo[],
        cacheConfigs: _.map(cacheInitColumns.current || [], v => _.omit(v, ['key'])) as DatasetDetaiInfo[],
        datasetId: value?.id
      } as any)
      resetForm()
      setTimeout(() => {
        datasetForm.resetFields()
        onClose?.()
      }, 0)
    } catch (error: any) {
      message.error(error.message)
    } finally {
      setSaveLoading(false)
    }
  })

  /** 选择数据源 */
  const onSelectConnectId = useMemoizedFn((val, node) => {
    dataOriginConfig.connectId = val
    dataOriginConfig.dbType = getDbType(node?.pId)
    dataOriginConfig.dbId = null
    datasetCommit('dataBase', { id: val })
  })

  /** 选择数据库 */
  const onSelectDb = useMemoizedFn(data => {
    dataOriginConfig.dbId = data
    datasetCommit('table', { id: data })
  })

  /** 加载列配置 */
  const onLoadColumnData = useMemoizedFn(async data => {
    if (data.loadType === 'field') datasetCommit('field', { id: data.id })
  })

  const handleOpenPopvoerChange = useMemoizedFn((newOpen: boolean) => {
    if (newOpen) {
      rowForm.setFieldValue('rows', previewData.rows)
    }
    previewData.openPopover = newOpen
  })

  const handleClosePopover = useMemoizedFn(() => {
    previewData.openPopover = false
  })

  const handleSureRows = useMemoizedFn(async () => {
    const data = rowForm.getFieldsValue()
    const row = Number(data?.rows)
    if (row > 10000) {
      message.warning('最大支持显示10,000条数据')
      return
    }
    const datasetData = datasetForm.getFieldsValue()
    const { sqlContent } = datasetData
    previewData.rows = row
    window.localStorage.setItem(PREVIEW_ROW_KEY, String(row))
    handleClosePopover()
    if (!sqlContent) return
    await queryDatasetSql({ dbId: dataOriginConfig.dbId, sqlContent, rows: row })
  })

  const handleDbClick = useMemoizedFn(data => {
    sqlEditorRef.current?.autoAddNewValue(data?.realName)
  })

  /**
   * 当列配置变更时同步更新预览数据列配置
   */
  const handleConfigValueChange = (data: any) => {
    isChangeDataRef.current = true
    isGetPreviewDataRef.current = true
    const list = data?.datasetConfig || []
    setTableColumns(list.map(v => ({ ...v, dataFormater: getFormaterStr(v) }))?.filter(v => v?.status === 'show'))
  }

  const handleDatasetBasicChange = (data: any) => {
    isChangeDataRef.current = true
    const { sqlContent = '' } = data
    const cacheSqlStr = cacheSqlContentRef.current.replace(/\s/g, '')
    const newSqlStr = sqlContent.replace(/\s/g, '')
    if (cacheSqlStr !== newSqlStr) {
      isAlreadyRunRef.current = false
    }
  }

  const handleRefresh = () => {
    if (!dataOriginConfig?.connectId || !dataOriginConfig.dbId) {
      return
    }
    const datasetData = datasetForm.getFieldsValue()
    const { sqlContent } = datasetData
    const fileTypeMap = configFormValues.reduce((r, v) => {
      r[v.name] = v.dataType
      return r
    }, {})
    onRefresh(
      fileTypeMap,
      sqlContent?.split(';') || [],
      dataOriginConfig.dbType,
      dataOriginConfig?.connectId?.toString(),
      _.get(datsetDataInfo?.databaseMap.entities, [dataOriginConfig.dbId, 'dbName'])
    )
  }

  const handelChangeTab = (key: string) => {
    setActiveTabKey(key)
    if (key === 'blood-kinship') {
      handleRefresh()
    }
    if (key === 'dataPreview') {
      if (!isGetPreviewDataRef.current) return
      const datasetData = datasetForm.getFieldsValue()
      const { sqlContent } = datasetData
      if (!dataOriginConfig?.dbId) return
      if (!sqlContent) return
      queryDatasetSql({
        dbId: dataOriginConfig.dbId,
        sqlContent,
        rows: previewData.rows
      })
    }
  }

  /**
   * 解析出sql 模板字符串 生成一个sql表格
   * @return DataType[]
   */
  const getTemplateVariable = () => {
    const sql = datasetForm.getFieldValue('sqlContent')
    if (!sql) return []
    // 获取${}中的内容 通过正则匹配 生成DataType[]数据
    const regExp = /\${(.*?)}/g
    const matchList = sql.match(regExp) || []
    const variableList = matchList.map((item, index) => ({
      key: index,
      varitableName: item.replace('${', '').replace('}', '')
    }))
    // 解析出来的变量名称数组
    const varitableList = _(variableList)
      .map(item => item.varitableName)
      .uniq()
      .map((item, index) => ({
        key: index + 1,
        varitableName: item
      }))
      .value()
    return varitableList as SqlVariableDataType[]
  }

  return (
    <Drawer
      title={(
        <div className='flex-row vcenter'>
          <h4 className='mb-0 mr-6 flex-1 title'>{title}</h4>
          <div className='dataset-editor-hint-box' onDragOver={e => e.preventDefault()}>
            把业务相关的事实明细表，以及所需的统一维度表关联起来，编织成一个逻辑的数据视图（系统不保存物理明细数据）
          </div>
        </div>
      )}
      width='100%'
      className='dataset-editor'
      // onCancel={handleClose}
      onClose={handleClose}
      open={visible}
      getContainer={() => document.getElementById('abi-app-modal') || document.body}
      // cancelText='取消'
      // okText='确定'
      // onOk={handleSave}
      // centered
      placement='bottom'
      height='95%'
      destroyOnClose
      maskClosable={false}

    // confirmLoading={saveLoading || sqlLoading}
    >
      <Spin spinning={loading} indicator={<LoadingOutlined />}>

        <div className='dataset-editor-content' onDragOver={e => e.preventDefault()}>

          <div className='dataset-editor-leftside'>
            <DatasetDataOrigin
              treeData={datsetDataInfo}
              onSelectConnectId={onSelectConnectId}
              onSelectDb={onSelectDb}
              connectId={dataOriginConfig.connectId}
              dbId={dataOriginConfig.dbId}
              onDoubleClick={handleDbClick}
              onLoadColumnData={onLoadColumnData}
            />
          </div>

          <div className='dataset-editor-rightside'>
            <DatasetEditorForm
              form={datasetForm}
              onRunSql={handleRunSql}
              sqlEditorRef={sqlEditorRef}
              onChange={() => forceRefresh()}
              onValuesChange={handleDatasetBasicChange}
              sqlLoading={sqlLoading}
              onClean={handleClaenSql}
            />

            {sqlError &&
              <Alert message={sqlError} type='error' className='mb-3' closable afterClose={() => setSqlError(undefined)} />
            }

            <Tabs
              activeKey={activeTabKey} defaultActiveKey='datasetConfig' onChange={handelChangeTab}
              className='dataset-editor-field-tabs'
            >
              <Tabs.TabPane tab='数据预览' key='dataPreview'>
                <div className='preview-edit-rows' title=''>
                  <Tooltip title='清空浏览器缓存将导致设置被重置' color='white' overlayInnerStyle={{ color: '#333' }}>
                    <span>显示{previewData.rows}行</span>
                  </Tooltip>
                  <Popover
                    content={
                      <Form form={rowForm}>
                        <Space direction='vertical'>
                          <span>显示行数</span>
                          <Form.Item name='rows' noStyle>
                            <Input type='number' placeholder='输入显示行数' max={10000} />
                          </Form.Item>

                          <Space>
                            <Button size='small' onClick={handleClosePopover}>
                              取消
                            </Button>
                            <Button size='small' type='primary' onClick={handleSureRows}>
                              确定
                            </Button>
                          </Space>
                        </Space>
                      </Form>
                    }
                    trigger='click'
                    open={previewData.openPopover}
                    onOpenChange={handleOpenPopvoerChange}
                  >
                    <EditOutlined className='preview-edit-rows-btn' />
                  </Popover>
                </div>

                <DatasetDataPreview
                  loading={previewData.loading}
                  columns={tableColumns}
                  dataSource={previewData.dataSource}
                />
              </Tabs.TabPane>
              <Tabs.TabPane tab='字段配置' key='datasetConfig'>
                {/* <div className='dataset-editor-btn-new-config-box'>
                  <Button type='primary' disabled>
                    新建计算字段
                  </Button>
                </div> */}

                <DatasetConfigForm
                  formValues={configFormValues}
                  form={configForm}
                  onConfigValueChange={handleConfigValueChange}
                />
              </Tabs.TabPane>
              {!isTieke && (
                <Tabs.TabPane tab='数据关系' key='blood-kinship'>
                  <div className='data-flow-panel'>
                    <DataFlow
                      data={bloodKinshipState.chartData}
                      onRefresh={handleRefresh}
                      indicesNameMap={bloodKinshipState.indicesNameMap}
                    />
                  </div>
                </Tabs.TabPane>
              )}
              <Tabs.TabPane tab='SQL变量' key='sqlVariable' forceRender>
                <SqlVariable
                  className={classNames({
                    'hidden': isAutoVariable()
                  })}
                  key={sqlVariableKey}
                  onRefresh={() => setSqlVariableKey(Math.random())}
                  form={sqlVariableFrom}
                  datasetVariableTable={getTemplateVariable()}
                  getVariableSql={getVariableSql}
                  status={sqlVariableState}
                />
                {isAutoVariable() && (
                  <pre className='mt-2'>
                    <h4>SQL变量使用示例</h4>
                    SELECT<br />
                    {' '}name, --SELECT查询的字段即为API返回参数<br />
                    {' '}addr as address, --如果定义了字段别名，则返回参数名称为字段别名<br />
                    {' '}sum(num) as total_num, --支持SQL函数<br />
                    FROM table_name<br />
                    WHERE<br />
                    user_id ={'${uid}'}; --WHERE条件中的参数为API请求参数，参数格式为：<Text mark>{'${参数名}'}</Text>
                  </pre>
                )}
              </Tabs.TabPane>
            </Tabs>
          </div>
        </div>
      </Spin>

      <footer className='dataset-editor-footer'>
        <Button onClick={handleClose} className='mr-4'>取消</Button>
        <Button onClick={handleSave} type='primary' loading={saveLoading || sqlLoading}>保存</Button>
      </footer>
    </Drawer>
  )
}

export default withRefModal<DatasetEditorProps>(DatasetEditor)
