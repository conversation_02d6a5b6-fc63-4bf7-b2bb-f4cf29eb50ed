import { format as d3Format } from 'd3-format'
import dayjs from 'dayjs'
import _ from 'lodash'
import { nanoid } from 'nanoid'

import { DEFAULT_DATA_FORMATER_MAP, formatSqlType } from '@/consts/dataset'
import type { ColumnType, DatasetDetaiInfo, QuerySqlFieldsInfo } from '@/types/dataset'

import type { DatasetConfigFormValue } from '../../components/config-form'

export const getFormaterStr = ({
  showFormat = '',
  dataType = 'string',
  isShowPermil = false,
  decimal = 0
}: {
  showFormat: string
  dataType: ColumnType
  isShowPermil: boolean
  decimal: number
}) => {
  if (!dataType || dataType === 'string') return ''
  if (dataType === 'date') return showFormat
  return `${isShowPermil ? ',' : ''}.${decimal}${showFormat}`
}

// 格式化帮助函数
export const formatMemo = _.memoize(d3Format)

/**
 * 获取数据集数据格式化函数
 * @param {string} type 'string' | 'number' | 'date' 数据类型
 */
export const getDatasetFormatFunc = (type: string) => {
  const map = {
    string: (val: any) => val,
    number: (val: any, specifier: string) => formatMemo(specifier)(val),
    date: (val: any, specifier: string) => dayjs(val).format(specifier)
  }
  if (map?.[type]) return map?.[type]
  return (val: any) => val
}

/**
 * 格式化保存的配置数据
 * @param newConfigs 配置表单数据，新增或修改的数据
 * @param isAdd 是否新增
 * @param cacheConfigs 编辑的数据，用于对比要删除的数据
 * @param editor 编辑人信息
 * @returns
 */
export const formatSubmitDataDetails = ({ newConfigs, isAdd, cacheConfigs, editor }) => {
  let res = _.map(newConfigs, item => ({
    ...editor,
    type: 'physical',
    ..._.omit(
      item,
      item?.id
        ? ['showFormat', 'isShowPermil', 'decimal', 'seq']
        : ['showFormat', 'isShowPermil', 'decimal', 'id', 'seq']
    ),
    dataFormater: getFormaterStr(item),
    updatedAt: new Date()
  }
  ))
  if (!isAdd) {
    const resKeys = _.map(res, v => v?.id).filter(Boolean)
    const deleteList = _.isEmpty(resKeys) ? cacheConfigs : _.filter(cacheConfigs, item => !resKeys.includes(item?.id))
    res = _.concat(
      res,
      deleteList.map(v => ({ id: v?.id, deleteAt: new Date() }))
    )
  }

  return res
}

/**
 * 格式化新增的配置数据
 * @param list sql字段配置
 * @returns
 */
export const formatNewConfigs: (
  list: QuerySqlFieldsInfo[],
  cacheConfigs: DatasetDetaiInfo[]
) => DatasetConfigFormValue[] = (list = [], cacheConfigs = []) => {
  const dict = _.keyBy(cacheConfigs, 'name')
  const res = _.map(list, v => {
    const dataType = formatSqlType(v?.columnType)
    const data = _.get(dict, v?.columnName)
    // 如果匹配上旧的就返回旧的。
    if (data) return data
    // 防止remarks返回null字符串
    const remarks = v?.remarks === 'null' ? null : v?.remarks
    return {
      title: remarks || v?.columnName,
      name: v?.columnName,
      dataType,
      status: 'show',
      key: nanoid(6),
      dataFormater: DEFAULT_DATA_FORMATER_MAP[dataType] || null,
      description: remarks
    }
  }) as DatasetConfigFormValue[]
  return res
}


/** 检查字段是否含有中文 */
export const checkIsExistCN = (str: string) => {
  if (!str) return false
  return /[\u4e00-\u9fa5]/g.test(str)
}
