import { useReactive } from 'ahooks'
import { message } from 'antd'
import _ from 'lodash'
import request from 'umi-request'

import { isTieke } from '@/consts/dataset'

import { FLOW_TABLE_TYPE } from '../components/blood-kinship/const'
import { IDiagramData, IDiagramLink, IDiagramTableNode } from '../components/blood-kinship/types'

/**
 * 获取模型血缘sql解析的结构
 */
export const getBloodKinshipCharData = async (sqls: string[], dbType: string, connectId: string, defaultDB: string) => {
  const res = await request.post('/app/database-manage-v1/sql-parser/lineage/simple', {
    data: {
      sqlList: sqls,
      dbType,
      connectId,
      defaultDB
    },
    timeout: 0
  })
  return res
}


/**
 * 模型血缘脚本
 */
interface ModelBloodKinshipInfo {
  id?: string
  /** 模型ID */
  modelId?: string
  /** sql名称 */
  name: string
  /** 脚本内容 */
  sql: string
}

interface ModelBloodKinshipState {
  data: ModelBloodKinshipInfo[]
  chartData?: IDiagramData
  indicesNameMap: { [key: string]: string }
}

/** 血缘接口返回的数据格式 */
interface ModelBloodKinShipData {
  tableName: string
  columns: {
    columnName: string
    from: {
      tableName: string
      columnName: string
    }[]
    aggs: string[]
  }[]
}

const genPosition = (idx: number) => {
  const count = _.floor(idx / 4)
  const x = 0 + idx * 300
  const y = -120 + count * 200
  return { x, y }
}

const getFieldType = (type: string, fieldName: string, fileTypeMap: any) => {
  if (type) return type
  return _.get(fileTypeMap, fieldName) === 'number' ? 'indices' : 'dimension'
}

/** 转换接口数据 */
const convertDataToFlowData = (data: ModelBloodKinShipData[], fileTypeMap: any, type?: string): IDiagramData => {
  const tables: { [key: string]: IDiagramTableNode } = {}
  const links: IDiagramLink[] = []
  data.forEach((item: any, i) => {
    if (_.isString(item)) {
      item = JSON.parse(item)
    }
    const tableName = item?.tableName || 'result'
    const fields: any[] = []
    item.columns.forEach(f => {
      const columnName = f.columnName?.replace('`', '')?.replace('"', '')
      fields.push({
        fieldName: columnName,
        fieldAlias: f?.columnAlias,
        type: getFieldType(f?.type, columnName, fileTypeMap)
      })
      f.from.forEach((ff, idx) => {
        const fromColumnName = ff.columnName?.replace('`', '')?.replace('"', '')
        const table: IDiagramTableNode = _.get(tables, [ff.tableName], {
          id: ff.tableName,
          name: ff.tableName,
          type: _.toLower(ff?.tableType || type),
          fields: [],
          order: i * 20 + 1 + idx,
          position: { x: 0, y: 0 }
        }) as IDiagramTableNode
        const feildIndex = _.findIndex(table.fields, p => p.fieldName === fromColumnName)
        if (feildIndex >= 0 && !_.isEmpty(f.aggs)) {
          _.set(table.fields, [feildIndex, 'aggs'], f.aggs.join(','))
        } else if (feildIndex === -1) {
          table.fields.push({
            fieldName: fromColumnName,
            fieldAlias: f?.columnAlias,
            aggs: f.aggs.join(','),
            type: getFieldType(f?.type, fromColumnName, fileTypeMap)
          })
        }
        tables[ff.tableName] = table
        links.push({
          target: tableName,
          targetKey: `in_${columnName}`,
          title: f.aggs.join(','),
          source: ff.tableName,
          sourceKey: `out_${fromColumnName}`
        })
      })
    })
    tables[tableName] = {
      id: tableName,
      name: tableName,
      type: FLOW_TABLE_TYPE.dataset,
      order: item?.tableName ? i * 20 : -1,
      fields: _.chain(_.get(tables, [tableName, 'fields'], []))
        .concat(fields)
        .unionBy(p => p.fieldName)
        .value(),
      position: { x: 0, y: 0 }
    }
  })

  return {
    id: 'data',
    tables: _.chain(tables)
      .values()
      .sortBy(p => p.order)
      .map((item, i) => ({
        ...item,
        position: genPosition(_.values(tables).length - i - 1),
        fields: _.sortBy(item.fields, p => p.fieldName)
      }))
      .value()
      .filter(p => p.fields?.length),
    links
  }
}
export function useBloodKinship() {
  const state = useReactive<ModelBloodKinshipState>({ data: [], indicesNameMap: {} })
  const onRefresh = async (
    fileTypeMap: { [key: string]: string },
    sqls: string[],
    type: string,
    connectId: string,
    defaultDB: string
  ) => {
    if (!sqls?.length) {
      state.chartData = undefined
    }

    // 铁科的不需要血缘
    if (isTieke) return

    const { data, message: msg, success } = await getBloodKinshipCharData(sqls, type, connectId, defaultDB)
    if (!success) {
      message.error(`sql解析失败,${msg}`)
      state.chartData = undefined
      return
    }
    let newData: IDiagramData = {}
    try {
      newData = convertDataToFlowData(data, fileTypeMap, type)
      const index = _.findIndex(newData.tables, p => p.name === 'result')
      const val = _.chain(_.get(newData, ['tables', index, 'fields'], []))
        .clone()
        .map(p => ({ ...p, type: _.get(fileTypeMap, p.fieldName) === 'number' ? 'indices' : 'dimension' }))
        .sortBy(p => p.type + p.fieldNamex)
        .value()
      _.set(newData, ['tables', index, 'fields'], val)
    } catch (error) {
      message.error('血缘分析数据解析失败')
      console.error('====>血缘分析数据解析失败', error)
    }
    state.chartData = newData
  }

  return {
    state,
    onRefresh
  }
}
