import { useSafeState } from 'ahooks'
import { useEffect } from 'react'

interface UseEditTableHeightParams {
  containerHeight?: number
  offsetTop?: number
}

/**
 * 用于动态修改视图编辑器中配置表格中的高度
 *@param params.containerHeight 容器全局高度, .dataset-editor-content类的height属性值80vh, 则当前值为0.8
 *@param params.offsetTop 当前表格距离页面顶部的距离
*/
export const useEditTableHeight = (params: UseEditTableHeightParams) => {
  const { containerHeight = 0.8, offsetTop = 453 } = params
  /** 默认1920*1080高度对应的表格高度 */
  const [tableHeight, setTableHeight] = useSafeState(304)

  useEffect(() => {
    const updateHeight = () => {
      const h = window.innerHeight * containerHeight - offsetTop
      setTableHeight(Math.floor(h))
    }

    updateHeight()

    window.addEventListener('resize', updateHeight)

    return () => {
      window.removeEventListener('resize', updateHeight)
    }
  }, [])

  return tableHeight
}
