.dataset-manage {
  width: 100%;
  height: 100vh;
  overflow: hidden;

  .action-item {
    display: flex;
    align-items: center;
    > :first-child {
      padding-left: 0;
    }
  }

  &-flex {
    display: flex;
    height: 100%;
  }

  &-leftside {
    flex-shrink: 0;
    border-radius: 4px;
    margin-left: 230px;
  }

  &-rightside {
    margin: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-radius: 4px;
  }

  &-rightside-header {
    flex-shrink: 0;
    box-shadow: 1px 2px 4px rgba(@primary-color, 0.04);
  }

  &-space {
    margin-bottom: 16px;
  }

  &-table-box {
    overflow-y: auto;
    background-color: @app-bg-color;
    border-radius: 4px;
    flex: 1;

    .design-filter-action-bar {
      border-radius: 0;
      box-shadow: 1px 2px 4px rgba(@primary-color, 0.04);
      min-height: 56px;

      .app-title {
        font-weight: bold;
        color: #444;
        font-size: 16px;
        margin: 0 4px;
        margin-left: 6px;
        min-width: 64px;
        display: inline-block;
      }
    }

    .ant-table-wrapper {
      padding-left: 12px;
      background-color: #fff;
      border-radius: 4px;
      margin: 16px;

      .ant-pagination {
        padding-right: 12px;
      }
      .ant-table-thead > tr > th {
        background-color: #fff;
      }
    }
  }
}
