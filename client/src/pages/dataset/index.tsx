import './index.less'

import { LoadingOutlined } from '@ant-design/icons'
import { FilterActionBar } from '@sugo/design'
import { fastMemo } from '@sugo/design/functions'
import { usePermissions } from '@sugo/design/hooks'
import { useMemoizedFn, useRequest } from 'ahooks'
import { Button, message, Modal, Popconfirm, Spin } from 'antd'
import dayjs from 'dayjs'
import _ from 'lodash'
import React, { lazy, Suspense, useMemo, useRef, useState } from 'react'

import BasicTable from '@/components/basic-table'
import Div from '@/components/div-wrap'
import WithDynamicTableSize from '@/components/with-dynamic-table-size'
import { ALL_GROUP, DS_PAGE_NAME } from '@/consts/dataset'
import { useNewBi } from '@/hooks/use-new-bi'
import { Service } from '@/services'
import { useCommit, useModelState } from '@/stores/models/dataset'
import { useModelState as useUserState } from '@/stores/models/user'
import type { DatesetType, GroupOperationType } from '@/types/dataset'

import type { DatasetGroupDataSourceItem } from './components'
import {
  DatasetGroup,
  DatasetGroupModalEditor,
  DatasetMoveGroupModal
} from './components'
import { CardList } from './components/cards'
// import DatasetEditor from './containers/editor'

const DatasetEditor = lazy(() => import('./containers/editor'))

/**
 * 数据集管理
 * @returns
 */
export const DatasetManage = fastMemo(() => {
  const currentDatasetType = useRef<DatesetType>('sql')
  const isAdmin = useUserState(s => s.type === 'built-in' || _.get(window, 'sugo.user.type') === 'built-in')

  const commit = useCommit()
  const {
    datasetGroupMap, datasetMap, activeGroupId, datasetTotal,
    connectionMap, current, pageSize,
    dataPermission, filter, loaded
  } = useModelState()
  const userInfo = useUserState()

  const { isNewBi } = useNewBi()

  const currentEditGroup = useRef<null | DatasetGroupDataSourceItem>(null)
  const cachePageSize = useRef<any>(pageSize)
  const editGroupRef = useRef<ModalDefaultRef>(null)
  const editorRef = useRef<ModalDefaultRef>(null)
  const datasetMoveGroupModalRef = useRef<ModalDefaultRef>(null)

  const [visible, setVisible] = useState(false)


  const [canEdit, canDelete, canCategory] = usePermissions([
    '/app/indices/dataset/edit',
    '/app/indices/dataset/delete',
    '/app/indices/dataset/category'
  ])

  const init = async () => {
    if (!userInfo?.id) return
    try {
      const [, list] = await Promise.all([
        commit('initDataPermission'),
        commit('asyncGetDatasetGrops', {})
      ])
      const groupId = _.get(list, '[0].id', '')
      commit('update', { activeGroupId: groupId })
      commit('asyncGetDatasets', {})
      commit('connection', {})
    } catch (error: any) {
      message.error({ content: error?.message || '初始化失败' })
    }
  }

  const { loading } = useRequest(init, {
    refreshDeps: [userInfo?.id]
  })

  // 新增分组
  const handleAddGroup = useMemoizedFn(() => {
    currentEditGroup.current = null
    editGroupRef.current?.show({ value: null })
  })

  // 编辑或删除分组
  const handleEditGroup = useMemoizedFn((data: DatasetGroupDataSourceItem, key: GroupOperationType) => {
    if (key === 'delete') {
      Modal.confirm({
        content: `确定删除${data?.title}码？`,
        centered: true,
        async onOk() {
          const res = await Service.Dataset.findOne({ where: { groupId: data?.id, deleteAt: { $eq: null } } })
          if (res) {
            message.error(`当前分组存在${DS_PAGE_NAME}，无法删除`)
            return
          }
          commit('asyncUpdateGroup', {
            id: data?.id,
            deleteAt: new Date()
          })
        }
      })
    }
    if (key === 'update') {
      currentEditGroup.current = data
      editGroupRef.current?.show({ value: { ...data } })
    }
  })

  // 选择分组
  const handleSelectGroup = useMemoizedFn((data: DatasetGroupDataSourceItem) => {
    // filterFormRef.current?.reset()
    commit('update', { activeGroupId: data?.id, filter: { ...filter, name: '', rangeDate: [] }, current: 1 })
    commit('asyncGetDatasets', {})
  })

  // 分组编辑保存
  const handleSaveGroup = useMemoizedFn(async (newTitle: string) => {
    const other: any = {}
    if (currentEditGroup.current?.id) {
      other.id = { $not: currentEditGroup.current?.id }
    }
    const res = await Service.DatasetGroup.findOne({
      where: {
        title: newTitle,
        ...other,
        deleteAt: { $eq: null }
      }
    })
    if (res || newTitle === ALL_GROUP.title) {
      message.warning({ content: '该分组名称已经存在，请重新填写' })
      return false
    }
    if (!currentEditGroup.current) {
      await commit('asyncAddGroup', {
        title: newTitle
      })
    } else {
      await commit('asyncUpdateGroup', {
        id: currentEditGroup.current?.id,
        title: newTitle
      })
    }
    return true
  })

  // 搜索数据集
  const handleSearchDataset = useMemoizedFn(async data => {
    await commit('update', { filter: data, current: 1 })
    commit('asyncGetDatasets', {})
  })

  // 搜索数据集重置操作
  const handleResetDataset = useMemoizedFn(() => {
    commit('update', { filter: { ...filter, name: '', rangeDate: [] }, current: 1 })
    // commit('asyncGetDatasets', {})
  })

  // 页码变化
  const handleChangePage = useMemoizedFn((page, size) => {
    if (cachePageSize.current === size) {
      commit('update', { current: page })
    } else {
      cachePageSize.current = size
      commit('update', { current: 1, pageSize: size })
    }
    commit('asyncGetDatasets', {})
  })

  // 新增数据集
  const handleAddDataset = useMemoizedFn((data?: DatesetType) => {
    currentDatasetType.current = data || 'sql'
    setVisible(true)
    setTimeout(() => {
      editorRef.current?.show({
        value: null,
        title: `新建${DS_PAGE_NAME}`
      })
    }, 100)
  })

  // 编辑数据集
  const handleEditDataset = useMemoizedFn(data => {
    const hasPermission = _.some(connectionMap.keys, k => {
      const item = connectionMap.entities?.[k] || {}
      return _.some(item?.databaseAndSchemaList || [], subItem => subItem.id === data.dbId)
    })
    if (!hasPermission && !isAdmin) {
      message.warning('数据源权限更新，当前用户暂未分配对应的数据源权限，请联系管理员！')
      return
    }
    currentDatasetType.current = data?.type
    setVisible(true)
    setTimeout(() => {
      editorRef.current?.show({
        value: { ...data },
        title: `编辑${DS_PAGE_NAME}`
      })
    }, 100)
  })

  // 删除
  const handleDeleteDataset = useMemoizedFn(data => {
    commit('asyncDeleteDataset', {
      datasetId: data.id,
      groupId: data?.groupId
    })
  })

  const handleCloseEditor = useMemoizedFn(() => {
    setVisible(false)
    editorRef.current?.hide()
  })

  const handleMoveGroup = useMemoizedFn(data => {
    datasetMoveGroupModalRef.current?.show(data)
  })

  const handleSaveMoveGroup = useMemoizedFn(async data => {
    await commit('asyncUpdatedDataset', data)
  })

  const allowManage = (id: string, createdBy?: string) => {
    if (dataPermission.isAdmin) return true
    if (createdBy === _.get(window, 'sugo.user.id')) return true
    if (dataPermission.configMap[id]?.auth === 'manage') return true
    return false
  }

  const columns: any = useMemo(
    () => [
      {
        title: '序号',
        dataIndex: '_index',
        width: 60
      },
      {
        title: `${DS_PAGE_NAME}名称`,
        dataIndex: 'title',
        width: 200
      },
      {
        title: '所属分组',
        dataIndex: 'groupId',
        width: 120,
        render(v) {
          return _.get(datasetGroupMap.entities, [v, 'title'], '--')
        }
      },
      {
        title: '最后修改',
        dataIndex: 'updatedAt',
        width: 120,
        render(text) {
          return dayjs(text).format('YYYY-MM-DD HH:mm:ss')
        }
      },
      {
        title: '创建人',
        dataIndex: 'createdByName',
        width: 80
      },
      {
        title: '操作',
        width: 120,
        render: (text, row) => (
          <div className='action-item'>
            {canCategory && (
              <Button type='link' onClick={() => handleMoveGroup(row)}>
                分组
              </Button>
            )}
            {(canEdit || allowManage(row.id, row.createdBy)) && (
              <Button type='link' onClick={() => handleEditDataset(row)}>
                编辑
              </Button>
            )}
            {(canDelete || allowManage(row.id, row.createdBy)) && (
              <Popconfirm title={`是否删除当前${DS_PAGE_NAME}？`} onConfirm={() => handleDeleteDataset(row)}>
                <Button type='link'>删除</Button>
              </Popconfirm>
            )}
          </div>
        )
      }
    ],
    [connectionMap, datasetGroupMap]
  )

  const datasetList = useMemo(
    () => datasetMap.keys.map((v, i) => ({ ...datasetMap.entities[v], _index: i + 1 + (current - 1) * pageSize })),
    [datasetMap.keys, pageSize, current]
  )

  const datasetMoveGroupOptions = useMemo(
    () =>
      datasetGroupMap.keys.map(v => ({
        label: datasetGroupMap.entities[v]?.title,
        value: datasetGroupMap.entities[v]?.id
      })),
    [datasetGroupMap.keys]
  )

  const groupOptions = useMemo(
    () => datasetGroupMap.keys.map(v => datasetGroupMap.entities[v] as DatasetGroupDataSourceItem),
    [datasetGroupMap.keys]
  )

  const datasetGroupId = useMemo(() => {
    if (activeGroupId !== ALL_GROUP.id) return activeGroupId
    const find = _.find(groupOptions, v => v.title === '默认分组')
    return find?.id
  }, [activeGroupId, groupOptions])

  return (
    <Spin spinning={loading} indicator={<LoadingOutlined />}>
      <Div className='dataset-manage'>
        <div className='dataset-manage-flex'>
          <div className='dataset-manage-leftside'>
            <DatasetGroup
              dataSource={groupOptions}
              selected={activeGroupId}
              onAddGroup={handleAddGroup}
              onSelect={handleSelectGroup}
              onEditGroup={handleEditGroup}
            />
          </div>
          <div className='dataset-manage-table-box'>
            <FilterActionBar
              value={filter}
              schemas={[
                { key: 'title', type: 'Text', text: <span className='app-title'>数据视图</span> as any, position: 'left' },
                { key: 'name', type: 'Search', wait: 500, trigger: 'change' },
                { key: 'rangeDate', type: 'RelativeTimeRangeSelect' },
                { key: 'reset', type: 'ResetButton', text: '重置' },
                { key: 'create', type: 'Button', preset: 'create', text: '新建数据视图', position: 'right' }
                // { key: 'mode', type: 'Segmented', preset: 'table-card-switch', position: 'right', hideFn: () => isNewBi }
              ]}
              onChange={handleSearchDataset}
              onClick={(e, key) => {
                e.stopPropagation()
                if (key === 'create') handleAddDataset('sql')
                if (key === 'reset') handleResetDataset()
              }}
            />
            {(filter.mode === 'card' || isNewBi) ?
              <CardList
                data={datasetList}
                groupMap={datasetGroupMap.entities}
                onCreate={handleAddDataset}
                filter={filter}
                loaded={loaded}
                pagination={{
                  current,
                  pageSize,
                  onChange: handleChangePage,
                  total: datasetTotal,
                  showTotal: () => `共 ${datasetTotal} 条`
                }}
                onDel={handleDeleteDataset}
                onEdit={handleEditDataset}
                onGroup={handleMoveGroup}
                canFn={item => ({
                  canCategory,
                  canEdit: canEdit || allowManage(item.id, item.createdBy),
                  cadDel: canDelete || allowManage(item.id, item.createdBy)
                })}
              /> :
              <div className='dataset-manage-rightside'>
                <WithDynamicTableSize standardDefaultHeight={24}>
                  {({ containerHeight, scroll, ref }) => (
                    <div style={{ height: containerHeight }} ref={ref}>
                      <BasicTable
                        columns={columns}
                        scroll={scroll}
                        dataSource={datasetList}
                        showSizeChanger
                        current={current}
                        pageSize={pageSize}
                        total={datasetTotal}
                        onChange={handleChangePage}
                      />
                    </div>
                  )}
                </WithDynamicTableSize>
              </div>
            }
          </div>
        </div>

        <DatasetGroupModalEditor
          title={currentEditGroup.current ? '编辑分组' : '新增分组'}
          ref={editGroupRef}
          onClose={() => editGroupRef.current?.hide()}
          onSave={handleSaveGroup}
        />

        <DatasetMoveGroupModal
          ref={datasetMoveGroupModalRef}
          options={datasetMoveGroupOptions}
          onSave={handleSaveMoveGroup}
        />
      </Div>

      {visible &&
        <Suspense fallback={<LoadingOutlined />}>
          <DatasetEditor
            onClose={handleCloseEditor}
            ref={editorRef}
            type={currentDatasetType.current}
            groupId={datasetGroupId}
          />
        </Suspense>
      }
    </Spin>
  )
})

export default DatasetManage
