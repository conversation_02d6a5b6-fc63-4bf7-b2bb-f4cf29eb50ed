
.feedback-page-detail {
  position: relative;
  color: #566;
  width: 400px;
  box-shadow: 0 0 6px rgba(#111, 0.12);
  border-radius: 4px;
  padding: 12px;
  height: 500px;
  max-height: calc(100vh - 100px);
  overflow-y: auto;
  background-color: #fff;
  z-index: 1100;

  > header {
    display: flex;
    align-items: center;
    padding-bottom: 8px;
    margin-bottom: 8px;
    margin-top: -12px;
    padding-top: 8px;
    border-bottom: 1px solid #f1f1f1;
    position: sticky;
    width: 100%;
    top: -12px;
    z-index: 1;
    background-color: #fff;
    .index {
      flex: 1;
      margin-left: 8px;
      font-size: 16px;
      font-weight: bold;
    }
    .time {
      color: #aaa;
      font-size: 14px;
      > .user {
        color: #333;
      }
    }
  }
  .box {
    margin-bottom: 10px;
    h4 {
      font-weight: bold;
      margin-bottom: 4px;
      color: #333;
    }
    img {
      max-width: 100%;
      display: block;
      border-radius: 4px;
      padding: 4px;
      border: 1px solid #f1f1f1;
      margin-bottom: 4px;
    }
    pre {
      margin: 0;
      font-family: inherit;
      word-wrap: break-word;
      white-space: pre-line;
      overflow-wrap: break-word;
    }
  }

  // 回复内容
  .reply-content {
    // background: linear-gradient(135deg, rgba(#c850c0, 0.1),rgba(#4158d0, 0.1));
    border: 1px solid  tint(@primary-color, 50%);
    border-radius: 4px;
    padding: 6px;
  }
  .reply-by {
    color: #aaa;
    font-size: 14px;
    margin-top: 2px;
    .reply-user {
      color: #333;
    }
  }

  .reply-panel {
    display: block;
    // height: 120px;
    position: sticky;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    background-color: tint(@primary-color, 99%);
    border: 1px solid #f1f1f1;
    padding: 12px;
    border-radius: 5px;
    box-shadow: 0 0 6px rgba(#111, 0.12);

    textarea {
      height: 100px;
    }

    .btns {
      margin: 0;
      margin-top: 8px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 100%;
      > button {
        margin-left: 12px;
      }
    }
  }

  .flex-center {
    display: flex;
    > div {
      margin-right: 16px;
      &:last-of-type {
        margin-right: 0;
      }
    }
  }
}
