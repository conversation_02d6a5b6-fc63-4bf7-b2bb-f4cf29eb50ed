
import './detail.less'

import { Tag } from '@sugo/design'
import { Button, Input, Spin } from 'antd'
import dayjs from 'dayjs'
import _ from 'lodash'
import React, { memo, useState } from 'react'
import Zoom from 'react-medium-image-zoom'

import { FEEDBACK_STATUS_DICT } from '@/consts/feedback'

export interface FeedbackDetailProps {
  data?: any
  onBack?: () => any
  groupTypeDict?: Record<string, { label: string, value: string, [key: string]: any }>
  loading?: boolean
  enableReply?: boolean
  onReply?: (data: { id: string, reply?: string, status: number }) => any
  getUserName?: (userId: string) => string
}

function _FeedbackDetail(props: FeedbackDetailProps) {
  const { data, groupTypeDict = {}, loading, enableReply, onReply, getUserName } = props
  const group = groupTypeDict[data?.groupType || '']?.label
  const statusItem = FEEDBACK_STATUS_DICT[data?.status || 0]

  const [reply, setReply] = useState<string | undefined>(undefined)

  const onSubmitReply = (status: number) => {
    if (!data?.id) return
    onReply?.({ id: data?.id, reply, status })
    setReply(undefined)
  }

  return (
    <Spin spinning={loading}>
      <div className='feedback-page-detail'>
        <header>
          {/* <CloseOutlined onClick={onBack} title='返回' /> */}
          <span className='index'>#{data?.index || 0}</span>
          <span className='time'>
            <span className='user'>{getUserName?.(data?.createdBy as string) || '未知'} </span>
            于 {dayjs(data?.createdAt).format('YYYY-MM-DD HH:mm:ss')} 提交
            {/* 创建于 {dayjs(data?.createdAt).format('YYYY-MM-DD HH:mm:ss')} */}
          </span>
        </header>

        <div className='box flex-center'>
          <div style={{ flex: 1 }}>
            <h4>关联菜单</h4>
            {data?.menuTitle || '无'}
          </div>
          <div>
            <h4>类型</h4>
            {group ?
              <Tag text={group} autoColor /> : '无'
            }
          </div>
          <div>
            <h4>状态</h4>
            <Tag text={statusItem.label} color={statusItem.color} />
          </div>
        </div>
        <div className='box'>
          <h4>反馈内容</h4>
          <pre>
            {data?.content || '无'}
          </pre>
        </div>
        <div className='box'>
          <h4>预期效果</h4>
          <pre>
            {data?.expect || '无'}
          </pre>
        </div>

        <div className='box'>
          <h4>提交人</h4>
          <span>{getUserName?.(data?.createdBy as string) || '未知'} </span>
          于 <span>{dayjs(data?.createdAt).format('YYYY-MM-DD HH:mm:ss')}</span> 提交
        </div>
        <div className='box'>
          <h4>相关图片</h4>
          {_.isEmpty(data?.images) ? '无' : ''}
          {_.map(data?.images, img => (
            <Zoom key={img} image={{ src: img }}
              defaultStyles={{
                overlay: { backgroundColor: 'rgba(1, 1, 1, 0.4)' },
                zoomImage: { borderRadius: 2 }
              }}
            />
          ))}
        </div>

        {(data?.status === -1 || data?.status === 2) &&
          <div className='box reply-box'>
            <h4>回复内容</h4>
            <pre className='reply-content'>
              {data?.reply || '无'}
            </pre>
            <div className='reply-by'>
              <span className='reply-user'>{getUserName?.(data?.replyBy as string) || '未知'} </span>
              于 {dayjs(data?.replyAt).format('YYYY-MM-DD HH:mm:ss')} 回复
            </div>
          </div>
        }

        {enableReply &&
          <div className='reply-panel'>
            <Input.TextArea
              value={reply}
              showCount
              autoFocus
              maxLength={1000}
              placeholder='回复内容，可选'
              onChange={e => setReply(e.target.value)}
            />
            <div className='btns'>
              <Button size='small' onClick={() => onSubmitReply(-1)} danger>不受理</Button>
              <Button size='small' type='primary' onClick={() => onSubmitReply(2)}>已处理</Button>
            </div>
          </div>
        }
      </div>
    </Spin>
  )
}

export const FeedbackDetail = memo(_FeedbackDetail)
