import { FeedBackForm as AppFeedbackForm } from '@sugo/design/dist/esm/components/app-feedback/form'
import { Modal } from 'antd'
import React, { memo, useEffect, useRef, useState } from 'react'

import withRefModal from '@/components/with-ref-modal'
import { FEEDBACK_GROUP_TYPE_OPTS } from '@/consts/feedback'
import { FileService } from '@/services/files'
import { useModelAction, useModelState } from '@/stores/models/feedback'

/**
 * 创建工单
 */
const _CreateFeedbackModal = withRefModal(props => {
  const { visible, modal } = props

  const actions = useModelAction()
  const formRef = useRef<any>()
  const menuData = useModelState(s => s.menuData || [])
  const [loading, setLoading] = useState(false)

  const onCancel = () => modal.hide()

  const onSubmit = async () => {
    try {
      setLoading(true)
      await formRef.current?.submit()
    } catch (err) {
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  const onFormSubmit = async data => {
    await actions.createFeedBack(data)
    setLoading(false)
    onCancel()
  }

  useEffect(() => {
    if (!visible) formRef.current?.cancel()
  }, [visible])

  return (
    <Modal
      title='创建工单'
      open={visible}
      onOk={onSubmit}
      onCancel={onCancel}
      confirmLoading={loading}
      bodyStyle={{ maxHeight: 550, overflowY: 'auto' }}
    >
      <AppFeedbackForm
        groupTypeOptions={FEEDBACK_GROUP_TYPE_OPTS}
        menuTreeData={menuData}
        hideButton
        treePanelStyle={{ width: 440, height: 360 }}
        ref={formRef}
        onSubmit={onFormSubmit}
        onUpload={async file => {
          const res = await FileService.upload(file)
          return res.path
        }}
      />
    </Modal>
  )
})

export const CreateFeedbackModal = memo(_CreateFeedbackModal)
