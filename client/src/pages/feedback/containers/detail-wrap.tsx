import React, { memo } from 'react'

import { FEEDBACK_GROUP_TYPE_DICT } from '@/consts/feedback'
import { useModelAction, useModelState } from '@/stores/models/feedback'

import { FeedbackDetail } from '../components/detail'

export const FeedbackDetailWrap = memo(() => {
  const { detail, detailLoading, enableReply, allUserDict } = useModelState()
  const actions = useModelAction()

  const onCleanDetail = () => {
    actions.update({ detail: undefined, enableReply: false })
    actions.queryList({})
  }

  const getUserName = userId => {
    const user = allUserDict[userId]
    return user?.first_name || user?.username
  }

  if (!detail) return null

  return (
    <FeedbackDetail
      data={detail}
      loading={detailLoading}
      enableReply={enableReply}
      groupTypeDict={FEEDBACK_GROUP_TYPE_DICT}
      onBack={onCleanDetail}
      onReply={data => actions.reply(data)}
      getUserName={getUserName}
    />
  )
})
