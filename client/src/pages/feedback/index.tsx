import './index.less'

import { FilterActionBar, FilterActionBarProps, Tag } from '@sugo/design'
import { fastMemo } from '@sugo/design/functions'
import { useDeepCompareEffect } from 'ahooks'
import { Alert, Button, Popover, Table, TableColumnType } from 'antd'
import dayjs from 'dayjs'
import _ from 'lodash'
import React, { useEffect, useMemo, useRef } from 'react'

import {
  FEEDBACK_GROUP_TYPE_DICT, FEEDBACK_GROUP_TYPE_OPTS,
  FEEDBACK_STATUS_DICT, FEEDBACK_STATUS_OPTS
} from '@/consts/feedback'
import type { Feedback } from '@/services/type'
import { useModelAction, useModelState } from '@/stores/models/feedback'

import { CreateFeedbackModal } from './containers/create-feedback-modal'
import { FeedbackDetailWrap } from './containers/detail-wrap'

/**
 * 意见反馈
 */
export const FeedbackPage = fastMemo(props => {
  const { initFilter } = props

  const { list, total, filter, allUserDict, statusCountMap, userIds } = useModelState()
  const actions = useModelAction()
  const createFeedbackModalRef = useRef<{ show: Function }>()

  const getUserName = userId => {
    const user = allUserDict[userId]
    return user?.first_name || user?.username
  }

  const columns = useMemo(() => [
    { dataIndex: 'index', title: '编号', width: 70, render: index => <b>#{index}</b> },
    {
      dataIndex: 'createdBy', title: '用户', width: 120, render: userId => getUserName(userId) || '未知用户'
    },
    {
      dataIndex: 'groupType', title: '类型', width: 120,
      render: type => (
        <Tag text={FEEDBACK_GROUP_TYPE_DICT[type]?.label || '未知'} color={FEEDBACK_GROUP_TYPE_DICT[type]?.color} size='samll' bordered={false} />
      )
    },
    {
      dataIndex: 'status', title: '状态', width: 120,
      render: status => {
        const { label, color } = FEEDBACK_STATUS_DICT[status || 0]
        return <Tag text={label} color={color} size='samll' bordered={false} />
      }
    },
    { dataIndex: 'menuTitle', title: '关联菜单', width: 120, render: menu => menu || '--' },
    { dataIndex: 'reply', title: '已读', width: 80, render: r => r ? '是' : '否' },
    { dataIndex: 'replyBy', title: '处理人', width: 120, render: userId => getUserName(userId) || '-' },
    {
      dataIndex: 'createdAt', title: '创建时间', width: 170,
      render: time => dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      dataIndex: '$action', title: '操作', width: 170, render: (_v, record) => {
        const popoverProps: any = {
          content: <FeedbackDetailWrap />,
          trigger: ['click'],
          placement: 'leftTop',
          arrowContent: null,
          showArrow: false,
          overlayClassName: 'feedback-detail-overlay',
          destroyTooltipOnHide: true,
          zIndex: 910
        }
        return (
          <>
            <Popover {...popoverProps} >
              <Button type='link' size='small' onClick={() => actions.loadDetail([record.id, false])}>查看</Button>
            </Popover>
            <Button type='link' size='small'
              disabled={record.status !== 0}
              onClick={() => actions.confirm(record.id)}>
              确认
            </Button>
            <Popover {...popoverProps} open={(record.status !== 0 && record.status !== 1) ? false : undefined}>
              <Button type='link' size='small'
                onClick={() => actions.loadDetail([record.id, true])}
                disabled={record.status !== 0 && record.status !== 1}>
                处理
              </Button>
            </Popover>
          </>
        )
      }
    }
  ] as TableColumnType<Feedback>[], [allUserDict])

  const userOpts = useMemo(() => _.map(userIds, id => allUserDict[id]).filter(i => i).map(u => ({
    value: u.id,
    label: u.first_name || u.username
  })), [allUserDict, userIds])

  const schemas = useMemo(() => [
    {
      key: 'groupType', type: 'Select', options: FEEDBACK_GROUP_TYPE_OPTS,
      label: '类型',
      placeholder: '请选择分组'
    },
    { key: 'createdBy', type: 'Select', label: '用户', placeholder: '请选择用户', options: userOpts },
    {
      key: 'status', type: 'Select', options: FEEDBACK_STATUS_OPTS, label: '状态', placeholder: '状态',
      props: { mode: 'multiple', maxTagCount: 'responsive', style: { width: 200 } }
    },
    {
      key: 'text', type: 'Custom', position: 'right', render: () => {
        const count = (statusCountMap[0] || 0) + (statusCountMap[1] || 0)
        if (!count) return null
        return <Alert message={`待处理：${count} 个`} />
      }
    },
    { key: 'refresh', position: 'right', type: 'Button', text: '刷新' },
    { key: 'create', position: 'right', type: 'Button', text: '创建工单', preset: 'create' }
  ] as FilterActionBarProps['schemas'], [statusCountMap, userOpts])

  const onCleanDetail = () => {
    actions.update({ detail: undefined, enableReply: false })
    actions.queryList({})
  }

  useEffect(() => {
    actions.init(initFilter)
  }, [initFilter])

  useDeepCompareEffect(() => {
    actions.queryList({})
  }, [filter])

  return (
    <div className='feedback-page'>
      <FilterActionBar
        value={filter}
        schemas={schemas}
        onClick={(e, key) => {
          e.stopPropagation()
          if (key === 'create') createFeedbackModalRef.current?.show({})
          if (key === 'refresh') onCleanDetail()
        }}
        onChange={val => actions.setFilter({ ...val, page: 1 })}
      />
      <Table
        columns={columns}
        rowKey='id'
        dataSource={list}
        scroll={{ x: '100%', y: window.innerHeight - 200 }}
        pagination={{
          showTotal: () => `共 ${total} 条`,
          total,
          current: filter.page,
          pageSize: filter.pageSize,
          onChange: (page, pageSize) => actions.setFilter({ page, pageSize })
        }}
      />
      <CreateFeedbackModal ref={createFeedbackModalRef} />
    </div>
  )
})

export default FeedbackPage
