@import '../variable.less';

.framework-abi-dashboards-container {
  height: calc(100vh - 60px);
  padding-top: 8px;
  padding-left: @menu-width + 8px;
  background-color: @app-bg-color;
  border-radius: 4px;
  position: relative;

  .fullscreen-btn {
    position: fixed;
    top: 120px;
    right: -20px;
    z-index: 999;
    &:hover {
      right: 5px;
      background-color: @primary-color;
      color: #fff;
    }
  }

  .loading-panel {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
  }

  .theme-analysis-list {
    max-width: 100%;
    height: 40px;
    margin-bottom: 8px;
    border-radius: 4px;

    .ant-tabs-content-holder {
      display: none;
    }

    .ant-tabs-nav {
      margin: 0;

      .ant-tabs-tab {
        padding: 8px 0;
      }

      .ant-tabs-tab + .ant-tabs-tab {
        margin: 0 0 0 24px;
      }
    }
  }

  .theme-analysis-previews {
    display: block;
    width: 100%;
  }

  .theme-analysis-editor-grid-layout-panel {
    height: calc(100% - 108px);
  }
  .theme-analysis-editor-sheet-container.sheet > .theme-analysis-editor-grid-layout-panel {
    height: calc(100% - 148px) !important;
  }
}

.framework-abi-dashboards {
  position: relative;
}
