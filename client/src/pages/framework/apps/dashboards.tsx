import './dashboards.less'

import { LoadingOutlined } from '@ant-design/icons'
import { fastMemo } from '@sugo/design/functions'
// import { useFullscreen } from 'ahooks'
import { Button } from 'antd'
import _ from 'lodash'
import React, { memo, useEffect } from 'react'

import { useWaitLoad } from '@/hooks/use-wait-load'
import { useAction, useStore } from '@/pages/framework/reactive'
import { AbiPreviewAllPage } from '@/pages/preview/preview-all'
import { ThemeEditorPreviewAll } from '@/pages/theme-analysis/editor/preview-all'

function enterFullScreen(el: any) {
  if (el.requestFullscreen) {
    el.requestFullscreen()  // 对现代浏览器使用 requestFullscreen
  } else if (el.mozRequestFullScreen) {
    el.mozRequestFullScreen()  // 对 Firefox 使用 mozRequestFullScreen
  } else if (el.webkitRequestFullscreen) {
    el.webkitRequestFullscreen()  // 对 Chrome, Safari 和 Opera 使用 webkitRequestFullscreen
  } else if (el.msRequestFullscreen) {
    el.msRequestFullscreen()  // 对 IE 和 Edge 使用 msRequestFullscreen
  }
}

// 卡片预览
const CardPreviewPanel = fastMemo(() => {
  // 等待 500 再加载 card preview
  const active = useStore(s => s.activeDashboardKey || 'all')
  const [type, groupId] = _.split(active, '@')


  const awit = useWaitLoad(300)
  const { loadThemeGroups, loadAbiTags } = useAction()

  const renderContent = (view: string, key: string) => {
    if (awit) return (
      <div className='loading-panel'>
        <LoadingOutlined className='mr-2' /> 加载中 ...
      </div>
    )

    _.set(window, 'wujieProps.croppY', 105)
    if (view === 'theme-analysis') {
      return <ThemeEditorPreviewAll key={key} groupId={groupId} />
    }
    if (view === 'abi-project') {
      return <AbiPreviewAllPage key={key} tagId={groupId} />
    }
    return null
  }

  useEffect(() => {
    loadThemeGroups()
    loadAbiTags()
  }, [])

  return (
    <div className='framework-abi-dashboards-container'>
      {renderContent(type, groupId)}

      <Button
        className='fullscreen-btn'
        size='small'
        onClick={e => {
          e.stopPropagation()
          const el = document.querySelector('.theme-analysis-editor-preview-container .theme-analysis-editor-preview')
          if (el) enterFullScreen(el)
        }}>
        全屏{' '}
      </Button>
    </div>
  )
})

/**
 * 我的仪表盘
 * @returns
 */
const Dashboards = memo(() => (
  <div className='framework-abi-dashboards'>
    <CardPreviewPanel />
  </div>
))

export default Dashboards
