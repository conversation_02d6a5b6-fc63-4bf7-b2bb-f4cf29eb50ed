@import '../variable.less';

.framework-abi-templates {
  background-color: @app-bg-color;
  overflow: hidden;
  height: 100%;
  padding: 16px;
  padding-left: @menu-width + 16px;
  border-radius: 4px;
  height: calc(100vh - 50px);

  > .header {
    padding-left: 8px;
    font-size: 15px;

    .tip {
      color: #aaa;
      font-size: 13px;
      margin-left: 4px;
    }
  }

  .list {
    display: flex;
    flex-wrap: wrap;
    min-height: 300px;

    .ant-empty {
      margin: 12px auto;
    }
  }

  .list-item {
    width: 220px;
    height: 186px;
    padding: 8px 12px;
    border-radius: 4px;
    background-color: #fff;
    list-style: none;
    margin: 10px;
    box-shadow: 0 0 4px rgba(@primary-color, 0.12);
    display: flex;
    flex-direction: column;

    .title {
      font-weight: bold;
      color: #444;
      font-size: 15px;
    }

    .summary {
      color: #888;
      flex: 1;
      margin-bottom: 4px;
    }

    .time {
      color: #aaa;
      font-size: 13px;
    }

    .icon {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 12px 0;

      .anticon {
        font-size: 45px;
        border-radius: 6px;
        background-image: linear-gradient(to top right, rgba(@primary-color, 0.15), #78f 90%);
        > svg > path {
          fill: rgba(@primary-color, 0.8);
        }
      }
    }

    &:hover {
      .title {
        color: @primary-color;
      }
      path {
        fill: @primary-color !important;
      }
    }

    .action {
      display: flex;
      align-items: center;
      border-top: 1px solid #f1f1f1;
      padding-top: 4px;
      margin-bottom: -2px;
      margin-top: 5px;

      > button {
        flex: 1;
        text-align: center;

        &:first-child {
          margin-right: 10px;
        }
      }
    }
  }
}

.framework-abi-templates-preview-drawer {
  .ant-drawer-header {
    display: none;
  }
  .ant-drawer-body {
    padding: 0 !important;
  }

  .close-icon {
    position: absolute;
    top: 50%;
    right: -16px;
    box-shadow: 1px 2px 6px rgba(@primary-color, 0.24);
    transform: translateY(-50%);
    z-index: 10000;
    background-color: #fff;
    border-radius: 100%;
    padding: 8px;
    cursor: pointer;
    transition: transform 0.3s ease-in-out;
    user-select: none;

    &:hover {
      transform: translateY(-50%) rotate(90deg);
    }
  }
}
