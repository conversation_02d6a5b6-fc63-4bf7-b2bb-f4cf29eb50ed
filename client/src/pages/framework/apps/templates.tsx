/* eslint-disable react-hooks/exhaustive-deps */
import './templates.less'

import { CloseOutlined, EditOutlined, EyeOutlined, LoadingOutlined } from '@ant-design/icons'
import { CardPagination } from '@sugo/design'
import { fastMemo, getFromNow } from '@sugo/design/functions'
import { useReactive } from 'ahooks'
import { Button, Drawer, Empty, Spin, Tabs, Tooltip, Typography } from 'antd'
import dayjs from 'dayjs'
import _ from 'lodash'
import React, { useEffect, useMemo, useRef } from 'react'

import { CustomIcon } from '@/components/icons/custom-icon'
import Icon from '@/components/icons/iconfont-icon'
import { useWaitLoad } from '@/hooks/use-wait-load'
import { AppContainer } from '@/pages/framework/containers/app-container'
import { useAction, useCompute, useStore } from '@/pages/framework/reactive'
import { CreateProjectModal } from '@/pages/project/components/modal/create-project'
import { ThemeEditor } from '@/pages/theme-analysis/editor'
import { CreateThemeModal } from '@/pages/theme-analysis/list/containers/modal/create-theme-modal'

const { Text } = Typography

// 预览
const TemplatePreview = fastMemo(({ type, id }) => {
  const wait = useWaitLoad(500)

  const renderContent = () => {
    if (type === 'theme-analysis') return <ThemeEditor id={id} isPreview />
    return <AppContainer pathname={`/abi/preview/${id}`} wujieProps={{ degrade: true }} />
  }

  return (
    <div>
      {wait ?
        <LoadingOutlined /> :
        renderContent()
      }
    </div>
  )
})

/**
 * 模板中心
 * @returns
 */
const Templates = fastMemo(() => {
  const { loadTemplateList, loadTemplateGroup, setActivTemplateGroupKey, loadTemplateMoveList } = useAction()

  const createThemeModalRef = useRef<{ show: Function }>()
  const createProjectModalRef = useRef<{ show: Function }>()

  const activeKey = useStore(s => s.activeTemplateKey)
  const groupKey = useStore(s => s.activeTemplateGroupKey)

  const groups = useCompute(s => s.templateGroup)
  const filters = useCompute(s => s.templateFilter)
  const [loading, template] = useCompute(s => s.templateList)

  const preview = useReactive({
    open: false,
    id: '',
    croppY: 0,
    onOpenChange: o => {
      preview.open = o
      if (o === true) {
        preview.croppY = _.get(window, 'wujieProps.croppY', 0)
        _.set(window, 'wujieProps.croppY', 0)
      }
    },
    onClose: () => {
      preview.id = ''
      preview.onOpenChange(false)
      _.set(window, 'wujieProps.croppY', preview.croppY)
    }
  })

  const items = useMemo(() => _.map(groups, i => ({
    key: i.id,
    label: i.title
  })), [groups])

  const onPreviewItem = item => e => {
    e.stopPropagation()

    preview.onOpenChange(true)
    preview.id = activeKey === 'theme-analysis' ? item.id : item.sign
  }

  const onCreateItem = item => e => {
    e.stopPropagation()
    preview.id = item.id

    setTimeout(() => {
      if (activeKey === 'abi-project' || activeKey === 'abi-report') {
        createProjectModalRef.current?.show({
          templateId: item.id,
          templateName: item.title,
          isTemplate: false,
          mode: activeKey === 'abi-project' ? 'project' : 'report'
        })
      }
      if (activeKey === 'theme-analysis') {
        createThemeModalRef.current?.show({
          templateId: item.id,
          templateName: item.title,
          groupId: /all/i.test(item.groupId) ? undefined : item.groupId
        })
      }
    }, 1)
  }

  const renderItem = item => (
    <div
      key={item.id}
      className='list-item'>
      <Text className='title' ellipsis title={item.title}>
        {item.title}
      </Text>
      <div className='icon'>
        {activeKey === 'theme-analysis' ?
          <CustomIcon type='myPie' /> :
          <Icon name='模板' />
        }
      </div>
      <Text className='summary' ellipsis>
        {item.description || '暂无描述'}
      </Text>
      <Tooltip title={dayjs(item.releasedAt || item.createdAt).format('YYYY-MM-DD HH:mm:ss')}>
        <div className='time'>
          发布于 {getFromNow(item.releasedAt || item.createdAt)}
        </div>
      </Tooltip>
      <div className='action'>
        <Button size='small' type='text' onClick={onPreviewItem(item)} icon={<EyeOutlined />}>预览</Button>
        <Button size='small' type='text' onClick={onCreateItem(item)} icon={<EditOutlined />}>使用</Button>
      </div>
    </div>
  )

  useEffect(() => {
    loadTemplateGroup()
  }, [activeKey])

  useEffect(() => {
    loadTemplateList(undefined, true)
  }, [groupKey, activeKey])

  return (
    <div className='framework-abi-templates'>
      <Tabs
        items={items} className='group-list'
        activeKey={groupKey}
        onChange={val => setActivTemplateGroupKey(val)}
      />

      <header className='header'>
        当前分类有 {template?.total || '0'} 项模板
        <span className='tip'>（需要发布才会在这里显示噢）</span>
      </header>

      <Spin spinning={loading} indicator={<LoadingOutlined />}>
        <div className='list'>
          {_.map(template?.list, renderItem)}

          {template && template.total === 0 && <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
        </div>

        <CardPagination
          current={filters.page}
          total={template?.total || 0}
          showTotal={total => `共 ${total} 项`}
          onChange={page => loadTemplateMoveList({ page })}
          isFixed
        />
      </Spin>

      <Drawer
        open={preview.open}
        onClose={preview.onClose}
        destroyOnClose
        title='预览'
        placement='left'
        width='80vw'
        className='framework-abi-templates-preview-drawer'
      >
        {preview.open &&
          <TemplatePreview key={preview.id} id={`${preview.id}`} type={activeKey} />
        }

        <CloseOutlined className='close-icon' onClick={preview.onClose} />
      </Drawer>

      <CreateThemeModal ref={createThemeModalRef} key={`${preview.id}_theme`} />
      <CreateProjectModal ref={createProjectModalRef} key={`${preview.id}_abi`} />
    </div>
  )
})

export default Templates
