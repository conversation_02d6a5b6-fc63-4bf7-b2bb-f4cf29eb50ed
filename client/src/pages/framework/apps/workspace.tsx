import './workspace.less'

import { LoadingOutlined } from '@ant-design/icons'
import { useLocation } from '@umijs/max'
import { useMemoizedFn } from 'ahooks'
import _ from 'lodash'
import qs from 'querystring'
import React, { lazy, memo, Suspense, useMemo, useRef, useState } from 'react'

import { AppLoading } from '@/components/app-loading'
import { useInjectCss } from '@/hooks/use-dark-css'
import { CausationAnalysisList } from '@/pages/causation-analysis/list'
import { AppContainer } from '@/pages/framework/containers/app-container'
import { useCompute, useStore } from '@/pages/framework/reactive'

// 懒加载的组件
const ThemeListPage = lazy(() => import('@/pages/theme-analysis/list'))
const ThemeEditor = lazy(() => import('@/pages/theme-analysis/editor'))
const ProjectPage = lazy(() => import('@/pages/project'))
const ChartsGallery = lazy(() => import('@/pages/charts-gallery'))
const MiniAppManage = lazy(() => import('@/pages/mini-app'))
const PcApp = lazy(() => import('@/pages/pc-app'))
const Weblogger = lazy(() => import('@/pages/web-log'))
const TableModelListPage = lazy(() => import('@/pages/table-model/list'))
const DatasetPage = lazy(() => import('@/pages/dataset'))

const componentMap = {
  'theme-analysis': () => <ThemeListPage />,
  'self-analysis': () => <ThemeEditor id='self' key='self' />,
  'abi-project': () => <ProjectPage />,
  'abi-report': () => <ProjectPage query={{ mode: 'report' }} />,
  'charts-gallery': () => <ChartsGallery />,
  'mini-app-manage': () => <MiniAppManage />,
  'causation-analysis': () => <CausationAnalysisList />,
  'pc-app-manage': () => <PcApp />,
  'web-log': () => <Weblogger />,
  'dataview': () => <DatasetPage />,
  'table-model': () => <TableModelListPage />
}

/**
 * 工作空间
 * @returns
 */
const Workspace = memo(() => {
  const location: Window['location'] = useLocation()
  const loadRef = useRef<Record<string, string>>({})
  const iframeMapRef = useRef<Record<string, any>>({})

  const appTheme = useStore(() => 'light')
  const workspaceSubAppUrlMap = useStore(s => s.workspaceSubAppUrlMap)
  const authWorkspaceMenus = useCompute(s => s.authWorkspaceMenus)

  const [loading, setLoading] = useState(false)

  // 存储 iframe 的 key
  const [iframeArray] = useState<string[]>([])
  const iframeLoadMapRef = useRef<Record<string, boolean>>({})
  const injectCss = useInjectCss()

  const setDrakThemeStyle = async (domDocument: Document) => {
    if (!domDocument || appTheme !== 'dark') return
    if (!injectCss) {
      console.log('注入样式不存在：', document.styleSheets)
    }

    // /.*workspace.*\.css/.test('http://192.168.0.230:30618/static-apps/abi/layouts__index.bca6021a.chunk.css')
    const style = domDocument.createElement('style')
    style.id = 'abi_workspace_css'
    style.innerHTML = injectCss
    // await new Promise(rs => setTimeout(rs, 100))
    domDocument.head.append(style)
    const h = domDocument.querySelector('html')
    h?.setAttribute('data-theme', 'dark')
    console.log('注入主题样式完成')
  }

  // 注入暗黑主题样式
  const injectDrakThemeStyle = async (dom: HTMLIFrameElement) => {
    const key = dom.getAttribute('data-key') as string
    setTimeout(() => {
      iframeLoadMapRef.current[key] = true
      setLoading(false)
    }, 100)

    if (!dom?.contentDocument) {
      console.warn('找不到 contentDocument')
      return
    }

    await setDrakThemeStyle(dom.contentDocument)
  }

  const onLoad = useMemoizedFn(e => injectDrakThemeStyle(e.target))

  const active = useMemo(() => {
    const search = qs.parse(location.search.replace(/^\?/, '')) as Record<string, any>
    const key = search.active
    const pathname = workspaceSubAppUrlMap[key || 'theme-analysis']
    console.log('load sub app:', pathname || '-')

    const keys = _.keys(loadRef.current)
    if (keys.length > 5) {
      loadRef.current = _.pick(loadRef.current, _.slice(keys, 0, 4))
    }

    return key || 'theme-analysis'
  }, [location.search, workspaceSubAppUrlMap])

  const Content = useMemo(() => {
    const key = active
    const pathname = workspaceSubAppUrlMap[key]
    const ComFn = componentMap[key]
    if (ComFn) {
      _.set(window, 'wujieProps.croppY', 50)
      return (
        <Suspense fallback={<AppLoading />}>
          <ComFn />
        </Suspense>
      )
    }

    return (
      <AppContainer
        key={`${pathname}_${appTheme}`}
        name={key}
        pathname={`${window.origin}${pathname}`}
        beforeLoad={appWindow => {
          // 注入主题样式
          setTimeout(() => setDrakThemeStyle(appWindow.document), 100)
        }}
      />
    )
  }, [active, appTheme])

  if (_.isEmpty(authWorkspaceMenus)) {
    return (
      <div className='framework-abi-workspace-container'>
        <div style={{ fontSize: 15, paddingRight: 200, textAlign: 'center', paddingTop: '20%' }}>你没有任何权限，请联系管理员</div>
      </div>
    )
  }

  return (
    <div className='framework-abi-workspace-container'>
      {Content}
      {/* 弃用 */}
      {!_.isEmpty(iframeArray) && _.map(iframeArray, (key, index) => (
        <iframe
          key={`${key}_${appTheme}`}
          title={`app_${key}`}
          data-key={key}
          data-index={index}
          src={workspaceSubAppUrlMap[key]}
          style={{
            display: active === key ? 'block' : 'none',
            visibility: ((active === key && iframeLoadMapRef.current[key]) || appTheme !== 'dark') ? 'visible' : 'hidden'
          }}
          ref={r => iframeMapRef.current[key] = r}
          onLoad={onLoad}
        />
      ))}

      {loading && (
        <div className='loading-icon'>
          <LoadingOutlined />
        </div>
      )}
    </div>
  )
})

export default Workspace
