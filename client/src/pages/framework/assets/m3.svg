<svg 
 xmlns="http://www.w3.org/2000/svg"
 xmlns:xlink="http://www.w3.org/1999/xlink"
 width="38px" height="41px">
<defs>
<filter id="Filter_0">
    <feOffset in="SourceAlpha" dx="0" dy="1" />
    <feGaussianBlur result="blurOut" stdDeviation="0" />
    <feFlood flood-color="rgb(255, 255, 255)" result="floodOut" />
    <feComposite operator="out" in="floodOut" in2="blurOut" result="compOut" />
    <feComposite operator="in" in="compOut" in2="SourceAlpha" />
    <feComponentTransfer><feFuncA type="linear" slope="0.6"/></feComponentTransfer>
    <feBlend mode="normal" in2="SourceGraphic" />
</filter>

</defs>
<path fill-rule="evenodd"  fill="rgb(88, 236, 231)"
 d="M16.000,10.000 L26.000,10.000 C27.104,10.000 28.000,10.895 28.000,12.000 L28.000,24.000 C28.000,25.104 27.104,26.000 26.000,26.000 L16.000,26.000 C14.895,26.000 14.000,25.104 14.000,24.000 L14.000,12.000 C14.000,10.895 14.895,10.000 16.000,10.000 Z"/>
<path fill-rule="evenodd"  fill="rgb(108, 94, 243)"
 d="M14.000,12.000 L24.000,12.000 C25.104,12.000 26.000,12.895 26.000,14.000 L26.000,27.000 C26.000,28.105 25.104,29.000 24.000,29.000 L14.000,29.000 C12.895,29.000 12.000,28.105 12.000,27.000 L12.000,14.000 C12.000,12.895 12.895,12.000 14.000,12.000 Z"/>
<g filter="url(#Filter_0)">
<path fill-rule="evenodd"  fill="rgb(242, 241, 255)"
 d="M10.000,14.000 L22.000,14.000 C23.105,14.000 24.000,14.895 24.000,16.000 L24.000,28.000 C24.000,29.104 23.105,30.000 22.000,30.000 L10.000,30.000 C8.895,30.000 8.000,29.104 8.000,28.000 L8.000,16.000 C8.000,14.895 8.895,14.000 10.000,14.000 Z"/>
</g>
<path fill-rule="evenodd"  opacity="0.702" fill="rgb(108, 94, 243)"
 d="M14.000,12.000 L24.000,12.000 C25.104,12.000 26.000,12.895 26.000,14.000 L26.000,27.000 C26.000,28.105 25.104,29.000 24.000,29.000 L14.000,29.000 C12.895,29.000 12.000,28.105 12.000,27.000 L12.000,14.000 C12.000,12.895 12.895,12.000 14.000,12.000 Z"/>
<path fill-rule="evenodd"  fill="rgb(255, 255, 255)"
 d="M19.000,26.000 L17.000,26.000 L16.000,26.000 L13.000,26.000 L12.000,26.000 L12.000,25.000 L12.000,19.000 L12.000,18.000 L13.000,18.000 L16.000,18.000 L17.000,18.000 L19.000,18.000 L20.000,18.000 L20.000,19.000 L20.000,21.000 L20.000,22.000 L20.000,25.000 L20.000,26.000 L19.000,26.000 ZM16.000,19.000 L13.000,19.000 L13.000,25.000 L16.000,25.000 L16.000,22.000 L16.000,21.000 L16.000,19.000 ZM19.000,19.000 L17.000,19.000 L17.000,21.000 L19.000,21.000 L19.000,19.000 ZM17.000,22.000 L17.000,25.000 L19.000,25.000 L19.000,22.000 L17.000,22.000 Z"/>
</svg>