<svg id="组_1" data-name="组 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="18" height="18" viewBox="0 0 18 18">
  <defs>
    <style>
      .cls-1 {
        fill: #6c5ef3;
        filter: url(#filter);
      }

      .cls-1, .cls-2 {
        fill-rule: evenodd;
      }

      .cls-2 {
        fill: #fff;
      }
    </style>
    <filter id="filter" x="1" y="1" width="18" height="18" filterUnits="userSpaceOnUse">
      <feImage preserveAspectRatio="none" x="1" y="1" width="18" height="18" result="image" xlink:href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCI+CiAgPGRlZnM+CiAgICA8c3R5bGU+CiAgICAgIC5jbHMtMSB7CiAgICAgICAgZmlsbDogdXJsKCNsaW5lYXItZ3JhZGllbnQpOwogICAgICB9CiAgICA8L3N0eWxlPgogICAgPGxpbmVhckdyYWRpZW50IGlkPSJsaW5lYXItZ3JhZGllbnQiIHgxPSI5IiB5MT0iMTgiIHgyPSI5IiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CiAgICAgIDxzdG9wIG9mZnNldD0iMCIgc3RvcC1jb2xvcj0iIzZhYmVmZiIvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiM2YzVlZjMiLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgogIDxyZWN0IGNsYXNzPSJjbHMtMSIgd2lkdGg9IjE4IiBoZWlnaHQ9IjE4Ii8+Cjwvc3ZnPgo="/>
      <feComposite result="composite" operator="in" in2="SourceGraphic"/>
      <feBlend result="blend" in2="SourceGraphic"/>
    </filter>
  </defs>
  <path id="椭圆_1" data-name="椭圆 1" class="cls-1" d="M15.872,16.811L19,19H9V18.941A9.01,9.01,0,1,1,15.872,16.811Z" transform="translate(-1 -1)"/>
  <path id="信息_1" data-name="信息 1" class="cls-2" d="M9.283,9.259C9.41,8.984,9.247,8.846,9.1,8.846c-0.672,0-1.543,1.331-1.87,1.331A0.236,0.236,0,0,1,7,9.978,3.464,3.464,0,0,1,8.03,8.861a4.4,4.4,0,0,1,2.723-1.025c0.781,0,1.616.4,0.962,1.881L10.408,12.7a2.589,2.589,0,0,0-.308.857,0.207,0.207,0,0,0,.218.214c0.544,0,1.543-1.3,1.8-1.3a0.236,0.236,0,0,1,.217.229c0,0.444-2.124,2.34-3.957,2.34A0.908,0.908,0,0,1,7.268,14.2a6.864,6.864,0,0,1,.745-2.248Zm0.98-3.013a1.393,1.393,0,0,1,1.489-1.223A1.108,1.108,0,0,1,13,6.062,1.378,1.378,0,0,1,11.5,7.286,1.082,1.082,0,0,1,10.263,6.246Z" transform="translate(-1 -1)"/>
</svg>
