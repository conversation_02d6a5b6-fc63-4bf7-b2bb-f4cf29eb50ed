import { Button } from 'antd'
import React, { PureComponent } from 'react'

export class ErrorWrap extends PureComponent<any, any> {
  constructor(props) {
    super(props)
    this.state = {
      isError: false,
      error: '',
      renderKey: Math.random().toString()
    }
  }

  componentDidCatch(err) {
    console.error('组件加载错误：', err)
    this.setState({ isError: true, error: err.data?.message || err.message })
  }

  render() {
    const { isError, renderKey, error } = this.state

    if (isError) return (
      <div
        key={renderKey}
        style={{
          height: '100%',
          width: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: 'column',
          paddingBottom: 30
        }}
      >

        <div style={{ padding: '0 20px' }}>
          {error}
        </div>

        <h3 style={{ color: 'red' }}>组件加载错误</h3>

        <Button type='primary' size='small' danger
          onClick={e => {
            e.stopPropagation()
            this.setState({ isError: false, renderKey: Math.random().toString() })
          }}
        >尝试加载</Button>
      </div>
    )

    return this.props.children
  }
}

export default ErrorWrap
