
.framework-abi-inline-collapsed-app-menu {
  padding: 6px;

  .menu-top-title {
    margin: 0;
    font-size: 15px;
  }

  .menu-item {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    background-color: tint(@primary-color, 99%);
    padding: 4px;
    margin: 12px 0;
    cursor: pointer;
    border: 1px solid transparent;

    .icon {
      font-size: 20px;
      margin-bottom: 1px;
    }

    &:hover,
    &.active {
      color: rgba(@primary-color, 1);
      border-color: rgba(@primary-color, 0.5);
      background-color: tint(@primary-color, 97%);
    }
  }
}

// ...
.framework-abi-inline-collapsed-app-menu-overlay {
  .menu-top-title {
    display: flex;
    align-items: center;
    font-size: 15px;
    padding: 8px;
    padding-right: 10px;
    border-bottom: 1px solid #f1f1f1;
    margin: 0;
    padding-bottom: 6px;
    color: #444;
    .icon {
      margin-right: 7px;
    }

    & + .ant-dropdown-menu {
      max-height: 400px;
    }
  }

  .ant-dropdown-menu {
    min-width: 110px;
  }
}
