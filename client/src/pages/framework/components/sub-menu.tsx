

import './sub-menu.less'

import { MenuDropdown } from '@sugo/design'
import { fastMemo } from '@sugo/design/functions'
import cn from 'classnames'
import _ from 'lodash'
import React, { useMemo } from 'react'

export interface SubMenuProps {
  activeKey?: string
  onMenuClick?: (e: any) => any
  menus?: any[]
}

/**
 * 自定义组件
 * @returns
 */
function _SubMenu(props: SubMenuProps) {
  const { menus, activeKey, onMenuClick } = props

  const childrenKeyBy = useMemo(() => {
    const dict: Record<string, string[]> = {}
    _.forEach(menus, item => {
      dict[item.key] = _.map(item.children, i => i.key)
    })
    return dict
  }, [menus])

  return (
    <div className='framework-abi-inline-collapsed-app-menu'>
      {_.map(menus, item => {
        const Content = (
          <div
            key={item.key}
            className={cn('menu-item', { active: childrenKeyBy[item.key]?.includes(activeKey!) || item.key === activeKey })}
            onClick={() => {
              if (_.isEmpty(item.children)) {
                onMenuClick?.(item)
              } else {
                onMenuClick?.(_.first(item.children))
              }
            }}
          >
            {item.icon &&
              <span className='icon'>{item.icon}</span>
            }
            <div className='title'>{item.label}</div>
          </div>
        )
        if (_.isEmpty(item.children)) return Content

        return (
          <MenuDropdown
            menuItems={item.children}
            placement='bottomLeft'
            align={{ offset: [76, -70] }}
            trigger={['hover']}
            onMenuClick={onMenuClick}
            menuActiveKey={activeKey}
            overlayClassName='framework-abi-inline-collapsed-app-menu-overlay'
            dropdownRender={node => (
              <div>
                <h3 className='menu-top-title'>
                  <span className='icon'>{item.icon}</span>
                  <span className='title'>{item.label}</span>
                </h3>
                {node}
              </div>
            )}
          >
            {Content}
          </MenuDropdown>
        )
      })}
    </div>
  )
}

export const SubMenu = fastMemo(_SubMenu)
