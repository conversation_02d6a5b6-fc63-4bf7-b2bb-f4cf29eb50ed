
/**
 * 精灵指南列表
 */
export const genieGuideList = [
  // 数据准备
  {
    key: 'start',
    title: '指引小精灵，阿甘',
    summary: '你好，我是你的指引小精灵，如果你不熟悉流程，那么请跟随我一起吧 😁'
  },
  {
    key: 'mydatasets-start',
    title: '导入数据文件',
    summary: '点击“我的数据集”开始准备导入你的数据文件吧',
    selector: '.framework-abi-workspace-menu li[menukey = "indices-dataset"]'
  },
  {
    key: 'mydatasets-list',
    title: '创建我的数据集',
    summary: '点击“新建”开始创建数据集吧 😳',
    wait: 1000,
    selector: () => {
      const root = document.querySelector('.framework-abi-app-container [data-wujie-id="app-mydatasets"]')?.shadowRoot
      if (root) {
        return root.querySelector('div[data-key="create"]')
      }
    }
  },
  {
    key: 'mydatasets-upload-file',
    title: '上传数据集文件',
    summary: '点击“选择文件”上传 excel 文件 😮‍💨',
    selector: () => {
      const root = document.querySelector('.framework-abi-app-container [data-wujie-id="app-mydatasets"]')?.shadowRoot
      if (root) {
        return root.querySelector('.dataset-edit-upload-template-modal .ant-upload-select > span > button')
      }
    }
  },
  {
    key: 'mydatasets-save',
    title: '保存我的数据集',
    summary: '上传文件后，点击确定/保存，保存我的数据集',
    offset: [-40, 45],
    selector: () => {
      const root = document.querySelector('.framework-abi-app-container [data-wujie-id="app-mydatasets"]')?.shadowRoot
      if (root) {
        return root.querySelector('.dataset-edit-upload-template-modal .ant-modal-footer .ant-btn-primary')
      }
    }
  },
  // 开始分析
  {
    key: 'theme-analysis-start',
    title: '准备进行分析',
    summary: '点击这里切换到我的分析列表',
    selector: '.framework-abi-workspace-menu li[menukey = "theme-analysis"]'
  },
  {
    key: 'theme-analysis-create',
    title: '创建我的分析',
    summary: '点击这里开始创建我的分析',
    offset: [0, 40],
    selector: '.theme-analysis-page div[data-key="create"]'
  },
  {
    key: 'theme-analysis-query-type-mydatasets',
    title: '使用数据集合',
    summary: '点击这里切换到数据集合',
    wait: 1500,
    selector: '.theme-analysis-editor-field-panel [id *= "tab-mydataset"]'
  },
  {
    key: 'theme-analysis-select-table',
    title: '添加数据集合',
    summary: '点击这里添加刚刚新建的数据集',
    wait: 500,
    selector: '.theme-analysis-editor-field-select .add-field-icon'
  },
  {
    key: 'theme-analysis-add-field',
    title: '拖拽字段到布局里',
    autoClick: false,
    summary: '按住鼠标左键，把字段拖拽到右边的区域里面，至少拖拽一个维度和一个度量',
    selector: '.theme-analysis-editor-field-table-panel .table-field-list'
  },
  {
    key: 'theme-analysis-created-card',
    title: '完成创建分析卡片',
    summary: '恭喜你 😉，已经成功完成创建一个分析卡片了',
    offset: ['50%', '40%'],
    autoChange: 1000 * 10
  },
  {
    key: 'theme-analysis-switch-chart-type',
    title: '切换图表类型',
    summary: '点击这里或者点击上面的图表页签，即可切换不同分析类型的图表',
    selector: '.config-base-panel .chart-select-btn'
  },
  {
    key: 'theme-analysis-save',
    title: '保存分析',
    summary: '点击这里即可保存创建的分析啦，在保存完成后，可以进行发布分析哦',
    selector: '.theme-analysis-editor-nav-bar .save-btn',
    offset: [0, 40]
  },
  {
    key: 'complete',
    title: '新手指引结束',
    summary: '恭喜你，你已经学会了如果从 “数据准备” 到 “分析数据” 的整个过程。接下来，你可以进行一场独特的数据分析之旅，其中还有很好玩的功能等着你去挖掘哦，如果有什么好的想法，也可以联系我们一起完善哦！good by！',
    wait: 500
  }

]
