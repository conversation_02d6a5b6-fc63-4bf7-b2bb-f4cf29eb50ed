import './app-container.less'

import { fastMemo } from '@sugo/design/functions'
import cn from 'classnames'
import _ from 'lodash'
import React, { CSSProperties } from 'react'
import WujieReact from 'wujie-react'

import { ErrorWrap } from '@/pages/framework/components/error-wrap'
import { useModelState as useUserStore } from '@/stores/models/user'

export interface AppContainerProps {
  pathname: string
  haves?: ('nav' | 'menu' | string & {})[]
  wujieProps?: any
  className?: string
  style?: CSSProperties
  beforeLoad?: (appWindow: Window) => any
  name?: string
}

/**
 * 子应用容器
 * @returns
 */
function _AppContainer(props: AppContainerProps) {
  const { pathname, haves = [], wujieProps = {}, className, style, beforeLoad, name } = props

  const user = useUserStore(s => s)
  const subAppPickCtx = ['dataServiceUrlPrefix', 'taskFuncs', 'sm4Config', 'onTablClick', 'onJump']
  const PERMISSIONS = _.map(_.get(window, 'sugo.permissions', []), p => p?.path).filter(i => i)

  const renderWujie = () => (
    <WujieReact
      width='100%'
      height='100%'
      name={`app-${name}`}
      url={pathname}
      // fiber
      // exec
      {...wujieProps as any}
      props={{
        isNewBi: true,
        sugo: { ...window.sugo, user },
        ..._.pick(window.sugo || {}, subAppPickCtx),
        permissions: PERMISSIONS,
        ...wujieProps?.props
      }}
      beforeLoad={appWindow => {
        // appWindow.__POWERED_BY_QIANKUN__ = true
        appWindow.sugo = window.sugo
        appWindow.permissions = PERMISSIONS
        beforeLoad?.(appWindow)
      }}
    />
  )

  return (
    <ErrorWrap>
      <div style={style} className={cn('framework-abi-app-container', className, {
        'show-nav': haves.includes('nav'),
        'show-menu': haves.includes('menu')
      })}>
        {pathname ? renderWujie() : <div className='load-error'>加载失败</div>}
      </div>
    </ErrorWrap>
  )
}

export const AppContainer = fastMemo(_AppContainer)
