@import '../variable.less';

.framework-abi-dashboard-menu {
  padding-top: @nav-height;
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  border-radius: 4px;
  box-shadow: 1px 0 6px rgba(@primary-color, 0.16);
  background-color: #fff;
  width: @menu-width;
  z-index: 800;

  &.inline-collapsed {
    width: 80px;
  }
  .ant-spin-nested-loading,
  .ant-spin-container {
    height: 100%;
  }
}

.framework-abi-fix-app-menu {
  margin: 0;
  box-shadow: none;
  width: @menu-width;
  padding-top: 20px;
  max-height: calc(100vh - @nav-height);

  .design-app-menu-title {
    font-weight: 400;
    text-align: left;
    padding-left: 12px;
    border-bottom: 1px solid #f1f1f1;
    margin-bottom: 6px;
    font-weight: bold;
    color: #444;
    letter-spacing: 1px;
  }

  // 顶端节点样式
  & > .ant-menu {
    border-top: none !important;

    > .ant-menu-submenu {
      > .ant-menu-submenu-title {
        height: 35px;
        line-height: 35px;
        margin: 0;

        > .ant-menu-title-content {
          font-weight: bold;
          color: #555;
        }
      }

      &.ant-menu-submenu-selected {
        .ant-menu-submenu-title > .ant-menu-title-content {
          color: @primary-color !important;
        }
      }
    }

    .ant-menu-item-icon {
      margin-right: 6px;
      font-size: 15px;
    }

    .ant-menu-item:hover {
      background-color: tint(@primary-color, 96%) !important;
    }
  }

  .ant-menu-sub.ant-menu-inline {
    background-color: #fff;
  }

  .ant-menu-sub.ant-menu-inline > .ant-menu-item,
  .ant-menu-sub.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
    height: 35px;
    line-height: 35px;
  }
}
