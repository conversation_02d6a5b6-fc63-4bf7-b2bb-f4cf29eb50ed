import './dashboard-menu.less'

import { LoadingOutlined } from '@ant-design/icons'
import { AppMenu } from '@sugo/design'
import { fastMemo } from '@sugo/design/functions'
import { Spin } from 'antd'
import React from 'react'

import { SubMenu } from '@/pages/framework/components/sub-menu'
import { useAction, useCompute, useStore } from '@/pages/framework/reactive'

/**
 * 仪表盘菜单
 * @returns
 */
function _DashboardMenu() {
  const menus = useCompute(s => s.authDashboardMenus)
  const navTopMenuDict = useCompute(s => s.navTopMenuDict)

  const activeKey = useStore(s => s.activeDashboardKey)
  const themeGroupLoading = useStore(s => s.loadingMap.themeGroup)
  const inlineCollapsed = useCompute(s => s.inlineCollapsed)

  const { setActiveDashboardKey } = useAction()

  return (
    <div className='framework-abi-dashboard-menu'>

      {inlineCollapsed &&
        <SubMenu
          activeKey={activeKey}
          onMenuClick={e => setActiveDashboardKey(e.key)}
          menus={menus}
        />
      }

      {!inlineCollapsed &&
        <Spin spinning={themeGroupLoading} indicator={<LoadingOutlined />}>
          <AppMenu
            title={navTopMenuDict.dashboards?.label}
            items={menus}
            className='framework-abi-fix-app-menu'
            defaultOpenKeys={['dashboard']}
            defaultSelectedKeys={['dashboard']}
            activeKey={activeKey}
            selectedKeys={activeKey ? [activeKey] : undefined}
            onSelect={e => setActiveDashboardKey(e.key)}
          />
        </Spin>
      }
    </div>
  )
}

export const DashboardMenu = fastMemo(_DashboardMenu)
