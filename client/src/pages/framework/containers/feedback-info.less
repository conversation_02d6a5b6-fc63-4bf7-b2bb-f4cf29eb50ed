
.framework-abi-feedback-info {
  .title {
    font-size: 18px;
    font-weight: bold;
    color: #555;
  }
  .feed-panel {
    margin-top: 12px;

    > div {
      margin-bottom: 8px;
      cursor: pointer;
      padding: 6px 8px;
      margin: 4px -6px;
      border-radius: 4px;
      display: flex;
      align-items: center;

      > div {
        flex: 1;
      }

      .anticon {
        opacity: 0.65;
      }

      &:hover {
        background-color: rgba(@primary-color, 8%);
        .anticon {
          opacity: 1;
          color: rgba(@primary-color, 0.8);
        }
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    h4 {
      margin-bottom: 0px;
    }

    .line {
      margin: 8px 0;
      padding: 0;
      cursor: default;
      height: 1px;
      background-color: rgba(#aaa, 0.15);
    }
  }

  .weather-panel {
    margin-top: 12px;
    background-color: rgba(#fff, 0.2);
    border-radius: 4px;
    padding: 6px 8px;
    line-height: 1.8;

    .city {
      .c {
        margin-left: 8px;
        font-weight: bold;
      }
    }

    .summary {
      color: #888;
      font-size: 13px;
      margin-top: 1px;
    }
  }
}

.framework-abi-feedback-info-overlay {
  background-color: #fff;
  box-shadow: 1px 1px 6px rgba(@primary-color, 0.24);
  padding: 16px;
  border-radius: 4px;
  background-image: linear-gradient(to top, rgba(#fff, 0.2), rgba(@primary-color, 0.12), rgba(#f56, 0.125));
  z-index: 901;
}

.framework-abi-feedback-info-contact-overlay {
  background-color: #fff;
  box-shadow: 1px 0 6px rgba(@primary-color, 0.16);
  padding: 12px 16px;

  .wechat-qrcode {
    user-select: none;
    width: 80px;
    height: 80px;
    border-radius: 4px;
    margin-top: 6px;
  }

  .contact-panel {
    .contact-list {
      display: flex;
      align-items: baseline;
      > div {
        flex: 1;
      }
    }
  }
}

.framework-abi-feedback-info-modal {
  .ant-modal-body {
    border-radius: 4px;
    padding: 0 !important;
  }
  .design-app-feedback {
    width: 100%;
    border: none;
    .feedback-tabs .ant-tabs-tabpane {
      height: 580px;
    }
    .help-text {
      position: sticky;
      left: 0;
      top: 0;
      background-color: #fff;
    }
  }
  .ant-modal-close-x {
    color: #fff;
    height: 46px;
    line-height: 46px;
    padding-right: 0;
  }
}

.framework-abi-feedback-info-manage-overlay {
  border-radius: 4px;
  padding: 4px 10px;
  z-index: 901;
  h4 {
    margin-bottom: 0;
  }
  .tip {
    margin-top: 1px;
    color: #aaa;
    display: inline-block;
  }
  .ant-divider {
    margin: 6px 0;
  }

  .notice-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .notice-item {
    position: relative;
    min-width: 120px;
    max-width: 135px;
    cursor: pointer;
    display: flex;
    align-items: center;
    margin-top: 1px;

    &:first-of-type {
      margin-top: 2px;
    }

    .dot {
      position: absolute;
      top: 5px;
      left: 0;
      z-index: 5;
      display: inline-block;
      width: 5px;
      height: 5px;
      background-color: #f56;
      border-radius: 100%;

      + span {
        margin-left: 8px;
      }
    }
    .design-tag {
      margin-right: 4px;
      padding: 0 4px;
    }
    &:hover {
      .notice-text {
        color: @primary-color;
      }
    }
  }
  .notice-text {
    max-width: 115px;
  }
  .all-read {
    margin-top: 1px;
    padding: 0;
    font-size: 13px;
  }
}

.framework-abi-feedback-info-manage-modal {
  .ant-modal-body {
    padding: 0;
    max-height: none;
  }
  .feedback-page {
    padding-top: 0;
    min-height: 750px;
    .design-filter-action-bar {
      padding-top: 0;
      min-height: auto;
    }
  }
}

.framework-abi-feedback-notice-detail-overlay {

  .ant-tooltip-inner {
    color: #353440b2;
    box-shadow: 1px 1px 6px rgba(@primary-color, 24%);
  }

  .notice-detail {
    min-width: 220px;
    max-width: 230px;
    max-height: 300px;
    overflow: auto;
    .title {
      color: #444;
      margin-bottom: 2px;
      font-weight: bold;
      display: flex;
      align-items: center;

      .design-tag {
        margin-right: 4px;
        padding: 0 4px;
      }
    }
    .time {
      color: #999;
      font-size: 14px;
      margin-top: 1px;
    }

    footer {
      display: flex;
      justify-content: flex-end;
    }
  }
}

.framework-abi-feedback-create-notice-modal {
  .ant-form-item {
    margin-bottom: 12px;
  }
  .ant-alert-warning {
    padding: 4px 12px;
    margin-top: 24px;
  }
}
