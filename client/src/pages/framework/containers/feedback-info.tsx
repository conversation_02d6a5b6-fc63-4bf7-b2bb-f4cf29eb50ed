import './feedback-info.less'

import { RightOutlined, WhatsAppOutlined } from '@ant-design/icons'
import { AppFeedback } from '@sugo/design'
import { fastMemo } from '@sugo/design/functions'
import { Button, Dropdown, message, Modal } from 'antd'
import dayjs from 'dayjs'
import _ from 'lodash'
import React, { useEffect, useMemo, useState } from 'react'

import { CustomIcon } from '@/components/icons/custom-icon'
import WechatQrCode from '@/pages/framework/assets/enterprise-wechat-customer-service-qrcode.png'
import { useAction, useStore } from '@/pages/framework/reactive'
import { useModelState as useUserState } from '@/stores/models/user'


// 渲染天气
export const WeatherInfo = fastMemo(() => {
  const weatherData = useStore(s => s.weatherData)

  const { suggestion, location, text, today, temperature } = weatherData || {}
  if (!location) return <div className='weather-panel' />
  return (
    <div className='weather-panel'>
      <div>今天是 {dayjs().format('MM月 DD日')}，今天
        <span className='city'>
          <span> {location}</span>
          <span className='c'> {temperature}C。</span>
        </span>
      </div>
      <div className='temperature'>
        <span>{text}天，</span>
        <span>最低：{today.low}，</span>
        <span>最高：{today.high}</span>
        <span>，请注意保暖。</span>
      </div>
      {/* <summary className='summary'>{suggestion}</summary> */}
      {/* <span className='time'>{updateAt}</span> */}
    </div>
  )
})

/**
 * 用户反馈入口
 * @returns
 */
function _FeedbackInfo() {
  const [open, setOpen] = useState(false)

  const { loadFeedBackList, createFeedBack, readFeedBack } = useAction()
  const feedbackList = useStore(s => s.feedbackList)
  const feedbackMenuTree = useStore(s => s.feedbackMenuTree)

  const userName = useUserState(s => s.first_name || '未登录')

  const nowHour = new Date().getHours()
  const timeText = useMemo(() => {
    if (nowHour <= 9 && nowHour > 4) return '早上'
    if (nowHour <= 10) return '上午'
    if (nowHour <= 16) return '中午'
    if (nowHour <= 18) return '下午'
    if (nowHour <= 22) return '晚上'
    return '夜间'
  }, [nowHour])


  const renderContact = () => (
    <div className='contact-panel'>
      <h3>联系我们</h3>
      <div className='contact-list'>
        <div>
          <div>电话咨询</div>
          <div>020-29882969</div>
        </div>
        <div>
          <div>邮件咨询</div>
          <div><EMAIL></div>
        </div>
        <div>
          <div>企微在线咨询</div>
          <img src={WechatQrCode} alt='' className='wechat-qrcode' />
        </div>
      </div>
    </div>
  )

  useEffect(() => {
    loadFeedBackList()
  }, [])

  const Content = (
    <div className='framework-abi-feedback-info'>
      <h3 className='title'>
        {timeText}好，{userName}
      </h3>
      <p>这里是{window.sugo?.abiName === '' ? '' : window.sugo?.abiName || 'ABI'} 一站式服务平台，请问有什么可以帮助到您？</p>

      <WeatherInfo />

      <div className='feed-panel' onClick={e => e.stopPropagation()}>
        <div>
          <div onClick={() => message.info('产品暂未提供文档')}>
            <h4>帮助文档</h4>
            <span>查阅常见问题解答和使用指南</span>
          </div>
          <RightOutlined />
        </div>
        {/* <div>
          <div onClick={() => setOpen(true)}>
            <h4>工单反馈</h4>
            <span>提交您遇到的问题与需求反馈</span>
          </div>
          <RightOutlined />
        </div> */}
        <div className='line' />
        <Dropdown
          dropdownRender={renderContact}
          overlayClassName='framework-abi-feedback-info-contact-overlay'
          placement='bottom'
        >
          <div>
            <div>
              <h4>产品咨询</h4>
              <span>获取有关产品的详细信息与咨询服务</span>
            </div>
            <WhatsAppOutlined />
          </div>
        </Dropdown>
      </div>

      <Modal
        open={open}
        className='framework-abi-feedback-info-modal'
        footer={null}
        onCancel={() => setOpen(false)}
        maskClosable={false}
      >
        <AppFeedback
          list={feedbackList}
          getUserName={() => _.get(window, 'sugo.user.first_name', '')}
          menuTreeData={feedbackMenuTree}
          onCreate={createFeedBack}
          onRead={readFeedBack}
        />
      </Modal>
    </div>
  )

  return (
    <Dropdown
      overlayClassName='framework-abi-feedback-info-overlay'
      trigger={['click']}
      dropdownRender={() => Content}
      placement='bottomLeft'
      align={{ offset: [-10, 3] }}
    >
      <Button
        className='feedback-icon'
        icon={<CustomIcon type='feedback' />}
        shape='circle'
      />
    </Dropdown>
  )
}

export const FeedbackInfo = fastMemo(_FeedbackInfo)
