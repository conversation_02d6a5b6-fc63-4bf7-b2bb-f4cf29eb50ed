import { BellOutlined, PlusOutlined } from '@ant-design/icons'
import { Flex, Tag } from '@sugo/design'
import { fastMemo, withRefModal } from '@sugo/design/functions'
import { useInterval, useLocalStorageState } from 'ahooks'
import {
  <PERSON><PERSON>, Badge, Button, Divider, Dropdown, Form,
  Input, message, Modal, Popconfirm,
  Radio, Select, Tooltip, Typography
} from 'antd'
import dayjs from 'dayjs'
import _ from 'lodash'
import React, { useEffect, useMemo, useRef, useState } from 'react'

import { FeedbackPage } from '@/pages/feedback'
import { useAction, useStore } from '@/pages/framework/reactive'
import { useAction as useNoticeAction, useStore as useNoticeStore } from '@/pages/framework/reactive/notice'
import { useModelState as useUserState } from '@/stores/models/user'

const { Text } = Typography

const noticeTypeMap = {
  info: { title: '公告', color: '#f90' },
  important: { title: '重要', color: '#f45' },
  sudden: { title: '紧急', color: '#f45' }
}

const ManageFeedbackModal = fastMemo(withRefModal(props => {
  const { visible, modal } = props

  return (
    <Modal
      title='工单管理'
      open={visible}
      width='90%'
      className='framework-abi-feedback-info-manage-modal'
      footer={null}
      onCancel={() => modal.hide()}
      maskClosable={false}
      centered
      zIndex={905}
    >
      <FeedbackPage initFilter={{ status: ['1', '0'] }} />
    </Modal>
  )
}))

const CreateNoticeModal = fastMemo(withRefModal(props => {
  const { visible, modal, roleList = [], userList = [] } = props

  const [form] = Form.useForm()
  const { loadUserList, loadRoleList, sendNotice } = useNoticeAction()

  const options = [
    { value: 'info', label: '公告' },
    { value: 'important', label: '重要' },
    { value: 'sudden', label: '紧急' }
  ]

  const onCancel = () => {
    modal.hide()
    form.resetFields()
  }

  const onOk = async () => {
    await form.validateFields()
    const data = await form.getFieldsValue()
    const res = await sendNotice(data)
    message.success('发送完成')
    onCancel()
  }

  const renderFooter = () => (
    <>
      <Button onClick={onCancel}>取消</Button>
      <Popconfirm
        title='将发送站内通知给：全部用户、全部角色'
        onConfirm={e => {
          e?.stopPropagation()
          return onOk()
        }}
      >
        <Button type='primary'>发送</Button>
      </Popconfirm>
    </>
  )

  useEffect(() => {
    if (visible) {
      loadUserList()
      loadRoleList()
    }
  }, [visible])

  return (
    <Modal
      title='发送站内通知'
      open={visible}
      onCancel={onCancel}
      maskClosable={false}
      onOk={onOk}
      className='framework-abi-feedback-create-notice-modal'
      footer={renderFooter()}
    >
      <Form form={form} initialValues={{ type: 'info' }} labelAlign='right' labelCol={{ span: 4 }}>
        <Form.Item name='type' label='类型' required>
          <Radio.Group options={options} />
        </Form.Item>
        <Form.Item name='title' label='标题' rules={[{ required: true, message: '标题必填' }]}>
          <Input maxLength={10} placeholder='输入标题' showCount />
        </Form.Item>
        <Form.Item name='content' label='内容'>
          <Input.TextArea rows={4} maxLength={200} placeholder='输入内容' showCount />
        </Form.Item>

        <Form.Item name='userIds' label='用户限定'>
          <Select placeholder='限定都不设置时，默认全部用户可见，用户限定优先度最高'
            showSearch
            filterOption={(kw, opt: any) => _.toLower(opt.name).indexOf(_.toLower(kw)) > -1}
            mode='multiple'
          >
            {_.map(userList, i => (
              <Select.Option key={i.id} value={i.id} name={i.first_name}>
                <Flex row vcenter spaceBetween>
                  <span>{i.first_name}</span>
                  <span style={{ opacity: 0.65, marginLeft: 3 }}>({i.username})</span>
                </Flex>
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item name='roleIds' label='角色限定'>
          <Select placeholder='限定都不设置时，默认全部角色可见'
            showSearch
            filterOption={(kw, opt: any) => _.toLower(opt.name).indexOf(_.toLower(kw)) > -1}
            mode='multiple'
          >
            {_.map(_.orderBy(roleList, 'userCount', 'desc'), i => (
              <Select.Option key={i.id} value={i.id} name={i.name}>
                <Flex row vcenter spaceBetween>
                  <span>{i.name}</span>
                  <span style={{ opacity: 0.65, marginLeft: 3 }}>({i.userCount} 个用户)</span>
                </Flex>
              </Select.Option>
            ))}
          </Select>
        </Form.Item>


        <Alert type='warning' message='提示：站内通知只有发送当天可见' />
      </Form>
    </Modal>
  )
}))

/**
 * 反馈管理
 * @returns
 */
function _FeedBackManage() {
  const [dropdownOpen, setDropdownOpen] = useState(false)

  const isAdmin = useUserState(s => s.type === 'built-in' || _.get(window, 'sugo.user.type') === 'built-in')

  const statusCountMap = useStore(s => s.feedbackStatusCountMap)

  const notices = useNoticeStore(s => s.notices)
  const userList = useNoticeStore(s => s.userList)
  const roleList = useNoticeStore(s => s.roleList)

  const createNoticeModalRef = useRef<{ show: Function }>()
  const manageFeedbackModalRef = useRef<{ show: Function }>()

  const { loadFeedBackStatus } = useAction()
  const { loadNoticeList, delNotice } = useNoticeAction()

  const [lockeds, setLockeds] = useLocalStorageState<string | null>('sugo-abi-newbi-notice-looked', {
    defaultValue: null
  })

  // 未读的
  const unlookedMap = useMemo(() => {
    const ids = _.split(lockeds, ',').map(i => _.trim(i))
    return _.keyBy(notices.filter(i => !ids.includes(i.id)), 'id')
  }, [notices, lockeds])

  const unlockedSize = useMemo(() => _.size(unlookedMap), [unlookedMap])

  // 通知数量
  const count = useMemo(() => {
    const worked = Number(statusCountMap['1'] + statusCountMap['0'])
    if (!isAdmin) return unlockedSize
    return worked + unlockedSize
  }, [isAdmin, statusCountMap, unlockedSize])


  const renderTypeTag = opt => opt?.title && (
    <Tag text={opt?.title} color={opt?.color} size='small' bordered={false} />
  )

  const init = () => {
    if (isAdmin) loadFeedBackStatus()
    loadNoticeList()
  }

  useInterval(() => {
    init()
  }, 1000 * 60 * 2)

  useEffect(() => {
    init()
  }, [])

  const dropdownRender = () => (
    <div>
      {isAdmin &&
        <>
          <div>
            <h4>工单管理</h4>
            <div>未确认：{statusCountMap['0'] || 0} 个</div>
            <div>待处理：{statusCountMap['1'] || 0} 个</div>
            <span className='tip'>请及时处理！</span>
          </div>
          <Divider type='horizontal' />
        </>
      }
      <div>
        <h4 className='notice-header'>
          站内通知
          {isAdmin &&
            <Button icon={<PlusOutlined />} size='small' shape='circle' className='ml-2'
              onClick={e => {
                e.stopPropagation()
                createNoticeModalRef.current?.show({})
              }}
            />
          }
        </h4>
        {_.isEmpty(notices) && <div className='tip'>暂无通知</div>}
        <div className='notice-list'>
          {_.map(notices, item => (
            <Tooltip
              key={item.id}
              placement='left'
              overlayClassName='framework-abi-feedback-notice-detail-overlay'
              onOpenChange={v => {
                if (v) {
                  // ... 设置已读
                  setLockeds([..._.split(lockeds || '', ','), item.id].join(','))
                }
              }}
              color='#fff'
              title={(
                <div className='notice-detail'>
                  <h4 className='title'>
                    {renderTypeTag(noticeTypeMap[item.type])}
                    {item.title}
                  </h4>
                  <summary className='summary'>{item.content}</summary>
                  <div className='time'>发布于：{dayjs(item.createdAt).format('YYYY-MM-DD HH:mm:ss')}</div>
                  {isAdmin &&
                    <footer>
                      <Button type='primary' danger size='small' onClick={async () => {
                        await delNotice(item.id)
                        await loadNoticeList()
                      }}>删除</Button>
                    </footer>
                  }
                </div>
              )}
            >
              <div key={item.id} className='notice-item'>
                {/* {renderTypeTag(noticeTypeMap[item.type])} */}
                {unlookedMap[item.id] &&
                  <span className='dot' />
                }
                <Text ellipsis className='notice-text'>
                  {item.title}
                </Text>
              </div>
            </Tooltip>
          ))}
        </div>
        {!_.isEmpty(unlookedMap) &&
          <Button size='small' type='link' className='all-read'
            onClick={e => {
              e.stopPropagation()
              setLockeds(_.map(notices, i => i.id).join(','))
              setDropdownOpen(false)
            }}
          >
            一键已读
          </Button>
        }
      </div>
    </div>
  )

  return (
    <>
      <Tooltip
        title={unlockedSize ? '有新通知' : undefined} placement='bottom' open={unlockedSize > 0}
        align={{ offset: [-4, -5] }}
        zIndex={901}
        overlayInnerStyle={{ padding: '3px 5px', fontSize: 13, minHeight: 'auto' }}
      >
        <Badge count={_.isNaN(count) ? 0 : count} size='small' title='通知' status='error' className='feedback-manage-badge'>
          <Dropdown
            dropdownRender={dropdownRender}
            overlayClassName='framework-abi-feedback-info-manage-overlay dropdown-overlay'
            placement='bottom'
            open={dropdownOpen}
            onOpenChange={setDropdownOpen}
            trigger={['click', 'hover']}
          >
            <Button
              className='feedback-manage-icon'
              icon={<BellOutlined />}
              shape='circle'
              onClick={e => {
                e.stopPropagation()
                if (isAdmin) return manageFeedbackModalRef.current?.show({})
                setTimeout(() => setDropdownOpen(true), 10)
              }}
            />
          </Dropdown>
        </Badge>
      </Tooltip>

      {isAdmin &&
        <>
          <CreateNoticeModal ref={createNoticeModalRef} userList={userList} roleList={roleList} />

          <ManageFeedbackModal ref={manageFeedbackModalRef} />
        </>
      }
    </>
  )
}

export const FeedBackManage = fastMemo(_FeedBackManage)
