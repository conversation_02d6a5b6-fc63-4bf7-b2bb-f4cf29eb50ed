import { Popup as MobilePopup } from '@nutui/nutui-react'
import { fastMemo } from '@sugo/design/functions'
import React from 'react'

import { useAction, useStore } from '@/pages/framework/reactive'

import { DashboardMenu } from './dashboard-menu'
import { TemplateMenu } from './templates-menu'
import { WorkspaceMenu } from './workspace-menu'


/**
 * 移动端布局
 * @returns
 */
function _MobileMenu() {

  const indashboards = window.location.href.indexOf('/framework/dashboards') > -1
  const intemplates = window.location.href.indexOf('/framework/templates') > -1
  const inworkspace = window.location.href.indexOf('/framework/workspace') > -1

  const open = useStore(s => s.mobileMenuOpen)
  const setMobileMenuOpen = useAction(s => s.setMobileMenuOpen)
  // ..
  if (!(indashboards || intemplates || inworkspace)) return null

  return (
    <div>
      <MobilePopup
        visible={open}
        // destroyOnClose
        style={{ width: '50%', minWidth: 210, maxWidth: 300, height: '100%' }}
        position='left'
        onClose={() => setMobileMenuOpen(false)}
      >
        {indashboards && <DashboardMenu />}
        {inworkspace && <WorkspaceMenu />}
        {intemplates && <TemplateMenu />}
      </MobilePopup>
    </div>
  )
}

export const MobileMenu = fastMemo(_MobileMenu)
