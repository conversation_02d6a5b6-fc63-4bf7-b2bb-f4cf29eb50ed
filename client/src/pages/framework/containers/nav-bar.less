@import '../variable.less';

.framework-abi-nav-bar {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  z-index: 900;
  height: @nav-height;
  background-color: #fff;
  box-shadow: 0 1px 6px rgba(@primary-color, 0.16);
  user-select: none;
  overflow-y: hidden;

  > .nav-bar-content {
    padding: 6px 16px;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
  }

  .flex-items-center {
    display: flex;
    align-items: center;
  }

  .logo-box {
    color: #444;
    display: flex;
    align-items: center;
    border-radius: 4px;
    cursor: pointer;
    user-select: none;

    .logo-title {
      font-weight: bold;
      font-size: 16px;
      background: linear-gradient(to right, #4567ff 0%, #7451ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .fish {
      font-size: 26px;
      margin-right: 12px;
      // transform: translateY(1px);
      background: linear-gradient(to right bottom, #4567ff 10%, #7451ff 90%);
      border-radius: 5px;
      padding: 3px 4px;
      transition: background 0.5s ease-in-out;
      width: 34px;
      height: 32px;
      path {
        transition: all 0.5s ease-in-out;
      }
      // border: 1px solid transparent;
      path:nth-child(1) {
        fill: rgba(#fff, 0.65);  // 鱼
      }
      path:nth-child(3) {
        fill: rgba(#fff, 0.85); // 手
      }
    }

    &:hover {
      color: @primary-color;

      .fish {
        background: linear-gradient(to right top, #fff 0%, rgba(#7451ff, 0.15) 100%);
        border-color: rgba(@primary-color, 0.5);
        path:nth-child(1) {
          fill: rgba(@primary-color, 0.8); // 鱼
        }
        path:nth-child(3) {
          fill: @primary-color; // 手
        }
      }
    }
  }

  .version-box {
    display: flex;
    align-items: center;
    border-radius: 12px;
    background-color: rgba(@primary-color, 0.136);
    cursor: pointer;
    transform: translateY(1px);
    margin-left: 15px;
    padding: 1px 0;

    > img {
      width: 16px;
      margin-left: 2px;
    }

    > span:first-of-type {
      font-size: 13px;
      font-family: PangMenZhengDao, PangMenZhengDao-Regular;
      font-style: italic;
      color: #6c5ef3;
      font-weight: bold;
      margin-left: 3px;
      margin-right: 3px;
      padding-right: 4px;
      background: linear-gradient(to right, #4567ff 0%, #7451ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .beta {
      font-size: 12px;
      font-weight: bold;
      padding-right: 6px;
      color: #6c5ef3;
      background: linear-gradient(to left, #4567ff 10%, #7451ff 90%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    &:hover {
      background-color: rgba(@primary-color, 0.18);
    }
  }

  .flex1 {
    flex: 1;
  }

  .more-icon {
    margin: 0 8px;
    border-radius: 4px;
    padding: 8px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transform: translateY(1px);
    img {
      width: 18px;
      user-select: none;
    }
    &:hover {
      background-color: #f6f7f8;
    }
  }

  .menu-center {
    .flex-items-center();
    width: max-content;
    position: absolute;
    left: 80px;
    right: 0;
    margin: 0 auto;
    z-index: 2;
    padding: 10px;
    font-size: 15px;

    > .menu-center-item {
      margin: 0 12px;
      cursor: pointer;
      position: relative;
      &:hover {
        text-decoration: underline;
        color: @primary-color;
      }
      &.active {
        color: @primary-color;
        font-weight: bold;
        &::after {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          height: 0;
          border-bottom: 2px solid rgba(@primary-color, 0.7);
          bottom: -6px;
          content: '';
          z-index: 2;
          width: 30px;
          border-radius: 4px;
          animation: menuItemLine 0.3s ease-in-out;
        }
      }
    }
  }

  .feedback-manage-icon {
    margin-right: 12px;
  }

  .feedback-manage-badge {
    .ant-badge-count {
      border: none;
      padding: 0 3px;
      transform: translate(-6px, -2px) !important;
      box-shadow: 0 1px 2px rgba(#111, 0.1);
      font-size: 12px;
    }
  }

  .feedback-icon {
    margin-right: 12px;
    svg {
      width: 20px;
      height: 16px;
    }
  }

  .app-theme {
    border-radius: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    padding: 4px 8px;
    margin-right: 12px;

    .anticon {
      font-size: 16px;
      margin-right: 4px;
      transform: translateY(1px);
    }

    &:hover {
      background-color: rgba(@primary-color, 0.12);
    }
  }

}

// 修复菜单的圆角问题
.framework-abi-nav-bar.fix-menu-radius {
  &::after {
    position: absolute;
    z-index: 2;
    content: '';
    width: @menu-width;
    height: 10px;
    bottom: -5px;
    background-color: #fff;
    left: 0;
  }

  &::before {
    position: absolute;
    z-index: 3;
    content: '';
    width: 20px;
    height: 20px;
    bottom: -10px;
    border-radius: 100%;
    background-color: #fff;
    left: @menu-width - (20px / 2);
    box-shadow: 4px 4px 4px rgba(@primary-color, 0.1);
  }
}

.gradient-bg {
  background: linear-gradient(to left bottom, rgba(@primary-color, 0.04), #fff 62%);
}

.framework-abi-nav-bar-platform-menus {
  box-shadow: 1px 1px 6px rgba(@primary-color, 0.22);
  background-color: #fff;
  border-radius: 4px;

  .platform-menus-content {
    padding: 10px;

    .logo-box {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      img {
        width: 30px;
        margin-right: 12px;
      }

      h3 {
        margin: 0;
        font-weight: bold;
        font-family: '微软雅黑';
        cursor: pointer;

        &:hover {
          text-decoration: underline;
          color: @primary-color;
        }
      }

      .home {
        padding: 2px 6px;
        margin-left: 4px;
        transform: translateY(3px);
        &:hover {
          text-decoration: underline;
        }
      }
    }

    .menu-item {
      display: flex;
      align-items: center;
      padding: 2px;
      padding-right: 8px;
      cursor: pointer;
      margin-bottom: 1px;
      border-radius: 5px;

      &.active {
        background-color: tint(@primary-color, 90%);
        .icon-wrap {
          background-color: rgba(@primary-color, 0.16);
        }
      }

      .icon-wrap {
        width: 36px;
        height: 36px;
        overflow: hidden;
        margin-right: 8px;
        border-radius: 5px;
      }

      .title {
        color: #444;
      }
      .summary {
        color: #9a9a9a;
        font-size: 13px;
      }

      img {
        object-fit: contain;
      }
      .icon1, .icon2, .icon3, .icon4, .icon5 {
        transform: translate(-2px, -4px);
      }
      .icon2 {
        transform: translate(-2px, -2px);
      }
      .icon3 {
        transform: translate(-1px, -2px);
      }
      .icon4 {
        transform: translate(-9px, -11px) scale(1.05);
      }
      .icon5 {
        transform: translate(-3px, -5px);
      }


      &:hover {
        .title {
          color: @primary-color;
          text-decoration: underline;
        }
        .icon-wrap {
          background-color: rgba(@primary-color, 0.16);
        }
      }
    }
  }
}

.framework-abi-nav-bar-workspace-menu-overlay {
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 1px 0 6px rgba(@primary-color, 0.16);

  .workspace-menu-panel {
    display: flex;
    padding: 8px 12px;

    .anticon {
      margin-right: 8px;
    }

    .menu-list {
      margin: 0 7px;

      > h4 {
        font-size: 15px;
        color: #444;
        border-bottom: 1px dashed #f4f4f4;
        padding-bottom: 6px;
      }

      .menu-list-item {
        display: block;
        color: inherit;
        margin-bottom: 2px;

        &:hover {
          text-decoration: underline;
          color: @primary-color;
        }
      }
    }
  }
}

@keyframes menuItemLine {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    opacity: 1;
    width: 30px;
  }
}
