import './nav-bar.less'

import { fastMemo } from '@sugo/design/functions'
import { history } from '@umijs/max'
import { Dropdown } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React, { useMemo } from 'react'

import { CustomIcon } from '@/components/icons/custom-icon'
import LogoImg from '@/pages/framework/assets/logo.png'
import MoreImg from '@/pages/framework/assets/more.png'
import VersionImg from '@/pages/framework/assets/version.svg'
import { FeedbackInfo } from '@/pages/framework/containers/feedback-info'
import { FeedBackManage } from '@/pages/framework/containers/feedback-manage'
import { UserInfo } from '@/pages/framework/containers/user-info'
import { useAction, useCompute, useStore } from '@/pages/framework/reactive'

// import { useModelState as useUserState } from '@/stores/models/user'
import { MobileMenu } from './mobile-menu'

export interface NavBarProps {
  className?: string
}

/**
 * 导航栏
 * @returns
 */
function _NavBar(props: NavBarProps) {
  const { className } = props

  const navTopMenus = useCompute(s => s.authNavTopMenus)
  const runInMobile = useStore(s => s.runInMobile)
  const showHomeMenu = useStore(s => s.showHomeMenu)

  const [platformMenus, activePlatformKey] = useStore(s => [s.platformMenu, s.activePlatformKey])

  const activeMenuKey = useStore(s => s.activeMenuKey)
  // const workspaceMenus = useStore(s => s.workspaceMenus)
  const version = useMemo(() => {
    const v = _.get(window, 'sugo.version', '1.0')
    return `${_.first(_.replace(v, /^v/, '').split('.')) || '1'}.0`
  }, [])
  // const isAdmin = useUserState(s => s.type === 'built-in')

  // const [open, setOpen] = useState(false)
  const { navTopMenusAction, onPlatformMenuClick, setAppTheme, setMobileMenuOpen } = useAction()

  const renderPlatformMenuPanel = () => (
    <div className='platform-menus-content'>
      <div className='logo-box'>
        <img src={LogoImg} alt='' />
        {window.sugo?.siteName !== '' &&
          <h3 onClick={e => onPlatformMenuClick(e, { key: '/console/first-page' })}>
            {window.sugo?.siteName || '数智云'} · 产品
          </h3>
        }
        <a className='home' href='/console/first-page'>首页</a>
      </div>

      <div className='menu-list'>
        {_.map(platformMenus, item => (
          <div
            key={item.key} onClick={e => onPlatformMenuClick(e, item)}
            className={cn('menu-item', { active: activePlatformKey === item.key })}
          >
            <span className='icon-wrap'>
              {item.icon}
            </span>
            <div>
              <span className='title'>{item.title}</span>
              <summary className='summary'>{item.summary}</summary>
            </div>
          </div>
        ))}
      </div>
    </div>
  )

  return (
    <div className={cn('framework-abi-nav-bar', className)}>
      <div className='nav-bar-content gradient-bg'>

        <MobileMenu />

        <div className='logo-box' onClick={e => {
          e.stopPropagation()
          // 移动端点图标会打开菜单
          if (runInMobile) {
            e.stopPropagation()
            setMobileMenuOpen(true)
          } else {
            history.push('/framework/workspace')
          }
        }}>
          <CustomIcon type='fish' className='fish' />
          {window.sugo?.abiName !== '' &&
            <span className='logo-title'>
              {window.sugo?.abiName || 'ABI 平台'}
            </span>
          }
        </div>

        {version && !runInMobile &&
          <div className='version-box'>
            <img src={VersionImg} alt='' />
            <span>v{version}</span>
            {/* <span className='beta'>beta</span> */}
          </div>
        }

        {window.sugo?.abiShowAllProductMenu !== false && !runInMobile && showHomeMenu &&
          <Dropdown
            overlayClassName='framework-abi-nav-bar-platform-menus'
            trigger={['click', 'hover']}
            dropdownRender={renderPlatformMenuPanel}
            placement='bottomLeft'
          >
            <div className='more-icon'>
              <img src={MoreImg} alt='' />
            </div>
          </Dropdown>
        }

        <div className='flex1' />

        {!runInMobile &&
          <div className='menu-center'>
            {_.map(navTopMenus, item => {
              const MenuContent = (
                <div
                  key={item.key}
                  className={cn('menu-center-item', {
                    active: activeMenuKey === item.key
                  })}
                  onClick={e => navTopMenusAction(e, item)}
                >
                  {item.label}
                </div>
              )
              return MenuContent
            })}
          </div>
        }
        <div className='flex1' />

        {/* <div className='app-theme' onClick={() => setAppTheme()}>
          {appTheme === 'dark' ?
            <div className='dark'>
              <CustomIcon type='darkTheme' />
              {runInMobile ? null : '暗黑'}
            </div> :
            <div className='light'>
              <CustomIcon type='lightTheme' />
              {runInMobile ? null : '明亮'}
            </div>
          }
        </div> */}

        {!runInMobile &&
          <>
            <FeedbackInfo />
            {/* <FeedBackManage /> */}
          </>
        }
        <UserInfo />
      </div>
    </div>
  )
}

export const NavBar = fastMemo(_NavBar)
