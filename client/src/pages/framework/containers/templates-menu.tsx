import './templates-menu.less'

import { AppMenu } from '@sugo/design'
import { fastMemo } from '@sugo/design/functions'
import React from 'react'

import { SubMenu } from '@/pages/framework/components/sub-menu'
import { useAction, useCompute, useStore } from '@/pages/framework/reactive'

/**
 * 模板中兴菜单
 * @returns
 */
function _TemplateMenu() {
  const menus = useCompute(s => s.authTemplatesMenus)

  const activeKey = useStore(s => s.activeTemplateKey)
  const inlineCollapsed = useCompute(s => s.inlineCollapsed)

  const { setActivTemplateKey } = useAction()

  return (
    <div className='framework-abi-templates-menu'>

      {inlineCollapsed &&
        <SubMenu
          activeKey={activeKey}
          onMenuClick={e => setActivTemplateKey(e.key)}
          menus={menus}
        />
      }

      {!inlineCollapsed &&
        <AppMenu
          items={menus}
          title='模板中心'
          className='framework-abi-fix-app-menu'
          activeKey={activeKey}
          selectedKeys={activeKey ? [activeKey] : undefined}
          defaultOpenKeys={menus.map(i => i.key)}
          onSelect={e => setActivTemplateKey(e.key)}
        />
      }

    </div>
  )
}

export const TemplateMenu = fastMemo(_TemplateMenu)
