
.framework-abi-user-info {
  min-width: 60px;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .user-name {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #444;

    .username {
      user-select: text;
    }
  }
}

.framework-abi-user-avatar {
  width: 30px;
  height: 30px;
  min-width: 30px;
  border-radius: 100%;
  background-color: rgba(@primary-color, 1);
  margin-right: 10px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
    border-radius: 100%;
  }
}

.framework-abi-user-info-overlay {
  box-shadow: 1px 0 6px rgba(@primary-color, 0.16);
  background-color: #fff;
  border-radius: 4px;
  min-height: 100px;
  width: 220px !important;
  max-width: 400px;
  overflow: hidden;

  .user-info-panel {
    padding: 12px;

    > header {
      display: flex;
      align-items: center;
      width: 100%;

      .account {
        flex: 1;
        font-size: 13px;
        color: #bbb;
        line-height: 1.2;
      }
    }

    > footer {
      padding-top: 12px;
    }

    .menus {
      margin-top: 6px;

      button {
        margin-bottom: 8px;
        &:last-of-type {
          margin-bottom: 0;
        }
      }
    }

    .version {
      color: #c4c4c4;
      font-size: 12px;
      text-align: center;
      margin-top: 8px;
      &:hover {
        color: #999;
      }
    }
  }



}

.framework-abi-user-info-mobile-popup {
  border-radius: 0;
  width: 220px !important;

  .user-info-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
    .flex-1 {
      flex: 1;
    }
    header {
      align-items: center;
      text-align: center;
      flex-direction: column;
      justify-content: center;
      padding: 12px;
      margin-bottom: 10px;
    }
    .framework-abi-user-avatar {
      margin: 0;
      margin-bottom: 8px;
    }
    .account {
      margin-top: 4px;
    }
    .account,
    .email {
      color: #aaa !important;
      font-size: 14px !important;
    }
  }

}

// 编辑弹窗
.framework-abi-user-info-modal {


  .ant-form-item-label {
    padding-bottom: 2px;
  }

  .ant-form-item {
    margin-bottom: 8px;
    padding: 0 24px;

    &:last-of-type {
      margin-bottom: 8px !important;
    }
  }

  .ant-form-item-with-help {
    margin-bottom: 10px;
  }

  .ant-modal-body {
    padding: 0;
    border-radius: 4px;
    overflow: hidden;
  }

  .ant-form {
    max-height: 640px;
    overflow-y: auto;
    min-height: 485px;
  }

  .titlt-form {
    min-height: 0;
    overflow: hidden;
    margin-bottom: 0;
    .ant-form-item {
      margin-bottom: 4px !important;
      margin-top: 6px;
    }
  }

  .user-header {
    position: sticky;
    left: 0;
    top: 0;
    z-index: 2;
    background-color: #fafafa;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 16px 0;
    margin-bottom: 20px;

    .avatar {
      height: 36px;
      width: 36px;
      border-radius: 100%;
      border: none;
      object-fit: cover;
      margin-bottom: 4px;
    }

    .username {
      font-size: 16px;
      color: #444;
    }

    .anticon-edit {
      margin-left: 6px;
      color: rgba(@primary-color, 1);
      cursor: pointer;
    }
  }

  .ant-modal-footer {
    text-align: center;
    height: 64px;
  }
}

// 绑定微信
.framework-abi-user-info-wechat-modal {

  .ant-modal-body {
    padding-top: 0;
  }

  .rebind-panel {
    text-align: center;
    min-height: 260px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  .anticon-wechat {
    color: #3a6;
    margin-right: 8px;
    font-size: 16px;
  }

  .actions {
    margin-top: 20px;
    button {
      margin-right: 10px;
    }
  }

  .wechat-qrcode-panel {
    position: absolute;
    z-index: 10;
    background-color: #fff;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    border-radius: 4px;
    height: 380px;

    .qrcode-loading,
    .qrcode-timeout {
      position: absolute;
      z-index: 5;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .qrcode-loading {
      top: -40px;
    }

    .qrcode-timeout {
      background-color: rgba(#333, 60%);
      color: #fff;
      flex-direction: column;
      cursor: pointer;
      backdrop-filter: blur(2px);

      .anticon {
        background-color: #fff;
        color: #555;
        padding: 10px;
        border-radius: 100%;
        margin-bottom: 5px;
        box-shadow: 0 0 6px rgba(#111, 8%);
        font-size: 20px;
      }
    }

    .qrcode {
      overflow: hidden;
      border-radius: 10px;
      border: 1px solid #f1f1f1;
      min-height: 163px;
      box-shadow: 0 0 6px rgba(#111, 8%);

      #wechat-qrcode {
        width: 160px;
        display: block;
        border-radius: 4px;
      }

      > iframe {
        border: none;
        width: 180px !important;
        height: 160px !important;
      }
    }

    .text {
      margin: 12px 0;
      font-size: 14px;
      text-align: center;

      &:first-child {
        margin-top: 0;
      }
    }

    .wechat-icon {
      width: 32px;
      height: 32px;
      margin-bottom: 4px;
    }
  }
}
