/* eslint-disable no-restricted-globals */
import './user-info.less'

import {
  CloseOutlined,
  EditOutlined, LoadingOutlined,
  LogoutOutlined,
  QuestionCircleOutlined,
  RedoOutlined,
  RetweetOutlined,
  UserOutlined, WechatOutlined
} from '@ant-design/icons'
import { Popup as MobilePopup } from '@nutui/nutui-react'
import { fastMemo, withRefModal } from '@sugo/design/functions'
import { history } from '@umijs/max'
import { useAsyncEffect, useReactive } from 'ahooks'
import { Button, Dropdown, Form, Input, message, Modal, Radio, Tooltip } from 'antd'
import Cookies from 'js-cookie'
import _ from 'lodash'
import React, { useEffect, useRef, useState } from 'react'
import umiRequest from 'umi-request'

import AvatarImg from '@/pages/framework/assets/avatar.png'
import { useStore } from '@/pages/framework/reactive'
import { MainCloud } from '@/services'
import { useCommit as useUserCommit, useModelState as useUserState } from '@/stores/models/user'
import { getUser } from '@/utils/query'

const pick = ['first_name', 'sex', 'description', 'location', 'email', 'cellphone', 'loss_efficacy_at']

const UserName = fastMemo(props => {
  const { username, onEdit } = props
  const [mode, setMode] = useState('default')

  const [form] = Form.useForm()

  const onSubmit = async () => {
    const data = form.getFieldsValue()
    const check = await MainCloud.User.findOne({
      where: { username: data.name },
      attributes: ['id']
    })
    if (check) {
      form.setFields([
        { name: 'name', errors: ['账号名称重复了'] }
      ])
    } else {
      onEdit?.(data.name)
    }
  }

  useEffect(() => {
    form.setFieldValue('name', username)
  }, [username])

  return (
    <Form form={form} initialValues={{ name: username }} className='titlt-form'>
      <div className='username'>
        {mode === 'input' &&
          <Form.Item name='name' label='账号名称'
            help='支持英文字符、数字和下划线，并且要求以英文开头'
            rules={[{
              validator(_rule, value) {
                if (_.size(value) < 4 || _.size(value) > 16) {
                  return Promise.reject(new Error('长度应该为 4-16 位'))
                }
                if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(value)) {
                  return Promise.reject(new Error('支持英文、数字和下划线，并且以英文开头，4-16位'))
                }
                if (_.startsWith(value, 'wx_') && value !== username) {
                  return Promise.reject(new Error('不能以 wx 开头'))
                }
              }
            }]}
          >
            <Input
              placeholder='请输入名称'
              maxLength={16}
              showCount
              onBlur={onSubmit}
              onKeyDown={e => e.key === 'Enter' && onSubmit()}
            />
          </Form.Item>
        }
        {mode !== 'input' && <b>{username}</b>}

        {_.startsWith(username, 'wx_') && mode !== 'input' &&
          <Tooltip title='当前可以编辑一次账号名称'>
            <EditOutlined onClick={e => {
              e.stopPropagation()
              setMode('input')
            }} />
          </Tooltip>
        }
      </div>
    </Form>

  )
})

const UserInfoEditModal = fastMemo(withRefModal(props => {
  const { modal, visible } = props

  const userCommit = useUserCommit()
  const user = useUserState(s => ({ ...s }))
  const [submitLoading, setSubmitLoading] = useState(false)
  const [form] = Form.useForm()

  const sexOpts = [
    { label: '未知', value: null },
    { label: '男', value: 1 },
    { label: '女', value: 2 }
  ]

  const initialValues = {
    ..._.pick(user, pick)
  }

  const onCancel = () => {
    modal.hide()
    form.resetFields()
  }

  const reload = () => {
    setTimeout(() => {
      window.top ? window.top.location.reload() : window.location.reload()
    }, 750)
  }

  const onEditUserName = (name: string) => {
    form.setFieldValue('username', name)
  }

  const onOk = async () => {
    try {
      setSubmitLoading(true)
      await form.validateFields()
      const data = form.getFieldsValue()
      if (data.sex === 'null') data.sex = null

      const diff = {}
      _.forEach(data, (v, k) => {
        if (user[k] !== v) diff[k] = v
      })

      if (_.isEmpty(diff)) {
        message.info('更新完成')
        reload()
        return
      }

      const res = await umiRequest.post('/app/user/update-profile', {
        data: { user: { ...diff } }
      })

      if (res) {
        message.info('更新完成')
        _.forEach(diff, (v, k) => {
          _.set(window, `sugo.user.${k}`, v)
        })
        userCommit('update', diff)
      }
    } catch (err: any) {
      console.error(err)
      message.error(`更新失败：${err.message}`)
    } finally {
      setSubmitLoading(false)
    }
    onCancel()
  }

  useAsyncEffect(async () => {
    if (visible) {
      const res = await getUser(true)
      form.setFieldsValue(_.pick(res.user, pick))
      form.setFieldValue('sex', res.user?.sex || null)
    }
  }, [visible])

  return (
    <Modal
      open={visible}
      onCancel={onCancel}
      onOk={onOk}
      okText='更新信息'
      cancelButtonProps={{ style: { display: 'none' } }}
      className='framework-abi-user-info-modal'
      confirmLoading={submitLoading}
      destroyOnClose
    >
      <div className='user-header'>
        <img className='avatar' src={user.avatar || AvatarImg} alt='' />
        <div className='username'>
          <UserName username={user.username} onEdit={onEditUserName} />
        </div>
      </div>
      <Form form={form} layout='vertical' initialValues={initialValues}>
        <Form.Item label='性别' name='username' hidden />
        <Form.Item label='用户名称' name='first_name' help='显示在界面的用户名称' rules={[{
          required: true,
          message: '用户名称不能为空'
        }]}>
          <Input placeholder='请输入用户名称' maxLength={20} showCount />
        </Form.Item>
        <Form.Item label='性别' name='sex'>
          <Radio.Group options={sexOpts as any} />
        </Form.Item>
        <Form.Item label='个人简介' name='description'>
          <Input.TextArea placeholder='请输入个人简介' maxLength={300} showCount />
        </Form.Item>
        <Form.Item label='地区' name='location' help='例如：GuangZhou, China'>
          <Input placeholder='请输入地区' maxLength={100} showCount />
        </Form.Item>
        <Form.Item label='邮箱地址' name='email' help='联系您的邮箱地址，例如：<EMAIL>'>
          <Input placeholder='请输入邮箱地址' maxLength={100} showCount />
        </Form.Item>
        <Form.Item label='联系电话' name='cellphone' help='必要时，可通过电话联系您，其他人并不会看到'>
          <Input placeholder='请输入联系电话' />
        </Form.Item>
        {user.loss_efficacy_at &&
          <Form.Item label='失效日期' name='loss_efficacy_at'>
            <Input readOnly />
          </Form.Item>
        }
      </Form>
    </Modal>
  )
}))

const UserBindWechat = fastMemo(withRefModal(props => {
  const { visible, modal } = props

  const user = useUserState(s => ({ ...s }))
  const isBinded = !!user.bindedWechat
  const qrcodeRef = useRef<HTMLImageElement | null>(null)

  const state = useReactive({
    timeout: false,
    loadingQrcode: false,
    showQrcode: false,
    wechat: {} as { sign?: string, ticket?: string }
  })
  const { loadingQrcode, showQrcode, timeout } = state

  const timeoutTimer = useRef<any>(0)
  const loopTimer = useRef<any>(0)

  const onCancel = () => {
    modal.hide()
  }

  const rebindWechat = async () => {
    const res = await umiRequest.post('/app/wechat/rebind', {
      data: {
        id: user.id,
        ticket: state.wechat.ticket
      }
    })
    if (String(res?.result) === '1') {
      message.info('换绑成功')
    }
    if (res.code !== 0 && res.message) {
      message.error(res.message)
    }
    else {
      message.error('换绑失败，重新扫码试试')
    }
    state.showQrcode = false
  }

  const unBindWechat = async () => {
    const res = await umiRequest.post('/app/wechat/unbind', {
      data: {
        id: user.id
      }
    })
    if (String(res?.result) === '1') {
      message.info('解绑成功')
    } else {
      message.error('换绑失败')
    }
  }

  const loopWechatSign = ticket => {
    clearTimeout(loopTimer.current)

    loopTimer.current = setTimeout(async () => {
      if (!ticket) return
      if (timeout) return

      const res = await umiRequest.post('/common/wechat/check', { data: { ticket } })
      if (res === 2) {
        console.log('已绑定')
        rebindWechat()
        return
      }
      if (res === 0) {
        console.log('二维码过期')
        state.timeout = true
      }
      else {
        console.log('未绑定')
      }

      loopWechatSign(ticket)
    }, 1000 * 3)
  }

  const getWechatQrcode = () => {
    state.showQrcode = true
    state.timeout = false
    state.loadingQrcode = true

    setTimeout(async () => {
      const el = qrcodeRef.current
      if (!el) return
      try {

        el.style.visibility = 'hidden'
        const res = await umiRequest.get(`/common/wechat/qrcode?action=rebind&sign=${user.id}`)
        el.src = `https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=${res?.ticket}`
        if (res?.ticket) {
          el.style.visibility = 'visible'
        }
        // 更新这个值
        state.wechat = res
        state.loadingQrcode = false
        // 轮询后端，检测是否存在 wechatSign
        loopWechatSign(res?.ticket)

        clearTimeout(timeoutTimer.current)
        // 二维码超时
        timeoutTimer.current = setTimeout(() => {
          state.timeout = true
          clearTimeout(loopTimer.current)
        }, 1000 * 60 * 5)
      } catch (err) {
        message.error('二维码生成失败')
        state.loadingQrcode = false
      }
    }, 100)
  }

  useAsyncEffect(async () => {
    if (visible) await getUser(true)
  }, [visible])

  useEffect(() => {
    clearTimeout(loopTimer.current)
    clearTimeout(timeoutTimer.current)
  }, [visible])

  return (
    <Modal
      title='绑定微信'
      open={visible}
      onCancel={onCancel}
      footer={null}
      className='framework-abi-user-info-wechat-modal'
    >
      <div className='rebind-panel'>
        <div>
          <WechatOutlined />
          {isBinded ? '已绑定微信' : '未绑定微信'}
        </div>

        <div className='actions'>
          {/* {isBinded &&
            <Popconfirm onConfirm={() => unBindWechat()} title={(
              <>
                确定要解除绑定吗？
                <br />
                <span style={{ color: '#f45' }}>解除账号与微信的绑定</span>
              </>
            )}>
              <Button type='primary' danger icon={<DisconnectOutlined />}>解除绑定</Button>
            </Popconfirm>
          } */}
          <Button type='primary' icon={<RetweetOutlined />} onClick={e => {
            e.stopPropagation()
            getWechatQrcode()
          }}>{isBinded ? '更换绑定' : '绑定微信'}</Button>
        </div>

        {showQrcode &&
          <div className='wechat-qrcode-panel'>
            <div className='text'>
              {/* <img src={require('./assets/wechat.svg')} alt='' className='wechat-icon' /> */}
              <WechatOutlined />
              <div>
                <b>绑定微信</b>
              </div>
            </div>
            <div className='qrcode' id='wechat-qrcode-wrap'>
              <img id='wechat-qrcode' ref={qrcodeRef} alt='' />
              {loadingQrcode && <LoadingOutlined className='qrcode-loading' />}
              {timeout && (
                <div
                  className='qrcode-timeout'
                  onClick={e => {
                    e.stopPropagation()
                    getWechatQrcode()
                  }}
                >
                  <RedoOutlined />
                  <div className='t'>二维码过期，点击重新生成</div>
                </div>
              )}
            </div>
            <div className='text'>
              使用微信扫一扫绑定 <br />
              关注“公众号”一键绑定
            </div>
            <Button icon={<CloseOutlined />} shape='circle' onClick={e => {
              e.stopPropagation()
              state.showQrcode = false
              clearTimeout(loopTimer.current)
              clearTimeout(timeoutTimer.current)
            }}
            />
          </div>
        }
      </div>
    </Modal>
  )
}))

/**
 * 用户信息
 * @returns
 */
function _UserInfo() {
  const [account, userName, email, avatar] = useUserState(s => [s.username, s.first_name || '未登录', s.email, s.avatar])

  const userCommit = useUserCommit()
  const runInMobile = useStore(s => s.runInMobile)
  const [open, setOpen] = useState(false)
  const wechatEnableLogin = _.get(window, 'sugo.wechat.enableLogin', false)

  const userInfoEditModalRef = useRef<{ show: Function }>()
  const userBindWechatRef = useRef<{ show: Function }>()

  const onLogoutFn = async e => {
    e.stopPropagation()
    umiRequest.get('/logout')
    window.sessionStorage.clear()
    Cookies.remove('sugo.sess')
    userCommit('reset')
    window.locationTo('/logout')
  }

  const onUserEdit = e => {
    e.stopPropagation()
    setOpen(false)
    userInfoEditModalRef.current?.show({})
  }

  // 实现换绑，解绑
  const onWechatEdit = e => {
    e.stopPropagation()
    setOpen(false)
    userBindWechatRef.current?.show({})
  }

  const renderUserPanel = () => (
    <div className='user-info-panel'>
      <header>
        <span className='framework-abi-user-avatar'>
          <img src={avatar || AvatarImg} alt='' />
        </span>
        <div>
          <b className='username'>{userName}</b>
          <div className='account'>{account}</div>
          <div className='email'>{email || <span style={{ opacity: 0.65 }}>未填写邮箱</span>}</div>
        </div>
      </header>
      {/* <WeatherInfo /> */}
      <div className='flex-1' />

      <footer>
        {/* {!runInMobile &&
          <div>
            <Button type='link' size='small' style={{ padding: 0 }} icon={<QuestionCircleOutlined />}
              onClick={() => {
                window.localStorage.setItem('sugo-bi-genie-guide-helper', 'start')
                history.push('/framework/workspace?active=mydatasets')
                setTimeout(() => {
                  window._genie_guide_next?.('xxx')
                }, 200)
              }}
            >
              新手指引
            </Button>
          </div>
        } */}
        <div className='menus'>
          <Button block onClick={onUserEdit} icon={<UserOutlined />}>个人信息</Button>
          {wechatEnableLogin &&
            <Button block onClick={onWechatEdit} icon={<WechatOutlined />}>绑定微信</Button>
          }
          <Button block onClick={onLogoutFn} icon={<LogoutOutlined />}>退出登录</Button>
        </div>
        <div className='version'>
          public v1.0.0-beta
        </div>
      </footer>

    </div>
  )

  return (
    <div className='framework-abi-user-info'>
      <Dropdown
        trigger={['click', 'hover']}
        placement='bottomLeft'
        align={{ offset: [-5, 8] }}
        dropdownRender={renderUserPanel}
        open={runInMobile ? false : open}
        overlayClassName='framework-abi-user-info-overlay'
        onOpenChange={setOpen}
      >
        <div className='user-name' onClick={() => { setTimeout(() => setOpen(true), 1) }}>
          <span className='framework-abi-user-avatar'>
            <img src={avatar || AvatarImg} alt='' />
          </span>
          <b className='username'>{userName}</b>
        </div>
      </Dropdown>

      {runInMobile &&
        <MobilePopup
          visible={open}
          onClose={() => setOpen(false)}
          // destroyOnClose
          className='framework-abi-user-info-overlay framework-abi-user-info-mobile-popup'
          style={{ width: '50%', minWidth: 200, height: '100%' }}
          position='right'
        >
          {renderUserPanel()}
        </MobilePopup>
      }

      <UserInfoEditModal ref={userInfoEditModalRef} />
      <UserBindWechat ref={userBindWechatRef} />
    </div>
  )
}

export const UserInfo = fastMemo(_UserInfo)
