import './workspace-menu.less'

import { AppMenu } from '@sugo/design'
import { fastMemo } from '@sugo/design/functions'
import _ from 'lodash'
import React from 'react'

import { SubMenu } from '@/pages/framework/components/sub-menu'
import { useAction, useCompute, useStore } from '@/pages/framework/reactive'

/**
 * 工作台菜单 workspace
 * @returns
 */
function _WorkspaceMenu() {
  const menus = useCompute(s => s.authWorkspaceMenus)
  const inlineCollapsed = useCompute(s => s.inlineCollapsed)
  const activeKey = useStore(s => s.activeWorkspaceKey)

  const { setActiveWorkspaceKey } = useAction()

  if (_.isEmpty(menus)) return null

  return (
    <div className='framework-abi-workspace-menu'>

      {inlineCollapsed &&
        <SubMenu
          activeKey={activeKey}
          onMenuClick={e => setActiveWorkspaceKey(e.key)}
          menus={menus}
        />
      }

      {!inlineCollapsed &&
        <AppMenu
          items={menus}
          title='工作空间'
          className='framework-abi-fix-app-menu'
          activeKey={activeKey}
          selectedKeys={activeKey ? [activeKey] : undefined}
          defaultOpenKeys={menus.map(i => i.key)}
          onSelect={e => setActiveWorkspaceKey(e.key)}
        />
      }

    </div>
  )
}

export const WorkspaceMenu = fastMemo(_WorkspaceMenu)
