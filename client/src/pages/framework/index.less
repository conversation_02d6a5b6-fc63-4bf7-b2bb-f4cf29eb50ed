@import './variable.less';

// abi 平台的容器层
.framework-abi {
  display: block;
  overflow: hidden;

  .framework-abi-content {
    // padding-top: @nav-height;
    max-height: 100vh;
    min-height: calc(100vh - 50px);

    &.not-allowed-mobile {
      text-align: center;
      margin-top: 50px;
      padding: 50px 12px 0;
    }

    &.no-padding-top {
      padding-top: 0 !important;
    }
  }

  // 收起情况
  &.inline-collapsed {
    .framework-abi-workspace-container,
    .framework-abi-dashboards-container {
      padding-left: 88px !important;
    }
    .framework-abi-templates {
      padding-left: 98px !important;
    }
    .framework-abi-workspace-menu,
    .framework-abi-templates-menu,
    .framework-abi-dashboard-menu {
      width: 80px !important;
    }
    .framework-abi-workspace-unfold {
      left: 80px !important;
    }
    .fix-menu-radius::after,
    .fix-menu-radius::before {
      display: none !important;
    }
  }

  // 在移动端上
  &.in-mobile {
    .framework-abi-workspace-container,
    .framework-abi-dashboards-container {
      padding-left: 0 !important;
    }
    .framework-abi-templates {
      padding-left: 0 !important;
    }
    .framework-abi-workspace-unfold {
      display: none;
    }
    .fix-menu-radius::after,
    .fix-menu-radius::before {
      display: none !important;
    }
    .framework-abi-workspace-menu,
    .framework-abi-templates-menu,
    .framework-abi-dashboard-menu {
      width: 50%;
      min-width: 210px;
      max-width: 300px;
      box-shadow: none;
      padding-top: 0;
      border-radius: 0;
      .framework-abi-fix-app-menu {
        width: 100%;
        max-height: 100vh;
        padding-top: 10px;
      }
      .ant-menu-item-selected .ant-menu-title-content {
        color: @primary-color !important;
      }
      .ant-menu-title-content {
        color: rgba(#555, 0.85);
      }
    }
    .framework-abi-nav-bar > .nav-bar-content {
      padding: 0 10px;
    }
    .framework-abi-nav-bar .app-theme {
      margin-right: 8px;
      border-radius: 100%;
      border: 1px solid #f8f8f8;
      .anticon {
        margin: 0;
      }
    }
    .framework-abi-copilot {
      .ai-chat-panel .sugo-ai-chat-app .sugo-ai-chat-app-custom-input {
        transform: translateY(-30px);
        border-radius: 0;
        border: none;
        box-shadow: 0 -1px 4px rgba(@primary-color, 0.12);
      }
      .ttyped {
        display: none;
      }
    }

    .theme-analysis-editor {
      .theme-analysis-editor-nav-bar {
        box-shadow: none;
        height: 40px;
        border-bottom: 1px solid #f8f8f8;
        .preview-nav {
          height: 40px;
        }
      }
      .theme-analysis-editor-sheet-container {
        .mobile-layout-main {
          max-height: calc(100vh - 145px) !important;
        }
      }
      .theme-analysis-editor-sheet-container.sheet {
        .mobile-layout-main {
          max-height: calc(100vh - 185px) !important;
        }
      }
    }
  }
}

.framework-abi-workspace-unfold {
  position: fixed;
  left: @menu-width;
  transform: translateX(-50%);
  z-index: 801;
  padding: 6px;
  border-radius: 100%;
  box-shadow: 1px 0 6px rgba(@primary-color, 0.16);
  bottom: 30px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}
