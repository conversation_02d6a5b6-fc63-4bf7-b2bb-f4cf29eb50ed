/* eslint-disable no-nested-ternary */
/* eslint-disable react-hooks/exhaustive-deps */
import './index.less'

import { LeftOutlined } from '@ant-design/icons'
import { Outlet, useLocation } from '@umijs/max'
import { Tooltip } from 'antd'
import Watermark from 'antd-watermark'
import cn from 'classnames'
import _ from 'lodash'
import React, { useEffect, useMemo } from 'react'

import { DashboardMenu } from '@/pages/framework/containers/dashboard-menu'
import { NavBar } from '@/pages/framework/containers/nav-bar'
import { TemplateMenu } from '@/pages/framework/containers/templates-menu'
import { WorkspaceMenu } from '@/pages/framework/containers/workspace-menu'
import { useAction, useCompute, useStore } from '@/pages/framework/reactive'

const isDev = process.env.NODE_ENV !== 'production'

/**
 * ABI 框架布局
 * @returns
 */
export function AbiFrameworkLayout() {
  const location = useLocation()
  const { initActiveMenu, loadWeatherData, setInlineCollapsed } = useAction()
  const inlineCollapsed = useCompute(s => s.inlineCollapsed)
  // 是否在 AI 管理页面
  const isInAiManagement = useCompute(s => s.isInAiManagement)
  const hasAiChat = useCompute(s => s.hasAiChat)
  const runInMobile = useStore(s => s.runInMobile)

  const allowMobileView = useMemo(() => {
    const href = window.location.href
    if (href.indexOf('/framework/dashboards') > -1) return true
    return false
  }, [window.location.href])

  const showMenu = useMemo(() => {
    const { pathname = '' } = location || {}
    initActiveMenu(pathname)

    if (pathname.indexOf('/framework/workspace') > -1) return 'workspace'
    if (pathname.indexOf('/framework/dashboards') > -1) return 'dashboards'
    if (pathname.indexOf('/framework/templates') > -1) return 'templates'
    return false
  }, [location?.pathname])

  const watermarkText = _.get(window, 'sugo.watermark.text', '')

  useEffect(() => {
    _.set(window, 'wujieProps.isNewBi', true)
    return () => {
      _.set(window, 'wujieProps.isNewBi', false)
      const html = document.querySelector('html')
      if (html) html.setAttribute('data-theme', '')
    }
  }, [])

  useEffect(() => {
    loadWeatherData()
  }, [])

  useEffect(() => {
    console.log('支持 ai:', hasAiChat)

    if (hasAiChat) return

    setTimeout(() => {
      const win = window.top?.window || window
      // 隐藏 ai 聊天窗口
      win.$sugoAICopilot?.toggle?.(false)
    }, 300)
  }, [hasAiChat])

  return (
    <>
      <div className={cn('framework-abi', {
        'inline-collapsed': inlineCollapsed,
        'in-mobile': runInMobile
      })}>
        {isInAiManagement ? null : <NavBar className={showMenu ? 'fix-menu-radius' : ''} />}

        {!runInMobile && showMenu === 'workspace' && <WorkspaceMenu />}
        {!runInMobile && showMenu === 'dashboards' && <DashboardMenu />}
        {!runInMobile && showMenu === 'templates' && <TemplateMenu />}

        {/* 子路由 */}
        {!allowMobileView && runInMobile ?
          <div className='framework-abi-content not-allowed-mobile'>
            不支持在移动端操作，请登录电脑端操作
          </div> :

          watermarkText ?
            <Watermark
              content={watermarkText}
              gap={[180, 180]}
              font={{
                fontSize: 18,
                color: 'rgba(1, 1, 1, 0.1)'
              }}
              className={cn('framework-abi-content', {
                'no-padding-top': isInAiManagement
              })}
            >
              <Outlet />
            </Watermark> :
            <div className={cn('framework-abi-content', {
              'no-padding-top': isInAiManagement
            })}>
              <Outlet />
            </div>
        }

        {/templates|dashboards|workspace/.test(showMenu || '') && !runInMobile &&
          <Tooltip title='收起/展开'>
            <LeftOutlined
              className='framework-abi-workspace-unfold'
              onClick={e => {
                e.stopPropagation()
                setInlineCollapsed()
              }}
            />
          </Tooltip>
        }

      </div>

      {/* {!isMobile() && window.location.href.indexOf('preview') === -1 &&
        <GenieGuideHelper genieGuides={genieGuideList as any[]} />
      } */}
    </>
  )
}


