/* eslint-disable react/no-children-prop */
/* eslint-disable no-restricted-globals */
/* eslint-disable max-len */
/* eslint-disable camelcase */
import {
  ApiOutlined,
  // AppstoreAddOutlined,
  // AppstoreOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  BugOutlined,
  B<PERSON>b<PERSON>utlined,
  // BuildOutlined,
  DatabaseOutlined, DesktopOutlined,
  FundOutlined,
  FundProjectionScreenOutlined,
  GoldOutlined, HddOutlined,
  Line<PERSON><PERSON>Outlined, NodeIndexOutlined,
  PieChartOutlined,
  TableOutlined
} from '@ant-design/icons'
import { loopTreeNode } from '@sugo/design/functions'
import { defineStore, ReactiveStore } from '@sugo/reactive'
import { history } from '@umijs/max'
import isMobileDeviceFn from 'is-mobile'
import _ from 'lodash'
import qs from 'querystring'
import { createElement, createElement as jsx } from 'react'

import { DEV_MENU_KEY_MAP } from '@/consts/abi'
import { FEEDBACK_MEBUS } from '@/consts/feedback'
import M1 from '@/pages/framework/assets/m1.svg'
import M2 from '@/pages/framework/assets/m2.svg'
import M3 from '@/pages/framework/assets/m3.svg'
import M4 from '@/pages/framework/assets/m4.svg'
import M5 from '@/pages/framework/assets/m5.svg'
// ..
import { Cloud, DocsCloud, MutCloud, umiRequest } from '@/services'
import { Theme } from '@/services/type'
import { initAppTheme } from '@/utils/index'
import { isDebugEnv } from '@/utils/logger'

const devMenu = DEV_MENU_KEY_MAP

const isDev = process.env.NODE_ENV !== 'production'
const ignoreMenus = _.split(_.get(window, 'initialState.themeAnalysisIgnoreMenus', ''), ',')


const getMenuDict = (key = 'menuDict', transform = true): Record<string, boolean | object> => {
  if (isDev) return devMenu

  let dict = _.get(ReactiveStore.get('app-ui'), `ref.${key}`, {}) ||
    _.get(ReactiveStore.get('astro-v4-layout'), `ref.${key}`, {})

  if (window.__POWERED_BY_WUJIE__ && _.isEmpty(dict)) {
    dict = _.get(top, `__REACTIVITY_STORE__["astro-v4-layout"].ref.${key}`, {})
  }
  else if (window.__POWERED_BY_QIANKUN__ && _.isEmpty(dict)) {
    dict = _.get(top, `__REACTIVITY_STORE__["app-ui"].ref.${key}`, {})
  }
  if (transform) return _.mapValues(dict, () => true)
  return dict || {}
}

/** 获取有权限的端菜单字典 */
const getTopMenuDict = () => {
  if (isDev) return devMenu
  let menus = ReactiveStore.get('astro-v4-layout') || []
  if (window.__POWERED_BY_WUJIE__) {
    menus = _.get(top, '__REACTIVITY_STORE__[\'astro-v4-layout\'].state.menus', [])
  }
  return _.keyBy(menus, 'key')
}

const checkMenuAuth = (key: string | string[], dict: Record<string, any>, menu?: any) => {
  // if ()
  const isAdmin = _.get(window, 'sugo.user.type') === 'built-in'
  if (isAdmin && menu?.experiment) return true

  if (_.isArray(key)) return _.some(key, k => !!dict[k])
  return !!dict[key]
}

const fixMenus = menus => {

  // 非 debug 不能看日志
  if (!isDebugEnv()) {
    const item = _.find(menus, i => i.key === 'abi')
    const index = _.findIndex(item?.children as any[], i => i.key === 'web-log')
    if (index > -1 && item?.children) {
      (item.children || []).splice(index, 1)
    }
  }

  if (!_.isEmpty(ignoreMenus) && _.isArray(ignoreMenus)) {
    loopTreeNode(menus, (node, trees) => {
      const index = _.findIndex(trees, i => i.key === node.key)
      if (ignoreMenus.includes(node.menuKey)) {
        trees.splice(index, 1)
      }
    })
  }

  return menus
}

const fixNavTopMenus = menus => {
  if (!_.isEmpty(ignoreMenus) && _.isArray(ignoreMenus)) {
    loopTreeNode(menus, (node, trees) => {
      const index = _.findIndex(trees, i => i.key === node.key)
      if (ignoreMenus.includes(node.menuKey)) {
        trees.splice(index, 1)
      }
    })
  }

  return menus
}

const urlQuery = qs.parse(window.location.search.replace(/^\?/, '')) as Record<string, any>
// const pathname = window.location.pathname

const initState = {
  menuDict: getMenuDict(),
  topMenuDict: getTopMenuDict(),

  runInMobile: isMobileDeviceFn(),
  appTheme: initAppTheme(),

  mobileMenuOpen: false,
  showHomeMenu: true,

  recommendIndiceList: [] as { id: string, name: string, dimensions: any[] }[],
  recommendIndiceGroups: [] as any[],
  indiceCount: -1,
  recommendIndiceLoading: false,
  recommendIndiceGroup: {
    id: 'ALL',
    title: '全部',
    majorId: ''
  },

  workspaceSubAppUrlMap: {
    'datasource': '/console/datasource-manager?mode=card&isNewBi',
    'mydatasets': '/console/mut/indices-dataset?mode=card&isNewBi'
  },

  activePlatformKey: '/console/abi/framework/dashboards',
  platformMenu: [
    {
      key: '/console/task-schedule-v3/overview',
      title: '数据基础平台',
      summary: '企业级一站式低代码数据开发平台',
      menuKey: 'datasource-manager-system',
      icon: jsx('img', { src: M1, className: 'icon1' })
    },
    {
      key: '/console/indices/overview',
      title: '智能指标平台',
      summary: '企业级的全域指标洞察管理中台',
      menuKey: 'indices-system',
      icon: jsx('img', { src: M2, className: 'icon2' })
    },
    {
      key: '/console/abi/framework/dashboards',
      title: 'ABI 平台',
      summary: '易用、炫酷、功能强大、满足多种可视化场景',
      menuKey: 'abi-system',
      icon: jsx('img', { src: M3, className: 'icon3' })
    },
    {
      key: '/console/data-service/operational/indicators',
      title: '数据服务平台',
      summary: '零代码、灵活、安全满足用户的数据服务需求',
      menuKey: 'data-service-system',
      icon: jsx('img', { src: M4, className: 'icon4' })
    },
    {
      key: '/console/crown/app-user-tags',
      title: '智能画像平台',
      summary: '赋能企业数字化运营的用户数据资产管理平台',
      menuKey: 'tags-smart-portrait',
      icon: jsx('img', { src: M5, className: 'icon5' })
    },
    {
      key: '/console/ai-management',
      title: 'AI 管理平台',
      summary: '企业AI全栈管理，降本增效利器',
      menuKey: 'ai-management',
      icon: jsx('img', { src: M5, className: 'icon5' })
    }
  ],

  // 工作空间菜单
  workspaceMenus: [
    {
      key: 'data', label: '数据准备', level: 0,
      icon: jsx(NodeIndexOutlined, {}),
      children: [
        { key: 'mydatasets', label: '我的数据集', menuKey: 'abi-newbi-data-mydatasets', icon: jsx(HddOutlined, {}) },
        { key: 'datasource', label: '数据源管理', menuKey: 'abi-newbi-data-datasource', icon: jsx(DatabaseOutlined, {}) },
        { key: 'dataview', label: '数据视图', menuKey: 'abi-newbi-data-dataview', icon: jsx(TableOutlined, {}) },
        { key: 'table-model', label: '数据模型', menuKey: 'abi-newbi-data-table-model', icon: jsx(GoldOutlined, {}) }
      ]
    },
    {
      key: 'analysis', label: '数据分析', level: 0,
      icon: jsx(BarChartOutlined, {}),
      children: [
        { key: 'theme-analysis', label: '我的分析', menuKey: 'abi-newbi-analysis-theme-analysis', icon: jsx(LineChartOutlined, {}) },
        { key: 'self-analysis', label: '智助分析', menuKey: 'abi-newbi-analysis-self-analysis', icon: jsx(PieChartOutlined, {}) },
        {
          key: 'causation-analysis',
          label: '成因分析',
          menuKey: 'abi-newbi-analysis-causation-analysis',
          icon: jsx(BulbOutlined, {}),
          hide: !window.isExperiment,
          experiment: true
        }
        // { key: 'data-api', label: '数据接口', menuKey: 'abi-newbi-analysis-data-api', icon: jsx(ApiOutlined, {}), hide: !window.initialState?.dataApiEnable && !window.isDev }
      ]
    },
    {
      key: 'abi', label: '数据应用', level: 0,
      icon: jsx(DesktopOutlined, {}),
      children: [
        { key: 'abi-project', label: '数据大屏', menuKey: 'abi-newbi-abi-project', icon: jsx(FundProjectionScreenOutlined, {}) },
        { key: 'abi-report', label: '数据报告', menuKey: 'abi-newbi-abi-report', icon: jsx(FundOutlined, {}) },
        { key: 'charts-gallery', label: '自定义图表', menuKey: 'abi-newbi-charts-gallery', icon: jsx(PieChartOutlined, {}) },
        { key: 'web-log', menuKey: 'abi-web-log', label: '监控错误日志', icon: jsx(BugOutlined, {}) }
      ]
    }
    // {
    //   key: 'app', label: '数据应用', level: 0,
    //   icon: jsx(AppstoreOutlined, {}),
    //   children: [
    //     // { key: 'mini-app-manage', label: '微应用管理', menuKey: 'abi-mobile-store', icon: jsx(BuildOutlined, {}) },
    //     // { key: 'pc-app-manage', label: '桌面应用商店', menuKey: 'abi-pc-app', icon: jsx(AppstoreAddOutlined, {}) },
    //   ]
    // }
  ],

  // 运营看板
  dashboardMenus: [
    {
      key: 'dashboard', label: '数据看板', level: 0,
      menuKey: ['abi-newbi-analysis', 'abi-newbi-analysis-theme-analysis'],
      icon: jsx(LineChartOutlined, {}),
      children: [
        { key: 'theme-analysis@all', label: '全部' }
      ]
    },
    {
      key: 'screen', label: '数据应用', level: 0,
      menuKey: ['abi-newbi-abi', 'abi-newbi-abi-project', 'abi-newbi-abi-report'],
      icon: jsx(FundProjectionScreenOutlined, {}),
      children: [
        { key: 'abi-project@all', label: '全部' }
      ]
    }
  ],

  // 顶端菜单
  navTopMenus: [
    { key: 'dashboards', menuKey: 'abi-newbi-dashboards', label: '运营看板' },
    { key: 'workspace', menuKey: 'abi-newbi-workspace', label: '工作空间' },
    { key: 'templates', menuKey: 'abi-newbi-templates', label: '模板中心' }
  ],
  // 用户菜单
  navUserMenus: [
    { key: 'logout', label: '退出登录' }
  ],
  // 模板
  templatesMenus: [
    { key: 'theme-analysis', label: '我的分析', menuKey: 'abi-newbi-analysis-theme-analysis', icon: jsx(LineChartOutlined, {}) },
    { key: 'abi-project', label: '数据大屏', menuKey: 'abi-newbi-abi-project', icon: jsx(FundProjectionScreenOutlined, {}) },
    { key: 'abi-report', label: '数据报告', menuKey: 'abi-newbi-abi-report', icon: jsx(FundOutlined, {}) }
  ],

  // 激活的菜单
  activeMenuKey: 'dashboards' as string | undefined,
  activeWorkspaceKey: (urlQuery?.active || 'theme-analysis') as string | undefined,
  activeTemplateKey: 'theme-analysis' as string | undefined,
  activeDashboardKey: (() => {
    if (urlQuery.type === 'abi-project') return 'abi-project@all'
    return 'theme-analysis@all'
  })() as string | undefined,
  activeTemplateGroupKey: 'ALL', // 模板分组

  // 主题列表
  themeList: [] as Theme[],

  // 模板列表
  templateListMap: {} as Record<string, {
    list: {
      id: string
      title: string
      thumb?: string
      type: 'theme-analysis' | 'abi-report' | 'abi-project' | (string & {})
      [key: string]: any
    }[],
    total: number
  }>,
  // key 是 类型，例如
  templateGroup: {} as Record<
    'theme-analysis' | 'abi-report' | 'abi-project' | (string & {}),
    { id: string, title: string, [key: string]: any }[]
  >,

  templateListLoadingMap: {} as Record<string, boolean>,
  templateListFilterMap: {} as Record<string, { page: number }>,

  /** 加载中 */
  loadingMap: {
    themeGroup: false,
    abiTag: false
  },

  weatherData: {} as any,

  /** 收起菜单，key 是路由类型 */
  inlineCollapsed: !!window.localStorage.getItem('sugo-abi-inline-collapsed'),

  /** 意见反馈入口 */
  feedbackList: [] as any[],
  feedbackLoading: false,
  feedbackMenuTree: FEEDBACK_MEBUS,
  feedbackStatusCountMap: {} as Record<string, number>
}

const menuDict = getMenuDict()

const stores = defineStore({
  namespace: 'abi-framework',
  state: initState,
  options: {
    actionLogger: {
      enable: isDev
    }
  },
  compute: state => ({
    // 是否在 AI 管理页面
    isInAiManagement: () => {
      try {
        const href = window.top?.location?.href
        return href ? href?.indexOf('/console/ai-management') > -1 : false
      } catch (err) {
        return false
      }
    },
    inlineCollapsed: () => {
      if (state.runInMobile) return false
      return state.inlineCollapsed
    },
    hasAiChat: () => !!menuDict['moyu-ai-chat'],
    navTopMenuDict: () => _.keyBy(state.navTopMenus, 'key'),
    authNavTopMenus: () => _.filter(state.navTopMenus, m => checkMenuAuth(m.menuKey, menuDict)),
    templateList: () => {
      const key = `${state.activeTemplateKey}@${state.activeTemplateGroupKey}`
      return [
        state.templateListLoadingMap[key],
        state.templateListMap[key]
      ] as [boolean, (typeof initState)['templateListMap'][string]]
    },
    templateGroup: () => {
      const key = state.activeTemplateKey || ''
      return _.concat([{ id: 'ALL', title: '全部' }], state.templateGroup[key] || [])
    },
    templateFilter: () => {
      const key = `${state.activeTemplateKey}@${state.activeTemplateGroupKey}`
      return state.templateListFilterMap[key] || { page: 1 }
    },
    authDashboardMenus: () => _.filter(state.dashboardMenus, m => checkMenuAuth(m.menuKey, menuDict)),
    // 获取主应用上的菜单
    authWorkspaceMenus: () => {
      const list = _(state.workspaceMenus).map(i => {
        const children = _.filter(i.children || [], m => checkMenuAuth(m.menuKey, menuDict, m) && m.hide !== true)
        return {
          ...i,
          children: _.map(children, c => ({
            ...c,
            label: !c.experiment ? c.label : createElement('span', {
              children: [
                c.label,
                createElement('span', { className: 'experiment-text' }, '实验性')
              ]
            })
          }))
        }
      }).filter(i => !_.isEmpty(i.children)).value()

      return list
    },
    authTemplatesMenus: () => _.filter(state.templatesMenus, m => checkMenuAuth(m.menuKey, menuDict))
  }),
  onMountBefore: state => {
    const homePath = _.get(window, 'sugo.user.SugoRoles[0].home_path')
    const onlyNewBi = /abi\/framework/i.test(homePath)
    // state.navTopMenus = fixNavTopMenus(state.navTopMenus)
    state.workspaceMenus = fixMenus(state.workspaceMenus)
    state.showHomeMenu = !onlyNewBi
    state.navTopMenus = fixNavTopMenus(state.navTopMenus)
    state.topMenuDict = getTopMenuDict()
    state.menuDict = getMenuDict()
    // 修复顶端菜单根据扩展和权限过滤
    state.platformMenu = _.filter(state.platformMenu, m => !!state.topMenuDict[m.menuKey])
    // 顶端的修复
    // state.navTopMenus = state.navTopMenus.filter(i => checkMenuAuth(i.menuKey, menuDict))
    // console.log('字典 key', menuDict, topMenuDict)
  },
  setup: state => {

    const resetState = (s: any) => {
      _.forEach(s, (v, k) => {
        if (k in initState) {
          state[k] = v
        }
      })
    }

    const loadWeatherData = async () => {
      if (state.weatherData?.location) return
      const res = await Cloud.$fn.getWeatherData({})
      state.weatherData = res
    }

    const setMobileMenuOpen = open => {
      state.mobileMenuOpen = open
    }

    const activeDefaultWorkspaceMenu = (key?: string) => {
      if (key === 'workspace') {
        state.activeWorkspaceKey = (qs.parse(window.location.search.replace(/^\?/, ''))?.active) as any || 'theme-analysis'
      }
    }

    const initActiveMenu = (path: string) => {
      const list = state.navTopMenus
      const item = _.find(list, i => path?.indexOf(`/${i.key}`) > -1)
      state.activeMenuKey = item?.key
      activeDefaultWorkspaceMenu(item?.key)
    }

    const setActiveMenu = key => state.activeMenuKey = key

    const setActiveWorkspaceKey = key => {
      state.activeWorkspaceKey = key
      history.push(`/framework/workspace?active=${key}`)
      setMobileMenuOpen(false)
    }

    const setActivTemplateKey = key => {
      state.activeTemplateKey = key
      history.push(`/framework/templates?active=${key}`)
      setMobileMenuOpen(false)
    }

    const setActivTemplateGroupKey = key => {
      state.activeTemplateGroupKey = key
      setMobileMenuOpen(false)
    }

    const setActiveDashboardKey = key => {
      state.activeDashboardKey = key
      setMobileMenuOpen(false)
    }

    const onPlatformMenuClick = (e, item) => {
      e.stopPropagation()
      const key = item?.key
      window.locationTo(key)
    }

    const navTopMenusAction = (e, item) => {
      e.stopPropagation()
      const key = item?.key
      const search = key === 'workspace' ? '?active=theme-analysis' : ''
      history.push(`/framework/${key}${search}`)
      activeDefaultWorkspaceMenu(key)
    }

    const navUserMenusAction = e => {
      e.domEvent.stopPropagation()
      if (e.key === 'logout') {
        // ...
      }
    }

    /** 加载模板列表 */
    const loadTemplateList = async (groupId?: string, init?: boolean) => {
      const type = state.activeTemplateKey || ''
      const active = groupId || state.activeTemplateGroupKey || ''
      const key = `${type}@${active}`
      let res: any

      if (init) {
        state.templateListFilterMap[key] = { page: 1 }
      }

      const { page = 1 } = state.templateListFilterMap[key] || {}

      state.templateListLoadingMap[key] = true

      try {
        if (type === 'theme-analysis') {
          res = await Cloud.Theme.findAndCountAll({
            attributes: ['id', 'title', 'releaseConfig', 'groupId', 'description', 'releasedAt'],
            where: {
              releaseStatus: 'released',
              isTemplate: true,
              groupId: /all/i.test(active) ? undefined : active
            },
            usePermission: 'new',
            order: [['releasedAt', 'DESC']],
            limit: 10,
            offset: (page - 1) * 10
          })

        }
        if (type === 'abi-project' || type === 'abi-report') {
          res = await Cloud.Project.findAndCountAll({
            attributes: ['id', 'title', 'releaseConfig', 'tags', 'releaseSign', 'description', 'createdAt'],
            where: {
              releaseStatus: 'released',
              tags: /all/i.test(active) ? undefined : { $regexp: active },
              isTemplate: true,
              mode: type === 'abi-project' ? 'project' : 'report'
            },
            order: [['createdAt', 'DESC']],
            usePermission: 'new',
            limit: 10,
            offset: (page - 1) * 10
          })
        }
        state.templateListMap[key] = {
          list: _.map(res.list, i => ({
            ...i,
            type,
            sign: i.releaseSign,
            thumb: _.get(i, 'releaseConfig.thumb') // 存 url
          })),
          total: res.total
        }
      } catch (err) {
        console.error(err)
      } finally {
        state.templateListLoadingMap[key] = false
      }
    }

    // 加载更多模版
    const loadTemplateMoveList = async filter => {
      const key = `${state.activeTemplateKey}@${state.activeTemplateGroupKey}`
      state.templateListFilterMap[key] = {
        ...state.templateListFilterMap[key],
        ...filter
      }
      await loadTemplateList()
    }

    /** 加载模板分组 */
    const loadTemplateGroup = async () => {
      const type = state.activeTemplateKey || ''

      if (type === 'theme-analysis') {
        const res = await Cloud.Theme.findAll({
          attributes: ['groupId'],
          where: { releaseStatus: 'released', isTemplate: true },
          group: ['groupId'],
          usePermission: 'new'
        })

        const groups = await Cloud.ThemeGroup.findAll({
          where: { id: { $in: _.map(res, i => i.groupId) } },
          attributes: ['id', 'title', 'parentId', 'order'],
          order: [['order', 'ASC']]
        })
        state.templateGroup[type] = groups
        state.activeTemplateGroupKey = 'ALL'
      }

      if (type === 'abi-project' || type === 'abi-report') {
        const res = await Cloud.Project.findAll({
          where: {
            releaseStatus: 'released',
            isTemplate: true,
            type: state.runInMobile ? 2 : undefined,
            mode: type === 'abi-project' ? 'project' : 'report'
          },
          group: ['tags'],
          attributes: ['tags'],
          usePermission: 'new'
        })
        const tags = _.flatMap(_.map(res, i => i.tags))
        const tagRes = await Cloud.Tag.findAll({
          where: { id: { $in: tags } },
          attributes: ['id', 'title']
        })
        state.templateGroup[type] = tagRes
        state.activeTemplateGroupKey = 'ALL'
      }
    }

    /** 加载我的分析分组 */
    const loadThemeGroups = async () => {
      state.loadingMap.themeGroup = true

      const res = await Cloud.Theme.findAll({
        attributes: ['groupId'],
        where: { releaseStatus: 'released' },
        group: ['groupId'],
        usePermission: 'new'
      })

      const groups = await Cloud.ThemeGroup.findAll({
        where: { id: { $in: _.map(res, i => i.groupId) } },
        attributes: ['id', 'title', 'parentId', 'order'],
        order: [['order', 'ASC']]
      })

      state.dashboardMenus[0].children = _.concat(
        [{ key: 'theme-analysis@all', label: '全部' }],
        groups.map(i => ({
          key: `theme-analysis@${i.id}`,
          label: i.title
        }))
      )
      state.dashboardMenus = [...state.dashboardMenus]

      state.loadingMap.themeGroup = false
    }

    /** 加载妙笔的标签 */
    const loadAbiTags = async () => {

      const res = await Cloud.Project.findAll({
        where: {
          releaseStatus: 'released',
          type: state.runInMobile ? 2 : undefined
        },
        group: ['tags'],
        attributes: ['tags'],
        usePermission: 'new'
      })
      const tags = _.flatMap(_.map(res, i => i.tags))
      const tagRes = await Cloud.Tag.findAll({
        where: { id: { $in: tags } },
        attributes: ['id', 'title']
      })
      state.dashboardMenus[1].children = _.concat(
        [{ key: 'abi-project@all', label: '全部' }],
        tagRes.map(i => ({
          key: `abi-project@${i.id}`,
          label: i.title
        }))
      )
      state.dashboardMenus = [...state.dashboardMenus]
    }

    /** 加载指标分组 */
    const loadIndiceGroups = async () => {
      const res = await umiRequest.get('/app/indices-base-category/list?cCache=600&sCache=3600', {
        headers: { 'micro-apps': 'sugo-total-mut' }
      })
      state.recommendIndiceGroups = _.map(res?.result || [], i => ({ ...i, key: i.id }))
    }

    /** 加载用户反馈列表 */
    const loadFeedBackList = async () => {
      state.feedbackLoading = true
      const list = await DocsCloud.Feedback.findAll({
        where: { createdBy: _.get(window, 'sugo.user.id') },
        limit: 20,
        order: [['createdAt', 'DESC']],
        attributes: { exclude: ['errorInfo', 'ipInfo'] }
      })
      state.feedbackList = list
      state.feedbackLoading = false
    }

    const createFeedBack = async data => {
      await DocsCloud.$execute('feedback.create', { ...data, userId: _.get(window, 'sugo.user.id') })
      await loadFeedBackList()
    }

    // 阅读反馈结果
    const readFeedBack = async data => {
      if (!data) return
      const id = data.id
      const item = state.feedbackList.find(i => i.id === id)
      if (!item) return
      if (item.isRead === false) {
        // 未读标记为已读
        await DocsCloud.Feedback.updateByPk(id, { isRead: true })
        if (item) item.isRead = true
        state.feedbackList = [...state.feedbackList]
      }
    }

    // 加载反馈状态
    const loadFeedBackStatus = async () => {
      const res: any[] = await DocsCloud.Feedback.count({ group: ['status'] })
      const statusCountMap = _.reduce(res, (o, v) => ({ ...o, [v.status]: v.count }), {})
      state.feedbackStatusCountMap = { ...statusCountMap }
    }

    /** 设置指标分类 */
    const setIndiceGroup = (id: string) => {
      const item = state.recommendIndiceGroups.find(i => i.id === id)
      state.recommendIndiceGroup = item || { id: 'ALL', title: '全部' }
      state.indiceCount = -1
    }

    /** 刷新推荐列表 */
    const refreshRecommendIndiceList = _.debounce(async () => {
      state.recommendIndiceLoading = true
      try {
        const { id, majorId } = state.recommendIndiceGroup

        const where: Record<string, any> = { type: 'base' }
        if (majorId) {
          where.themeId = majorId
        }
        // 有分组时，把主题去掉
        if (id !== 'ALL' && majorId !== id) {
          where['categorys.id'] = { $in: [id] }
          where.themeId = undefined
        }

        if (state.indiceCount === -1) {
          const res1 = await MutCloud.$fn.indiceMetaFind({
            where,
            limit: 1,
            attributes: { include: ['id'] }
          })
          state.indiceCount = res1.total
        }

        const res = await MutCloud.$fn.indiceMetaFind({
          where,
          limit: 10,
          // eslint-disable-next-line no-bitwise
          offset: Math.max(Math.random() * (state.indiceCount - 10) | 0, 0),
          attributes: { include: ['id', 'name', 'dimensions', 'categorys'] }
        })
        state.recommendIndiceList = res.data
      } finally {
        state.recommendIndiceLoading = false
      }
    }, 10)

    const setInlineCollapsed = (flag?: boolean) => {
      state.inlineCollapsed = flag === undefined ? !state.inlineCollapsed : flag
      window.localStorage.setItem('sugo-abi-inline-collapsed', state.inlineCollapsed ? '1' : '')
    }

    const setAppTheme = (type?: 'ligin' | 'dark') => {
      // eslint-disable-next-line no-nested-ternary
      state.appTheme = type === undefined ? (state.appTheme === 'dark' ? 'ligin' : 'dark') : type

      window.localStorage.setItem('sugo_theme', state.appTheme)
      _.set(window, 'appTheme', state.appTheme || 'ligin')

      try {
        // const editor = __
        const fn: any = _.get(window, '__REACTIVITY_STORE__["theme-analysis-editor"].action.setAppTheme')
        if (fn && _.isFunction(fn)) fn(state.appTheme)
      } catch (err) {
        // ...
      }
    }


    return {
      navTopMenusAction,
      navUserMenusAction,
      initActiveMenu,

      setActiveMenu,
      setActiveWorkspaceKey,
      setActiveDashboardKey,
      setActivTemplateKey,
      setActivTemplateGroupKey,
      setMobileMenuOpen,
      onPlatformMenuClick,

      // 加载
      loadThemeGroups,
      loadWeatherData,
      loadAbiTags,
      loadFeedBackList,
      loadIndiceGroups,
      loadFeedBackStatus,

      refreshRecommendIndiceList,

      // 选择指标分组
      setIndiceGroup,
      // 模板列表
      loadTemplateList,
      loadTemplateGroup,
      loadTemplateMoveList,

      setInlineCollapsed,
      setAppTheme,

      readFeedBack,
      createFeedBack,

      resetState
    }
  }
})

export const { useAction, useCompute, useStore } = stores

