
import { defineStore } from '@sugo/reactive'
import { message } from 'antd'
import _ from 'lodash'

import { MainCloud, umiRequest } from '@/services'

const initState = {

  /** 站内通知 */
  notices: [] as {
    id: string
    title: string
    type: string
    content: string
    createdAt: string
  }[],

  // 用户和角色
  userList: [] as { id: string, username: string, first_name: string }[],
  roleList: [] as { id: string, name: string, userCount: number }[]
}

const stores = defineStore({
  namespace: 'abi-framework-notice',
  state: initState,
  setup(state) {
    const loadNoticeList = async () => {
      const res = await umiRequest.get('/app/notice/list')
      state.notices = _.get(res, 'result', [])
    }

    const loadUserList = async () => {
      if (!_.isEmpty(state.userList)) return
      const res = await MainCloud.User.findAll({
        attributes: ['id', 'username', 'first_name']
      })
      state.userList = res as any[]
    }

    const loadRoleList = async () => {
      if (!_.isEmpty(state.roleList)) return
      const [res, res2] = await Promise.all([
        MainCloud.Role.findAll({
          attributes: ['id', 'name']
        }),
        MainCloud.UserRole.count({
          group: ['role_id']
        })
      ])
      const groups = _.mapValues(_.keyBy(res2 as any, 'role_id'), i => i.count)

      state.roleList = _.map(res, (i: any) => ({
        ...i,
        userCount: groups[i.id] || 0
      }))
    }

    const sendNotice = async data => {
      const record = {
        ...data
      }

      const res = await umiRequest.post('/app/notice/send', {
        data: record,
        requestType: 'json'
      })

      return res
    }

    const delNotice = async id => {
      await umiRequest.delete('/app/notice/del', {
        params: { id }
      })
      message.success('已删除通知')
    }

    return {
      loadNoticeList,

      loadRoleList,
      loadUserList,

      sendNotice,
      delNotice
    }
  }
})

export const { useAction, useStore } = stores

