import { CaretDownOutlined, SearchOutlined, SyncOutlined } from '@ant-design/icons'
import { useDebounceFn } from 'ahooks'
import { Button, Col, Form, Input, Row, Select, Space } from 'antd'
import React, { forwardRef, useImperativeHandle } from 'react'

import { AppStatus } from '@/consts/mini-app-manage'

const FilterStatus = [{ label: '全部', value: 'all' }, ...AppStatus]

export interface MiniAppFilterFormValue {
  title?: string | null
  alias?: string | null
  status: 'all' | 'normal' | 'stop' | 'offShelves'
}

export interface MiniAppFilterProps {
  onSearch?: (data: MiniAppFilterFormValue) => any
  onReset?: () => any
}

/**
 * 过滤组件
 * @returns
 */
function MiniAppFilter(props: MiniAppFilterProps, ref) {
  const { onSearch, onReset } = props
  const [form] = Form.useForm()

  useImperativeHandle(ref, () => ({
    reset: () => {
      form.resetFields()
    }
  }))

  const handleSearch = () => {
    onSearch?.(form.getFieldsValue())
  }

  const handleReset = () => {
    form.resetFields()
    onReset?.()
  }
  const { run: debounceHandleSearch } = useDebounceFn(handleSearch, {
    wait: 1000
  })
  return (
    <div className='mini-app-manage-filter'>
      <Row>
        <Col flex='auto'>
          <Form layout='inline' form={form} initialValues={{ status: 'all' }} >
            <Form.Item name='title'>
              <Input
                placeholder='搜索应用全称'
                autoComplete='off'
                suffix={<SearchOutlined />}
                onChange={debounceHandleSearch} />
            </Form.Item>
            <Form.Item name='alias'>
              <Input placeholder='搜索应用简称'
                autoComplete='off'
                suffix={<SearchOutlined />}
                onChange={debounceHandleSearch} />
            </Form.Item>
            <Form.Item label='状态' name='status'>
              <Select placeholder='请选择' options={FilterStatus} onChange={handleSearch} suffixIcon={<CaretDownOutlined />} />
            </Form.Item>
            <Button icon={<SyncOutlined />} onClick={handleReset}>
              重置
            </Button>
          </Form>
        </Col>

        {/* <Col>
          <Space size='middle'>

          </Space>
        </Col> */}
      </Row>
    </div>
  )
}

export default forwardRef(MiniAppFilter)
