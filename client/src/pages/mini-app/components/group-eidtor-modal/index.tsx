import { Form, Input } from 'antd'
import React, { useEffect, useState } from 'react'

import Modal from '@/components/customs/custom-modal'
import withRefModal from '@/components/with-ref-modal'


export interface MiniAppGroupEditorModalProps {
  visible?: boolean
  title?: string
  labelName?: string
  value?: any
  onClose?: () => any
  modal?: { hide: () => any }
  onSave?: (data: {id: any, title: string}, ctx: { close: () => any }) => Promise<any>
}

/**
 * 分类编辑框
 * @param props
 * @returns
 */
function MiniAppGroupEditorModal(props: MiniAppGroupEditorModalProps) {
  const { title, value, visible, labelName='目录名称', modal, onClose, onSave } = props
  const [form] = Form.useForm()
  const [confirmLoading, setConfirmLoading] = useState(false)

  useEffect(() => {
    if (visible && value) form.setFieldsValue({ title: value?.title })
    else form.resetFields()
  }, [visible, value])

  const handleClose = () => {
    setConfirmLoading(false)
    modal?.hide?.()
    onClose?.()
  }

  const handleSave = async () => {
    try {
      const res = await form.validateFields()
      setConfirmLoading(true)
      if (onSave) await onSave({ id: value?.id, title: res.title }, { close: handleClose })
    } finally {
      setConfirmLoading(false)
    }
  }

  return (
    <Modal
      title={title}
      confirmLoading={confirmLoading}
      open={visible}
      okText='保存'
      onOk={handleSave}
      onCancel={handleClose}
    >
      <Form form={form}>
        <Form.Item
          label={labelName}
          name='title'
          rules={[{required: true, message: `${labelName}不能为空`, whitespace: true}]}
        >
          <Input maxLength={30} placeholder='请输入' showCount />
        </Form.Item>
      </Form>
    </Modal>
  )
}
export default withRefModal(MiniAppGroupEditorModal)
