import { PlusOutlined } from '@ant-design/icons'
import { But<PERSON> } from 'antd'
import React from 'react'

export interface AppGroupHeaderProps {
  title?: string
  onClick?: () => any
}

/**
 * 分组头部
 * @returns
 */
function AppGroupHeader(props) {
  const { onClick, title = '数据集分组' } = props

  return (
    <h3 className='app-manage-group-header'>
      <span className='app-manage-group-title'>{title}</span>
      <Button type='text' size='small' icon={<PlusOutlined />} onClick={onClick} />
    </h3>
  )
}

export default AppGroupHeader
