.app-manage-group {
  padding: 10px;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
    padding: 12px 0;
    padding-top: 0;

    .anticon-plus {
      opacity: 0.65;
    }
  }
  &-title {
    font-size: 15px;
  }

  &-list {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    font-size: 14px;
    list-style: none;
    overflow-y: auto;
  }

  &-item {
    margin-bottom: 6px;
    padding: 6px 28px 6px 10px;

    position: relative;
    border-radius: 3px;
    cursor: pointer;

    .dataset-manage-group-item-icon {
      color: #9e9e9e;
    }

    &:hover {
      background-color: tint(@primary-color, 94%);
      color: @primary-color;
      .dataset-manage-group-item-icon {
        color: @primary-color;
      }
    }

    .app-group-item-content {
      display: flex;
      align-items: center;
    }
  }



  .group-active {
    color: @primary-color;
    background-color: tint(@primary-color, 92%);

    .dataset-manage-group-item-icon {
      color: @primary-color;
    }
  }

  &-item-icon {
    flex-shrink: 0;
  }

  &-text {
    margin-left: 8px;
    white-space: nowrap;
    line-height: 1.2em;
    text-overflow: ellipsis;
    // display: inline-block;
    // width: 100%;
    overflow: hidden;
  }

  &-dropdown {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 1;
  }
}
