import './index.less'

import React, { useRef } from 'react'

import MiniAppGroupEditorModal from '../group-eidtor-modal'
import AppGroupHeader from './header'
import type { AppGroupListDataSourceItem, GroupOperationType } from './list'
import AppGroupList from './list'

export type AppGroupDataSourceItem = AppGroupListDataSourceItem

export interface MiniAppGroupProps {
  dataSource: AppGroupDataSourceItem[]
  selected?: string | number
  title?: string
  onSelect?: (data: AppGroupDataSourceItem, index?: number) => any
  onDelete?: (data: AppGroupDataSourceItem) => any
  onSave?: (data: { id: any, title: string }, ctx: { close: () => any }) => any
}

/**
 * app分组
 * @returns
 */
function MiniAppGroup(props: MiniAppGroupProps) {
  const { dataSource, selected, title = '全部', onDelete, onSelect, onSave } = props
  const miniAppGroupEditorModalRef = useRef<{show: (value: any) => any}>(null)

  const onAddGroup = () => {
    miniAppGroupEditorModalRef.current?.show({ value: null, title: '新增目录' })
  }

  const onEditGroup = (data:AppGroupListDataSourceItem, key:GroupOperationType) => {
    if (key === 'delete') {
      onDelete?.(data)
    }
    if (key === 'update') {
      miniAppGroupEditorModalRef.current?.show({ value: { ...data }, title: '编辑目录' })
    }
  }

  return (
    <div className='app-manage-group'>
      <AppGroupHeader title={title} onClick={onAddGroup} />
      <AppGroupList onEditGroup={onEditGroup} dataSource={dataSource} selected={selected} onClick={onSelect} />

      <MiniAppGroupEditorModal ref={miniAppGroupEditorModalRef} onSave={onSave} />
    </div>
  )
}

export default MiniAppGroup
