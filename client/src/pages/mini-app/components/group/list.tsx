import { EllipsisOutlined } from '@ant-design/icons'
import { Button, Dropdown, Empty, Menu, Tooltip } from 'antd'
import classNames from 'classnames'
import _ from 'lodash'
import React from 'react'

import { GROUP_OPERATIONS } from '@/consts/mini-app-manage'

export type GroupOperationType = 'update' | 'delete'


export interface AppGroupListDataSourceItem {
  title: string
  id: string | number
  hideEdit?: boolean
  [key: string]: any
}

/**
 * @param dataSource [] 数据源
 * @param selected striong | number 列表选中值
 * @param onEditGroup 编辑
 * @param onClick  选中
 */
export interface AppGroupListProps {
  dataSource: AppGroupListDataSourceItem[]
  selected?: number | string
  className?: string
  onEditGroup?: (row: AppGroupListDataSourceItem, type: GroupOperationType) => any
  onClick?: (data: AppGroupListDataSourceItem, index?: number) => any
}

/**
 * 数据集分组列表
 * @param props
 * @returns
 */
function AppGroupList(props: AppGroupListProps) {
  const { dataSource = [], selected = '', className, onEditGroup, onClick } = props

  const handleClick = (val, idx) => {
    if (selected === val.id) return
    onClick?.(val, idx)
  }

  const handleSelect = (data: AppGroupListDataSourceItem, key: GroupOperationType) => {
    onEditGroup?.(data, key)
  }

  return (
    <ul className={classNames('app-manage-group-list', className)}>
      {dataSource.map((val, idx) => (
        <li
          key={val?.id}
          className={classNames('app-manage-group-item', selected === val?.id ? 'group-active' : '')}
        >

          {/* TODO: 添加tooltiops */}
          <Tooltip title={val?.title} placement='right' align={{ offset: [20, 2] }}>
            <div className='app-group-item-content' onClick={() => handleClick(val, idx)}>
              {/* <FolderOutlined className='app-manage-group-item-icon' /> */}
              <span className='app-manage-group-text'>{val?.title || '--'}</span>
            </div>
          </Tooltip>


          {
            !val?.hideEdit && (
              <Dropdown
                overlay={
                  <Menu onClick={data => handleSelect(val, data?.key as GroupOperationType)} items={GROUP_OPERATIONS} />
                }
                disabled={val?.hideEdit}
                placement='bottomLeft'
              >
                <Button
                  type={selected === val?.id ? 'link' : 'text'}
                  icon={<EllipsisOutlined />}
                  className='app-manage-group-dropdown'
                />
              </Dropdown>
            )
          }
        </li>
      ))}
      {_.isEmpty(dataSource) && (
        <li>
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </li>
      )}
    </ul>
  )
}

export default AppGroupList
