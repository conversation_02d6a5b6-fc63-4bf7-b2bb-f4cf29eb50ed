import { Form, Select } from 'antd'
import React, { useEffect, useState } from 'react'

import Modal from '@/components/customs/custom-modal'
import withRefModal from '@/components/with-ref-modal'
import { AppStatus } from '@/consts/mini-app-manage'

export interface MiniAppStatusChangeModalProps {
  visible?: boolean
  title?: string
  value?: any
  onClose?: () => any
  modal?: { hide: () => any }
  onSave?: (data: {id: any, status: string}, ctx: { close: () => any }) => Promise<any>
}

/**
 * 分类编辑框
 * @param props
 * @returns
 */
function MiniAppStatusChangeModal(props: MiniAppStatusChangeModalProps) {
  const { title='变更状态', value, visible,  modal, onClose, onSave } = props
  const [form] = Form.useForm()
  const [confirmLoading, setConfirmLoading] = useState(false)

  useEffect(() => {
    if (visible && value) form.setFieldsValue({ status: value?.status })
  }, [visible, value])

  const handleClose = () => {
    setConfirmLoading(false)
    modal?.hide?.()
    form.resetFields()
    onClose?.()
  }

  const handleSave = async () => {
    try {
      const res = await form.validateFields()
      setConfirmLoading(true)
      if (onSave) await onSave({ id: value?.id, status: res.status }, { close: handleClose })
    } finally {
      setConfirmLoading(false)
    }
  }

  return (
    <Modal
      title={title}
      confirmLoading={confirmLoading}
      open={visible}
      okText='保存'
      onOk={handleSave}
      onCancel={handleClose}
    >
      <Form form={form}>
        <Form.Item label='状态' name='status' rules={[{required: true, message: '状态不能为空'}]}>
          <Select options={AppStatus} placeholder='请选择' />
        </Form.Item>
      </Form>
    </Modal>
  )
}
export default withRefModal(MiniAppStatusChangeModal)
