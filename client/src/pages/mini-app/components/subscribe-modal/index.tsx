import './index.less'

import { Button, Form, message, Modal as ConfirmModal, Popconfirm, Select, Spin, Table, TimePicker } from 'antd'
import type { ColumnGroupType, ColumnType } from 'antd/lib/table'
import _ from 'lodash'
import moment from 'moment'
import React, { useEffect, useState } from 'react'

import Modal from '@/components/customs/custom-modal'
import { ChannelMap, TimeTypeMap, TimeTypeOptions } from '@/consts/mini-app-manage'


export interface MiniAppSubscribeModalProps {
  visible?: boolean
  title?: string
  value?: any
  total?: number
  loading?: boolean
  subscribeUsers: any[]
  onClose?: () => any
  modal?: { hide: () => any }
  onChangePushStatus?: (data: any, ctx: { close: () => any }) => Promise<any>
  onSave?: (data: any, ctx: { close: () => any }) => Promise<any>
}

/**
 * 订阅管理弹框
 * @param props
 * @returns
 */
function MiniAppSubscribeModal(props: MiniAppSubscribeModalProps) {
  const {
    title,
    value,
    visible,
    total = 0,
    loading,
    subscribeUsers = [],
    modal,
    onChangePushStatus,
    onClose,
    onSave
  } = props

  const [form] = Form.useForm()

  const [confirmLoading, setConfirmLoading] = useState(false)
  const [deleteSubscribes, setDeleteSubscribes] = useState<any[]>([])

  useEffect(() => {
    // 初始化弹窗
    if (visible && value) {
      form.setFieldsValue({
        timeType: value?.timeType,
        time: value?.time ? moment(value?.time) : null
      })
    }
  }, [visible, value])

  const handleClose = () => {

    setConfirmLoading(false)
    modal?.hide?.()
    setDeleteSubscribes([])
    // form.resetFields()
    onClose?.()
  }

  const handleSave = async () => {
    try {
      const res = await form.validateFields()
      // 1. 获取时间配置； 2. 取消订阅的用户列表
      const time = moment(res.time).format('HH:mm:ss')
      const timeArr = _.split(time, ':')
      const timeType = res.timeType
      const pushCron = `${timeArr[2]} ${timeArr[1]} ${timeArr[0]} ${TimeTypeMap[timeType]}`
      if (pushCron === value?.pushCron && _.isEmpty(deleteSubscribes)) {
        message.warning('无修改内容')
        return
      }
      setConfirmLoading(true)
      if (onSave) {
        await onSave(
          {
            currentValue: value,
            pushCron,
            deleteSubscribes
          },
          { close: handleClose }
        )
      }
    } finally {
      setConfirmLoading(false)
    }
  }

  /**
   * 取消推送/恢复推送
   * 1. 取消推送=取消定时任务
   * 2. 恢复推送=重新添加定时任务
  */
  const handleChangePushStatus = () => {
    ConfirmModal.confirm({
      centered: true,
      content: value?.pushStatus === 'push' ? '确定关闭应用推送功能?' : '确定恢复应用推送功能？',
      async onOk() {
        try {
          setConfirmLoading(true)
          if (onChangePushStatus) await onChangePushStatus?.(value, { close: handleClose })
        } finally {
          setConfirmLoading(false)
        }
      }
    })
  }

  const handleDelete = val => {
    deleteSubscribes.push(val)
    setDeleteSubscribes([...deleteSubscribes])
  }

  const columns: Array<ColumnGroupType<any> | ColumnType<any>> = [
    {
      title: '序号',
      align: 'center',
      render(_value, _record, index) {
        return index + 1
      }
    },
    {
      title: '用户姓名',
      align: 'center',
      dataIndex: 'createdByName'
    },
    {
      title: '用户登录名',
      align: 'center',
      dataIndex: 'createdByAccountName'
    },
    {
      title: '订阅渠道',
      align: 'center',
      dataIndex: 'channel',
      render() {
        return _.map(value?.channel || [], v => _.get(ChannelMap, v, '--')).join('/')
      }
    },
    {
      title: '订阅时间',
      align: 'center',
      dataIndex: 'createdAt',
      render(val) {
        return moment(val).format('YYYY-MM-DD HH:mm:ss')
      }
    },
    {
      title: '操作',
      align: 'center',
      render(_value, row) {
        return (
          <Popconfirm
            title={`确定取消 " ${row.createdByName} " 的订阅么？`}
            onConfirm={() => handleDelete(row)}
            okText='确定'
            cancelText='取消'
          >
            <Button type='link'>取消订阅</Button>
          </Popconfirm>
        )
      }
    }
  ]

  // 过滤取消的订阅
  const dataSource = subscribeUsers.filter(v => !_.find(deleteSubscribes, (item => item?.id === v?.id)))

  return (
    <Modal
      title={title}
      open={visible}
      width={800}
      centered
      onCancel={handleClose}
      footer={
        <>
          <Button onClick={handleClose}>取消</Button>
          <Button loading={confirmLoading} type='primary' onClick={handleChangePushStatus}>
            {value?.pushStatus === 'push' ? '关闭推送' : '恢复推送'}
          </Button>
          <Button loading={confirmLoading} type='primary' onClick={handleSave}>确定</Button>
        </>
      }
      className='app-subscribe-modal'
    >
      <Spin spinning={loading}>
        <Form form={form} layout='inline' initialValues={{
          timeType: 'day',
          time: moment(`${moment().format('YYYY-MM-DD')} 18:00:00`)
        }}>
          <Form.Item name='timeType' label='推送配置'>
            <Select className='select-width' options={TimeTypeOptions} />
          </Form.Item>
          <Form.Item name='time'>
            <TimePicker />
          </Form.Item>
        </Form>

        <div className='subscribe-caption-content'>
          订阅用户总数<span className='subscribe-counts'>{total}</span> 人
        </div>

        <Table
          size='small'
          pagination={false}
          bordered
          rowKey='id'
          columns={columns}
          scroll={{ y: 250 }}
          dataSource={dataSource}
        />
      </Spin>
    </Modal>
  )
}
export default MiniAppSubscribeModal
