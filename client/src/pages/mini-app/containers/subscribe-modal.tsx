import { useRequest } from 'ahooks'
import _ from 'lodash'
import React, { useEffect } from 'react'

import withRefModal from '@/components/with-ref-modal'
import { Service } from '@/services'
import { useModelState } from '@/stores/models/mini-app-manage'

import { MiniAppSubscribeModal } from '../components'
import { useMiniAppSubscribeModal } from '../hooks'
import { getTimeInfo } from '../utils'


function SubscribeModal(props: { visible?: boolean, value?: any, modal?: any }) {
  const { visible, value, modal } = props

  const {
    subscribeLoading,
    subscribeTotal,
    subscribeUsers
  } = useModelState()

  const {
    onChangePushStatus,
    onSaveSubscribeModify
  } = useMiniAppSubscribeModal()

  const { run, data } = useRequest(async id => {
    const res: any = await Service.MiniApp.findOne({
      where: {
        id
      },
      include: {
        model: 'Project',
        required: true,
        attributes: ['id', 'title', 'description', 'releaseStatus', 'releaseSign']
      }
    })

    const typeInfo = getTimeInfo(res?.pushCron)
    return { ...res, ...typeInfo }
  }, { manual: true })

  useEffect(() => {
    // 初始化弹窗
    if (visible && value) {
      run(value?.id)
    }
  }, [visible, value])

  return (
    <MiniAppSubscribeModal
      title='订阅管理'
      value={data}
      modal={modal}
      visible={visible}
      loading={subscribeLoading}
      total={subscribeTotal}
      subscribeUsers={subscribeUsers}
      onChangePushStatus={onChangePushStatus}
      onSave={onSaveSubscribeModify}
    />
  )
}


export default withRefModal(SubscribeModal)
