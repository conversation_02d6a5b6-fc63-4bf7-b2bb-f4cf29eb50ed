import { useMemoizedFn } from 'ahooks'
import { message } from 'antd'
import { useEffect } from 'react'

import { useCommit } from '@/stores/models/mini-app-manage'

import type { AppGroupDataSourceItem } from '../components/group'


/**
 * 分类组件 业务逻辑
 * @returns
 */
export function useMiniAppGroup() {

  const commit = useCommit()

  useEffect(() => {
    // 初始化分类数据
    commit('asyncGetMiniAppGroup')
  }, [])

  const onSaveGroupModify = useMemoizedFn(async (data: { id: any, title: string }, { close }) => {
    if (!data.title) {
      message.info({ content: '目录名称不能为空' })
      return
    }
    const hasSave = await commit('asyncModifyMiniAppGroup', data)
    if (hasSave) close?.()
  })

  const onSelectGroup = useMemoizedFn(async (data: AppGroupDataSourceItem) => {
    await commit('update', { activeGroupId: data.id })
  })

  const onDeleteGroup = useMemoizedFn((data: AppGroupDataSourceItem) => {
    commit('asyncDeleteGroup', data)
  })

  return {
    onSaveGroupModify,
    onSelectGroup,
    onDeleteGroup
  }
}
