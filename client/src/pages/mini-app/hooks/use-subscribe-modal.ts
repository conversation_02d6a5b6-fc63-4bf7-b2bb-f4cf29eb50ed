import { useMemoizedFn } from 'ahooks'

import { useCommit } from '@/stores/models/mini-app-manage'


/**
 * 订阅管理
 * @returns
 */
export function useMiniAppSubscribeModal() {
  const commit = useCommit()

  const onSaveSubscribeModify = useMemoizedFn(async (data, ctx) => {
    await commit('asyncSaveSubscribeData', data)
    // 保存完毕，关闭
    ctx.close?.()
  })

  const onChangePushStatus = useMemoizedFn(async (data, ctx) => {
    await commit(
      'asyncModifyMiniApp',
      {
        id: data?.id,
        pushStatus: data?.pushStatus === 'stop' ? 'push' : 'stop'
      }
    )
    // 变更完毕，关闭
    ctx.close?.()
  })

  return {
    onSaveSubscribeModify,
    onChangePushStatus
  }
}
