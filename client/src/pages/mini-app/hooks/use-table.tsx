
import { useMemoizedFn } from 'ahooks'
import { But<PERSON> } from 'antd'
import dayjs from 'dayjs'
import _ from 'lodash'
import React, { useEffect, useMemo } from 'react'

import type { ColumnsType } from '@/components/basic-table'
import { AppStatusMap, ChannelMap } from '@/consts/mini-app-manage'
import { useCommit, useModelState } from '@/stores/models/mini-app-manage'


export interface UseMiniAppTableProps {
  miniAppSubscribeModalRef: React.RefObject<any>
  miniAppStatusChangeModalRef: React.RefObject<any>
}

/**
 * app应用内容
 * @returns
 */
export function useMiniAppTable(props: UseMiniAppTableProps) {
  const { miniAppSubscribeModalRef, miniAppStatusChangeModalRef } = props

  const commit = useCommit()
  const { activeGroupId, miniAppGroups } = useModelState()

  useEffect(() => {
    // 初始化数据
    commit('asyncInitMiniApps')
  }, [activeGroupId])

  const onSearch = useMemoizedFn(async data => {
    await commit('asyncInitMiniApps', { currentPage: 1, filter: data })
  })

  const onReset = useMemoizedFn(async () => {
    await commit('asyncInitMiniApps', { currentPage: 1, filter: null })
  })

  /** 修改状态 */
  const onChangeMiniAppStatus = useMemoizedFn(data => {
    miniAppStatusChangeModalRef.current?.show({ value: { id: data?.id, status: data?.status } })
  })

  /** 订阅管理 */
  const onSubscribe = useMemoizedFn(async data => {
    miniAppSubscribeModalRef.current?.show({
      value: {
        ...data
      }
    })
    commit('asyncInitSubscribeData', data)
  })

  const onSaveAppStatusModify = useMemoizedFn(async (data, ctx) => {
    await commit('asyncEditMiniApp', data)
    ctx.close?.()
  })

  const onChangePage = useMemoizedFn(async (current: number, size?: number) => {
    await commit('asyncInitMiniApps', { currentPage: current, pageSize: size })
  })

  const columns = useMemo<ColumnsType[]>(() => {
    const groupMap = _.keyBy(miniAppGroups, 'id')
    return [
      {
        title: '序号',
        width: 80,
        dataIndex: '_order'
      },
      {
        title: '应用全称',
        width: 180,
        dataIndex: ['project', 'title']
      },
      {
        title: '应用简称',
        width: 120,
        dataIndex: ['alias']
      },
      {
        title: '所属目录',
        dataIndex: ['appGroupId'],
        width: 120,
        render(val) {
          const res = _.get(groupMap, `[${val}].title`)
          if (!res) return '--'
          return res
        }
      },
      {
        title: '版本号',
        width: 100,
        dataIndex: ['project', 'releaseConfig', 'version'],
        render(val) {
          return val || '--'
        }
      },
      {
        title: '有效期',
        dataIndex: ['expiryDate'],
        width: 120,
        render(val) {
          if (!val) return '--'
          return dayjs(val).format('YYYY/MM/DD HH:mm')
        }
      },
      {
        title: '最后更新',
        dataIndex: 'updatedAt',
        width: 120,
        render(val) {
          return dayjs(val).format('YYYY/MM/DD HH:mm')
        }
      },
      {
        title: '渠道',
        dataIndex: ['channel'],
        width: 80,
        render(val) {
          if (_.isArray(val)) return _.map(val, v => _.get(ChannelMap, v, '--')).join('/')
          return val
        }
      },
      {
        title: '状态',
        width: 80,
        dataIndex: ['status'],
        render(v) {
          return AppStatusMap?.[v] || v
        }
      },
      {
        title: '操作',
        width: 200,
        align: 'center',
        render(_text, row) {
          return (
            <>
              <Button
                disabled={row.status === 'offShelves'}
                type='link'
                onClick={() => onChangeMiniAppStatus?.(row)}
              >
                变更状态
              </Button>

              <Button
                type='link'
                disabled={row.status === 'offShelves'}
                onClick={() => onSubscribe?.(row)}
              >
                订阅管理
              </Button>
            </>
          )
        }
      }
    ]
  }, [miniAppGroups])

  return {
    columns,
    onReset,
    onSearch,
    onChangePage,
    onSaveAppStatusModify
  }
}
