.mini-app-manage-center {
  display: flex;
  background-color: transparent;

  &-left {
    flex-shrink: 0;
    overflow-y: auto;
    border-radius: 4px;
    width: 230px;
    box-shadow: 1px 2px 4px rgba(@primary-color, 0.08);
    z-index: 3;
    background-color: #fff;
  }

  &-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    border-radius: 4px;
  }

  .mini-app-manage-filter {
    flex-shrink: 0;
    padding: 12px;
    background-color: #fff;
    box-shadow: 1px 2px 4px rgba(@primary-color, 0.04);
  }

  .mini-app-table {
    overflow-y: hidden;
    border-radius: 4px;
  }

  .mini-app-manage-center-right {
    padding: 16px;

    .my-basic-table {
      border-radius: 4px;
      background-color: #fff;
      padding-left: 12px;

      .ant-pagination {
        padding-right: 12px;
      }
    }
  }
}
