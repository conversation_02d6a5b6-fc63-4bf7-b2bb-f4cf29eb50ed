import './index.less'

import { fastMemo } from '@sugo/design/functions'
import React, { useRef } from 'react'

import BasicTable from '@/components/basic-table'
import Div from '@/components/div-wrap'
import WithDynamicTableSize from '@/components/with-dynamic-table-size'
import { useModelState } from '@/stores/models/mini-app-manage'

import {
  MiniAppFilter,
  MiniAppGroup,
  MiniAppStatusChangeModal
} from './components'
import SubscribeModal from './containers/subscribe-modal'
import { useMiniAppGroup, useMiniAppTable } from './hooks'


/**
 * 微应用管理中心
 */
export const MiniAppManage = fastMemo(() => {

  const miniAppSubscribeModalRef = useRef(null)
  const miniAppStatusChangeModalRef = useRef(null)

  const {
    activeGroupId,
    miniAppGroups,
    miniApps,
    tableLoading,
    appsTotal,
    pageSize,
    currentPage
  } = useModelState()

  const {
    onDeleteGroup,
    onSaveGroupModify,
    onSelectGroup
  } = useMiniAppGroup()


  const {
    columns,
    onSearch,
    onReset,
    onSaveAppStatusModify,
    onChangePage
  } = useMiniAppTable({
    miniAppSubscribeModalRef,
    miniAppStatusChangeModalRef
  })


  const isMain = window.__POWERED_BY_QIANKUN__

  return (
    <Div className='mini-app-manage-center'>
      <div className='mini-app-manage-center-left' >
        <MiniAppGroup
          title='应用目录'
          dataSource={miniAppGroups}
          selected={activeGroupId}
          onSelect={onSelectGroup}
          onSave={onSaveGroupModify}
          onDelete={onDeleteGroup}
        />
      </div>
      <div className='mini-app-table'>
        <MiniAppFilter onSearch={onSearch} onReset={onReset} />
        <WithDynamicTableSize>
          {({ containerHeight, scroll, ref }) => (
            <div className='mini-app-manage-center-right' ref={ref} style={{ height: containerHeight - 32, minHeight: 200 }}>
              <BasicTable
                dataSource={miniApps}
                showSizeChanger
                total={appsTotal}
                pageSize={pageSize}
                current={currentPage}
                scroll={scroll}
                loading={tableLoading}
                onChange={onChangePage}
                columns={columns}
              />
            </div>

          )}
        </WithDynamicTableSize>
      </div>
      <MiniAppStatusChangeModal
        title='变更状态'
        ref={miniAppStatusChangeModalRef}
        onSave={onSaveAppStatusModify}
      />

      <SubscribeModal ref={miniAppSubscribeModalRef} />
    </Div>
  )
})

export default MiniAppManage
