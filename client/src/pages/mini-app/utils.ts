
import { parseExpression } from 'cron-parser'
import dayjs from 'dayjs'

interface GetTimeTypeProps {
  dayOfMonth: number[]
  dayOfWeek: number[]
  month: number[]
}

const getTimeType = (fields: GetTimeTypeProps) => {
  if (fields?.month?.length === 12 && fields.dayOfWeek.length === 8 && fields.dayOfMonth.length === 31) return 'day'
  if (fields?.month?.length === 12 && fields.dayOfWeek.length === 8 && fields.dayOfMonth.length === 1) return 'month'
  return 'week'
}

export const getTimeInfo = (pushCron: string) => {
  if (!pushCron) return {
    time: '',
    timeType: ''
  }
  const interval = parseExpression(pushCron)
  const h = interval.fields.hour[0]
  const m = interval.fields.minute[0]
  const s = interval.fields.second[0]
  return {
    time: `${dayjs().format('YYYY-MM-DD')} ${h}:${m}:${s}`,
    timeType: getTimeType(interval.fields as unknown as GetTimeTypeProps)
  }
}
