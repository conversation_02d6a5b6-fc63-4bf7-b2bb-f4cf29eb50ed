import './index.less'

import { StarFilled, StarOutlined } from '@ant-design/icons'
import { CardPagination } from '@sugo/design'
import { Empty } from 'antd'
import _ from 'lodash'
import React, { memo } from 'react'

import cloud from '@/assets/icons/cloud.png'
import { useCommit, useModelState } from '@/stores/models/pc-app'
import type { AppList as AppListType } from '@/types/pc-app-manage.d'
import { generateColor } from '@/utils'
import { getPreviewUrl } from '@/utils/preview'

function PcAppComponent({ miniApp }: { miniApp: AppListType }) {
  const commit = useCommit()
  // 根据 id 来生成固定的颜色
  const handleStar = e => {
    e.stopPropagation()
    commit('forFavorite', miniApp.id)
  }
  return (
    <div
      className='rounded-lg bg-white w-[210px] h-[162px] gap-4 flex flex-col cursor-pointer justify-center items-center pc-app-card-item'
      // eslint-disable-next-line no-restricted-globals
      onClick={() => window.open(getPreviewUrl(miniApp?.project?.releaseSign))}
    >
      <div
        className='w-[56px] h-[56px] rounded-xl flex items-center justify-center'
        style={{ background: generateColor(miniApp.id) }}
      >
        <img src={cloud} alt='' />
      </div>
      <span className='flex gap-1 justify-center items-center'>
        {miniApp.alias}
        {miniApp?.isFavorite ? (
          <StarFilled
            className='!text-[#ffd700]'
            style={{ fontSize: '16px' }}
            onClick={handleStar}
          />
        ) : (
          <StarOutlined
            style={{ fontSize: '16px' }}
            onClick={handleStar}
          />
        )}
      </span>
    </div>
  )
}

// 分页控件
function PagingComponent() {
  const commit = useCommit()
  const { page, pageSize, total } = useModelState()

  return (
    <CardPagination isFixed
      current={page}
      pageSize={pageSize}
      total={total}
      showTotal={t => `共 ${t} 项`}
      onChange={p => commit('changePaging', { page: p, pageSize })}
    />
  )

}

function AppList() {
  const { appList } = useModelState()
  return (
    <>
      {appList?.length === 0 && <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />}
      <div className='gap-10 min-w-full flex overflow-auto flex-wrap h-full content-start pl-[15%] pr-[15%]' style={{ paddingTop: 12 }}>
        {_.map(appList, i => (
          <PcAppComponent key={i.id} miniApp={i} />
        ))}
      </div>
      <div className='flex items-center flex-col gap-10 justify-center mb-8 mt-8'>
        <PagingComponent />
      </div>
    </>
  )
}

export default memo(AppList)
