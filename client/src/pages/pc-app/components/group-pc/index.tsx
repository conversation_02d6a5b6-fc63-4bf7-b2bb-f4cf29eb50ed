import './index.less'

import { DownOutlined } from '@ant-design/icons'
import { Dropdown, Menu } from 'antd'
import classNames from 'classnames'
import _ from 'lodash'
import React, { memo } from 'react'

import { useCommit, useModelState } from '@/stores/models/pc-app'

function GroupComponent() {
  const commit = useCommit()
  const { groupList, activeGroup } = useModelState()
  const visibleItems = _.slice(groupList, 0, 5)
  const hiddenItems = _.slice(groupList, 5)

  const switchGroup = (id: string) => commit('switchGroup', id)

  const dropdownMenu = (
    <Menu>
      {_.map(hiddenItems, item => (
        <Menu.Item key={item.id} onClick={() => switchGroup(item.id)}>
          <span
            className={classNames({
              'hover:text-[#6C5EF3]': true,
              'active-group': activeGroup === item.id
            })}
          >
            {item.title}
          </span>
        </Menu.Item>
      ))}
    </Menu>
  )
  return (
    <div className='bg-white flex items-center justify-center text-base p-2 rounded pc-app-toolbar' style={{ userSelect: 'none' }}>
      {_.map(visibleItems, item => (
        <div
          key={item.id}
          onClick={() => switchGroup(item.id)}
          className={classNames({
            'pl-16 pr-16 cursor-pointer hover:text-[#6C5EF3] ': true,
            'pc-app-active-group': activeGroup === item.id
          })}
        >
          {item.title}
        </div>
      ))}
      <Dropdown overlay={dropdownMenu} trigger={['click']}>
        <DownOutlined />
      </Dropdown>
    </div>
  )
}

export default memo(GroupComponent)
