
import { fastMemo } from '@sugo/design/functions'
import React, { memo, useEffect } from 'react'

// import pcBottom from '@/assets/images/pc-bottom.png'
import Div from '@/components/div-wrap'
import { useCommit } from '@/stores/models/pc-app'

import AppList from './components/app-list'
import GroupComponent from './components/group-pc'

export const PcApp = fastMemo(() => {
  const commit = useCommit()

  useEffect(() => {
    commit('asyncInit')
    return () => {
      commit('resetState')
    }
  }, [commit])

  return (
    <Div
      className='h-screen w-full !bg-[#f4f6fc] pc-app-manage'
      style={{
        // background: `url(${pcBottom})`,
        backgroundPosition: 'top',
        backgroundSize: '100% auto',
        backgroundRepeat: 'no-repeat',
        borderRadius: 4
      }}
    >
      <section className='flex h-full flex-col items-center'>
        <div className='p-4 flex color-[#333333] font-[500] opacity-[0.87] flex-col justify-center items-center'>
          <h1>{window.sugo?.siteName || '数智云'}应用商店</h1>
          <GroupComponent />
        </div>
        <AppList />
      </section>
    </Div>
  )
})

export default memo(PcApp)
