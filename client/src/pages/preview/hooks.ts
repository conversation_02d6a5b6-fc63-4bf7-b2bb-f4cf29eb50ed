/* eslint-disable no-await-in-loop */
import '@/styles/nprogress.less'

import { useEventListener, useMemoizedFn } from 'ahooks'
import { message } from 'antd'
import Async from 'async'
import _ from 'lodash'
import { CSSProperties, useEffect, useState } from 'react'

import { useEditorCoreRuntimeAPI } from '@/cores/evaction'
import { Service } from '@/services'
import { store } from '@/stores'
import { useModelAction } from '@/stores/models/editor-core-preview'

type State = {
  loading: boolean
  error: string
  status: 'default' | 'fail' | 'password' | 'success'
  style: CSSProperties
  projectId: string
  screenId: string
  password: string
  release: {
    project?: any
    screen?: any
    [key: string]: any
  }
}

export const useData = ({ sign, query }) => {
  const actions = useModelAction()

  const [state, setState] = useState<State>({
    loading: false,
    error: '',
    // 默认，失败，密码验证，通过
    status: 'default',
    style: {}, // screen.style
    projectId: '',
    screenId: '',
    password: '',
    release: {}
  })

  const update = (newState: Partial<State>) => setState(s => ({ ...s, ...newState }))
  const wait = t => new Promise(rs => setTimeout(rs, t))

  const editorCorePreviewRuntimeAPI = useEditorCoreRuntimeAPI({ isPreview: true })
  const pageDidMounted = () => {
    logger.log('finish 页面挂载完成')
    editorCorePreviewRuntimeAPI?.getScreen().genEventHandler('unknown')?.didMounted?.(null)
  }

  // 查询数据
  const queryData = useMemoizedFn(async (screenId?: string | boolean) => {
    update({ loading: true })
    // nprogress.start()

    // 加载详情数据出来
    const args = {
      screenId: screenId || state.screenId as (string | undefined),
      projectId: query.projectId || state.projectId,
      sign,
      reportId: query.reportId,
      arhType: query.arhType
    }

    if (screenId === false) {
      args.screenId = undefined
    }

    try {
      if (args.arhType) {
        logger.log('arhType url:', window.location.href)
        logger.log('arhType args:', args)
      }
      const release = await Service.$execute('projectReleaseVerify', args)

      if (release?.error || !release) {
        update({
          error: release?.error || '找不到目标',
          loading: false,
          status: 'fail'
        })
        return
      }

      if (release?.project) {
        await actions.update({
          project: release.project,
          screen: release.screen
        })
      }

      if (release && release.project?.mode !== 'report') {
        await actions.asyncInitData(release)
      }

      update({
        projectId: release.projectId,
        screenId: release.screenId,
        release,
        status: 'success',
        style: release?.screen?.style || {}
      })

      // 预览运行时临时存储一些数据
      window.previewRuntime = {
        projectId: release.project.id,
        screenId: release.screen.id
      }

      pageDidMounted()
      return release
    } catch (err) {
      update({ status: 'fail' })
      logger.error(err)
    } finally {
      update({ loading: false })
      // nprogress.done()
    }
  })

  const checkQuery = useMemoizedFn(async (password?: string) => {
    update({ password: password || '' })
    await queryData(query.screenId)
  })

  const setError = err => {
    update({ status: 'fail', error: err })
    return undefined
  }

  /**
   * 初始化数据
   * - 如果 initState 里面有 project 就跳过主动查询
   * - 如果 initState 里面有 isNotFound 就直接显示 404
   */
  const initData = useMemoizedFn(async () => {
    // 未登录不加载
    if (!window._oAuthStatus && !_.get(window, 'sugo.user.id')) {
      console.log('预览失败，未登录')
      return
    }
    if (!sign) return setError('目标不存在')
    window.isPreviewPage = true

    const initState = window.initialState
    if (initState?.isNotFound) {
      update({ status: 'fail', error: initState.error, loading: false })
      return
    }

    // 服务端有发布数据过来
    if (initState?.release) {
      // 更新内部数据
      update({
        status: 'success',
        loading: false,
        screenId: initState.screenId,
        projectId: initState.projectId,
        style: initState?.release?.screen?.style || {},
        release: initState?.release
      })
      window.previewRuntime = {
        projectId: initState.projectId!,
        screenId: initState.screenId!
      }
      // 同步触发 redux
      if (initState.release && initState.release.project?.mode !== 'report') {
        await actions.asyncInitData(initState.release as any)
      }

      pageDidMounted()
      return
    }

    queryData(query.screenId ? query.screenId : false)
  })

  const postMessage = (type: string, data?: any) => {
    try {
      const pm = window.parent?.postMessage
      pm?.({ type, data }, window.origin)
    } catch (err) {
      logger.error(err)
    }
  }

  // 编辑并保存组件（特定值）
  const saveComponent = useMemoizedFn(async () => {
    const { filterLinkage, configLinkage } = store.getState().editorCorePreview || {}
    // const list = _.values(components.entities).filter(i => i.extraConfig?.isEditable)
    // 找出固定的可编辑组件
    if (!state.projectId || !state.screenId) {
      logger.error('缺失相关 id，无法保存组件', state)
      postMessage('save-component-success')
      return
    }
    const res = await Service.$execute('release.saveComponent', {
      key: `${state.projectId},${state.screenId}`,
      arhType: query.arhType,
      reportId: query.reportId,
      filterLinkage,
      configLinkage
    })
    logger.log('保存组件成功：', res)

    postMessage('save-component-success')
  })

  // 发布快照，遍历多个 screenId
  const releaseSnapshot = useMemoizedFn(async () => {
    const list = _.get(state, 'release.project.directory.nodes', [])

    const screenIds = _.filter(list, i => i.screenId).map(i => i.screenId) as string[]
    const oldScreenId = state.screenId
    const size = screenIds.length || 0

    const div = document.createElement('div')
    div.id = 'statization-progress'
    document.body.appendChild(div)

    window.doNotAutoQuery = true
    // 请求并切换数据
    const start = Date.now()
    const results = {}
    await Async.timesSeries(size, async (index, done) => {
      await wait(200)
      const screenId = screenIds[index]
      logger.log(`正在静态化：${index + 1}/${size}，${screenId}`)

      div.textContent = `正在静态化：${index + 1}/${size}`

      postMessage('arh-release-snapshot-progress', [index, size])

      await queryData(screenId)
      // 歇 1s
      await wait(800)
      // ... 查询全部数据
      const chartData = await actions.asyncQueryChartData({ componentKey: ['*'] })
      // 歇 1s
      results[screenId] = chartData

      done(null, true)
    })

    const time = _.floor((Date.now() - start) / 1000, 2)
    logger.log('results:', results)
    logger.log(`${size} 个页面静态化结束！耗时：${time}s，平均：${_.floor(time / size, 2)}s`)

    document.body.removeChild(div)
    window.doNotAutoQuery = false

    await wait(1000)
    // 发布为快照
    const params = {
      projectId: state.projectId,
      dataMap: results,
      reportId: query.reportId
    }

    logger.log('快照参数：', params)
    const res = await Service.$execute('release.arhSnapshot', params)
    if (!_.isEmpty(res)) {
      message.success('发布成功')
      postMessage('arh-release-snapshot-success')
    } else {
      message.error('发布失败')
      postMessage('arh-release-snapshot-fail')
    }

    logger.log('快照返回：', res)

    await wait(500)
    queryData(oldScreenId)

  })

  useEffect(() => {
    // nprogress.start().set(0.25)
    if (query.arhType === 'snapshotable') {
      window.doNotAutoQuery = true
    }
    if (query.arhType && window.isDev) {
      window.arh = { saveComponent, releaseSnapshot }
    }
  }, [query.arhType, state.screenId, state.status, state.loading])

  // postMessage({ type: 'xxx' }, window.origin)
  useEventListener('message', e => {
    if (!e.data.type) return
    // 保存组件
    if (window.isDev) {
      logger.log('监听到消息：', e.data.type, window.location.href)
    }
    if (!query.reportId) return
    if (query.arhType !== 'editable') return
    // 发布快照（定制）
    // if (e.data.type === 'save-component') saveComponent()
    if (e.data.type === 'arh-release-snapshot') releaseSnapshot()
  })

  // 触发保存组件
  useEventListener('reportComponentSave' as any, () => {
    if (!query.reportId) return
    if (query.arhType !== 'editable') return
    saveComponent()
  })

  return {
    state,
    initData,
    queryData,
    checkQuery
  }
}
