.screen-preview-main {
  width: 100%;
  height: 100%;
  position: relative;
  // background-color: rgba(#f8fafe, 0.5);
  background-color: transparent;

  .screen-preview-content {
    position: relative;
    // overflow: hidden;
  }
}

.password-verify {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin: 0 auto;
  padding-top: 30vh;
  width: 240px;
  .verify-icon {
    font-size: 100px;
  }

  button {
    margin: 12px 0;
  }

  .ant-alert {
    padding: 8px 12px;
  }
}

.preview-not-found {
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin: 0;
}

.print-mode-preview {
  background-image: url('~@/assets/icons/dot.svg');
  background-repeat: repeat;
  background-color: #fcfefe;
  background-size: 18px;
  user-select: none;
  overflow: auto;

  .print-views {
    margin-right: 295px;
  }
}

#statization-progress {
  position: fixed;
  top: 10px;
  right: 10px;
  background-color: #fff;
  box-shadow:
    1px 2px 6px rgba(#111, 0.12),
    0 0 2px rgba(#111, 0.1)
  ;
  z-index: 10000;
  font-weight: bold;
  border-radius: 3px;
  padding: 4px 8px;
}


// 有这个类，下面的组件会变成不可点击
.fix-allow-static-component {
  .screen-element[data-define-group='timePicker'],
  .screen-element[data-define-group='textInput'],
  .screen-element[data-define-group='numPicker'],
  .screen-element[data-define-group='selector'] {
    pointer-events: none !important;
    * {
      pointer-events: none !important;
    }
  }
}

