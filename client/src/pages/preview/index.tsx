import './index.less'

import { fastMemo } from '@sugo/design/functions'
import { useMatch } from '@umijs/max'
import { useTitle } from 'ahooks'
import { Result } from 'antd'
import cn from 'classnames'
import React, { useEffect } from 'react'

import { useMobileAdapter } from '@/hooks/use-mobile-adapter'
import { useQueryParams } from '@/hooks/use-query-params'

import { useData } from './hooks'
import PagePreviewContent from './page/content'
import PasswordVerify from './password-verify'
import PPTPreviewContent from './ppt'

/** 预览页 */
export const PreviewPage = fastMemo(props => {

  const shortMatch = useMatch({ path: '/preview/:sign' })
  const longMatch = useMatch({ path: '/abi/preview/:sign' })
  const match = shortMatch || longMatch
  const sign = props.query?.sign || match?.params.sign
  const query = props.query || useQueryParams()

  const { state, initData, checkQuery } = useData({ sign, query })
  const { loading, error, release: { project }, status } = state

  useTitle(project?.title || '')

  useEffect(() => {
    initData()
  }, [sign, query.screenId])

  useEffect(() => {
    if (
      window.location.pathname.indexOf('/preview') > -1 ||
      window.location.pathname.indexOf('/abi/preview/') > -1
    ) {
      setTimeout(() => {
        const win = window.top?.window || window
        // 隐藏 ai 聊天窗口
        win.$sugoAICopilot?.toggle?.(false)
      }, 300)
    }
  }, [])

  // 监听 window size 改变时，重置缩放大小
  useMobileAdapter(state.style)

  let Content: any = null

  // 显示欢迎页
  if (status === 'default') Content = null
  else if (status === 'password') {
    Content = <PasswordVerify loading={loading} error={error} onSubmit={checkQuery} />
  }
  else if (status === 'success' && project?.mode !== 'report') {
    Content = (
      <PagePreviewContent
        className={cn({ 'fix-allow-static-component': query.arhType === 'snapshotable' })}
      />
    )
  }
  else if (status === 'success' && project?.mode === 'report') {
    Content = (
      <PPTPreviewContent
        project={project}
        projectId={state.projectId}
        screenId={state.screenId}
      />
    )
  }
  else {
    Content = (
      <Result
        className='preview-not-found'
        status='error'
        title={error}
      />
    )
  }

  return Content
})

export default PreviewPage
