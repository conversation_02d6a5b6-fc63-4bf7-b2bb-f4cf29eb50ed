import React from 'react'

import { PREVIEW_CONTAINER_ID } from '@/consts/elements'
import PrintPreview from '@/pages/preview/print'
// 目前是直接复用 screen-dev-preview 的组件
import usePreviewContent from '@/pages/screen/containers/dev-preview/preview-hook'

import { useRenderContent } from '../render'

const ID = PREVIEW_CONTAINER_ID.replace(/^#/, '')

export interface PreviewContentProps {
  className?: string
}

/**
 * 页面内容
 * 项目和数据报告（ppt 也是用同一个）
 */
export default function PreviewContent(props: PreviewContentProps) {
  const { className } = props

  const {
    pageStyle,
    isPrintMode,
    screenId
  } = usePreviewContent({})

  const renderContent = useRenderContent({ className, ID, idPrefix: '' })

  if (isPrintMode) return (
    <PrintPreview
      idPrefix=''
      pageStyle={pageStyle as any}
      renderContent={renderContent}
    />
  )

  return renderContent({ screenId })
}
