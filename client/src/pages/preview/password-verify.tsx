import { Alert, Button, Input } from 'antd'
import React, { useRef } from 'react'

import CustomIcon from '@/components/icons/custom-icon'

export default function PasswordVerify(props) {
  const { loading, error, onSubmit } = props

  const inputRef = useRef<any>()

  // 验证密码
  const onEnter = async _e => {
    const val = inputRef.current.state.value
    if (val) onSubmit(val)
  }

  return (
    <div className='password-verify'>
      <CustomIcon type='verify' className='verify-icon' />
      <h3>访问验证</h3>
      <Input.Password
        placeholder='输入密码'
        maxLength={16}
        onKeyPress={e => e.code === 'Enter' && onEnter(e)}
        ref={inputRef}
      />
      <Button loading={loading} onClick={onEnter}>
        确定
      </Button>
      {error && <Alert type='error' description={error} />}
    </div>
  )
}
