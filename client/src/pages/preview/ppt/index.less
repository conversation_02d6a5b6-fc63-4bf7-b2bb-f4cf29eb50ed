.screen-design-ppt-preview {
  height: 100vh;
  width: 100vw;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 999;
  background-color: #fff;

  // 工具栏
  &-toolbox {
    position: absolute;
    box-shadow: 0 0 4px rgba(#111, 0.18);
    min-height: 35px;
    background-color: #fff;
    display: flex;
    align-items: center;
    right: 30px;
    bottom: 30px;
    z-index: 1;
    min-width: 100px;
    border-radius: 2px;
    padding: 4px 10px;

    &,
    * {
      cursor: default !important;
    }

    .logo {
      width: 25px;
      height: 25px;
      background-color: #f4f5f6;
      margin-right: 12px;
      border-radius: 3px;
      cursor: pointer !important;
    }
    .action {
      height: 26px;
      width: 26px;
      line-height: 26px;
      text-align: center;
      font-size: 15px;
      border-radius: 3px;
      // background-color: #faf3f4;
      margin: 0 5px;
      cursor: pointer !important;
      user-select: none;

      &:last-of-type {
        margin-right: 0;
      }

      &:hover {
        background-color: var(--tint-color-95);
      }

      svg {
        cursor: pointer;
      }
    }
  }

  &.cursor-active * {
    cursor: url('~@/assets/icons/cursor.svg'), auto !important;
  }
}
