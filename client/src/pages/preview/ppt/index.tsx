import './index.less'

import { useAsyncEffect } from 'ahooks'
import { klona as cloneDeep } from 'klona/lite'
import _ from 'lodash'
import React, { useState } from 'react'

import { Service } from '@/services'
import { useCommit } from '@/stores/models/editor-core-preview'
import type { ProjectType } from '@/types/entitys/project'

import PagePreviewContent from '../page/content'
import Toolbox from './toolbox'

export interface PPTPreviewProps {
  project: ProjectType
  screenId: string
  projectId: string
}

/**
 * 类似 ppt 的预览模式
 */
function PPTPreview(props: PPTPreviewProps) {
  const { project, projectId, screenId } = props
  const commit = useCommit()

  const [state, setState] = useState({
    index: 0, // 页面的索引位置，
    total: 0, // 总数
    loading: false,
    error: '',
    screenNodes: [] as any[],
    screenMap: {} as Record<string, any>
  })
  const update = (newState: Partial<typeof state>) => setState({ ...state, ...newState })
  const maxCount = state.screenNodes.length

  const getData = (index: number) => {
    const node = state.screenNodes[index]

    window.previewRuntime = {
      projectId,
      screenId: node?.screenId
    }

    return state.screenMap[node?.screenId]
  }

  const onExit = () => {
    window.opener = null
    window.open('', '_self')
    window.close()
  }
  const onNext = async () => {
    if (state.index === state.screenNodes.length - 1) return
    const index = state.index + 1
    update({ index })
    setTimeout(() => commit('asyncInitData', getData(index)), 100)
  }
  const onPrev = async () => {
    if (state.index === 0) return
    const index = state.index - 1
    update({ index })
    setTimeout(() => commit('asyncInitData', getData(index)), 100)
  }

  // 1. 加载定义表信息
  // 2. 加载页面信息
  // 3. 加载组件信息
  useAsyncEffect(async () => {
    if (!projectId) return console.error('项目 id 为空')

    // 按照目录顺序
    const screenNodes = _(project?.directory?.nodes || [])
      .filter(i => !!(i.screenId && !i.isFolder))
      .map(i => cloneDeep(i))
      .orderBy('order')
      .value()
    // 全部查询出来
    const screens = await Service.ScreenRelease.findAll({
      where: { projectId },
      attributes: ['components', 'componentDefine', 'screen', 'screenId']
    })
    const screenMap = _.keyBy(screens, 'screenId')
    const index = 0
    // const index = screenNodes.findIndex(i => i.screenId === screenId)
    update({ index, screenMap, screenNodes })

    const keys = _.keys(screenMap)
    const data = screenMap[screenNodes?.[index]?.screenId || (_.first(keys) as string) || '']

    window.previewRuntime = {
      projectId,
      screenId: data.id
    }

    if (!data) {
      logger.log('空值错误，检查 screenNodes')
    }

    if (data) await commit('asyncInitData', data as any)
  }, [])

  if (state.loading) {
    return (
      <div className='screen-design-ppt-preview'>
        <span>加载中...</span>
      </div>
    )
  }

  return (
    <div className='screen-design-ppt-preview'>
      <PagePreviewContent />
      <Toolbox onExit={onExit} onNext={onNext} onPrev={onPrev} index={state.index} count={maxCount} />
    </div>
  )
}

export default PPTPreview
