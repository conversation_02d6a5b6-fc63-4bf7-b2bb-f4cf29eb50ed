import { useEventListener, useKeyPress, useReactive } from 'ahooks'
import _ from 'lodash'
import React, { useEffect, useRef } from 'react'

// import CursorSvg from '@/assets/icons/cursor.svg'
import Icon from '@/components/icons/iconfont-icon'

export interface ToolboxProps {
  logo?: string
  onPrev: (e: any) => any // 上一页
  onNext: (e: any) => any // 下一页
  onExit: (e: any) => any // 退出
  index: number
  count: number
}

/**
 * 工具栏
 */
export default function Toolbox(props: ToolboxProps) {
  const { onExit, onNext, onPrev, index, count } = props
  const timeRef = useRef<any>(0)
  const state = useReactive({
    visible: false,
    isCursor: false
  })
  // 防抖
  const onLeftArrow = _.debounce(e => onPrev(e), 100)
  const onRightArrow = _.debounce(e => onNext(e), 100)
  const onMousemove = _.debounce(() => {
    clearTimeout(timeRef.current)
    state.visible = true
    timeRef.current = setTimeout(() => {
      state.visible = false
    }, 1000 * 5)
  }, 100)

  const onCursor = () => {
    state.isCursor = !state.isCursor
    const el = document.querySelector('.screen-design-ppt-preview') as HTMLElement
    if (!el) return console.error('element is not find.')

    // cursor-active
    if (el.classList.contains('cursor-active')) {
      el.classList.remove('cursor-active')
    } else {
      el.classList.add('cursor-active')
    }
  }

  useKeyPress('leftarrow', onLeftArrow)
  useKeyPress('rightarrow', onRightArrow)
  useEventListener('mousemove', onMousemove)

  useEffect(() => () => {
    clearTimeout(timeRef.current)
  }, [])

  if (!state.visible) return null

  return (
    <div className='screen-design-ppt-preview-toolbox'>
      <div className='logo' />
      <div className='action' title='回退' onClick={onPrev}>
        <Icon name='上一张' disabled={index === 0} />
      </div>
      <div className='action' title='前进' onClick={onNext}>
        <Icon name='下一张' disabled={index === count - 1} />
      </div>
      <div className='action' title='激光笔' onClick={onCursor}>
        <Icon name='笔' />
      </div>
      <div className='action' title='退出' onClick={onExit}>
        <Icon name='退出' />
      </div>
    </div>
  )
}
