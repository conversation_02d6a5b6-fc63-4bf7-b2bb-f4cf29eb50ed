
.screen-preview-all {
  height: 100%;
  position: relative;

  .full-center {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 60px;
    color: #999;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;

    .anticon-loading {
      margin-right: 8px;
      color: @primary-color;
    }
  }

  .preview-list {
    max-width: 100%;
    height: 45px;
    margin-bottom: 0;
    border-radius: 4px;

    .ant-tabs-content-holder {
      display: none;
    }

    .ant-tabs-nav {
      margin: 0;
      background-color: #fff;

      .ant-tabs-tab {
        padding: 10px 12px;
        border-radius: 8px 8px 2px 2px;
        min-width: 60px;

        .ant-tabs-tab-btn {
          margin: auto;
        }
      }

      .ant-tabs-tab + .ant-tabs-tab {
        margin: 0 0 0 4px;
      }

      .ant-tabs-tab-active {
        background-color: rgba(@primary-color, 0.08);
      }

      .ant-tabs-ink-bar {
        // height: 1px;
        border-radius: 2px;
        background-color: rgba(@primary-color, 0.65);
        transform: scaleX(0.35);
      }
    }

    .ant-tabs-nav-more {
      cursor: pointer;
      border-radius: 4px;
      overflow: hidden;
    }
  }

  .preview-container {
    position: relative;
    height: 100%;
    // overflow: auto;
    min-height: 300px;
    // transform: translateZ(0);
    border-radius: 4px;

    iframe {
      height: 100%;
      width: 100%;
      border: none;
      padding: 0;
      margin: 0;
    }
  }
}
