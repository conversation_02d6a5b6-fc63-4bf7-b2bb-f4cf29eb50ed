
import './preview-all.less'

import { LoadingOutlined } from '@ant-design/icons'
import { fastMemo } from '@sugo/design/functions'
import { useAsyncEffect } from 'ahooks'
import { Tabs } from 'antd'
import _ from 'lodash'
import React, { useRef, useState } from 'react'

// import WujieReact from 'wujie-react'
import Div from '@/components/div-wrap'
import { useCommit, useModelState } from '@/stores/models/editor-core-preview'

/**
 * 预览全部，有一个分组
 */
export const AbiPreviewAllPage = fastMemo(props => {
  const { tagId = 'all' } = props
  // ...
  const [active, setActive] = useState('')
  const projecRef = useRef<any>({})

  const [loading, setLoading] = useState(true)
  // 加载完后，没有内容就显示指引创建
  const [loaded, setLoaded] = useState(false)

  const list = useModelState(s => s.tagGroupMap[tagId] || [])
  const items = list.map(i => ({
    key: i.id,
    label: i.title
  }))
  const commit = useCommit()

  const renderLoading = () => (
    <div className='full-center'>
      <LoadingOutlined /> 加载中 ...
    </div>
  )

  useAsyncEffect(async () => {
    setLoading(true)
    const res = await commit('loadTagGroupBy', tagId)
    if (_.isArray(res)) {
      projecRef.current = res[0]
      setActive(_.first(res)?.id)
    }
    setLoading(false)
    setLoaded(true)
  }, [tagId])

  return (
    <div className='screen-preview-all'>
      <div className='preview-list'>
        <Tabs
          items={items}
          activeKey={active}
          popupClassName='theme-analysis-editor-preview-all-tab-popup'
          onChange={val => {
            const item = list.find(i => i.id === val)
            projecRef.current = item
            setActive(val)
          }}
        />
      </div>
      <Div className='preview-container'>
        {/* {active &&
          <WujieReact
            key={active}
            width='100%'
            height='100%'
            name={`app_abi_${active}`}
            url={`/abi/preview/${projecRef.current?.releaseSign}`}
            degrade
            props={{
              isNewBi: false,
              sugo: { ...window.sugo }
            }}
          />
        } */}
        {active &&
          <iframe
            key={active}
            width='100%'
            height='100%'
            title={`app_abi_${active}`}
            src={`/abi/preview/${projecRef.current?.releaseSign}`}
          />
        }
      </Div>

      {(!loaded || loading) && renderLoading()}
      {_.isEmpty(items) && loaded && <div className='full-center'>暂无内容</div>}
    </div>
  )
})
