/* eslint-disable no-template-curly-in-string */
import { BgColorsOutlined, EyeOutlined, QuestionCircleOutlined, RedoOutlined } from '@ant-design/icons'
import { useDebounceEffect } from 'ahooks'
import { Checkbox, Dropdown, Input, InputNumber, Popover } from 'antd'
import _ from 'lodash'
import React, { CSSProperties, useEffect, useMemo, useState } from 'react'
import { useDeepCompareEffect } from 'use-deep-compare'

import { FormSchema } from '@/components/form-schema'

interface HideSettingProps {
  text: string
  value: string[]
  onChange: (value: string[]) => any
}

interface TipPopoverProps {
  content?: string
}

interface FontSettingProps {
  valuePath: string
  value: CSSProperties | undefined
  onChange: (value: CSSProperties) => any
  ignore: string[]
}

interface PageHeaderSettingProps {
  title: string
  value: {
    enable?: boolean
    style?: CSSProperties
    text?: string
    hide?: string[]
  }
  onChange: (value: this['value']) => any
}

/** 隐藏设置 */
export function HideSetting({ text, value, onChange }: HideSettingProps) {
  const opts = [
    { label: '隐藏第一页', value: 'head' },
    { label: '隐藏奇数页', value: 'odd' },
    { label: '隐藏偶数页', value: 'even' },
    { label: '隐藏最后一页', value: 'tail' }
  ]
  const overlay = () => (
    <div className='print-mode-preview-hide-form-content'>
      <h4>{text}</h4>
      {opts.map(i => (
        <div key={i.value}>
          <Checkbox
            checked={_.includes(value, i.value)}
            onChange={e => {
              if (e.target.checked) { // 选中时加进去
                onChange([...value, i.value])
              } else { // 非选中
                onChange(_.filter(value, f => f !== i.value))
              }
            }}
          >
            {i.label}
          </Checkbox>
        </div>
      ))}
    </div>
  )

  return (
    <Dropdown
      placement='bottomRight'
      trigger={['click']}
      overlayClassName='print-mode-preview-hide-form-overlay'
      overlay={overlay}
    >
      <div className='font-setting'>
        <EyeOutlined /> 隐藏
      </div>
    </Dropdown>
  )
}


/** 字体设置 */
export function FontSetting({ valuePath, value, onChange, ignore }: FontSettingProps) {
  const schema = useMemo(() => ({
    'font': {
      'title': '字体',
      'type': 'font',
      'defaultValue': {
        'fontSize': 14,
        'fontFamily': 'Microsoft Yahei',
        'fontWeight': 'normal',
        'fontStyle': 'normal',
        'color': '#222',
        'lineHeight': 14
      },
      'valuePath': valuePath,
      'ignore': ignore
    }
  }), [valuePath, ignore])
  const val = useMemo(() => ({ [valuePath]: value }), [valuePath, value])

  return (
    <Dropdown
      placement='bottomLeft'
      trigger={['click']}
      overlayClassName='print-mode-preview-setting-form-overlay'
      overlay={
        <FormSchema
          schema={schema}
          value={val}
          onChange={v => onChange(v[valuePath])}
        />
      }
    >
      <div className='font-setting pointer'>
        <BgColorsOutlined /> 字体
      </div>
    </Dropdown>
  )
}

/** 提示语 */
export function TipPopover({ content }: TipPopoverProps) {
  return (
    <Popover
      placement='top'
      overlayClassName='print-mode-preview-setting-form-popoevr'
      content={content || (
        <div>
          目前支持语法：
          <ul>
            <li>index：当前页位置</li>
            <li>total：页面总数</li>
            <li>dayFormat：时间格式化函数</li>
          </ul>
          例如：
          <ul>
            <li>${'{'}index{'}'}页/${'{'}total{'}'}页</li>
            <li>${'{'}dayFormat({'\''}YYYY-MM-DD{'\''}){'}'}</li>
          </ul>
        </div>
      )}
    >
      <QuestionCircleOutlined />
    </Popover>
  )
}

/** 页头页尾设置 */
export function PageHeaderSetting(props: PageHeaderSettingProps) {
  const { onChange, title, value } = props

  const [text, setText] = useState<string | undefined>('')
  const update = (newValue: Partial<typeof value>) => {

    onChange({ ...value, ...newValue })
  }

  const onEnable = (checked: boolean) => {
    if (checked && !value.text) {
      update({ enable: checked, text: '${index}页/${total}页' })
    } else {
      update({ enable: checked })
    }
  }

  // 输入防抖
  useDebounceEffect(() => {
    update({ text })
  }, [text], { wait: 500 })

  // 同步数据
  useEffect(() => {
    setText(value.text)
  }, [value.text])

  return (
    <div className='form-item'>
      <div className='form-item-row'>
        <Checkbox checked={value.enable} onChange={e => onEnable(e.target.checked)}>
          {title}
          <TipPopover />
        </Checkbox>
        <FontSetting
          ignore={[]}
          valuePath='footerStyle'
          value={value.style}
          onChange={val => update({ style: val })}
        />
        <HideSetting text={title} value={value.hide || []} onChange={v => update({ hide: v })} />
      </div>
      <Input.TextArea
        placeholder={`请输入${title}`}
        rows={4}
        value={text}
        onChange={e => setText(e.target.value)}
      />
    </div>
  )
}

interface PageTransformSettingProps {
  value: number[],
  onChange: (value: number[]) => any
}

/** 页面的偏移设置 */
export function PageTransformSetting(props: PageTransformSettingProps) {
  const { value = [], onChange } = props

  const [val, setVal] = useState<number[]>([0, 0])
  const list = ['X 方向', 'Y 方向']

  useDebounceEffect(() => {
    onChange(val)
  }, [val], {
    wait: 500
  })

  // 这两个都是数组会存在引用的改变，因此要深度比较
  useDeepCompareEffect(() => {
    setVal(value)
  }, [value])

  const onSet = (v: number | undefined | null, index: number) => {
    const newVal = [...val]
    newVal[index] = v as number
    setVal(newVal)
  }

  return (
    <div className='page-transform-setting'>
      {list.map((item, index) => (
        <InputNumber
          key={index}
          step={50}
          placeholder='请输入'
          addonBefore={item}
          addonAfter={
            <RedoOutlined title='重置' onClick={() => onSet(undefined, index)} />
          }
          value={val?.[index] || undefined}
          onChange={v => onSet(v, index)}
        />
      ))}
    </div>
  )
}
