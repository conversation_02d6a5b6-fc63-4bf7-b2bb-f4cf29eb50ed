import { useMemoizedFn, useRequest } from 'ahooks'
import { Tooltip } from 'antd'
import dayjs from 'dayjs'
import isEqual from 'fast-deep-equal'
import { diff } from 'just-diff'
import _ from 'lodash'
import React, { CSSProperties, Fragment, useEffect, useRef, useState } from 'react'
import { useDeepCompareCallback, useDeepCompareMemo } from 'use-deep-compare'

import { PRINT_SIZE_MAP } from '@/consts/screen'
import { useEditorCoreRuntimeAPI } from '@/cores/evaction'
import { getSafePaperHeight } from '@/cores/evaction/config-resolver/print-page'
import { useQueryParams } from '@/hooks/use-query-params'
import { Service } from '@/services'
import type { Component, ComponentDefine } from '@/services/type'
import { useCommit, useModelState } from '@/stores/models/editor-core-preview'

import type { ParamsType } from './index'
import { getContentHeight, getCursors } from './utils'


type Paper = CSSProperties
type PaddingStyle = CSSProperties
type PageStyle = CSSProperties
type UsePrintParams = {
  onAfterPrint: () => any
  onBeforePrint: () => any
  bodyClassName: string
  style?: string
}

/** 查询发布表 */
async function queryTable(arhType: string, reportId: string, params: Record<string, any>) {
  let res: any = []

  // 运营资源定制
  if (arhType && reportId) {
    _.set(params, 'where.type', arhType)
    _.set(params, 'where.reportId', reportId)
    res = await Service.ArhScreenRelease.findAll(params)
  } else {
    res = await Service.ScreenRelease.findAll(params)
  }

  return res
}

/**
 * 获取选中的页面的信息
 * 如果是运营报告的要把 arhType，reportId 传过去
 */
export function useScreenData({ screenIds }) {

  const commit = useCommit()

  const { projectId, sign, screenId, screenOpts } = useModelState(s => ({
    projectId: s.project.id,
    sign: s.project.releaseSign,
    screenId: s.screen.id,
    screenOpts: _.filter(s.project.directory.nodes, i => !!i.screenId).map(i => ({
      label: i.screenId === screenId ? `${i.title}（当前页）` : i.title,
      value: i.screenId
    }))
  }), isEqual)

  const { arhType, reportId } = useQueryParams()

  // 存储数据，避免重新请求
  const cacheRef = useRef<{
    screenStyleMap: Record<string, any>, // key 是 id
    componentMap: Record<string, Component[]> // key 是 screenId
    componentDefineMap: Record<string, ComponentDefine> // key 是 define key
  }>({
    screenStyleMap: {},
    componentMap: {},
    componentDefineMap: {}
  })

  // 把所有页面的 style 查询过来
  const { data: screenStyleMap = {} } = useRequest(async () => {
    const params = {
      attributes: ['id', 'screenId',
        ['literal("JSON_EXTRACT(screen, \'$.style\')")', 'screenStyle']
      ],
      where: { sign, projectId }
    }

    const res: any[] = await queryTable(arhType, reportId, params)

    const styleMap = {}
    _.forEach(res, item => {
      styleMap[item.screenId] = item.screenStyle
      cacheRef.current.screenStyleMap[item.screenId] = item.screenStyle
    })

    return styleMap
  }, {
    refreshDeps: [screenOpts]
  })

  // 判断缓存在不在，在的话不请求
  const queryScreenData = useMemoizedFn(async () => {
    const ids: string[] = []

    // 已经请求过的不需要拿
    _.forEach(screenIds, id => {
      if (!cacheRef.current.componentMap[id]) {
        ids.push(id)
      }
    })

    // 这里找页面的发布表
    const params = {
      attributes: ['id', 'screenId', 'components', 'componentDefine'],
      where: { sign, projectId, screenId: { $in: ids } }
    }

    if (_.isEmpty(ids)) {
      logger.log('无 screen 查询，取缓存数据：', cacheRef.current)
    }

    if (!_.isEmpty(ids)) {
      const res: any[] = await queryTable(arhType, reportId, params)

      _.forEach(res, item => {
        cacheRef.current.componentMap[item.screenId] = item.components

        _.forEach(item.componentDefine, define => {
          cacheRef.current.componentDefineMap[define.key] = define
        })
      })
    }

    // 选择之后，把 component，componentDefineMap 加到 redux

    const componentDefines: any[] = []
    const components: any[] = []
    _.forIn(cacheRef.current.componentDefineMap, value => {
      componentDefines.push(value)
    })
    // 追加组件
    _.forIn(cacheRef.current.componentMap, list => {
      _.forEach(list, comp => {
        components.push(comp)
      })
    })

    logger.log('> 追加组件到 redux -->：', components, componentDefines)
    commit('printMultScreen', { components, componentDefines })
    logger.log('cacheRef:', cacheRef)
  })

  useDeepCompareMemo(() => {
    queryScreenData()
  }, [arhType, reportId, screenIds])

  return {
    screenStyleMap,
    componentMap: cacheRef.current.componentMap,
    componentDefineMap: cacheRef.current.componentDefineMap,
    screenOpts,
    projectId,
    sign,
    screenId
  }
}

/** 获取分页的游标 */
export function usePageCursors({ paddingStyle, pageStyle, compHeightDict, paper }) {

  // 分页后的页面个数
  // const { data } = useRequest(
  //   async () => {
  //     const yPadding =
  //       parseFloat((paddingStyle?.paddingTop as any) || 0) +
  //       parseFloat((paddingStyle?.paddingBottom as any) || 0)
  //     const pageMaxHeight = getContentHeight?.(compHeightDict) || _.parseInt(pageStyle?.height?.toString() || '0')
  //     // 这个 cursor 的值理解为页面内的 Y 坐标，不算边距
  //     return getCursors([], getSafePaperHeight(paper.height) - yPadding, pageMaxHeight)
  //   },
  //   {
  //     refreshDeps: [paper.height, paddingStyle, compHeightDict, pageStyle],
  //     debounceWait: 100 // 减少计算次数，不加的话会触发两次
  //   }
  // )

  const data = useDeepCompareMemo(() => {
    const yPadding =
      parseFloat((paddingStyle?.paddingTop as any) || 0) +
      parseFloat((paddingStyle?.paddingBottom as any) || 0)
    const pageMaxHeight = getContentHeight?.(compHeightDict)
      || _.parseInt(pageStyle?.height?.toString() || '0')
    // 这个 cursor 的值理解为页面内的 Y 坐标，不算边距
    return getCursors([], getSafePaperHeight(paper.height) - yPadding, pageMaxHeight)
  }, [paper.height, paddingStyle, compHeightDict, pageStyle])

  return {
    cursors: data || [],
    pageCount: _.size(data)
  }
}

/** 滚动页码显示 */
export function usePageing(
  { paper, screenStyleMap, params, singleScreenHeight }:
    { params: ParamsType, singleScreenHeight?: number, [key: string]: any }
) {
  const pageIndexRef = useRef<number>(1) // 从 1 开始算
  const tipRef = useRef<HTMLDivElement | null>(null)

  const style = useModelState(s => s.screen.style)

  const data = useDeepCompareMemo(() => {
    const { multipleScreenIds, multiplePageEnable } = params
    let _h = 0

    if (multiplePageEnable) {
      _.forEach(multipleScreenIds, id => {
        _h += screenStyleMap[id]?.height || 0
      })
    } else {
      _h = singleScreenHeight || _.toInteger(style?.height) || 0
    }

    const _count = _.ceil(_h / paper.height) || 0
    const _arr = _.isNaN(_count) ? [] : new Array(_count).fill(1)
    // ...
    return { count: _count, arr: _arr } as { count: number, arr: number[] }
  }, [params, paper, screenStyleMap, style, singleScreenHeight])

  // 页面个数，数组
  const { count, arr } = data

  // 显示当前滚动的页码
  const onScroll = useDeepCompareCallback(e => {
    const el = e.target as HTMLDivElement
    const list = [...new Array(count)].map((_v, i) => (i + 1) * paper.height)

    for (let i = 0; i < list.length; i++) {
      if (el.scrollTop <= list[i]) {
        pageIndexRef.current = i + 1
        tipRef.current!.innerHTML = `${i + 1} 页/${count} 页`
        return
      }
    }
  }, [paper, count])

  const Tip = useDeepCompareMemo(() => (
    <Tooltip
      title={`总高度：${paper.height * count}，每页高度：${_.toInteger(paper.height)}`}
      placement='left'
      key={paper.height}
    >
      <div className='print-tip' ref={tipRef}>1 页/{count} 页</div>
    </Tooltip>
  ), [paper, count])

  useEffect(() => {
    tipRef.current!.innerHTML = `${pageIndexRef.current} 页/${count} 页`
  }, [count])

  // 分页线
  const Line = useDeepCompareMemo(() => !(params.showLine ?? true) ? null : (
    <Fragment key='Line'>
      {arr.map((_line, index) => (
        index > 0 &&
        <div className='paging-line' key={index} style={{ top: index * paper.height + 1 }} title='分页线' />
      ))}
    </Fragment>
  ), [params.showLine, paper, arr])

  // index 从 1 开始
  const renderPagePagination = (show: boolean | undefined, index: number, hide: string[] | undefined, render) => {
    if (!show) return null
    if (index === 1 && _.includes(hide, 'head')) return null
    if (index % 2 === 1 && _.includes(hide, 'odd')) return null
    if (index % 2 === 0 && _.includes(hide, 'even')) return null
    if (index === count && _.includes(hide, 'tail')) return null
    return render()
  }

  // index 从 1 开始
  const genTempVariable = (text: string | undefined, index: number, total: number) => {
    try {
      return _.template(text)({ index, total, dayFormat: f => dayjs().format(f) })
    } catch (err) {
      logger.error(err)
      return '存在变量绑定错误，请重新输入'
    }
  }

  // 渲染全部分页
  const renderAllPagePagination = useDeepCompareCallback(
    (index, headerStyle?: CSSProperties, footerStyle?: CSSProperties) => (
      <Fragment key={index}>
        {renderPagePagination(params.headerEnable, index + 1, params.headerHide, () => (
          <pre className='page-header-text' style={{ ...params.headerStyle, ...headerStyle }}>
            {genTempVariable(params.headerText, index + 1, count)}
          </pre>
        ))}
        {renderPagePagination(params.footerEnable, index + 1, params.footerHide, () => (
          <pre className='page-footer-text' style={{ ...params.footerStyle, ...footerStyle }}>
            {genTempVariable(params.footerText, index + 1, count)}
          </pre>
        ))}
      </Fragment>
    ), [
    params.headerEnable,
    params.headerHide,
    params.headerStyle,
    params.headerText,
    params.footerEnable,
    params.footerHide,
    params.footerStyle,
    params.footerText,
    params.size,
    params.direction
  ])

  const PageNumber = useDeepCompareMemo(() => arr.map((_line, index) => (
    renderAllPagePagination(index,
      { top: index * paper.height + 2 }, // 页头位置
      { top: (index + 1) * paper.height - 38 } // 页尾位置
    )
  )), [paper, params, arr])

  return {
    Tip,
    Line,
    PageNumber,
    pageCount: count,
    onScroll
  }
}

/** 打印 */
export function usePrint(params: UsePrintParams) {
  const { onAfterPrint, onBeforePrint, bodyClassName, style } = params

  const handlePrint = async () => {
    // 只能通过隐藏样式，不然会引起 iframe 重新加载
    // 1. 设置隐藏样式
    // 2. 调用 window.print
    // 3. 换回去样式
    const styleDom = document.createElement('style')
    styleDom.innerHTML = style || ''
    styleDom.id = 'print-inner-style'

    document.head.appendChild(styleDom)
    document.body.classList.add(bodyClassName)

    const screenDesign = document.querySelector('.screen-design') as HTMLDivElement
    if (screenDesign) screenDesign.style.display = 'none'

    // 打印之后
    const afterprint = () => {
      try {
        window.removeEventListener('afterprint', afterprint)
        document.body.classList.remove(bodyClassName)
        document.head.removeChild(styleDom)
        if (screenDesign) screenDesign.style.display = 'flex'
        onAfterPrint()
      } catch (err) {
        logger.error(err)
      }
    }

    window.addEventListener('afterprint', afterprint)
    await onBeforePrint()

    const dom = document.querySelector('.print-active')
    if (!dom) throw new Error('打印元素不存在')

    window.print()
    // afterprint()
  }

  return handlePrint
}

/** 打印事件相关 */
export function usePrintEvent({ triggerId, idPrefix }) {
  const previewRuntimeAPI = useEditorCoreRuntimeAPI({ isPreview: true })

  const isHideRef = useRef<boolean>(false)

  useEffect(() => {
    // 这里应该把所有组件有打印预览的找出来
    const evs = previewRuntimeAPI?.getPrintPreviewEvent('enter') as any[]
    _.forEach(evs, ev => ev.printPreviewEnter?.())
    logger.log('进入打印预览：', triggerId)
    return () => {
      const evs2 = previewRuntimeAPI?.getPrintPreviewEvent('exit') as any[]
      _.forEach(evs2, ev => ev.printPreviewExit?.())
      logger.log('退出打印预览：', triggerId)
    }
  }, [triggerId])

  // 触发打印的按钮要隐藏不出现在打印里
  useEffect(() => {
    // if (isHideRef.current) return
    const el = document.getElementById(`${idPrefix}${triggerId}`)
    if (el) {
      el.style.display = 'none'
      isHideRef.current = true
    }
  })
}

const getPagerSize = (s: string, dir: 'landscape' | 'portrait') => {
  const d = PRINT_SIZE_MAP[s] || PRINT_SIZE_MAP.A4
  return dir === 'landscape' ? { width: d.height, height: d.width } : d
}

/** 内部数据 */
export function useDataState({ pageStyle, direction, size, paddingStyle, triggerId }) {
  const [isPrint, setIsPrint] = useState(false)

  /** 纸张宽高 */
  const paper = useDeepCompareMemo(() => getPagerSize(size, direction), [size, direction])

  const warning = useDeepCompareMemo(() => {
    const printW = paper.width
    const pageW = _.parseInt(pageStyle?.width?.toString() || '0px')
    if (pageW > printW * 1.2) return '纸张过小，建议设置较大的纸张'
    return ''
  }, [pageStyle.width, paper.width])

  /*
  A4纸大小：201mm*297mm
  对应的像素：794px*1123px
  所以 1mm = 3.78px
*/
  const toPrint = usePrint({
    bodyClassName: 'print-mode-preview-body',
    style: `
      .print-views-list-box {
        height: ${paper.height}px;
        width: ${paper.width}px;
        padding: ${['top', 'right', 'bottom', 'left']
        .map(i => paddingStyle?.[`padding${_.upperFirst(i)}`] || 0)
        .join('px ')}px;
      }
      /* 预览时支持分页 */
      .screen-design-dev-preview {
        position: static;
      }
      @page {
        size: ${size} ${direction || 'portrait'};
      }
    `,
    onBeforePrint: () => {
      logger.log('打印之前:', new Date().toLocaleString())
    },
    onAfterPrint: () => {
      logger.log('打印之后:', new Date().toLocaleString())
      setIsPrint(false)
    }
  })

  // 调用打印
  const onPrint = useMemoizedFn(() => {
    setIsPrint(true)
    const el = document.getElementById(triggerId)
    if (el) el.classList.add('no-print')
    setTimeout(toPrint, 200)
  })

  return {
    warning,
    paper,
    isPrint,
    onPrint
  }
}

/** 加载设置的表单 */
export function useSettingForm({ compHeightDict, params }: { params: ParamsType, [key: string]: any }) {
  const commit = useCommit()

  // 重绘内容
  const reCalcContentHeight = (val: ParamsType) => {
    const newPagerSize = getPagerSize(val.size, val.direction)
    const safePageHeight = getSafePaperHeight(newPagerSize.height)
    const yPadding =
      parseFloat((params.paddingStyle?.paddingTop as any) || 0) +
      parseFloat((params.paddingStyle?.paddingBottom as any) || 0)

    // 页高变化后，马上计算最大内容高度
    const nextCompHeights = _.map(compHeightDict, v =>
      _.includes(v.id, 'component-luckysheet')
        ? { ...v, height: v.getHeight(safePageHeight - yPadding) }
        : v
    )
    commit('update', prev => ({
      ...prev,
      print: { ...prev.print, compHeightDict: _.keyBy(nextCompHeights, 'id') } as any
    }))
  }

  const onSettingFormChange = useMemoizedFn(val => {
    if (params.autoPaging) {
      reCalcContentHeight(val)
    }
    commit('setPrintParams', { ...params, ...val })
  })

  return {
    onSettingFormChange
  }
}

/** 打印的样式 */
export function usePrintPageStyle({ paper, paddingStyle, pageTransform }) {

  const printPageStyle = useDeepCompareMemo(() => {
    const style = { ...paper, padding: 0 } as CSSProperties

    if (paddingStyle) {
      style.padding = `${['top', 'right', 'bottom', 'left']
        .map(i => paddingStyle?.[`padding${_.upperFirst(i)}`] || 0)
        .join('px ')}px`
    }

    const [x, y] = pageTransform || []
    if (x || y) {
      style.transform = `translate(${x || 0}px, ${y || 0}px)`
    }

    return style
  }, [paper, paddingStyle, pageTransform])

  return {
    printPageStyle
  }
}

/** 计算渲染时间，渲染次数 */
export function useTiming(componentName?: string, params?: any) {
  const countRef = useRef(0)
  const start = useRef(performance.now())
  const prevParams = useRef({})

  useEffect(() => {
    if (!window.isDev) return

    const end = performance.now()
    const timing = _.floor(end - start.current, 2)
    start.current = performance.now()

    const res = diff({ to: prevParams.current }, { to: params })

    prevParams.current = _.cloneDeep(params)

    logger.groupCollapsed(
      `%c[${componentName || ''}] %crender: ${countRef.current += 1}，interval time: %c${timing} %cms，${_.isEmpty(res) ? 'Not Diff' : ''}`,
      'color: #89f', 'color: #333', 'color: #f45', 'color: #333'
    )
    _.forEach(res, i => {
      logger.log(' diff:', `${i.op} -> ${i.path.join('.')}`, i.value)
    })
    logger.groupEnd()
  })

}
