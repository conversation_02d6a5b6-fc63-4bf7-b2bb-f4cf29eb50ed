
.print-mode-preview {
  position: absolute;
  width: 100%;
  min-height: 100%;
  overflow-y: hidden;

  .print-views {
    position: relative;
    margin-right: 300px;
    overflow: auto;
    padding: 30px 50px;
    max-height: 100vh;
  }

  .ruler-guide {
    .scena-manager {
      pointer-events: none;
    }
    .ruler.horizontal {
      display: none;
    }
    .ruler.vertical {
      background-color: #fff;
      box-shadow: 1px 2px 2px rgba(1, 1, 1, 0.1);
    }
    .rule-eye {
      display: none;
    }
  }

  .print-views-tool {
    position: absolute;
    top: 30px;
    left: 0;
    right: 0;
    width: 100%;
    margin: auto;
  }

  .print-views-list {
    position: relative; // 分割线
    margin: auto;
    box-shadow: 0 0 6px rgba(1, 1, 1, 0.12);
    background-color: #fff;
    border-radius: 5px;
    overflow: hidden;

    .print-views-list-box {
      position: relative; // 限制组件定位
      // A4
      height: 649.695px;
      width: 988.111px;
      box-sizing: border-box;
      transition: all 0.25s linear;

      overflow: hidden;
      background: #fff;

      pointer-events: none;
    }
  }

  // 分页时的线
  .paging-line {
    border-bottom: 2px dashed #f45;
    position: absolute;
    z-index: 1010;
    width: 100%;
    left: 0;
    right: 0;
  }

  // 开始打印时，去掉边距
  .print-active {
    .print-views-list-box {
      margin: 0;
      box-shadow: none;
      border: none;
      border-radius: 0;
      overflow: hidden;
    }
    .no-print {
      display: none;
    }
  }

  .print-setting-panel {
    position: fixed;
    right: 0;
    top: 0;
    width: 300px;
    height: 100%;
    background-color: #fff;
    border-left: 1px solid #f1f1f1;
    z-index: 902;

    > header {
      display: flex;
      align-items: center;
      padding: 10px 12px;
      border-bottom: 1px solid #f1f1f1;

      .anticon {
        cursor: pointer;
      }

      > span:first-of-type {
        margin: 0;
      }
      > button:last-of-type {
        margin-right: 0;
      }

      > span,
      > button {
        margin: 0 6px;
      }
    }
  }

  // 页码提示
  .print-tip {
    position: absolute;
    top: 10px;
    right: 310px;
    z-index: 10;
    color: var(--primary-color);
    background-color: #fff;
    border-radius: 3px;
    box-shadow: 0 0 6px rgba(1, 1, 1, 0.12);
    font-size: 14px;
    padding: 3px 8px;
    font-weight: 500;
  }

  .not-page {
    height: 500px;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #888;
    opacity: 0.6;
  }
}


.print-mode-preview {
  // 页头页尾
  .page-header-text {
    position: absolute;
    top: 0;
    width: 100%;
    font-size: 14px;
    z-index: 2000;
    padding: 8px 10px;
    margin: 0;
  }
  .page-footer-text {
    position: absolute;
    bottom: 0;
    width: 100%;
    font-size: 14px;
    z-index: 2000;
    padding: 8px 10px;
    height: 38px;
    overflow-y: hidden;
    margin: 0;
  }
}




// 打印时的样式（隐藏某些元素）
.print-mode-preview-body {

  // 页码在这里
  .print-views-tool {
    padding-top: 0;
    right: 0;
    top: 0;
    margin: 0;
  }
  .print-views-list-box {
    position: relative;
    margin: 0;
    box-shadow: none;
    border: none;
    overflow: hidden;
    box-sizing: border-box;
  }
  .print-views-list {
    box-shadow: none;
    border: none;
    background-color: transparent;
  }
  .paging-line {
    display: none;
  }
  // 页头页尾
  .page-header-text {
    position: absolute;
    top: 0;
    width: 100%;
    font-size: 14px;
    z-index: 2000;
    padding: 8px 10px;
    margin: 0;
  }
  .page-footer-text {
    position: absolute;
    bottom: 0;
    width: 100%;
    font-size: 14px;
    z-index: 2000;
    padding: 8px 10px;
    margin: 0;
  }

  // 隐藏相关样式
  .print-setting-panel,
  .print-tip,
  .ruler-guide {
    position: fixed;
    display: none;
    z-index: -1000;
  }
  .print-views {
    max-height: none;
    padding: 0 !important;
    margin: 0 !important;
    overflow-x: hidden;
  }
  .screen-design-dev-preview,
  .print-mode-preview {
    background-color: #fff;
    background-image: none;
  }
  .print-views-list-box > section.screen-main,
  .print-views-list-box > section.screen-preview-main {
    position: relative !important;
  }
  .print-views-list {
    margin: 0;
  }
  .print-views-list-box {
    padding: 0;
  }
}

