/* eslint-disable no-template-curly-in-string */
import './index.less'

import { Button } from 'antd'
import cn from 'classnames'
import isEqual from 'fast-deep-equal'
import _ from 'lodash'
import React, { CSSProperties, memo, useMemo, useRef } from 'react'
import { useDeepCompareMemo } from 'use-deep-compare'

import { PRINT_DIRECTION_OPTIONS, PRINT_SIZE_OPTIONS } from '@/consts/screen'
import RulerGuide from '@/pages/screen/components/canvas-layout/ruler-guide'
import { useCommit, useModelState } from '@/stores/models/editor-core-preview'

import {
  useDataState,
  usePageCursors,
  usePageing,
  usePrintEvent,
  usePrintPageStyle,
  useScreenData,
  useSettingForm,
  useTiming
} from './hooks'
import SettingForm from './setting-form'

export interface PrintPreviewProps {
  // 原始页面的高度
  pageStyle: {
    height: number,
    width: number
    [key: string]: any
  }
  renderContent: (parmas: any) => JSX.Element
  idPrefix: string
}

// 打印预览设置的参数
export interface ParamsType {
  size: string
  paddingStyle?: CSSProperties
  direction: 'portrait' | 'landscape'
  headerStyle?: CSSProperties
  headerEnable?: boolean
  headerText?: string
  headerHide?: ('head' | 'tail' | 'odd' | 'even')[]
  footerHide?: ('head' | 'tail' | 'odd' | 'even')[]
  footerEnable?: boolean
  footerText?: string
  footerStyle?: CSSProperties
  watermarkEnable: boolean
  watermarkText?: string
  watermarkStyle?: CSSProperties
  // TODO: 目前这个功能没有实现
  sliceEnable?: boolean // 对分页进行自由切片功能
  slicePosition?: number[] // 开启后，这个值应该等于游标值
  // ----
  showLine?: boolean // 是否显示分割线
  autoPaging?: boolean
  multiplePageEnable?: boolean
  multipleScreenIds?: string[] // 多页面打印
  pageTransform: number[]
}

// 上面的空闲距离
const PADDING_TOP = 30


function PrintPreviewContent(props) {
  const {
    paper,
    isPrint,
    autoPaging,
    multiplePageEnable,
    multipleScreenIds,
    cursors,
    printPageStyle,
    renderContentMemo,
    screenId,
    pageStyle,
    screenStyleMap
  } = props

  useTiming('PrintPreviewContent', _.omitBy(props, _.isFunction))

  // 渲染不自动分页的
  const NotAutoPaging = useDeepCompareMemo(() => {
    const getStyle = (s: CSSProperties) => ({
      padding: printPageStyle.padding,
      width: printPageStyle.width,
      transform: printPageStyle.transform,
      ...s
    })

    // 本页面
    const Page = (
      <div
        key='page'
        className='print-views-list-box'
        style={getStyle({
          height: pageStyle.height, // 原始页面高度
          minHeight: paper.height // 最小高度
        })}
      >
        {renderContentMemo({ screenId })}
      </div>
    )

    if (!multiplePageEnable) return Page

    // 页面列表
    const list: (string | JSX.Element)[] = [..._.filter(multipleScreenIds, id => screenId !== id && !!id)]
    // 把当前页面插入节点里
    const index = _.findIndex(multipleScreenIds, id => id === screenId)
    if (index > -1) list.splice(index, 0, Page)

    /* 启用多页面打印 */
    return list.map(sid => {
      if (!_.isString(sid)) return sid // 当前页面
      const style = screenStyleMap?.[sid] || {}
      if (!screenStyleMap?.[sid]) {
        logger.error('找不到 style 配置信息：', screenStyleMap, sid)
      }
      // ...
      return (
        <div className='print-views-list-box' key={sid} style={getStyle({
          height: style.height,
          minHeight: paper.height
        })}>
          {renderContentMemo({ screenId: sid })}
        </div>
      )
    })
  }, [
    pageStyle.height,
    paper.height,
    multiplePageEnable,
    multipleScreenIds,
    printPageStyle.padding,
    printPageStyle.width,
    printPageStyle.transform,
    renderContentMemo,
    screenId,
    screenStyleMap
  ])

  return (
    <div
      style={{ width: paper.width }}
      className={cn('print-views-list', { 'print-active': isPrint })}
    >

      {autoPaging === false && multiplePageEnable && _.isEmpty(multipleScreenIds) &&
        <div className='not-page'>请选择一个页面</div>
      }
      {/* 关闭自动分页 */}
      {autoPaging === false && NotAutoPaging}
      {/* 自动分页 */}
      {autoPaging !== false && cursors.map((val, index) => (
        <div className='print-views-list-box' key={`${val}-${index}`} style={printPageStyle}>
          {renderContentMemo({
            pageIdx: index,
            startY: index === 0 ? 0 : cursors[index - 1],
            endY: val,
            screenId
          })}
        </div>
      ))}

    </div>
  )
}

const PrintPreviewContentMemo = memo(PrintPreviewContent, isEqual)

/**
 * 打印预览组件
 * @returns
 */
function PrintPreview(props: PrintPreviewProps) {
  const { renderContent, pageStyle, idPrefix } = props

  const rootRef = useRef<HTMLDivElement | null>(null)
  const commit = useCommit()

  const printInfo = useModelState(s => s.print, isEqual)

  const { triggerId, compHeightDict } = printInfo
  const params = printInfo.params as ParamsType
  const { paddingStyle, size, multiplePageEnable, direction, multipleScreenIds, pageTransform } = params

  // 获取多页面的基础信息
  const { screenStyleMap, screenOpts, screenId } = useScreenData({ screenIds: multipleScreenIds })

  // 一些内部数据
  const { paper, isPrint, warning, onPrint } = useDataState({
    size,
    direction,
    pageStyle,
    triggerId,
    paddingStyle
  })

  // 分页后的页面个数
  const { cursors } = usePageCursors({
    compHeightDict,
    paddingStyle,
    pageStyle,
    paper
  })

  // 打印样式
  const { printPageStyle } = usePrintPageStyle({ paddingStyle, paper, pageTransform })
  // 表单的 change
  const { onSettingFormChange } = useSettingForm({ compHeightDict, params })
  // 分页线，页头，页尾等
  const { Tip, Line, PageNumber, onScroll } = usePageing({
    paper,
    params,
    screenStyleMap,
    singleScreenHeight: _.last(cursors)
  })

  usePrintEvent({ triggerId: printInfo.triggerId, idPrefix })

  /**
   * renderContentMemo({ pageIdx, startY, endY, screenId })
   * @param {{ pageIdx, startY, endY, screenId }}
   */
  const renderContentMemo = useDeepCompareMemo(
    // 加了一个记忆器避免重新计算
    () => _.memoize(renderContent, args => `${JSON.stringify(args)}-${params.autoPaging}`),
    [renderContent, params.autoPaging]
  )

  return (
    <div className='print-mode-preview'>
      {Tip}
      {/* 这里就是打印的页面列表 */}
      <div
        className='print-views'
        onScroll={onScroll}
        ref={rootRef}
        style={{ paddingTop: PADDING_TOP }}
      >
        <PrintPreviewContentMemo
          autoPaging={params.autoPaging}
          cursors={cursors}
          isPrint={isPrint}
          multiplePageEnable={multiplePageEnable}
          multipleScreenIds={multipleScreenIds}
          pageStyle={pageStyle}
          paper={paper}
          printPageStyle={printPageStyle}
          renderContentMemo={renderContentMemo}
          screenId={screenId}
          screenStyleMap={screenStyleMap}
        />
        <div className='print-views-tool' style={{ width: paper.width }}>
          {Line}
          {PageNumber}
        </div>
      </div>

      <div className='print-setting-panel'>
        <header>
          <span>打印预览</span>
          <div className='flex-1' />
          <Button size='small' onClick={() => commit('cancelPrint')}>取消</Button>
          <Button size='small' type='primary' onClick={onPrint}>下一步</Button>
        </header>

        <SettingForm
          warning={warning}
          sizeOptions={PRINT_SIZE_OPTIONS}
          diectionOpts={PRINT_DIRECTION_OPTIONS}
          screenOpts={screenOpts}
          value={params}
          onChange={onSettingFormChange}
          defalutSeleted={screenId}
        />

      </div>

      <RulerGuide
        containerRef={rootRef}
        keyId='print'
        zoom={1}
        verticalStyle={{ top: `${PADDING_TOP}px` }}
        defaultOptions={{
          backgroundColor: '#fff',
          textColor: '#333',
          showGuides: false,
          displayDragPos: false
        }}
      />
    </div>
  )
}

export default memo(PrintPreview, () => false)
