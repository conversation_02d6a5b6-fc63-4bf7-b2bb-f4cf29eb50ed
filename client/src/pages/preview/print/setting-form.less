
.print-mode-preview-setting-form {
  position: relative;

  .warning {
    background-color: tint(#f90, 75%);
    border: 1px solid tint(#f90, 40%);
    font-size: 14px;
    border-radius: 3px;
    padding: 3px 8px;
    margin-top: 6px;
  }

  .form-item {
    padding: 8px 12px;
    display: flex;
    flex-direction: column;

    > span:first-child {
      font-size: 14px;
      color: #777;
      margin-bottom: 5px;
    }
    .ant-checkbox + span {
      font-size: 14px;
      color: #777;
    }

    .anticon-question-circle {
      margin-left: 6px;
    }

    .abi-form-schema-margin-view {
      padding: 0;
    }

    > .form-item-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 5px;

      > .ant-checkbox-wrapper:first-child {
        flex: 1;
      }
    }

    .info-msg {
      font-size: 14px;
      color: #444;
      background-color: var(--tint-color-90);
      border: 1px solid var(--tint-color-70);
      font-size: 14px;
      border-radius: 3px;
      padding: 3px 8px;
      margin-top: 6px;
    }

    .slice-position {
      margin-top: 8px;
      > div {
        display: flex;
        align-items: center;
        font-size: 14px;
        margin-bottom: 2px;
        .anticon-pushpin {
          margin-right: 3px;
        }
      }
    }
  }

  .page-diection {
    flex-direction: row;
    > span:first-of-type {
      margin-right: 16px;
    }
  }

  .font-setting {
    margin-top: 4px;
    font-size: 14px;
    color: #888;
    cursor: pointer;
    margin-left: 12px;
  }

  .header-setting-tabs,
  .padding-setting-tabs {
    .ant-tabs-nav {
      margin-bottom: 0;
      padding-left: 16px;

      .ant-tabs-tab {
        padding: 4px 0;
        margin-left: 24px;
        &:first-of-type {
          margin: 0;
        }
      }
    }

    .form-item {
      margin-top: 0;
    }
  }

  .padding-setting-tabs .form-item {
    height: 140px;
  }

  .multiple-page-select {
    width: 100%;
    margin-top: 8px;
  }

  .ant-input-number-group-addon {
    background-color: #fff;
  }

  .page-transform-setting {
    margin-bottom: 6px;
    > div:first-of-type {
      margin-bottom: 6px;
    }
    .ant-input-number-group-addon {
      cursor: pointer;
    }
  }
}

.print-mode-preview-setting-form-overlay,
.print-mode-preview-hide-form-overlay {
  background-color: #fff;
  // border: 1px solid #f1f1f1;
  border-radius: 5px;
  box-shadow: 0 0 6px rgba(1, 1, 1, 0.14);
  width: 300px;
}

.print-mode-preview-hide-form-overlay {
  width: 180px;
  padding: 8px;
}

.print-mode-preview-hide-form-content {
  h4 {
    border-bottom: 1px solid #f1f1f1;
    padding-bottom: 5px;
  }
  span {
    color: #555;
  }
}

.print-mode-preview-setting-form-popoevr {
  .ant-popover-inner-content {
    border-radius: 5px;
    padding: 8px;
  }

  ul {
    font-size: 14px;
    margin: 0;
    padding-left: 24px;
  }
  ul:last-of-type {
    color: #345;
  }
}
