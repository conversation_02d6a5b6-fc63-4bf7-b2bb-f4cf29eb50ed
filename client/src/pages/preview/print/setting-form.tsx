import './setting-form.less'

import { useMemoizedFn } from 'ahooks'
import { Checkbox, Radio, Select, Tabs } from 'antd'
import isEqual from 'fast-deep-equal'
import _ from 'lodash'
import React, { memo, useMemo } from 'react'

import DebounceInput from '@/components/debounce-input'
import { FormSchema } from '@/components/form-schema'
import { enableSelectSearch } from '@/utils'

import { PageHeaderSetting, PageTransformSetting, TipPopover } from './comp'
import type { ParamsType } from './index'

export interface SettingFormProps {
  value: ParamsType
  warning: string | undefined
  onChange: (val: ParamsType) => any
  sizeOptions: any[]
  diectionOpts: any[]
  screenOpts: any[]
  defalutSeleted?: string
  loading?: boolean
}

/** 打印预览设置 */
function SettingForm(props: SettingFormProps) {
  const { value, onChange, sizeOptions, diectionOpts, warning, loading } = props
  const { screenOpts, defalutSeleted } = props

  const { multipleScreenIds = [], multiplePageEnable = false, pageTransform } = value
  const screenDict = useMemo(() => _.keyBy(screenOpts, 'value'), [screenOpts])

  const update = useMemoizedFn((newValue: Partial<typeof value>) => {
    const newVal = { ...value, ...newValue }
    if (isEqual(value, newVal)) return //
    onChange(newVal)
  })

  return (
    <div className='print-mode-preview-setting-form'>
      <div className='form-item'>
        <span>纸张大小</span>
        <Select
          placeholder='请选择纸张大小'
          options={sizeOptions}
          defaultValue='A4'
          value={value.size}
          onChange={size => update({ size })}
        />
        {warning && <div className='warning'>{warning}</div>}
      </div>
      <div className='form-item page-diection'>
        <span>纸张方向</span>
        <Radio.Group
          options={diectionOpts}
          value={value.direction || 'portrait'}
          onChange={e => update({ direction: e.target.value })}
        />
      </div>

      <Tabs
        className='padding-setting-tabs'
        defaultActiveKey='padding'
        items={[{
          label: '边距', key: 'padding', children: (
            <div className='form-item'>
              <span>页面边距
                <TipPopover content='部分设置可能不生效' />
              </span>
              <DebounceInput
                Component={FormSchema}
                schema={{ padding: { title: '', type: 'padding', defaultValue: {}, valuePath: 'paddingStyle' } }}
                value={value as any}
                onChange={nextVal => update({ paddingStyle: _.merge({}, value.paddingStyle, nextVal.paddingStyle) })}
                wait={1000}
              />
            </div>
          )
        }, {
          label: '偏移', key: 'transform', children: (
            <div className='form-item'>
              <span>页面偏移
                <TipPopover content='设置页面的偏移量，可以为负数' />
              </span>
              <PageTransformSetting
                value={pageTransform}
                onChange={val => update({ pageTransform: val })}
              />
            </div>
          )
        }]}
      />

      <Tabs
        className='header-setting-tabs'
        defaultActiveKey='header'
        items={[{
          label: '页头', key: 'header', children: (
            <PageHeaderSetting
              title='页头'
              value={{
                enable: value.headerEnable,
                hide: value.headerHide,
                style: value.headerStyle,
                text: value.headerText
              }}
              onChange={val => onChange({
                ...value, ...{
                  headerEnable: val.enable,
                  headerHide: val.hide,
                  headerStyle: val.style,
                  headerText: val.text
                } as any
              })}
            />
          )
        }, {
          label: '页尾', key: 'footer', children: (
            <PageHeaderSetting
              title='页尾'
              value={{
                enable: value.footerEnable,
                hide: value.footerHide,
                style: value.footerStyle,
                text: value.footerText
              }}
              onChange={val => onChange({
                ...value, ...{
                  footerEnable: val.enable,
                  footerHide: val.hide,
                  footerStyle: val.style,
                  footerText: val.text
                } as any
              })}
            />
          )
        }]}
      />

      <div className='form-item'>
        <span>辅助线</span>
        <div>
          <Checkbox
            defaultChecked
            checked={value.showLine ?? true}
            onChange={e => update({ showLine: e.target.checked })}
          >
            显示分页线
            <TipPopover content='在页面上显示分页线，并不会打印进去' />
          </Checkbox>
        </div>
      </div>

      <div className='form-item'>
        <h4>其他设置</h4>
        <div>
          <Checkbox checked={value.autoPaging} onChange={e => update({
            autoPaging: e.target.checked,
            multiplePageEnable: false,
            multipleScreenIds: multipleScreenIds?.length === 0 && defalutSeleted ? [defalutSeleted] : multipleScreenIds
          })}>
            组件自动换页（实验性支持）
            <TipPopover content='实验性支持不能应对所有场景，慎用。组件如果穿插在页面分割处会自动换到下一页，不支持和组合打印一起使用' />
          </Checkbox>
        </div>
        <div>
          <Checkbox
            checked={multiplePageEnable}
            // disabled={loading === true}
            onChange={e => update({
              multiplePageEnable: e.target.checked,
              multipleScreenIds: multipleScreenIds?.length === 0 && defalutSeleted ? [defalutSeleted] : multipleScreenIds,
              autoPaging: false // 这个要关闭
            })}
          >
            多页面组合打印（实验性支持）
            <TipPopover content='支持多个页面套打，注意预览模式不可用，需要在发布页上使用' />
          </Checkbox>
          <div>
            <Select
              className='multiple-page-select'
              options={screenOpts}
              mode='multiple'
              loading={loading}
              disabled={!multiplePageEnable}
              placeholder='请选择页面'
              value={multipleScreenIds.filter(i => !!screenDict[i])} // 过滤掉不存在的
              onChange={val => update({ multipleScreenIds: val })}
              {...enableSelectSearch}
            />
          </div>
        </div>
      </div>

    </div>
  )
}

export default memo(SettingForm, isEqual)
