/* eslint-disable no-template-curly-in-string */
import { produce } from 'immer'
import _ from 'lodash'
import * as transform from 'transform-parser'

import { State } from '@/stores/models/editor-core-preview'
import { Component } from '@/types/editor-core/component'

import type { ParamsType } from './index'


export function updateTranslateY(originTransform: string, y: number) {
  const trans = transform.parse(originTransform)
  trans.translate[1] = y
  return transform.stringify(trans)
}

// 获取默认值
export const getDefaultConfig = () => ({
  size: 'A4',
  direction: 'portrait',
  footerText: '${index}页/${total}页',
  headerText: '${index}页/${total}页',
  headerHide: [],
  footerHide: [],
  footerEnable: false,
  headerEnable: false,
  headerStyle: { fontSize: 14, textAlign: 'center' },
  footerStyle: { fontSize: 14, textAlign: 'center' },
  paddingStyle: { paddingTop: 30, paddingBottom: 10 },
  watermarkEnable: false,
  watermarkText: '水印',
  watermarkStyle: { fontSize: 20 } as Record<string, any>,
  sliceEnable: false,
  slicePosition: [],
  showLine: true,
  autoPaging: false,
  multiplePageEnable: false,
  multipleScreenIds: [],
  pageTransform: []
}) as ParamsType

/**
 * 获取分页的游标位置
 * [100, 500, 1500, 2246, 3369]
 */
export const getCursors = (slicePosition: number[], pageHeight: number, pageMaxHeight: number) => {
  // 获取页面的高度
  // 3000 / 1123 ~ 3 => [1123, 2246, 3369] 向上取整
  // [100, 500, 1500] => [100, 500, 1500, 2246, 3369] 计算最大值，
  const cs: number[] = _.sortBy(_.uniq([...slicePosition]))
  const pageCount = _.ceil(pageMaxHeight / pageHeight) // 原始能分到的个数
  const pageArr = [...new Array(pageCount)].map((_v, i) => (i + 1) * pageHeight)
  // const prevMaxCursor = _.max(cs) || 0
  // pageArr.forEach(i => {
  //   if (i > prevMaxCursor) cs.push(i)
  // })
  cs.push(...pageArr)
  return _.sortBy(_.uniq(cs)) // 去重
}

/** 根据页码，计算当前页的元素 */
export function getModifyFnForPrinting(
  modifyFn: (s: State, key: string, comp: Component) => Partial<Component>,
  compHeightDict: Record<string, { y: number; height: number; getHeight: (pageHeight?: number) => number }>
) {
  // const cache = {}
  // 不在当前页面的 component.config?.visible = false 即可隐藏
  // 对于特别长的组件，例如报表，需要通过 pageIdx 来控制输出哪一页
  return (pageIdx: number, startY: number, endY: number) =>
    function (s: State, k: string, comp: Component) {

      const nextComp = modifyFn(s, k, comp)
      const pageHeight = endY - startY
      const isReport = comp?.name === 'component-luckysheet'
      const { y, height: scrollHeight } = compHeightDict[comp.key] || {}
      const compEndY = y + scrollHeight

      const onlyInPrevPage = compEndY <= startY // 上一页的
      const unreachableInCurrPage = endY <= y // 不在当前页的
      const inCurrAndNextPage = y < endY && endY < compEndY // 中间件截断的

      const updateComp = (updater: (curr: Component) => any) =>
        produce(nextComp, draft => {
          if (!draft.style) {
            draft.style = comp.style ? { ...comp.style } : {}
          }
          updater(draft as any)
        })

      // 报表组件 TODO 抽出
      if (isReport) {
        if (onlyInPrevPage || unreachableInCurrPage) {
          return updateComp(draft => {
            draft.config!.visible = false
          })
        }
        // 部分看见的，报表组件，截断
        // 从本页开始，超出本页的
        return updateComp(draft => {
          if (!draft.config || !draft.style) return draft

          draft.config.page = pageIdx - Math.floor(y / pageHeight) + 1
          draft.config.pagePadding = 0
          draft.config.pageHeight = pageHeight
          if (draft.config.printMode) {
            draft.config.pageSize = 0
            draft.config.printMode = 'printing'
            draft.style.transform = updateTranslateY(draft.style.transform, Math.max(0, y - startY))
          }
          // draft.style!.height = `${Math.min(endY - y, compEndY - startY, pageHeight)}px`
          draft.eventAction = { keys: [], entities: {} } // 清除事件
        })
      }

      // 一般组件，从本页开始，超出本页的，放在下一页
      if (onlyInPrevPage || unreachableInCurrPage || inCurrAndNextPage) {
        return updateComp(draft => {
          draft.config!.visible = false
        })
      }

      // 仅本页能看见，y = y - viewStartY；
      // 部分看见的，除了报表组件，统一放在当前页的顶部
      return updateComp(draft => {
        // 已经渲染过了（避免重复渲染同一个组件）
        draft.style!.transform = updateTranslateY(draft.style!.transform, Math.max(0, y - startY))
      })
    }
}

/** 获取内容高度，之后会根据这个高度计算 cursor，需要考虑边距 */
export const getContentHeight = (compHeightDict: Record<string, any>) =>
  _.isEmpty(compHeightDict) ? 0 : _.max(_.map(compHeightDict, v => v.y + v.height))
