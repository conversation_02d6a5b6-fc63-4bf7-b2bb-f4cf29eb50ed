import cn from 'classnames'
import _ from 'lodash'
import React from 'react'
import { useDeepCompareCallback } from 'use-deep-compare'

import ElementList from '@/pages/screen/containers/dev-preview/elements'
import PageWatermarkWarp from '@/pages/screen/containers/dev-preview/page-watermark-warp'
import usePreviewContent from '@/pages/screen/containers/dev-preview/preview-hook'

type RenderContentParams = {
  pageIdx?: number,
  startY?: number,
  endY?: number,
  screenId?: string
}

export function useRenderContent({ className, ID, idPrefix }) {
  const {
    modifyFn,
    pageDomEventHandlerMap,
    pageStyle,
    modifyFnByPage,
    isPrintMode,
    screenConfig
  } = usePreviewContent({})

  const renderContent = useDeepCompareCallback((params?: RenderContentParams) => {
    const { pageIdx, startY, endY } = params || {}
    const sid = params?.screenId
    // 处理分页的
    const _modifyFn = _.isNil(pageIdx) ? modifyFn : modifyFnByPage(pageIdx, startY!, endY!)
    // 根据 screenId 过滤出组件
    const _componentSelector = sid ? c => c.screenId === sid : undefined

    return (
      <section className={cn('screen-preview-main', className)} {...pageDomEventHandlerMap}>
        <PageWatermarkWarp screenConfig={screenConfig} pageStyle={pageStyle}>
          <div id={ID} className='screen-preview-content' style={pageStyle}>
            <ElementList
              elementClassName='preview-element'
              isPreview
              idPrefix={idPrefix}
              modifyFn={_modifyFn}
              forceRender={isPrintMode}
              componentSelector={_componentSelector}
            />
          </div>
        </PageWatermarkWarp>
        {/* position=fixed 布局的容器 */}
        <div id='dev-preview-fixed-container' />
      </section>
    )
  }, [
    modifyFn,
    pageDomEventHandlerMap,
    pageStyle,
    modifyFnByPage,
    isPrintMode,
    screenConfig
  ])

  return renderContent
}
