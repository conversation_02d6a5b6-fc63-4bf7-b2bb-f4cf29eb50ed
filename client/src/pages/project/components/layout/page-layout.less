.project-layout {
  height: calc(100vh - 50px);
  position: relative;
  background-color: @app-bg-color;

  .app-title {
    font-weight: bold;
    color: #444;
    font-size: 16px;
    display: block;
    border-bottom: 1px solid #f8f8f8;
    padding: 16px;
    padding-bottom: 16px;
    min-width: 64px;
    display: inline-block;
  }

  // 左边的工具面板
  &-left {
    width: 220px;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 1px 2px 4px rgba(@primary-color, 0.08);

    &-search {
      background-color: #fff;
      border-bottom: 1px solid #f4f4f4;
    }

    &-menu {
      height: 200px;
      margin-top: 16px;
      border-bottom: 1px solid #f4f4f4;

      &-item {
        height: 36px;
        cursor: pointer;
        user-select: none;
        font-size: 15px;
        display: flex;
        align-items: center;
        padding: 0 30px;
        border-radius: 4px;
        margin-bottom: 4px;
        transition: none;

        .anticon {
          font-size: 17px;
          margin-right: 12px;
          transform: translateY(-1px); // 修复偏移
        }

        &:hover {
          background-color: var(--tint-color-95);
        }
        &.menu-active {
          color: var(--primary-color);
          background-color: var(--tint-color-90);
        }
      }
    }

    &-look {
      display: block;

      .not-data {
        color: #999;
        font-size: 14px;
        margin-left: 22px;
      }

      .recentlys {
        max-height: 500px;
        overflow-y: auto;
      }
      > h3:first-of-type {
        font-size: 15px;
        padding: 15px 20px 12px;
        margin: 0;
        .anticon {
          margin-right: 6px;
        }
      }
    }
  }

  &-right {
    margin-left: 220px;
    // background-color: #f5f6fc;
    height: 100%;
  }

  &-toolbox {
    // padding: 16px 6px 10px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 1px 2px 4px rgba(@primary-color, 0.04);
    // border-bottom: 1px solid #fafafa;
    padding: 12px 16px;

    & > div {
      display: flex;
      align-items: center;
      // padding: 6px 12px;
    }

    .tit {
      margin: 0;
      font-size: 17px;
      line-height: 1;
      color: #353440dd;
    }
    .tit-sub {
      color: #888;
      margin-left: 12px;
      font-size: 15px;
      line-height: 1;
    }
    .flex-1 {
      flex: 1;
    }
  }

  &-list {
    display: flex;
    flex-wrap: wrap;
    padding: 10px;
    overflow-y: auto;
    max-height: calc(100vh - 170px);

    > .ant-empty {
      margin: 30px auto;
    }
  }

}
