import './page-layout.less'

import { PlusOutlined } from '@ant-design/icons'
import { CardPagination } from '@sugo/design'
import { usePermissions } from '@sugo/design/hooks'
import { useDeepCompareEffect, useReactive } from 'ahooks'
import { Button, Select, Spin } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React from 'react'

import Div from '@/components/div-wrap'
import { EmptyData } from '@/components/empty-data'
import Icon from '@/components/icons/iconfont-icon'
import { DEVICE_TYPE_MAP } from '@/consts/screen'
import { useNewBi } from '@/hooks/use-new-bi'

const DEVICE_OPTIONS = [
  {
    label: '全部',
    value: -1
  },
  ..._.values(DEVICE_TYPE_MAP).map(i => ({
    label: i.title,
    value: i.value
  }))
]

export interface PageLayoutProps {
  loading: boolean
  itemTotal: number
  itemCount: number // 列表迭代个数
  lookItemCount: number
  /** 渲染列表的元素 */
  renderItem: (data: any, index: number) => JSX.Element | null
  /** 渲染最近记录的元素 */
  renderLookItem: (data: any, index: number) => JSX.Element | null
  renderSearchPanel: () => JSX.Element | null
  /** 过滤项 */
  filter: {
    mode: 'project' | 'report'
    device?: number | undefined
    title?: string | undefined
    tags?: string[]
    page: number
    pageSize?: number
    isTemplate?: boolean
  }
  onFilterChange: (filter: PageLayoutProps['filter']) => any
  // 左边的菜单栏
  menus: { label: string; value: string; icon: string }[]
  onMenuClick: (item: PageLayoutProps['menus'][number], e: any) => any

  // 插入的文本
  prefixText: string

  onCreateClick: (e: any) => any
}

/**
 * 用于项目列表的可复用布局组合
 * @param props
 */
export default function PageLayout(props: PageLayoutProps) {
  const { prefixText = '', filter, loading } = props
  const { menus = [], itemCount = 0, itemTotal = 0, lookItemCount = 0 } = props
  const { renderItem, renderLookItem, onMenuClick, onFilterChange, renderSearchPanel, onCreateClick } = props

  const state = useReactive({ active: '' })
  const { canAdd } = usePermissions({ canAdd: '/app/abi/project/add' })

  const items = [...new Array(itemCount)].map((_v, i) => i)
  const recentlys = [...new Array(lookItemCount)].map((_v, i) => i)

  const { isNewBi } = useNewBi()

  // 定制逻辑
  useDeepCompareEffect(() => {
    const temp = menus.find(i => i.value.indexOf('template') > -1)
    const other = menus.find(i => i.value.indexOf('template') === -1)
    state.active = (filter.isTemplate ? temp?.value : other?.value) || ''
  }, [filter.isTemplate, menus])

  return (
    <Div className='project-page project-layout'>
      <div className='project-layout-left'>
        {/* <div className='project-layout-left-search'>{renderSearchPanel()}</div> */}
        <div>
          <span className='app-title'>{prefixText === '数据报告' ? '数据报告' : '项目门户'}</span>
        </div>
        <div className='project-layout-left-menu'>
          {_.map(menus, item => (
            <div
              key={item.value}
              onClick={e => {
                e.stopPropagation()
                onMenuClick(item, e)
                state.active = item.value
              }}
              className={cn({
                'project-layout-left-menu-item': true,
                'menu-active': item.value === state.active
              })}
            >
              <Icon name={item.icon} />
              <span>{item.label}</span>
            </div>
          ))}
        </div>
        <div className='project-layout-left-look'>
          <h3>
            <Icon name='时间' />
            最近查看
          </h3>
          {recentlys?.length === 0 && <span className='not-data'>暂无数据</span>}
          <div className='recentlys'>{recentlys.map(renderLookItem)}</div>
        </div>
      </div>

      <div className='project-layout-right'>
        <div className='project-layout-toolbox'>
          <div className='project-layout-toolbox-header'>
            <h3 className='tit'>
              当前{filter.isTemplate ? '模板' : ''}
              {prefixText}
            </h3>
            <span className='tit-sub'>
              共 {itemTotal} 个{filter.isTemplate ? '模板' : ''}
              {prefixText}
            </span>

            {renderSearchPanel()}
            <div className='flex-1' />
            <Select
              // getPopupContainer={() => window.rootElement!}
              getPopupContainer={triggerNode => triggerNode.parentNode}
              options={DEVICE_OPTIONS}
              value={filter.device === undefined ? -1 : filter.device}
              style={{ width: 90, marginLeft: 12 }}
              // pageSize: undefined 是用于复原偏移量
              onChange={val => onFilterChange({ ...filter, device: val, page: 1, pageSize: undefined })}
            />
            {canAdd && (
              <Button type='primary' icon={<PlusOutlined />} onClick={onCreateClick} className='ml-4'>
                新建
              </Button>
            )}
          </div>
        </div>

        <Spin spinning={loading}>
          <>
            <div className='project-layout-list'>
              {items.map(renderItem)}
              {itemTotal === 0 && (
                <EmptyData
                  title={`暂无${prefixText}，您可以立即开始创建`}
                  buttonText='创建项目'
                  onCreate={onCreateClick}
                />
              )}
            </div>

            <CardPagination
              mode={isNewBi ? 'card' : 'default'}
              total={itemTotal}
              current={filter.page || 0}
              pageSize={filter.pageSize || 12}
              showTotal={total => `共 ${total} 条`}
              showSizeChanger={false}
              isFixed
              onChange={(page, pageSize) => onFilterChange({ ...filter, page, pageSize })}
            />
          </>
        </Spin>
      </div>
    </Div>
  )
}
