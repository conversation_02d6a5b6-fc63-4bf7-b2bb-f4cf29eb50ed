.project-item {
  position: relative;
  width: 220px;
  background-color: #fff;
  border: 1px solid #f1f1f1;
  border-radius: 4px;
  margin: 6px;
  height: 200px;
  // box-shadow: 0 0 4px rgba(#111, 0.08);
  text-align: center;

  .project-item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 30px;
    border-radius: 4px;

    .stauts {
      margin-right: 4px;
    }

    .tag {
      font-size: 12px !important;
      background-color: var(--tint-color-10);
      background-image: linear-gradient(to top left, var(--tint-color-10), #78f);

      border-radius: 0 5px 5px 0;
      padding: 0 5px;
      color: #fff;
      text-shadow: 0 0 1px rgba(#111, 0.08);
      cursor: default;
      &:hover {
        box-shadow: 1px 3px 3px rgba(#111, 0.1);
      }
    }
  }

  &:hover {
    box-shadow: 1px 1px 6px rgba(@primary-color, 0.12);
  }

  &-device {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1;
    background-color: #f4f4f4;
    width: 24px;
    height: 24px;
    border-radius: 2px;
    font-size: 18px;
    padding-top: 3px;
    border-radius: 3px;
    color: #666;
  }

  &-logo {
    display: flex;
    width: 45px;
    height: 45px;
    margin: 12px auto;
    text-align: center;
    background-color: var(--primary-color-80);
    background-image: linear-gradient(to top right, var(--primary-color-90), #78f 90%);
    border-radius: 10px;
    margin-top: 10px;
    font-size: 45px;
  }

  &-title {
    font-size: 15px;
    text-align: center;
    padding: 0 8px;
    height: 38px;
    cursor: default;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    padding-left: 4px;
    line-height: 1.2;

    .ant-input {
      z-index: 2;
      box-shadow: 0 0 16px var(--tint-color-70);
    }

    .anticon-edit {
      width: 0;
      overflow: hidden;
      color: var(--primary-color);
      transition: width 0.3s ease-in-out;
    }

    &:hover {
      .anticon-edit {
        width: 20px;
        cursor: pointer;
        user-select: none;
      }
    }
  }

  .project-item-time {
    font-size: 13px;
    color: #b1b3b5;
    margin-bottom: 3px;
    text-align: center;
  }

  &-actions {
    display: flex;
    align-items: center;
    border-top: 1px solid #f4f4f4;
    height: 40px;
    background-color: var(--tint-color-98);
    border-radius: 0 0 3px 3px;
    border-radius: 4px;
    background-color: #fff;

    > .action-item {
      flex: 1;
      border-right: 1px solid var(--border-color-base);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #666;
      cursor: pointer;
      user-select: none;
      list-style: none;

      &:last-of-type {
        border: none;
      }
      &:hover {
        color: var(--primary-color);
      }
      &.disabled {
        cursor: not-allowed;
        opacity: 0.4 !important;
      }
    }
  }
}

.project-item-share-overlay {
  // background-color: rgba(34, 34, 34, 0.88);
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 1px 2px 10px rgba(#111, 16%);
  width: 300px;
  padding: 8px;

  .qrcode-panel {
    display: flex;
    align-items: center;

    > .qrcode-view {
      margin-right: 8px;
      margin-bottom: 8px;
      border-radius: 3px;
      box-shadow: 0 0 6px rgba(#fff, 0.2);
    }
  }

  a {
    word-break: break-all;
    text-decoration: underline;
    // color: rgba(#fff, 0.88);
    &:hover {
      color: var(--primary-color-90);
    }
  }
}
