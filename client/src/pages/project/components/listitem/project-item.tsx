import './project-item.less'

import { EditOutlined } from '@ant-design/icons'
import { Tag } from '@sugo/design'
import { fastMemo, getFromNow } from '@sugo/design/functions'
import { usePermissions } from '@sugo/design/hooks'
import { NavLink } from '@umijs/max'
import { Dropdown, Input, Popconfirm, Tooltip } from 'antd'
import cn from 'classnames'
import dayjs from 'dayjs'
import _ from 'lodash'
import React, { useState } from 'react'

import CustomIcon from '@/components/icons/custom-icon'
import Icon from '@/components/icons/iconfont-icon'
import QrcodeView from '@/components/qrcode-view'
import type { Project } from '@/services/type'
import { copyText } from '@/utils/dom'
import { getPreviewUrl } from '@/utils/preview'

export interface ProjectItemProps {
  /** 动作列表 */
  actions: {
    label: string
    value: string
    icon: string
    isConfirm?: boolean
    confirmText?: string
    to?: (data: any) => string
  }[]
  onActionClick: (action: ActionType, e: any) => any
  /** 是否是模板 */
  logo?: string
  /** 启用不可选状态 */
  disabledFn?: (action: ActionType) => any
  /** 渲染的数据 */
  data: Project & { createdUser?: any }
  onEditTitle: (title: string) => any
  tags: { title: string, color?: string, id: string }[]

  isManageAuth: () => boolean
}

type ActionType = ProjectItemProps['actions'][number]

function MyNavLink(props) {
  return (
    <a target='_blank' className='link' rel='noreferrer' href={props.to} {...props}>
      {props.children}
    </a>
  )
}

function Title({ title, onEditTitle, disabled = false }) {
  const [val, setVal] = useState<string>(title)
  const [isEdit, setIsEdit] = useState(false)
  const [canEdit] = usePermissions(['/app/abi/project/edit'])
  const onSubmit = () => {
    if (val.trim().length !== 0) onEditTitle(val)
    setIsEdit(false)
  }

  if (isEdit) return (
    <div className='project-item-title' style={{ paddingRight: 0 }} title={title}>
      <Input
        value={val}
        placeholder='输入项目名称'
        autoFocus
        maxLength={30}
        onChange={e => setVal(e.target.value || '')}
        onKeyDown={e => e.key === 'Enter' && onSubmit()}
        onBlur={onSubmit}
      />
    </div>
  )

  return (
    <div className='project-item-title' title={title}>
      {!disabled && canEdit && (
        <Tooltip title='编辑名称'>
          <EditOutlined
            onClick={e => {
              e.stopPropagation()
              setVal(title)
              setIsEdit(true)
            }}
          />
        </Tooltip>
      )}
      {title}
    </div>
  )
}

/**
 * 项目列表的 item 渲染
 * @param props
 */
function ProjectItem(props: ProjectItemProps) {
  const { data, logo, actions = [], onActionClick, disabledFn, onEditTitle, tags = [], isManageAuth } = props

  const [submitLoading, setSubmitLoading] = useState(false)
  // const hasManage = DataPermission.allowAbiProjectManageById(data.id, data.createdBy) || isDev
  // { 0: { ... } }
  // const deviceMap = useMemo(() => _.keyBy(_.values(DEVICE_TYPE_MAP), 'value'), [])
  const user = data.createdUser || {}

  // 删除确认
  const onConfirm = async (item: ActionType, e) => {
    try {
      setSubmitLoading(true)
      await onActionClick(item, e)
    } finally {
      setSubmitLoading(false)
    }
  }

  // 渲染的元素
  const renderItem = (item: ActionType, isDisabled?: boolean, onClick?: Function) => {
    const itemClassName = cn({ 'action-item': true, 'disabled': isDisabled })
    const NavLinkTo = item.value === 'preview' ? MyNavLink : NavLink
    if (item.to) {
      return (
        <Tooltip title={item.label} destroyTooltipOnHide key={item.value}>
          <NavLinkTo
            key={item.value}
            target='_blank'
            to={item.to(data)}
            className={itemClassName}
            onClick={e => {
              if (isDisabled) return e.preventDefault()
              onClick?.(item, e)
            }}
          >
            <Icon name={item.icon} />
          </NavLinkTo>
        </Tooltip>
      )
    }
    return (
      <Tooltip title={item.label} destroyTooltipOnHide key={item.value}>
        <li key={item.value} onClick={e => !isDisabled && onClick?.(item, e)} className={itemClassName}>
          <Icon name={item.icon} />
        </li>
      </Tooltip>
    )
  }

  // 分享的 overlay
  const renderShareOverlay = (sign?: string) => (
    <div className='project-item-share-overlay'>
      {sign && (
        <>
          <div className='qrcode-panel'>
            <QrcodeView data={getPreviewUrl(sign)} />
            <div>移动端可手机扫码浏览</div>
          </div>
          <div>
            <MyNavLink to={getPreviewUrl(sign)}>{getPreviewUrl(sign)}</MyNavLink>
            <Icon name='copy' title='复制链接' className='mx-2.5' onClick={() => copyText(getPreviewUrl(sign), true)} />
          </div>
        </>
      )}
    </div>
  )

  // 渲染标题
  const renderTitle = (title: string) => {
    if (title.length > 16) {
      return (
        <Tooltip title={title} destroyTooltipOnHide={{ keepParent: true }}>
          <Title
            title={title}
            onEditTitle={onEditTitle}
            disabled={!isManageAuth() || disabledFn?.({ label: '编辑标题', value: 'editTitle', icon: '' })}
          />
        </Tooltip>
      )
    }
    return (
      <Title
        title={title}
        onEditTitle={onEditTitle}
        disabled={!isManageAuth() || disabledFn?.({ label: '编辑标题', value: 'editTitle', icon: '' })}
      />
    )
  }

  const renderTag = (title: string) => {
    if (title?.length > 8) return `${title.slice(0, 8)}...`
    return title
  }

  const releaseStatusMap = {
    released: '已发布',
    pause: '已暂停',
    revoke: '已撤销',
    default: '未发布'
  }

  const releaseStatusColorMap = {
    revoke: '#f90',
    released: '#3b5',
    pause: '#f45',
    default: '#888'
  }

  return (
    <div className='project-item'>
      {/* <Icon name={deviceMap[data.type]?.icon} className='project-item-device' /> */}

      <header className='project-item-header'>
        <span className='tag' title={tags?.[0]?.title}>
          {renderTag(tags?.[0]?.title)}
        </span>

        <Tag
          className='stauts'
          text={releaseStatusMap[data.releaseStatus || 'default']} bordered={false}
          color={releaseStatusColorMap[data.releaseStatus || 'default']}
        />
      </header>

      {logo === '项目' ? (
        <CustomIcon type='projectItem' className='project-item-logo' />
      ) : (
        <Icon name={logo} className='project-item-logo' />
      )}



      {renderTitle(data.title || '')}

      <div className='project-item-time'>
        <Tooltip title={dayjs(data.createdAt).format('YYYY-MM-DD HH:mm:ss')}>
          由 {user.first_name || '用户'} 创建于 {getFromNow(data.createdAt as string)}
        </Tooltip>
      </div>

      <footer className='project-item-actions'>
        {actions.map(item => {
          const isDisabled = disabledFn?.(item)

          if (!['preview'].includes(item.value) && !isManageAuth()) {
            return null
          }

          // 内置逻辑
          if (item.isConfirm && !isDisabled)
            return (
              <Popconfirm
                title={item.confirmText}
                okText='确定'
                cancelText='取消'
                key={item.value}
                okButtonProps={{ loading: submitLoading }}
                onConfirm={e => onConfirm(item, e)}
                destroyTooltipOnHide
              >
                {renderItem(item, isDisabled)}
              </Popconfirm>
            )
          // 定制逻辑
          if (item.value === 'share' && !isDisabled)
            return (
              <Dropdown
                // trigger={['click']}
                placement='bottom'
                key={item.value}
                overlay={renderShareOverlay(data?.releaseSign)}
                destroyPopupOnHide
              >
                {renderItem(item, isDisabled)}
              </Dropdown>
            )
          return renderItem(item, isDisabled, onActionClick)
        })}
      </footer>
    </div>
  )
}

// 性能优化
export default fastMemo(ProjectItem, _.isFunction)
