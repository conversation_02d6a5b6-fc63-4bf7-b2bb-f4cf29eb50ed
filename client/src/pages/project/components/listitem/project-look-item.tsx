import './project-look-item.less'

import { NavLink } from '@umijs/max'
import { useInterval } from 'ahooks'
import React, { useState } from 'react'

import { getFromNow } from '@/utils'

export interface ProjectLookItemProps {
  data: any
  onClick?: (e: any) => any
}

export default function ProjectLookItem(props: ProjectLookItemProps) {
  const { data, onClick } = props
  const [count, set] = useState(0)

  useInterval(() => {
    set(count + (1 % 10000))
  }, 1000 * 30) // 30s

  return (
    <NavLink to={`/project/${data.id}`} target='_blank' className='project-look-item' onClick={onClick}>
      <span className='title'>{data.title}</span>
      <span className='time'>{getFromNow(data.time)}</span>
    </NavLink>
  )
}
