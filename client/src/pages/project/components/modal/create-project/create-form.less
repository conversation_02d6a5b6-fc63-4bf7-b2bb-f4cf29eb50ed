.project-create-form {
  padding: 5px 24px;

  .right-col {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    flex: 1;
  }

  .device-type {
    width: 70px;
    height: 70px;
    border: 1px solid var(--border-color-base);
    border-radius: 3px;
    display: inline-block;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    cursor: pointer;
    .anticon {
      font-size: 16px;
      margin-bottom: 4px;
    }
  }

  .active {
    color: var(--primary-color);
    border-color: var(--tint-color-40);
  }

  .title,
  .ant-row {
    margin: 20px 0;
  }

  .tag-panel {
    cursor: default;
    background-color: #fefefe;
    min-height: 24px;
    .tag-box {
      border: 1px solid var(--border-color-base);
      border-radius: 3px;
      padding: 1px 4px;
      font-size: 13px;
      margin-right: 5px;
      margin-bottom: 5px;
      background-color: #fcfcfc;
      color: #444;
      user-select: none;
      cursor: pointer;
      & > .anticon {
        margin-left: 3px;
      }
    }
    .not-tag {
      color: #777;
    }
  }

  .close-template {
    margin-left: 4px;
    cursor: pointer;
  }
}
