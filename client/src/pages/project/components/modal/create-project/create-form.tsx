import './create-form.less'

import { CloseOutlined, PlusOutlined } from '@ant-design/icons'
import { Button, Col, Input, Row } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React from 'react'

import Icon from '@/components/icons/iconfont-icon'
import { DEVICE_TYPE_MAP } from '@/consts/screen'

export interface CreateFormProps {
  title: string
  mode?: 'project' | 'report'
  tagMap: Record<string, any> // 标签的全量字典
  value: {
    title: string
    type: number
    templateId: string
    templateName: string
    tags: string[] // 标签 Id
    [key: string]: any
  }
  onChange: (value: CreateFormProps['value']) => any
  renderSelectTagPanel: (tags: string[]) => JSX.Element | null
  onSelectTemp: () => any
}

/**
 * 创建的表单
 * @param props
 */
export default function CreateForm(props: CreateFormProps) {
  const { title, value, onChange, tagMap, renderSelectTagPanel, mode, onSelectTemp } = props
  const tags = (_.isArray(value.tags) ? value.tags : []) || []

  return (
    <div className='project-create-form'>
      <h3 className='title'>{title}</h3>

      <Row>
        <Col span={4}>{mode === 'report' ? '报告名称' : '项目名称'}</Col>
        <Col className='right-col'>
          <Input
            placeholder='输入名称'
            maxLength={20}
            className='!w-[250px]'
            value={value.title}
            onChange={e => onChange({ ...value, title: e.target.value?.trim() })}
          />
        </Col>
      </Row>

      {mode !== 'report' &&
        <Row>
          <Col span={4}>默认终端</Col>
          <Col className='right-col'>
            {_.values(DEVICE_TYPE_MAP).map(item => (
              <div
                key={item.value}
                className={cn({
                  'device-type': true,
                  'active': value.type === item.value
                })}
                onClick={() => onChange({ ...value, type: item.value })}
              >
                <Icon name={item.icon} />
                <div>{item.title}</div>
              </div>
            ))}
          </Col>
        </Row>
      }

      <Row>
        <Col span={4}>项目标签</Col>
        <Col className='right-col' style={{ flexDirection: 'column', alignItems: 'flex-start' }}>
          <div className='tag-panel right-col'>
            {tags.map(tagId => (
              <div
                className='tag-box'
                key={tagId}
                onClick={e => {
                  e.stopPropagation()
                  onChange({ ...value, tags: tags.filter(id => id !== tagId) })
                }}
              >
                {tagMap[tagId]?.title || '未知标签'}
                <PlusOutlined title='移除' rotate={45} />
              </div>
            ))}
            {_.isEmpty(tags) && <span className='not-tag'>无</span>}
          </div>

          {tags?.length < 5 && renderSelectTagPanel(tags)}
        </Col>
      </Row>

      <Row>
        <Col span={4}>已选模板</Col>
        <Col>
          {value.templateId ?
            <span>{value.templateName}</span> :
            <>
              <div>无</div>
              <Button
                size='small'
                type='dashed'
                icon={<PlusOutlined />}
                className='active mt-1'
                onClick={onSelectTemp}
              >
                选择模板
              </Button>
            </>
          }

          {value.templateId &&
            <CloseOutlined
              className='close-template'
              title='删除模板'
              onClick={() => onChange({ ...value, templateId: '' })}
            />
          }
        </Col>
      </Row>
    </div>
  )
}
