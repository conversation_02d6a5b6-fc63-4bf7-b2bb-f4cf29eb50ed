.project-create-modal {
  .ant-modal-body {
    padding: 0 !important;
  }

  &-content {
    min-height: 400px;

    .form-panel {
      padding-right: 32px;
    }

    .ant-tabs-tabpane {
      padding-left: 0 !important;
    }
    .ant-tabs-nav-list {
      width: 120px;
      padding-top: 16px;

      .ant-tabs-tab {
        > div {
          margin: auto;
        }
        margin-top: 4px !important;
        height: 36px;
        &.ant-tabs-tab-active,
        &:hover {
          background-color: var(--tint-color-95);
        }
      }
      .ant-tabs-ink-bar {
        display: none;
      }
    }
  }
}

.project-create-tags-manager-overlay {
  background-color: #fff;
  border-radius: 3px;
  box-shadow: 1px 1px 10px rgba(#111, 0.1);
  width: 360px;
  overflow: hidden;
}
