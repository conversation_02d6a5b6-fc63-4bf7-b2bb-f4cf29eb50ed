import './index.less'

import { PlusOutlined } from '@ant-design/icons'
import { useReactive } from 'ahooks'
import { Button, Dropdown, message, Tabs } from 'antd'
import { nanoid } from 'nanoid'
import React, { useEffect } from 'react'

import Modal from '@/components/customs/custom-modal'
import withRefModal from '@/components/with-ref-modal'
import TagsManager, { TagManagerProps } from '@/pages/project/components/tags-manager'
import TemplateSelectConatiner from '@/pages/project/containers/template-select'
import { useCommit, useModelState } from '@/stores/models/project-manager'

import CreateForm from './create-form'

export interface CreateProjectModalProps {
  mode: 'project' | 'report'
  isEdit?: boolean
  data?: any // 当 mode 为 edit 时，需要传进来
  prefixText?: string
  isTemplate?: boolean
  tagMap: TagManagerProps['tagMap'] // 标签的全量字典

  templateId?: string
  templateName?: string

  onSuccess?: (data: any) => any
  onlyCreate?: boolean
}

/**
 * 创建 + 编辑项目的弹窗
 */
export const CreateProjectModal = withRefModal<Partial<CreateProjectModalProps>>(props => {
  const { modal, visible, mode, isEdit, isTemplate, onSuccess } = props

  const commit = useCommit()

  const tagMap = useModelState(s => s.tagMap)

  const onCreateTag = tag => commit('createTag', tag)
  const prefixText = props.prefixText || mode === 'project' ? '项目' : '报告'

  let title = !isEdit ? `创建空白${prefixText}` : `编辑${prefixText}`
  if (isTemplate) title += '模板'

  const initData = {
    type: 0,
    title: '' as string,
    templateId: props.templateId || '' as string,
    templateName: props.templateName || '' as string,
    tags: [] as string[]
  }

  const state = useReactive({
    data: initData,
    submitLoading: false,
    activeKey: 'create' as 'create' | 'temp' | (string & {})
  })

  useEffect(() => {
    state.data = {
      type: 0,
      title: '',
      templateId: '',
      templateName: '',
      tags: []
    }
    if (visible) {
      commit('initTagMap')
    }
    if (!visible) {
      state.activeKey = 'create'
    }
  }, [visible])

  useEffect(() => {
    if (visible) {
      state.data.templateId = props.templateId || ''
    }
  }, [visible, props.templateId])

  useEffect(() => {
    if (visible) {
      state.data.templateName = props.templateName || ''
    }
  }, [visible, props.templateName])

  const onSubmit = async () => {
    state.submitLoading = true
    try {
      await commit('createProject', { mode, isTemplate, ...state.data })
      message.success('创建成功')
      modal.hide(true)
      onSuccess?.({ mode, isTemplate, ...state.data })
    } catch (err: any) {
      message.error(`创建失败：${err?.message}` || '0001')
    } finally {
      state.submitLoading = false
    }
  }

  const renderSelectTagPanel = () => (
    <Dropdown
      placement='bottomLeft'
      trigger={['click']}
      overlayClassName='project-create-tags-manager-overlay'
      overlay={
        <TagsManager
          tagMap={tagMap}
          value={state.data.tags}
          onCreate={onCreateTag}
          onSelect={tags => (state.data.tags = tags)}
        />
      }
    >
      <Button size='small' type='dashed' icon={<PlusOutlined />} className='active mt-1'>
        选择标签
      </Button>
    </Dropdown>
  )

  return (
    <Modal
      title=''
      open={visible}
      onCancel={() => modal.hide()}
      okButtonProps={{
        loading: state.submitLoading
      }}
      onOk={onSubmit}
      maskClosable={false}
      className='project-create-modal'
      width={600}
    >
      <Tabs
        className='project-create-modal-content'
        animated={false}
        activeKey={state.activeKey}
        onChange={activeKey => (state.activeKey = activeKey)}
        tabPosition='left' // ...
      >
        <Tabs.TabPane key='create' tab='新建'>
          <CreateForm
            title={title}
            value={state.data}
            onChange={val => (state.data = val)}
            tagMap={tagMap}
            mode={mode}
            renderSelectTagPanel={renderSelectTagPanel}
            onSelectTemp={() => state.activeKey = 'temp'}
          />
        </Tabs.TabPane>
        <Tabs.TabPane key='temp' tab='模板'>
          <TemplateSelectConatiner
            mode={mode!}
            templateId={state.data.templateId}
            onSelect={(templateId: string, data) => {
              state.data.templateId = templateId
              state.data.templateName = data.title
              state.data.title = state.data.title || `${data.title.slice(0, 12)}_new_${nanoid(3)}`.trim()
              state.data.tags = data.tags
              state.data.type = data.type || 0
              state.activeKey = 'create'
            }}
          />
        </Tabs.TabPane>
      </Tabs>
    </Modal>
  )
})

export default CreateProjectModal
