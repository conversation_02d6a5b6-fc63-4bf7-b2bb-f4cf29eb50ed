.project-list-search-toolbox {
  padding: 0;
  margin-left: 12px;

  .anticon-search {
    opacity: 0.65;
  }

  .tag-history {
    font-size: 12px;
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;
    margin-bottom: -8px;
    max-height: 60px;
    overflow-y: auto;

    > b:first-of-type {
      font-size: 13px;
    }
    &-item {
      margin-right: 5px;
      margin-bottom: 5px;
      background-color: var(--primary-color);
      border-radius: 3px;
      padding: 0px 3px 1px;
      color: #fff;
      cursor: pointer;
    }
  }

  &:hover {
    .anticon-search {
      opacity: 1;
    }
  }
}

// 搜索弹窗
.project-list-search-overlay {
  background-color: rgba(34, 34, 34, 0.88);
  border-radius: 3px;
  box-shadow: 1px 2px 12px rgba(#111, 0.2);
  width: 100%;
  max-width: 300px;
  padding: 8px 10px;

  .title {
    color: #fafafc;
    margin-bottom: 8px;
    button {
      margin-left: 12px;
    }
  }

  .tag-box {
    border-radius: 3px;
    padding: 0px 3px;
    font-size: 13px;
    margin-right: 5px;
    margin-bottom: 5px;
    border: 1px solid rgba(#fff, 0.12);
    background-color: rgba(#fff, 0.125);
    color: #f1f1f1;
    display: inline-block;
    cursor: pointer;
    user-select: none;
    &:hover {
      color: var(--primary-color);
      border-style: dashed;
      border-color: var(--tint-color-40);
      background-color: rgba(#111, 0.24);
    }
  }
  .tag-active {
    color: #fff;
    background-color: var(--primary-color);
    &:hover {
      color: #fff;
      background-color: var(--tint-color-20);
    }
  }
}
