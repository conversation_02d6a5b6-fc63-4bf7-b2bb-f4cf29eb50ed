import './index.less'

import { SearchOutlined } from '@ant-design/icons'
import { Input } from '@sugo/design'
import { useDeepCompareEffect, useEventListener, useReactive } from 'ahooks'
import { Button, Dropdown } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React, { forwardRef, useImperativeHandle, useMemo, useRef } from 'react'

import TagManageModal from '@/pages/project/containers/tag-manage-modal'

export interface SearchToolboxProps {
  tags: string[]
  onSearch: (data: { title: string; tags: string[] }) => any
  tagMap: Record<string, any>
}

/**
 * 项目的搜索面板
 *
 * 结合标签使用
 */
const TabSearch = forwardRef((props: SearchToolboxProps, ref) => {
  const { onSearch, tagMap, tags } = props

  const inputRef = useRef<any>()
  const manageRef = useRef<ModalDefaultRef>()

  const tagList = useMemo(() => _.values(tagMap), [tagMap])
  const state = useReactive({
    tagDict: {} as Record<string, string>,
    visible: false,
    keyword: ''
  })

  const tagsList = _.keys(state.tagDict)

  const _onSearch = (title: string) => {
    onSearch({ title, tags: _.values(state.tagDict).filter(id => id) })
    state.keyword = title
    state.visible = false
  }

  const renderOverlay = () => (
    <div className='project-list-search-overlay' onClick={e => e.stopPropagation()}>
      <div className='title'>
        标签筛选
        <Button
          type='primary'
          size='small'
          onClick={() => {
            state.visible = false
            manageRef.current?.show()
          }}
        >
          管理标签
        </Button>
      </div>
      <div>
        {tagList.map(tag => (
          <div
            key={tag.id}
            className={cn({
              'tag-box': true,
              'tag-active': state.tagDict[tag.id]
            })}
            onClick={e => {
              e.stopPropagation()
              state.tagDict[tag.id] = state.tagDict[tag.id] ? undefined : tag.id
            }}
          >
            {tag?.title || '未知标签'}
          </div>
        ))}
      </div>
    </div>
  )

  useImperativeHandle(ref, () => ({
    cleanInput() {
      state.keyword = ''
    }
  }))

  useDeepCompareEffect(() => {
    state.tagDict = tags?.reduce((o, v) => ({ ...o, [v]: v }), {})
  }, [tags])

  useEventListener('click', () => {
    if (state.visible) {
      state.visible = false
      requestAnimationFrame(() => _onSearch(state.keyword))
    }
  })

  return (
    <>
      <div className='project-list-search-toolbox' onClick={e => e.stopPropagation()}>
        <Dropdown placement='bottomLeft' overlay={renderOverlay()} open={state.visible}>
          <Input
            placeholder='输入名称搜索'
            value={state.keyword}
            onChange={e => (state.keyword = e)}
            wait={500}
            allowClear
            ref={inputRef}
            suffix={<SearchOutlined />}
            onFocus={() => (state.visible = true)}
            onKeyDown={e => {
              if (e.code === 'Enter') {
                const title = (e.target as HTMLInputElement).defaultValue
                onSearch({ title, tags: _.values(state.tagDict).filter(i => i) })
              }
            }}
            onClick={() => {
              if (!state.visible) state.visible = true
            }}
          />
        </Dropdown>
        <div className='tag-history'>
          {tagsList.length > 0 && <b>标签筛选：</b>}
          {tagsList.map(tagId => (
            <span
              className='tag-history-item'
              key={tagId}
              title='删除标签过滤'
              onClick={() => {
                const title = state.keyword
                onSearch({ title, tags: _.values(state.tagDict).filter(id => id !== tagId) })
              }}
            >
              {tagMap[tagId]?.title}
            </span>
          ))}
        </div>
      </div>
      <TagManageModal ref={manageRef} />
    </>
  )
})

export default TabSearch
