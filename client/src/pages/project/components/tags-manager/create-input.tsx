import { Button, Input } from 'antd'
import React, { useState } from 'react'

export default function CreateInput({ onSubmit, loading }) {
  const [text, setText] = useState('')

  return (
    <div className='tags-manager-panel-footer'>
      <Input
        size='small'
        placeholder='输入标签名称'
        maxLength={8}
        value={text}
        onChange={e => setText(e.target.value)}
      />
      <Button
        disabled={text?.length <= 0}
        size='small'
        type='primary'
        onClick={() => {
          onSubmit(text)
          setText('')
        }}
        loading={loading}
      >
        新建
      </Button>
    </div>
  )
}
