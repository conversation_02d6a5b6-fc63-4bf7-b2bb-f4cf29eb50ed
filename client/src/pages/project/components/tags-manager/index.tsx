import './index.less'

import { useReactive } from 'ahooks'
import { message } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React, { useMemo } from 'react'

import type { Tag } from '@/services/type'

import CreateInput from './create-input'

export interface TagManagerProps {
  value?: string[]
  tagMap: Record<string, Tag>
  onSelect?: (tags: string[]) => any
  onCreate?: (tag: { title: string }) => any
}

// 标签管理器，包含了标签的 curd
export default function TagManager(props: TagManagerProps) {
  const { value = [], tagMap, onSelect, onCreate } = props

  const state = useReactive({ submitLoading: false })
  const selectSet = useMemo(() => new Set(value), [value])
  const tags = useMemo(() => _.values(tagMap), [tagMap])

  const _onCreate = async (title: string) => {
    if (!title) return
    const has = tags.find(i => i.title === title)
    if (has) return message.error('标签名称重复')
    try {
      state.submitLoading = true
      await onCreate?.({ title })
    } finally {
      state.submitLoading = false
    }
  }

  return (
    <div className='tags-manager-panel'>
      <div className='tags-manager-panel-content'>
        {tags.map(tag => (
          <div
            key={tag.id}
            className={cn({
              'tag-item': true,
              'tag-active': selectSet.has(tag.id)
            })}
            onClick={() => {
              const _value = value.filter(id => !!tagMap[id])
              if (selectSet.has(tag.id)) {
                onSelect?.(_value.filter(id => id !== tag.id))
              } else {
                onSelect?.([..._value, tag.id])
              }
            }}
          >
            {tag.title}
          </div>
        ))}
      </div>

      <CreateInput loading={state.submitLoading} onSubmit={_onCreate} />
    </div>
  )
}
