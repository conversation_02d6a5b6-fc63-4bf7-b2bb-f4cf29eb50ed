

import './index.less'

import { useMemoizedFn } from 'ahooks'
import { Checkbox } from 'antd'
import _ from 'lodash'
import React from 'react'

import type { CheckboxListProps, CheckboxListSelectedKey } from '../type'

/**
 * 授权左侧用户checkbox选择列表
 * @returns
 */
function CheckboxList(props: CheckboxListProps) {
  const { options = [], hideSelectAll = false, selectedKeys = [], onChange, onClear } = props

  const handleChange =  useMemoizedFn(e => {
    if (e.target.checked) {
      onChange?.(options.map(v => (v.value as string)))
    } else {
      onClear?.()
    }
  })

  const onSelect = useMemoizedFn((data: CheckboxListSelectedKey[]) => {
    if (!hideSelectAll) return onChange?.(data)
    /** 选中值在options */
    const cacheInOptions: any[] = _.filter(selectedKeys, k => _.some(options, item => item.value === k))
    const adds = _.difference(data, cacheInOptions)
    const dels = _.difference(cacheInOptions, data)
    const res = _.filter(selectedKeys, v => !dels.includes(v)).concat(adds)
    onChange?.(res)
  })

  const indeterminate = selectedKeys.length > 0 &&  selectedKeys.length !== options.length

  const checkAll = selectedKeys.length > 0 &&  selectedKeys.length === options.length

  return (
    <div className='auth-checkbox-list'>
      {
        hideSelectAll || _.isEmpty(options) ? null : (
          <Checkbox
            className='checkbox-all'
            indeterminate={indeterminate}
            checked={checkAll}
            onChange={handleChange}
          >全选</Checkbox>
        )
      }
      <div className='checkbox-container'>
        <Checkbox.Group options={options} value={selectedKeys} onChange={onSelect} />
      </div>
    </div>
  )
}

export default CheckboxList
