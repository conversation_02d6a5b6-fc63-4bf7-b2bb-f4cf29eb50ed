import './index.less'

import { SearchOutlined } from '@ant-design/icons'
import { useMemoizedFn } from 'ahooks'
import { Button, CheckboxOptionType, Input,Modal, Popconfirm } from 'antd'
import classNames from 'classnames'
import _ from 'lodash'
import React, { CSSProperties, useMemo, useState } from 'react'

import CheckboxList from './checkbox-list'
import SelectedUsers from './selected-users'
import type { CheckboxListProps,  CheckboxListSelectedKey} from './type'

export interface UserSelectProps {
  /** 类 */
  className?: string
  /** 清空提示语句 */
  clearTips?: string
  /** 选择项 */
  options: CheckboxListProps['options']
  /** 已选中值 */
  value: Array<CheckboxListSelectedKey>
  /** 行内样式 */
  style?: CSSProperties
  /* 值变更事件 */
  onChange?: (data: CheckboxListSelectedKey[]) => any
}

/**
 * 用户授权内容组件
 * @returns
 */
export function UserSelect(props: UserSelectProps) {
  const { className, clearTips = '是否清空所有授权？', options = [], value = [], style, onChange } = props
  const [searchKey, setSearchKey] = useState('')

  const handleSearch = useMemoizedFn(e => {
    const val = e.target.value
    setSearchKey(val)
  })

  const handleClear = useMemoizedFn(() => {
    onChange?.([])
  })

  const onDelete = useMemoizedFn((data: CheckboxOptionType) => {
    onChange?.(value?.filter(v => v !== data?.value))
  })

  const onClear = useMemoizedFn(() => {
    Modal.confirm({
      title: '提示',
      content: clearTips,
      centered: true,
      onOk(){
        onChange?.([])
      }
    })
  })

  const filterOptions = useMemo(
    () => {
      if (!searchKey) return options
      const rgx = new RegExp(searchKey, 'ig')
      return options.filter(v => rgx.test(v?.label as string))
    },
    [searchKey, options]
  )

  const optionsDict = useMemo(() => _.keyBy(options, 'value'), [options])

  // 未匹配上的，过滤掉，有可能已经在用户或角色中删除
  const authUsersOptions = useMemo(() => _.map(value, v => _.get(optionsDict, [v as string])).filter(Boolean)
  , [value, optionsDict])

  return (
    <div className={classNames('project-auth-content', className)} style={style}>
      <div className='project-auth-left'>
        <Input
          placeholder='搜索'
          suffix={<SearchOutlined />}
          allowClear
          value={searchKey}
          onChange={handleSearch}
          className='project-auth-search'
        />
        <div className='checkbox-list-wrap'>
          <CheckboxList
            hideSelectAll={!!searchKey}
            selectedKeys={value}
            options={filterOptions}
            onChange={onChange}
            onClear={onClear}
          />
        </div>
      </div>

      <div className='project-auth-right'>
        <div className='project-auth-right-header'>
          <span>已选{authUsersOptions?.length || 0}个</span>

          <Popconfirm title={clearTips} onConfirm={handleClear}>
            <Button type='text'>清空</Button>
          </Popconfirm>
        </div>
        <div className='checkbox-list-wrap'>
          <SelectedUsers dataSource={authUsersOptions} onDelete={onDelete} />
        </div>
      </div>
    </div>
  )
}
