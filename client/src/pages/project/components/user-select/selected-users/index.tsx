import './index.less'

import { CloseOutlined } from '@ant-design/icons'
import type { CheckboxOptionType } from 'antd'
import classNames from 'classnames'
import React from 'react'

export interface SelectedUserItemProps {
  data: CheckboxOptionType
  onClick?: (data: CheckboxOptionType) => any
}

/**
 * 授权用户列表单项组件
 * @param props
 * @returns
 */
function SelectedUserItem(props: SelectedUserItemProps) {
  const { data, onClick } = props
  return (
    <li className={classNames('auth-users-item')}>
      <span className='auth-users-lable'>{data.label}</span>
      <CloseOutlined className='auth-users-close' onClick={() => onClick?.(data)} />
    </li>
  )
}

/**
 * 授权用户列表组件属性接口
 */
export interface SelectedUsersProps {
  /** 数据源 */
  dataSource: CheckboxOptionType[]
  /** 删除事件 */
  onDelete?: (data: CheckboxOptionType) => any
}

/**
 * 授权用户列表组件
 * @returns
 */
function SelectedUsers(props: SelectedUsersProps) {
  const { dataSource = [], onDelete } = props
  return (
    <ul className='auth-users-box'>
      {dataSource.map(v => (
        <SelectedUserItem
          key={v.value as string}
          data={v}
          onClick={onDelete}
        />
      ))}
    </ul>
  )
}

export default SelectedUsers
