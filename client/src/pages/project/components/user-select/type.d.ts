import type { CheckboxOptionType } from 'antd'


/** 选择列表选中类型 */
export type CheckboxListSelectedKey = CheckboxOptionType['value']

/** 左侧可选用户列表类型 */
export interface CheckboxListProps {
  /** 选择列表选项内容 */
  options: CheckboxOptionType[]
  /** 是否隐藏全选 */
  hideSelectAll: boolean
  /** 选中值 */
  selectedKeys: Array<CheckboxListSelectedKey>
  /** 选中事件 */
  onChange?: (data: Array<CheckboxListSelectedKey>) => any
  /** 清空事件 */
  onClear?: () => any
}
