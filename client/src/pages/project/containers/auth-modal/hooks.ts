import { useMemoizedFn, useReactive, useSafeState } from 'ahooks'
import { message, Modal } from 'antd'
import isEqual from 'fast-deep-equal'
import _ from 'lodash'
import { useEffect, useMemo, useRef } from 'react'

import { Service } from '@/services'
import { useCommit, useModelState } from '@/stores/models/project-auth'
import { getRoles, getUserInfoById } from '@/utils/query'

import { ROLE_TAB_CODE, USER_TAB_CODE } from './consts'

export interface Option {
  value: string | number
  label: string
  [key: string]: any
}

/** 状态属性接口 */
interface StateProps {
  loading: boolean
  confirmLoading: boolean
  /** 创建者名称 */
  creatorName: string
  activeKey: string
  roleOptions: Option[]
  roleValue: string[]
  roleLoading: boolean
}

/** 业务逻辑入参接口 */
export interface UseAuthModalProps {
  /** 项目id */
  projectId?: string
  /** 创建人id */
  createdBy?: string
  /** 显示隐藏 */
  visible?: boolean
  /** modal实例 */
  modal?: any
}

/**
 * 授权用户管理业务逻辑hook
 * @param props
 * @returns
 */
export function useAuthModal(props: UseAuthModalProps) {

  const { projectId, createdBy, visible, modal } = props

  const commit = useCommit()
  const authState = useModelState(s => s, isEqual)

  const state = useReactive<StateProps>({
    loading: false,
    creatorName: '',
    confirmLoading: false,
    roleOptions: [],
    activeKey: USER_TAB_CODE,
    roleValue: [],
    roleLoading: false
  })

  const [selectedUserId, setSelectedUserId] = useSafeState<string[]>([])

  const getRoleOptions = useMemoizedFn(async () => {
    if (!_.isEmpty(state.roleOptions)) return
    try {
      state.roleLoading = true
      const res = await getRoles()
      const list = _.get(res, 'result', []).map(v => ({
        ...v,
        label: v?.name,
        value: v.id
      }))
      state.roleOptions = list
    } catch (error) {
      console.log(error)
    } finally {
      state.roleLoading = false
    }
  })


  const allUsersOptions = useMemo(() => {
    const list =  authState.allUsers.map(v => ({
      label: v.first_name,
      value: v.id
    })).filter(v => createdBy !== v.value)
    return list
  }, [authState.allUsers, createdBy])


  /**
   * 初始化数据
   * 1. 根据projectId获取授权用户列表
   * 2. 获取创建者名称
   */
  const initAuthUsersAndCreatorName = async () => {
    state.loading = true
    try {
      const [userRes, authRes] = await Promise.all([
        // 获取创建者名称
        createdBy ? getUserInfoById({ id: createdBy as string }) : Promise.resolve({}),
        // 根据项目id获取授权用户
        Service.ProjectAuth.findAll({ where: { projectId } })
      ])
      state.creatorName = userRes?.result?.first_name || '--'
      const users: string[] = []
      const roles: string[] = []
      _.forEach(authRes, item => {
        if (item.userId) users.push(item.userId)
        if (item.roleId) roles.push(item.roleId)
      })
      setSelectedUserId(users)
      state.roleValue = roles
    } catch (error: any) {
      const msg = `获取创建人信息失败， ${error?.data?.error || error?.message}`
      message.error(msg)
    } finally {
      state.loading = false
    }

  }

  const reset = () => {
    state.loading = false
    state.confirmLoading = false
    setSelectedUserId([])
    state.creatorName = ''
    state.activeKey = USER_TAB_CODE
    state.roleValue = []
  }

  useEffect(() => {
    if (visible) { // 打开 modal 才请求
      commit('asyncGetAllUsersByRouteIds')
      initAuthUsersAndCreatorName()
    } else {
      reset()
    }
  }, [visible])

  const onDelete = useMemoizedFn(data => {
    setSelectedUserId(list => list.filter(v => v !== data.value))
  })

  const onChangeUser = setSelectedUserId

  const onChangeRole = useMemoizedFn((data: any[]) => {
    state.roleValue = data
  })

  const confirmSave = useMemoizedFn(() => new Promise(res => {
    if (_.isEmpty(selectedUserId) && _.isEmpty(state.roleValue)) {
      Modal.confirm({
        content: '是否清空授权？清空后将只有创建者可编辑',
        centered: true,
        onOk: () => {
          res(true)
        },
        onCancel() {
          res(false)
        }
      })
    } else {
      res(true)
    }
  }))

  const onSave = useMemoizedFn(async () => {
    const isRunSave = await confirmSave()
    if (!isRunSave) return
    await commit('asyncSaveModifyAuth', {
      projectId,
      userIds: selectedUserId,
      roleIds: state.roleValue
    })
    modal?.hide?.()
  })

  const onChangeTab = useMemoizedFn(tab => {
    if (tab === ROLE_TAB_CODE) {
      getRoleOptions()
    }
    state.activeKey = tab
  })

  return {
    state,
    selectedUserId,
    allUsersOptions,
    onDelete,
    onChangeUser,
    onChangeRole,
    onSave,
    onChangeTab
  }
}
