import {  Modal, Space, Spin, Tabs } from 'antd'
import React from 'react'

import withRefModal from '@/components/with-ref-modal'

import { UserSelect } from '../../components/user-select'
import { ROLE_TAB_CODE,USER_TAB_CODE } from './consts'
import type { UseAuthModalProps } from './hooks'
import { useAuthModal } from './hooks'


/**
 * 授权管理弹窗组件
 * @returns
 */
function AuthModal(props: UseAuthModalProps) {
  const { visible = false, modal } = props

  const {
    state,
    allUsersOptions,
    selectedUserId,
    onSave,
    onChangeUser,
    onChangeRole,
    onChangeTab
  } = useAuthModal(props)

  return (
    <Modal
      title='授权管理'
      open={visible}
      centered
      width={620}
      onOk={onSave}
      confirmLoading={state.confirmLoading}
      onCancel={() => modal?.hide?.()}
    >
      <Spin spinning={state.loading}>
        <Space direction='vertical'>
          <span>创建者： {state.creatorName|| '--'}</span>
          <Tabs
            activeKey={state.activeKey}
            onChange={onChangeTab}
            items={[
              {
                label: '用户',
                key: USER_TAB_CODE,
                children: (
                  <UserSelect
                    value={selectedUserId}
                    options={allUsersOptions}
                    onChange={onChangeUser}
                  />
                )
              },
              {
                label: '角色',
                key: ROLE_TAB_CODE,
                children: (
                  <Spin spinning={state.roleLoading}>
                    <UserSelect
                      value={state.roleValue}
                      options={state.roleOptions}
                      onChange={onChangeRole}
                    />
                  </Spin>
                )
              }
            ]}
          />
        </Space>
      </Spin>
    </Modal>
  )
}

export default withRefModal(AuthModal)
