
.tag-manage-modal {

  .ant-modal-body {
    max-height: 610px;
    overflow-y: auto;
    padding: 10px 16px;
  }

  .tag-item {
    display: flex;
    align-items: center;
    padding: 5px 0;
    min-height: 35px;
    border-bottom: 1px solid #f1f1f1;

    .ant-tag {
      margin: 0 6px;
      margin-right: 0;
    }

    .tag-ref {
      font-size: 13px;
      color: #999;
      justify-content: space-around;
      margin-left: 8px;
    }

    &:last-of-type {
      border-bottom: none;
    }
    > span:first-of-type {
      flex: 1;
    }

    > button {
      display: none;
    }

    > .tag-del {
      display: none !important;
      margin: 0 8px;
    }

    &:hover {
      .tag-del, button {
        display: block;
      }
    }
  }

  .edit-panel {
    display: block;
    width: 100%;
    background-color: var(--tint-color-98);
    border: 2px dashed #f1f1f1;
    padding: 6px;
    border-radius: 3px;
    .ant-row {
      margin-top: 3px;
    }

    > footer {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 5px 0 3px;
    }
  }
}

