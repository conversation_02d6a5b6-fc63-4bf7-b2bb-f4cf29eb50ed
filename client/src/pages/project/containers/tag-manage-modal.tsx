/* eslint-disable no-nested-ternary */
import './tag-manage-modal.less'

import { PlusOutlined } from '@ant-design/icons'
import { useAsyncEffect, useDeepCompareEffect } from 'ahooks'
import { Button, Input, message, Modal, Table } from 'antd'
import Tooltip from 'antd/es/tooltip'
import _ from 'lodash'
import React, { useEffect, useState } from 'react'

import ColorSelect from '@/components/color-picker/color-select'
import withRefModal from '@/components/with-ref-modal'
import { Service } from '@/services'
import { useCommit, useModelState } from '@/stores/models/project-manager'

const useColums = ({ editId, refData, onEditIdChange, visible, onCancel }) => {
  const [data, setData] = useState<{ title?: string, color?: string, namespace?: string, id?: string }>({})
  const updateData = (newData: Partial<typeof data>) => setData({ ...data, ...newData })

  const commit = useCommit()

  const onSubmit = async () => {
    if (!data.title?.trim()) {
      message.warn('标签名称不能为空')
      return
    }
    if (data.id && data.id !== '$') {
      await commit('editTag', data as any)
    } else {
      await commit('createTag', data as any)
    }
    setData({})
    onEditIdChange('')
  }

  const onCancelClick = () => {
    onEditIdChange('')
    setData({})
    onCancel()
  }

  const onOpenEdit = item => {
    onEditIdChange(item.id)
    setData(item)
  }

  const onDel = item => {
    commit('delTag', item)
  }

  useEffect(() => {
    if (visible) setData({})
  }, [visible])

  const columns = [
    {
      title: <span><span style={{ color: 'red' }}>*</span> 标签名称</span>,
      dataIndex: 'title', render: (title, item) => (
        editId !== item.id ? title :
          <Input
            prefix={<span style={{ color: 'red' }}>*</span>}
            defaultValue={title}
            value={data.title}
            onChange={e => updateData({ title: e.target.value })}
            placeholder='请输入'
            maxLength={30}
            className='w-[200px]'
            showCount
            size='small'
          />
      )
    },
    {
      title: '命名空间', dataIndex: 'namespace', render: (namespace, item) => (
        editId !== item.id ? (namespace || '-') :
          <Input
            defaultValue={namespace}
            value={data.namespace}
            onChange={e => updateData({ namespace: e.target.value })}
            placeholder='请输入'
            maxLength={30}
            className='w-[200px]'
            showCount
            size='small'
          />
      )
    },
    {
      title: '标签颜色', dataIndex: 'color', render: (color, item) => (
        editId !== item.id ? (
          color ?
            <span style={{ color }}>{color}</span> : '-'
        ) :
          <ColorSelect value={data.color} onChange={c => updateData({ color: c })} />
      )
    },
    { title: '引用次数', dataIndex: 'id', render: id => `${refData[id]?.length || 0} 次` },
    {
      title: '操作', dataIndex: '$action',
      width: 120,
      render: (_t, r) => {
        if (editId === r.id) return (
          <div>
            <Button size='small' type='link' onClick={onCancelClick}>取消</Button>
            <Button size='small' type='link' onClick={onSubmit}>确定</Button>
          </div>
        )
        if (editId) return null
        return (
          <div>
            <Button size='small' type='link' onClick={() => onOpenEdit(r)}>编辑</Button>

            {refData[r.id]?.length > 0 ?
              <Tooltip title='标签有被引用，无法删除'>
                <Button size='small' type='link' disabled={refData[r.id]?.length > 0}>删除</Button>
              </Tooltip> :
              <Button size='small' type='link' onClick={() => onDel(r)} disabled={refData[r.id]?.length > 0}>删除</Button>
            }
          </div>
        )
      }
    }
  ]

  return columns
}

/**
 * 标签管理，可编辑名称，颜色
 */
const TagManageModal = withRefModal<any>(props => {
  const { modal, visible } = props

  const tagMap = useModelState(s => s.tagMap)
  const [refData, setRefData] = useState({})
  const [editId, setEditId] = useState('')
  const [list, setList] = useState<any[]>([])
  const [page, setPage] = useState(1)

  const columns = useColums({
    editId,
    refData,
    visible,
    onCancel: () => setList(list.filter(i => i.id !== '$')),
    onEditIdChange: setEditId
  })

  const onAddClick = () => {
    setList([{ id: '$' }, ...list])
    setEditId('$')
    setPage(1)
  }

  useAsyncEffect(async () => {
    if (!visible) return
    if (list[0]?.id === '$') return
    // 查出标签的分布
    const res = await Service.Project.findAll({
      where: {
        tags: { $regexp: list.map(i => i.id).join('|') as any }
      },
      attributes: ['id', 'tags']
    })
    const obj = {}
    _.forEach(res, i => {
      _.forEach(i.tags, f => {
        if (!_.isArray(obj[f])) obj[f] = []
        obj[f].push(i.id)
      })
    })
    setRefData(obj)
  }, [visible, list.length])

  useDeepCompareEffect(() => {
    if (!visible) {
      setEditId('')
      setList([])
      return
    }
    setList(_.values(tagMap).sort((a: any, b: any) => b.updatedAt.localeCompare(a.updatedAt)))
  }, [tagMap, visible])

  return (
    <Modal
      width={840}
      bodyStyle={{
        height: 525,
        overflowY: 'auto'
      }}
      title={(
        <div className='flex items-center'>
          <span className='flex-1' style={{ marginRight: 12 }}>标签管理</span>
          {!editId &&
            <Button size='small' type='primary' icon={<PlusOutlined />} onClick={onAddClick} className='mr-8'>
              新建
            </Button>
          }
        </div>
      )}
      open={visible}
      onCancel={() => modal.hide()}
      onOk={() => modal.hide()}
      className='tag-manage-modal'
    >
      <Table
        rowKey='id'
        size='small'
        columns={columns}
        dataSource={list}
        pagination={{
          current: page,
          onChange: setPage
        }}
      />
    </Modal>
  )
})

export default TagManageModal
