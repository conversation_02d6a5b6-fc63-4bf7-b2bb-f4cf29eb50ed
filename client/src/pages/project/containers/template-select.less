.project-template-select {
  width: 100%;
  height: 100%;
  padding: 5px 0;

  .ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .template-selected {
    display: flex;
    align-items: center;
    padding: 0 12px;
    justify-content: space-between;
    border-radius: 3px;
    border-bottom: 1px solid var(--tint-color-95);
    margin-top: 28px;
    padding-bottom: 12px;
    margin-bottom: 12px;

    .search-input {
      width: 160px;
      margin-right: 12px;
    }
    .ellipsis {
      flex: 1;
      color: #777;
    }
  }

  .template-list {
    &-row {
      display: flex;
      align-items: center;
      padding: 0 5px;
      // border-bottom: 1px solid var(--border-color-base);
    }

    &-item {
      flex: 1;
      display: flex;
      align-items: center;
      border-radius: 4px;
      padding: 4px 5px;
      padding-right: 8px;
      max-width: 50%;
      margin: 2px 0;
      border-left: 3px solid transparent;
      border-right: 3px solid transparent;

      .tit {
        flex: 1;
      }

      .anticon {
        color: #888;
        margin-left: 4px;
        &:hover {
          color: var(--primary-color);
        }
      }

      &.active,
      &:hover {
        cursor: pointer;
        background-color: var(--tint-color-95);
      }
    }
  }
}
