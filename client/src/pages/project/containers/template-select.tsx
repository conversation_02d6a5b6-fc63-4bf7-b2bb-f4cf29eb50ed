import './template-select.less'

import { CheckOutlined } from '@ant-design/icons'
import { useAsyncEffect, useReactive } from 'ahooks'
import { Empty, Input } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React from 'react'

import { Service } from '@/services'
import type { Project } from '@/services/type'

export interface TemplateSelectProps {
  mode: 'project' | 'report'
  templateId: string
  onSelect: (templateId: string, data: any) => any
}

type State = {
  list: Project[]
  selected?: Project // 选中的项目
  filter: {
    title?: string // 关键字搜索
    page: number
  }
}

/**
 * 选择基于模板创建项目
 * @param props
 */
export default function TemplateSelect(props: TemplateSelectProps) {
  const { templateId, mode } = props
  const pageSize = 20

  const state = useReactive<State>({
    list: [],
    selected: undefined,
    filter: {
      title: undefined,
      page: 1
    }
  })

  const onSelect = item => props.onSelect(item.id, item)

  // 加载更多
  const search = async title => {
    state.filter.title = title
    state.list = await Service.Project.findAll({
      where: { mode, isTemplate: true, title: { $like: `%${title}%` } },
      limit: pageSize,
      attributes: ['id', 'title', 'tags']
    })
  }

  useAsyncEffect(async () => {
    state.list = await Service.Project.findAll({
      where: { isTemplate: true, mode },
      limit: pageSize,
      attributes: ['id', 'title', 'tags', 'type']
      // select: { id: true, title: true, tags: true, type: true }
    })
  }, [mode])

  useAsyncEffect(async () => {
    if (templateId) {
      const res = await Service.Project.findByPk(templateId, {
        // where: { id: templateId },
        attributes: ['id', 'title']
      })
      if (res) state.selected = res
    } else {
      state.selected = undefined
    }
  }, [templateId])

  return (
    <div className='project-template-select'>
      <div className='template-selected'>
        <Input.Search placeholder='搜索模板' className='search-input' onSearch={search} allowClear />
        <div className='ellipsis'>已选：{state.selected?.title || '无'}</div>
      </div>

      <div className='template-list'>
        {_.chunk(state.list, 2).map((list, index) => (
          <div key={index} className='template-list-row'>
            {list.map(item => (
              <div
                key={item.id}
                className={cn({
                  'template-list-item': true,
                  'active': item.id === templateId
                })}
                onClick={() => onSelect(item)}
              >
                <span className='tit ellipsis'>{item.title || '空标题'}</span>
                {item.id === templateId && <CheckOutlined />}
              </div>
            ))}
          </div>
        ))}

        {state.list.length === 0 && <Empty />}
      </div>
    </div>
  )
}
