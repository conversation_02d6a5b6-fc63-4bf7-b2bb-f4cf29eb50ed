/* eslint-disable arrow-body-style */
import { fastMemo } from '@sugo/design/functions'
import { usePermissions } from '@sugo/design/hooks'
import { history } from '@umijs/max'
import { useAsyncEffect } from 'ahooks'
import { message } from 'antd'
import _ from 'lodash'
import React, { useRef } from 'react'

import { useNewBi } from '@/hooks/use-new-bi'
import { useQueryParams } from '@/hooks/use-query-params'
import { useTitleFromRouter } from '@/hooks/use-title'
import PageLayout from '@/pages/project/components/layout/page-layout'
import ProjectItem from '@/pages/project/components/listitem/project-item'
import ProjectLookItem from '@/pages/project/components/listitem/project-look-item'
import CreateProjectModal from '@/pages/project/components/modal/create-project'
import SearchToolbox from '@/pages/project/components/search-toolbox'
import { useCommit as useCommitAuth } from '@/stores/models/project-auth'
import { useCommit, useModelState } from '@/stores/models/project-manager'
import { getPreviewUrl } from '@/utils/preview'

// import { getProjectPermissionStatus } from '@/utils/project-permission-status'
import AuthModal from './containers/auth-modal'


// const isDev = process.env.NODE_ENV !== 'production'

/**
 * 项目列表页
 */
export const ProjectPage = fastMemo(props => {
  useTitleFromRouter()

  const query = props.query || useQueryParams()
  const commit = useCommit()
  const createProjectModalRef = useRef<{ show: Function }>(null)
  const searchToolboxRef = useRef<{ cleanInput: Function }>(null)
  const projectAuthModalRef = useRef<{ show: Function }>(null)
  const { isNewBi } = useNewBi()
  const commitAuth = useCommitAuth()

  const { list = [], total, recentlyViewed = [], filter, loading, tagMap, dataPermission } = useModelState()
  const prefixText = filter.mode === 'project' ? '项目' : '数据报告'

  const memus = {
    project: [
      { label: '项目空间', value: 'project', icon: '控件_1' },
      { label: '模板项目', value: 'project-template', icon: '收藏' }
    ],
    report: [
      { label: '数据报告', value: 'report', icon: '控件_1' },
      { label: '报告模板', value: 'report-template', icon: '收藏' }
    ]
  }[filter.mode]

  const [canEdit, canAuthorize, canShare, canPreview, canDelete] = usePermissions([
    '/app/abi/project/edit',
    '/app/abi/project/authorize',
    '/app/abi/project/share',
    '/app/abi/project/preview',
    '/app/abi/project/delete'
  ])
  // 定义操作到权限的映射
  const actionToPermissionMap = {
    'edit': canEdit,
    'auth': canAuthorize,
    'share': canShare,
    'preview': canPreview,
    'del': canDelete
  }

  const actionsMap = {
    default: [
      { label: '编辑', value: 'edit', icon: '编辑', to: ({ id }) => `/project/${id}` },
      { label: '授权', value: 'auth', icon: '权限' },
      { label: '分享', value: 'share', icon: '分享' },
      { label: '预览', value: 'preview', icon: '预览', to: ({ releaseSign }) => getPreviewUrl(releaseSign) },
      { label: '删除', value: 'del', icon: '删除', isConfirm: true, confirmText: '是否确定删除' }
    ],
    template: [
      { label: '编辑', value: 'edit', icon: '编辑', to: ({ id }) => `/project/${id}` },
      { label: '授权', value: 'auth', icon: '权限' },
      { label: '复制', value: 'copy', icon: '复制', isConfirm: true, confirmText: '是否确定复制' },
      { label: '删除', value: 'del', icon: '删除', isConfirm: true, confirmText: '是否确定删除' }
    ]
  }

  // 这里处理权限
  // 对每个操作列表进行处理
  _.forEach(_.entries(actionsMap), ([key, actions]) => {
    actionsMap[key] = _.filter(actions, action => {
      if (!(action.value in actionToPermissionMap)) return true
      return actionToPermissionMap[action.value]
    })
  })

  const init = async (isTemplate?: boolean) => {
    try {
      await commit('initDataPermission') // 先加载权限
      await commitAuth('asyncInitAuth')
      await commit('initProjectList', {
        isTemplate: isTemplate !== undefined ? isTemplate : !!query.isTemplate,
        mode:
          // eslint-disable-next-line no-nested-ternary
          query?.mode ? query.mode :
            _.includes(window.location.pathname, 'report') ? 'report' : 'project'
      })
    } catch (err: any) {
      message.error(`请求错误：${err.message}`)
    }
  }

  // 是否有管理权限
  const allowManage = (id: string, createdBy?: string) => {
    if (dataPermission.isAdmin) return true
    if (createdBy === _.get(window, 'sugo.user.id')) return true
    if (dataPermission.configMap[id]?.auth === 'manage') return true
    return false
  }

  // 单个卡片渲染
  const renderItem = (data: typeof list[number]) => (
    <ProjectItem
      key={data.id}
      data={data}
      tags={_.map(data.tags, t => tagMap[t]).filter(i => i)}
      logo={data.isTemplate ? '模板' : '项目'}
      actions={data.isTemplate ? actionsMap.template : actionsMap.default}
      isManageAuth={() => allowManage(data.id, data.createdBy)}
      disabledFn={({ value }) => {
        const set = new Set(['share', 'preview'])

        // if (value === 'copy') return true
        // 发布状态下，分享及预览可操作，否则不可操作
        if (set.has(value)) return data.releaseStatus !== 'released'

        return false
        // const hasPermission = getProjectPermissionStatus({
        //   user: userState,
        //   permissionProjectIds: authIds,
        //   currentProject: data
        // })

        // return !hasPermission
      }}
      onActionClick={async ({ value }) => {
        const set = new Set(['del', 'copy'])
        if (value === 'auth') {
          projectAuthModalRef.current?.show({ projectId: data.id, createdBy: data.createdBy })
          return
        }
        if (set.has(value)) {
          try {
            if (value === 'del') await commit('removeProject', data)
            if (value === 'copy') await commit('copyProject', data)
            message.success('操作成功')
          } catch (err: any) {
            message.error(`失败：${err?.message || '0001'}`)
          }
        }
      }}
      onEditTitle={title => {
        if (title === data.title) return
        commit('renameProject', { id: data.id, title })
      }}
    />
  )

  const renderLookItem = data => <ProjectLookItem data={data} key={data.id} />

  const onCreateClick = () => {
    createProjectModalRef.current?.show({
      isTemplate: filter.isTemplate
    })
  }

  const onMenuClick = ({ value }) => {
    searchToolboxRef.current?.cleanInput()
    const isTemplate = value.indexOf('template') > -1 ? 1 : ''
    const mode = value.replace(/(\w+)-?.*/, '$1') || 'project'

    if (isNewBi) {
      history.replace(`/framework/workspace?active=abi-${mode}&isTemplate=${isTemplate}`)
    } else {
      history.replace(`/${mode}?isTemplate=${isTemplate}`)
    }

    init(!!isTemplate)
  }

  // 搜索面板
  const renderSearchPanel = () => (
    <SearchToolbox
      ref={searchToolboxRef}
      tags={filter.tags || []}
      tagMap={tagMap}
      onSearch={val => {
        if (!_.isEqual(val, _.pick(filter, ['title', 'tags']))) {
          commit('queryList', { ...filter, ...val, page: 1, pageSize: undefined })
        }
      }}
    />
  )

  useAsyncEffect(async () => {
    // 延迟 200ms 再初始化
    await new Promise(rs => setTimeout(rs, 200))
    await init()
    await commit('initRecentlyView')
  }, [query?.mode])

  return (
    <>
      <PageLayout
        loading={loading}
        prefixText={prefixText}
        itemCount={list?.length || 0}
        itemTotal={total}
        filter={filter}
        lookItemCount={recentlyViewed?.length || 0}
        renderLookItem={index => renderLookItem(recentlyViewed[index])}
        renderItem={index => renderItem(list[index])}
        onFilterChange={newfilter => commit('queryList', newfilter)}
        menus={memus || []}
        onMenuClick={onMenuClick}
        onCreateClick={onCreateClick}
        renderSearchPanel={renderSearchPanel}
      />

      <AuthModal ref={projectAuthModalRef} />

      <CreateProjectModal
        ref={createProjectModalRef}
        prefixText={prefixText}
        tagMap={tagMap} // ...
        mode={filter.mode}
      />
    </>
  )
})

export default ProjectPage
