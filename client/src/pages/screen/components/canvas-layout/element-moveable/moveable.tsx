import { useDeepCompareEffect } from 'ahooks'
import isEqual from 'fast-deep-equal'
import { diff } from 'just-diff'
import _ from 'lodash'
import React, { createElement, CSSProperties, memo, useEffect, useMemo, useRef, useState } from 'react'
import Moveable, {
  BeforeRenderableEvents,
  MoveableProps,
  OnDrag,
  OnEvent,
  OnResize,
  OnRotate,
  OnRound,
  RectInfo
} from 'react-moveable'
import * as transform from 'transform-parser'

import { memoryStorage } from '@/storages/memory'

import { ChartSwitchViewable, LockViewable, OffsetViewable, SizeViewable, TabsEnterViewable } from './offset-view'
import type { ElementMoveableProps, FrameMap } from './type'

/**
 * 画布的拖拽器
 * @param props
 */
export function ElementMoveable(props: ElementMoveableProps) {
  // targets 就是激活选中的拖拽元素
  const { sizes, activeIds, onConfigModify } = props
  const { moveableRef, elementIds, selectoRef, canvasRef, containerRef } = props

  const [elements, setElements] = useState<any[]>([])
  const [keyId, setKeyId] = useState<string>(() => Math.random().toString(32))
  // 拖拽时单个或多个进行存储的一个临时区
  // TODO: 这里会存在数据不同步问题
  const cacheRef = useRef({ styles: [] as any[] })
  const { current: frameMap } = useRef<FrameMap>(new Map())
  const directions = { top: true, right: true, bottom: true, left: true /** center: true, middle: true */ }

  const elementGuidelines = useMemo(() => elements.slice(0, 2), [elements])
  const targets = useMemo(() => elements.filter(i => activeIds.includes(i.id)), [activeIds, elements])

  // 更新选中
  const initElements = (list = elementIds) => {
    const arr = list.map(id => document.getElementById(id)).filter(i => i) as HTMLElement[]
    arr.forEach((el: any) => {
      const tf = transform.parse(el.style.transform || '')
      frameMap.set(el, {
        id: el.id,
        translate: (tf.translate as number[]) || [0, 0],
        rotate: (tf.rotate as number) || 0
      })
    })
    setElements(arr)
  }

  useDeepCompareEffect(() => {
    // setTimeout(() => {
    initElements(elementIds)
    // }, 300)
  }, [elementIds, keyId])

  useDeepCompareEffect(() => {
    cacheRef.current.styles = []
  }, [activeIds, keyId])

  useEffect(() => {
    // TODO: 选择的组件多时，这个可能会卡，需要优化，按需更新
    function forceUpdate(keys?: string[]) {
      setKeyId(Math.random().toString(32))
      moveableRef.current?.updateTarget()
    }
    if (moveableRef.current) {
      moveableRef.current.forceUpdate = forceUpdate
    }
    memoryStorage.set('moveable', moveableRef.current)
  })

  // 元素样式改变时才触发
  const onElementStyleChange = (styles: { key: string; style: CSSProperties }[]) => {
    const prev = cacheRef.current.styles
    const next = styles

    if (!prev) {
      cacheRef.current.styles = next
      return
    }

    const valDiff = diff(prev, next)
    if (_.isEmpty(valDiff)) return

    props.onElementStyleChange?.(styles)
    // cacheRef.current.styles = next
  }

  // ---------------------------------------------------------------------------
  const getTransform = frame => {
    const x = _.round(frame.translate[0], 2)
    const y = _.round(frame.translate[1], 2)
    let str = `translate(${x}px, ${y}px) translateZ(0)`
    if ('rotate' in frame) {
      str += typeof frame.rotate === 'number' ? ` rotate(${frame.rotate}deg)` : ` rotate(${frame.rotate})`
    }
    return str
  }

  const getStyle = (target: HTMLElement | SVGElement, rect: RectInfo) => ({
    width: _.round(rect.offsetWidth, 2),
    height: _.round(rect.offsetHeight, 2),
    borderRadius: target.style.borderRadius || 'none',
    transform: target.style.transform || 'none'
    // top: rect.top,
    // left: rect.left
  })

  // 拖拽
  const onDragStart = (e: OnEvent) => {
    const target = e.target
    const fr = frameMap.get(target)

    if (!fr) {
      frameMap.set(target, { id: target.id, translate: [0, 0], rotate: 0 })
    } else if (target.style.transform) {
      const tf = transform.parse(target.style.transform)
      frameMap.set(target, { id: target.id, ...tf } as any)
    }
    // 此处有啥用？
    // if (fr) {
    //   e.set(fr.translate || [])
    // }
  }

  // 拖拽
  const onDrag = (e: OnDrag) => {
    const target = e.target
    const fr = frameMap.get(target)
    if (!fr) return
    fr.translate = e.beforeTranslate
    // 锁定
    const lock = target.dataset.lock || false
    if (lock.toString() === 'true') return

    target.style.transform = getTransform(fr)
  }

  // 旋转
  const onRotate = (e: OnRotate) => {
    const target = e.target
    const fr = frameMap.get(target)
    if (!fr) return
    fr.rotate = e.beforeRotate
    e.target.style.transform = getTransform(fr)
  }

  // 大小
  const onResize = (e: OnResize) => {
    const target = e.target
    const fr = frameMap.get(target)
    if (!fr) return
    fr.translate = e.drag.beforeTranslate

    const width = e.width
    let height = e.height
    // 比例锁功能
    const lockRatio = _.toNumber(e.target?.dataset?.lockRatio)
    if (!_.isNaN(lockRatio)) {
      height = _.round(width * lockRatio, 0)
    }

    e.target.style.width = `${Math.max(width, 2)}px`
    e.target.style.height = `${Math.max(height, 2)}px`
    e.target.style.transform = getTransform(fr)
  }

  // 圆角调整
  const onRound = (e: OnRound) => {
    const target = e.target
    const fr = frameMap.get(target)
    if (!fr) return
    fr.borderRadius = e.borderRadius
    e.target.style.borderRadius = e.borderRadius
  }

  // 位置，大小等
  const onBeforeRenderEnd = (e: BeforeRenderableEvents['onBeforeRenderEnd']) => {
    const target = e.target
    const rect = e.moveable.getRect() as RectInfo
    onElementStyleChange([{ key: target.id, style: getStyle(target, rect) }])
  }

  const onBeforeRenderGroupEnd = (e: BeforeRenderableEvents['onBeforeRenderGroupEnd']) => {
    const styles = e.targets.map((target, index) => {
      const rect = (e as any).moveable.moveables[index].getRect() as RectInfo
      return { key: target.id, style: getStyle(target as HTMLElement, rect) }
    })
    onElementStyleChange(styles)
  }

  const onBeforeRenderStart = (e: BeforeRenderableEvents['onBeforeRenderStart']) => {
    const target = e.target
    const rect = e.moveable.getRect() as RectInfo
    cacheRef.current.styles = [{ key: target.id, style: getStyle(target, rect) }]
  }

  const onBeforeRenderGroupStart = (e: BeforeRenderableEvents['onBeforeRenderGroupStart']) => {
    const styles = e.targets.map((target, index) => {
      const rect = (e as any).moveable.moveables[index].getRect() as RectInfo
      return { key: target.id, style: getStyle(target, rect) }
    })
    cacheRef.current.styles = styles
  }

  let moveableProps: MoveableProps = {
    target: targets as any[],
    rootContainer: containerRef.current,
    edgeDraggable: true, // 是否通过拖动边缘线移动
    throttleDrag: 1,
    // 插件
    ables: [OffsetViewable, SizeViewable, ChartSwitchViewable, LockViewable, TabsEnterViewable],
    props: {
      offsetViewable: true,
      sizeViewable: true,
      chartSwitchViewable: true,
      lockViewable: true,
      tabsEnterViewable: true
    },
    padding: { left: 0, right: 0, top: 0, bottom: 0 },
    zoom: 0.6, // 工具栏的放大倍数
    onClickGroup: e => selectoRef.current?.clickTarget(e.inputEvent, e.inputTarget),

    // 大小改变
    origin: false, // 中间是否显示一个点
    container: canvasRef.current,
    // 界限
    snappable: true,
    bounds: {
      left: 0,
      top: 0,
      right: (sizes[0] as number) + 1000,
      bottom: (sizes[1] as number) + 1000
    },

    // 大小
    renderDirections: ['nw', 'ne', 'sw', 'se'],
    resizable: true,
    throttleResize: 1, // 大小变化步数
    onResize,

    // 旋转
    rotatable: true,
    throttleRotate: 1,
    rotationPosition: 'top',
    onRotateStart: onDragStart,
    onRotate,

    // 拖拽
    draggable: true,
    onDragStart,
    onDrag,
    onDragGroupStart: e => e.events.forEach(ev => onDragStart(ev)),
    onDragGroup: e => e.events.forEach(ev => onDrag(ev)),

    // 圆角调整
    roundable: true,
    roundRelative: true, // 用 % 代替 px
    onRound,

    // 操作结束之后
    onBeforeRenderEnd,
    onBeforeRenderStart,
    onBeforeRenderGroupEnd,
    onBeforeRenderGroupStart,

    // 对齐线
    elementGuidelines,
    elementSnapDirections: directions,

    verticalGuidelines: [201, 401, 601, 801, 1001],
    horizontalGuidelines: [201, 401, 601, 801, 1001],
    snapThreshold: 2, // 捕获的距离
    isDisplaySnapDigit: true, // 是否显示元素内捕捉距离
    snapDirections: directions
  }

  // TODO: 有多个元素时，禁用功能
  if (targets.length > 1) {
    moveableProps.renderDirections = []
    moveableProps.resizable = false
    moveableProps.rotatable = false
    moveableProps.roundable = false
  }

  // 提供给外面修饰配置
  if (onConfigModify) {
    const config = onConfigModify({ ...moveableProps } as any, targets)
    if (config) moveableProps = config
  }

  return createElement(Moveable, { ...moveableProps, key: keyId, ref: moveableRef as any })
}

export default memo(ElementMoveable, isEqual)
