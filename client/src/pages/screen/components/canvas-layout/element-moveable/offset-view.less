.fix-font-vague {
  // 下面两行解决了字体放大后模糊的问题
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

// 位置显示
.moveable-offset-viewable {
  position: absolute;
  top: -20px;
  left: -2px;
  border-radius: 2px;
  font-size: 10px;
  line-height: 1;
  white-space: nowrap;
  user-select: none;
  .fix-font-vague();
}

// 大小显示
.moveable-size-viewable {
  position: absolute;
  background: var(--primary-color-30);
  border-radius: 2px;
  padding: 3px 3px 2px;
  color: #333;
  font-size: 11px;
  white-space: nowrap;
  user-select: none;
  line-height: 1;
  transform: translate(-50%, 2px);
  .fix-font-vague();
}

// 锁显示
.moveable-lock-viewable {
  position: absolute;
  color: var(--primary-color);
  font-size: 12px;
  user-select: none;
  line-height: 1;
  left: -15px;

  .anticon {
    margin-bottom: 3px;
  }
}

// tabs-layout 的按钮
.moveable-tabs-enter-viewable {
  position: absolute;
  top: 0;
  left: 0;
  background-color: #fff;
  transform: translate(-50%, -49%);
  border: 1px dashed rgba(@primary-color, 0.7);
  outline: none;
  display: block;
  width: 120px;
  border-radius: 8px;
  color: @primary-color;

  &:hover {
    cursor: pointer;
    background-color: rgba(@primary-color, 0.9);
    color: #fff;
  }
}

// 组件切换功能
.moveable-chart-switch-viewable {
  position: absolute;
  background-color: #fff;
  box-shadow: 0 0 3px rgba(1, 1, 1, 0.24);
  border-radius: 3px;

  // height: 24px;
  // line-height: 24px;
  top: -24px - 3px;
  min-width: 25px;
  display: flex;
  align-items: center;
  justify-content: space-around;

  color: #333;
  font-size: 14px;
  white-space: nowrap;
  user-select: none;
  transform: translate(-100%);
  .fix-font-vague();

  .anticon {
    cursor: pointer;
    font-size: 16px;
    padding: 4px;
    border-radius: 3px;

    &:hover {
      background-color: #f1f1f1;
    }
  }
}
