// import React from 'react'
import './offset-view.less'

import { EyeInvisibleOutlined, LockFilled, RetweetOutlined } from '@ant-design/icons'
import { Tooltip } from 'antd'
import { CSSProperties } from 'react'
import { MoveableManagerInterface, Renderer } from 'react-moveable'

import Icon from '@/components/icons/iconfont-icon'
import { TABS_LAYOUT_NAMES } from '@/consts/elements'
import { memoryStorage } from '@/storages/memory'
import { store } from '@/stores'

type Moveable = MoveableManagerInterface<any, any>

const getComponet = (key: string) => store.getState().editorCore?.components?.entities[key]

/**
 * 定义元素的浮点工具
 */

/**
 * 大小显示
 */
export const SizeViewable = {
  name: 'sizeViewable',
  props: {},
  events: {},
  render(moveable: Moveable, React: Renderer) {
    const rect = moveable.getRect() // 潜在性能问题，rect 会导致重绘
    const style: CSSProperties = {
      left: `${rect.width / 2}px`,
      top: `${rect.height + 6}px`
    }
    // Add key (required)
    // Add class prefix moveable-(required)
    return (
      <div key='sizeViewable' className='moveable-size-viewable' style={style}>
        {Math.round(rect.offsetWidth)} x {Math.round(rect.offsetHeight)}
      </div>
    )
  }
}

/**
 * 位置显示
 */
export const OffsetViewable = {
  name: 'offsetViewable',
  props: {},
  events: {},
  render(moveable: Moveable, React: Renderer) {
    const rect = moveable.getRect() // 潜在性能问题，rect 会导致重绘
    const rotation = moveable.getRotation()
    const rotate = Math.round(rotation)
    // Add key (required)
    // Add class prefix moveable-(required)
    return (
      <div key='offsetViewable' className='moveable-offset-viewable'>
        ({Math.round(rect.left)}, {Math.round(rect.top)}, {rotate === 360 ? 0 : rotate}°)
      </div>
    )
  }
}

/**
 * 锁显示
 */
export const LockViewable = {
  name: 'lockViewable',
  props: {},
  events: {},
  render(moveable: Moveable, React: Renderer) {
    const isLock = moveable.props.lock
    const key = moveable.props.target?.id || ''
    const els: JSX.Element[] = []
    // EyeInvisibleOutlined
    // Add key (required)
    // Add class prefix moveable-(required)
    if (isLock) {
      els.push(<LockFilled key='a' title='已设置锁定' />)
    }

    if (key) {
      const component = getComponet(key)
      const keys = component?.eventAction?.keys
      if (component && keys?.length > 0) {
        els.push(<Icon name='交互' key='b' title='已设置交互' />)
      }
      if (component && component.config?.visible === false) {
        els.push(<EyeInvisibleOutlined key='c' title='已设置隐藏' />)
      }
    }

    if (els.length === 0) return null

    return (
      <div key='lockViewable' className='moveable-lock-viewable'>
        {els}
      </div>
    )
  }
}

// ... ... ||| ^^^ www sss lll rrr aaa qqq eee
const openTabsLayout = key => e => {
  e.stopPropagation()
  store.dispatch.editorCore.useTabsLayout(key)
}

/**
 * Tabs 的进入编辑按钮
 */
export const TabsEnterViewable = {
  name: 'tabsEnterViewable',
  props: {},
  events: {},
  render(moveable: Moveable, React: Renderer) {
    const key = moveable.props.target?.id || ''
    const name = moveable.props.target?.dataset.name || ''
    if (!TABS_LAYOUT_NAMES.includes(name)) return null

    const rect = moveable.getRect() // 潜在性能问题，rect 会导致重绘

    return (
      <button
        type='button'
        key='tabsEnterViewable'
        className='moveable-tabs-enter-viewable'
        style={{
          left: `${rect.width / 2}px`,
          top: `${rect.height / 2}px`
        }}
        onClick={openTabsLayout(key)}
      >
        点我进入编辑
      </button>
    )
  }
}

/**
 * 组件切换功能
 * 1. 只支持自定义组件，
 * 2. 判断 config 的 valuePath 和 valueType 是否一致，一致的保留，不一致的覆盖掉
 */
export const ChartSwitchViewable = {
  name: 'chartSwitchViewable',
  props: {},
  events: {},
  render(moveable: Moveable, React: Renderer) {
    const key = moveable.props.target?.id || ''
    const allowSwitch = moveable.props.target?.dataset.allowSwitchDefine === 'true'

    if (!allowSwitch || !key) return null

    const rect = moveable.getRect() // 潜在性能问题，rect 会导致重绘
    const style: CSSProperties = {
      left: `${rect.width}px`
    }
    // Add key (required)
    // Add class prefix moveable-(required)

    return (
      <div
        key='chartSwitchViewable'
        className='moveable-chart-switch-viewable moveable-toolbar pointer' // moveable-toolbar 可以避免激活变化
        style={style}
        onClick={e => e.stopPropagation()} // 阻止一切发生
      >
        {allowSwitch &&
          <Tooltip
            title='切换组件类型'
            destroyTooltipOnHide={{ keepParent: true }}
            placement='top'
          >
            <RetweetOutlined
              onClick={e => {
                e.stopPropagation()
                memoryStorage.get('shitchComponentDefineRef')?.show({ sourceComponentKey: key })
              }}
            />
          </Tooltip>
        }
      </div>
    )
  }
}
