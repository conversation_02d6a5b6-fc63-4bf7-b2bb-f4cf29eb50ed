import { useDeepCompareEffect } from 'ahooks'
import isEqual from 'fast-deep-equal'
import _ from 'lodash'
import React, { memo, useEffect, useState } from 'react'
import Selecto from 'react-selecto'

import { memoryStorage } from '@/storages/memory'
import { findNodeByPath } from '@/utils/dom'

import type { ElementSelectoProps } from './type'

// 额外方法，用于选中元素
function setSelectedTargetByKey(selecto: Selecto, ids: string[], done: Function) {
  const set = new Set(ids)
  const els: any[] = selecto?.getSelectableElements?.() || []
  const actives: any[] = els.filter(i => set.has(i.id))
  els.forEach(el => {
    el.classList.remove('selected')
    el.style.zIndex = el.getAttribute('original-zindex') || '0' // 复原旧值
  })
  // 选中的
  selecto?.setSelectedTargets?.(actives)
  actives.forEach(el => {
    let oldZindex = _.parseInt(el.getAttribute('original-zindex') || '0')
    if (_.isNaN(oldZindex)) oldZindex = 0
    el.classList.add('selected')
    el.style.zIndex = `${oldZindex + 508}` // 给个最大值 ...
  })
  return requestAnimationFrame(() => done?.(actives))
}

/**
 * 画布的元素选择器
 * @param props
 */
export function ElementSelecto(props: ElementSelectoProps) {
  const { selectoRef, containerRef, moveableRef, elementIds } = props
  const { onSelectChange, onAeraClick, isDragElement } = props

  const [elements, setElements] = useState<HTMLElement[]>([])
  const [keyId] = useState(Math.random().toString(32))

  const initElements = (ids: string[] = elementIds) => {
    const list = ids?.map(id => document.getElementById(id)).filter(i => i) as HTMLElement[]
    setElements(list)
  }

  useEffect(() => {
    if (!selectoRef.current) return
    const selecto: any = selectoRef.current

    selecto.setSelectedTargetByKey = (ids: string[], done: any) => setSelectedTargetByKey(selecto, ids, done)
    selecto.forceUpdate = () => initElements(elementIds)
    memoryStorage.set('selecto', selecto)
    return () => memoryStorage.del('selecto')
  })

  useDeepCompareEffect(() => {
    // setTimeout(() => {
    initElements(elementIds)
    // }, 300)
  }, [elementIds, keyId])

  return (
    <Selecto
      key={keyId}
      ref={selectoRef as any}
      dragContainer={containerRef.current as Element}
      selectableTargets={elements}
      hitRate={100}
      ratio={0}
      // selectByClick={false} // 点击时是否能拖动

      // https://daybrush.com/selecto/release/latest/doc/
      selectFromInside={false}
      selectByClick
      preventDragFromInside={false}
      // clickBySelectEnd

      toggleContinueSelect={['shift']}
      onDragStart={e => {
        const target = e.inputEvent.target // 停止拖拽
        // 多选
        if (e.datas.startSelectedTargets?.length > 1) onAeraClick?.(e)
        if (isDragElement?.(moveableRef.current, target)) e.stop()
      }}
      onSelectStart={() => {
        ; (document as any).activeElement?.blur()
      }}
      onSelect={e => {
        e.added.forEach(el => {
          let oldZindex = _.parseInt(el.getAttribute('original-zindex') || '0')
          if (_.isNaN(oldZindex)) oldZindex = 0
          el.classList.add('selected')
          el.style.zIndex = `${oldZindex + 508}` // 给个最大值
        })
        e.removed.forEach(el => {
          // const orgindex =
          el.classList.remove('selected')
          el.style.zIndex = el.getAttribute('original-zindex') || '0' // 复原旧值
        })
      }}
      onSelectEnd={e => {
        // 点击工具栏时不触发后面的
        const ev = e.inputEvent
        const target = ev.target as HTMLDivElement
        const hasTool = findNodeByPath(target, el => el.classList.contains('moveable-toolbar'))
        if (hasTool) return
        if (target.classList.contains('context-menu-view-item')) {
          return
        }

        onSelectChange?.(e.selected.map(i => i.id), e)
        if (e.isDragStart && e.inputEvent.shiftKey === false) {
          e.inputEvent.preventDefault()
          setTimeout(() => {
            moveableRef.current?.dragStart(e.inputEvent)
          }, 10)
        }
      }}
    />
  )
}

export default memo(ElementSelecto, isEqual)
