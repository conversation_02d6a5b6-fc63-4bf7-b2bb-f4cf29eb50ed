import { CSSProperties } from 'react'
import { MoveableInterface, MoveableProps } from 'react-moveable'
import Selecto from 'selecto/declaration/Selecto'

export type Frame = {
  id: string
  translate: number[]
  rotate: number
  borderRadius?: string
}

export type ELement = HTMLElement | SVGElement

// 这里大量使用 ref 来提高渲染性能
export interface CommonRef {
  moveableRef: {
    current?: MoveableInterface & {
      forceUpdate: () => any
    }
  }
  selectoRef: {
    current?: Selecto & {
      // 用于选中元素
      setSelectedTargetByKey: (ids: string[], done: (ids: string[]) => any) => any
    }
  }
  canvasRef: { current: HTMLElement | HTMLDivElement | null }
  containerRef: { current: HTMLElement | HTMLDivElement | null }
}

export interface ElementMoveableProps extends CommonRef {
  activeIds: string[] // 激活 id 数组
  sizes: (number | string | undefined)[] // 宽高
  onSelectElement?: (es: Element[]) => any
  // 样式变化
  onElementStyleChange?: (values: { key: string; style: CSSProperties }[]) => any
  onConfigModify: (
    config: MoveableProps & { lock?: boolean; props: any },
    targets: any[]
  ) => MoveableProps | undefined | void

  /** 全部的元素 id */
  elementIds: string[]
}

export interface ElementSelectoProps extends CommonRef {
  // 可选元素
  onSelectChange?: (
    ids: string[],
    e: {
      added: Element[]
      removed: Element[]
      selected: Element[]
      [key: string]: any
    }
  ) => any
  isDragElement?: (moveable: any, target: Element) => boolean
  /** 多选时，点击面积区域的事件 */
  onAeraClick?: (e: any) => any

  /** 全部的元素 id */
  elementIds: string[]
}

export type FrameMap = Map<ELement, Frame>
