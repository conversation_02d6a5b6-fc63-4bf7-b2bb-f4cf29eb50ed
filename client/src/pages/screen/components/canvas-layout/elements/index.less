// 组件列表
.screen-element-list {
  // position: relative; // 这里不能设置，千万别设置
  width: 100%;
  height: 100%;

  // 组件
  .screen-element {
    position: absolute;
    z-index: 0;
    top: 0;
    left: 0;

    width: 60px;
    height: 60px;
    // border-radius: 3px;

    // 原来设置 flex 有什么用？
    // display: flex;
    // justify-content: center;
    // align-items: center;

    // background-color: darken(#faf8ff, 0.5%);
    font-size: 18px;
    margin: 0;
    color: #333;
    background-color: transparent;
    // border: 1px solid transparent;
    border: none;
    box-shadow: none;
    outline: none;
    // user-select: none;
    box-sizing: border-box !important;

    &.selected {
      overflow: hidden;
    }
  }

  // 装饰
  .screen-element > .renderer-container {
    position: relative;
    width: 100%;
    height: 100%;
    border-width: 0;
    border-color: #fff;
    border-style: none;
    box-sizing: border-box;
    background-repeat: no-repeat;
    background-size: auto;

    & > div,
    & > [class*='local-element'] {
      // overflow: hidden; // 先去掉
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      border-style: none;
      border-color: #fff;
      border-width: 1px;
    }

    &.has-carousel {
      height: calc(100% - 20px);
    }
    &.has-pointer {
      cursor: pointer;
    }
    // 轮播控制
    + .carousel-control {
      height: 15px;
      // max-width: 75%;
      margin: 0 auto;
      text-align: center;
      font-size: 11px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      user-select: none;

      position: absolute;
      z-index: 100;
      left: 0;
      right: 0;
      bottom: 2px;

      // 点点
      .carousel-control-dot {
        width: 30px;
        height: 5px;
        border-radius: 3px;
        background-color: rgba(#333, 0.1);
        // border: 1px solid rgba(#fff, 0.2);
        margin: 0 3px;
        cursor: pointer;
        z-index: 9999;
        &-active {
          background-color: var(--tint-color-30);
        }
      }
    }
  }

  // 不是预览时
  &.is-not-preview {
    .screen-element {
      user-select: none;
      // 不给超出边界
      // overflow: hidden;
      > .renderer-container {
        position: relative;
        & > div,
        & > [class*='local-element'] {
          overflow: hidden;
        }
      }

      &::after {
        position: absolute;
        left: -1px;
        right: -1px;
        bottom: -1px;
        top: -1px;
        content: ' ';
        border: 1px solid var(--tint-color-30);
        z-index: 0; // 这里不能设置成 0 以上否则会挡住 div 无法触发 click 等事件
        display: none;
      }
    }

    .screen-element:hover {
      box-shadow: 0 0 2px rgba(#111, 0.12);
      cursor: move;
    }

    // 点击时触发
    .screen-element:hover,
    .screen-element:active,
    .screen-element:focus,
    .screen-element:focus-within,
    .screen-element:focus-visible {
      &::after {
        display: block;
      }
    }

    .mask-layer > .renderer-container {
      pointer-events: none;
    }
  }
}
