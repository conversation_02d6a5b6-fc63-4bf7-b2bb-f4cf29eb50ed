import './index.less'

import cn from 'classnames'
import isEqual from 'fast-deep-equal'
import _ from 'lodash'
import React, { ComponentType, CSSProperties, memo } from 'react'

import { ELEMENT_OMIT_FIELD } from '@/consts/screen'
import { EditorCorePreviewRuntimeAPI } from '@/cores/evaction/rumtime-api/preview'
import { Component, ComponentDefine } from '@/types/editor-core/component'
import { EventsDefine } from '@/types/editor-core/events'
import { isAllowSwitch } from '@/utils/editor-core/element'

import RendererContainer from './renderer'

export interface ElementProps {
  elementClassName?: string
  idPrefix?: string
  isPreview?: boolean
  component: Component & { eventDefine: EventsDefine; chartData: any; define: any }
  // onUpdateComponent: (val: any) => any
  onConfigChange: any
  onStyleChange: any
  onUpdateLinkage: (val: any, type: 'configLinkage' | 'filterLinkage') => any

  previewRuntimeAPI?: EditorCorePreviewRuntimeAPI // 预览时才需要传
  forceRender?: boolean

  onEnterOrLeave?: (isEnter: boolean) => any // 元素进入/离开视野回调
  onClick?: (e: any) => any
  releaseConfig?: any

  RendererContainer?: ComponentType
  /** tabs layout 用到 */
  renderTabLayoutContent?: (tab: any, index: number) => JSX.Element | null
  // onRightClick?: (e: any, item: any) => any // 右键
}

/**
 * 单个元素的渲染
 * 纯函数组件，不连接 redux
 * @param props
 */
function ElementView(props: ElementProps) {
  const { idPrefix = '', isPreview, elementClassName, component, previewRuntimeAPI, releaseConfig } = props
  const { onUpdateLinkage, onEnterOrLeave, onConfigChange, onStyleChange, forceRender } = props
  const { renderTabLayoutContent } = props
  const Renderer = props.RendererContainer || RendererContainer

  const define = component.define as ComponentDefine

  const getElement = (key: string) => document.getElementById(`layer-node-${key}`) as HTMLElement
  const injectEvent = (key: string) => ({
    title: component.title,
    onClick: props.onClick,
    onContextMenu: e => e.preventDefault(),
    onDragOver: e => e.preventDefault(),
    // 实现图层的效果联动
    onMouseEnter: () => {
      const el = getElement(key)
      if (el && !el.style.backgroundColor) el.style.backgroundColor = '#f5f1fb'
    },
    onMouseLeave: () => {
      const el = getElement(key)
      if (el && el.style.backgroundColor) el.style.backgroundColor = ''
    }
  })

  const getStyle = (style: CSSProperties) => {
    const pickField: string[] = [...ELEMENT_OMIT_FIELD]
    if (!isPreview) {
      style.opacity = Math.max(style.opacity === undefined ? 1 : style.opacity as number, 0.2)
    }
    // 非预览
    if (!isPreview) return _.pick(style, pickField)

    // 如果是渲染，则需要添加 top，left
    if (style.position === 'fixed') pickField.push('position')
    // ---
    return _.pick(style, pickField)
  }

  return (
    <div
      key={component.key}
      id={`${idPrefix}${component.key}`}
      // title={component.alias || component.title}
      // 以下为挂载的辅助信息
      data-name={component.name}
      data-define-type={define.type}
      data-define-key={component.defineKey}
      data-define-group={define.group}
      data-define-name={define.name}
      data-lock={isPreview ? null : component.config?.lock}
      data-lock-ratio={isPreview ? null : component.config?.lockRatio}
      data-allow-switch-define={isAllowSwitch(define)}
      original-zindex={component.style.zIndex}
      style={getStyle(component.style)}
      className={cn(elementClassName, {
        'screen-element': true, // 图表的组件，添加一个遮罩层
        'mask-layer': !isPreview && component.define?.type === 'chart'
      })}
      {...(isPreview ? {} : injectEvent(component.key))}
    >
      <Renderer
        key={component.key}
        define={component.define}
        forceRender={forceRender}
        component={component}
        onConfigChange={onConfigChange}
        onStyleChange={onStyleChange}
        isPreview={isPreview}
        idPrefix={idPrefix} // 数据筛选器需要
        eventDefine={component.eventDefine}
        chartData={component.chartData}
        previewRuntimeAPI={previewRuntimeAPI}
        releaseConfig={releaseConfig}
        onUpdateLinkage={onUpdateLinkage}
        onEnterOrLeave={onEnterOrLeave}
        renderTabLayoutContent={renderTabLayoutContent}
      />
    </div>
  )
}

const omitField = [
  'component.config.lock',
  'component.style.opacity'
]

export default memo(ElementView, (prev, next) => isEqual(
  _.omit(prev, omitField),
  _.omit(next, omitField)
))
