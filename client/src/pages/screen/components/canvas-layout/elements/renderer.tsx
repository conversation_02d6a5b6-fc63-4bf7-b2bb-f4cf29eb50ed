import { LoadingOutlined } from '@ant-design/icons'
import { useMemoizedFn } from 'ahooks'
import { Skeleton } from 'antd'
import cn from 'classnames'
import isEqual from 'fast-deep-equal'
import { klona as cloneDeep } from 'klona/lite'
import _ from 'lodash'
import React, { lazy, memo, Suspense, useEffect, useMemo } from 'react'
import { useInView } from 'react-intersection-observer'

import ErrorWrap from '@/components/error-wrap'
import { ELEMENT_MAP, TABS_LAYOUT_NAMES } from '@/consts/elements'
import { EditorCorePreviewRuntimeAPI } from '@/cores/evaction/rumtime-api/preview'
import useDataCarousel from '@/hooks/editor-core/use-data-carousel'
import { useTopQueryParams } from '@/hooks/use-query-params'
import type { Component, ComponentDefine } from '@/types/editor-core/component'
import type { EventsDefine } from '@/types/editor-core/events'
import type { ProjectType } from '@/types/entitys/project'
import { getDemoData, getGlobalVarValue, getInjectElementStyle, isComponentHasDataSource, transformConfigOptions } from '@/utils/editor-core/element'
import { tryJsonParse } from '@/utils/json-utils'


const SafeComponentNotebookAdapter = lazy(() => import('@/pages/charts-gallery/components/component-notebook-adapter'))

export interface RendererContainrProps {
  define: ComponentDefine
  component: Component & { eventDefine: EventsDefine; chartData: any; define: any }
  eventDefine: EventsDefine

  onConfigChange: (id: string, val: any) => any
  onStyleChange: (id: string, val: any) => any
  onUpdateLinkage: (val: any, linkageType: 'configLinkage' | 'filterLinkage') => any

  isPreview?: boolean
  idPrefix?: string

  /** 默认是可视区域才渲染，这个变量用于控制是否强行渲染 */
  forceRender?: boolean
  chartData: any[] | null | undefined
  // 项目的数据（可选，目前只要发布信息）
  releaseConfig?: ProjectType['releaseConfig']

  previewRuntimeAPI?: EditorCorePreviewRuntimeAPI // 预览时才需要传
  onEnterOrLeave?: (isEnter: boolean) => any // 元素进入/离开视野回调

  /** tabs layout 用到 */
  renderTabLayoutContent?: (tab: any, index: number) => JSX.Element | null
}

/** 监听全局变量变更，然后更新组件的 config.value */
function useStorageListener(
  { globalVarName, initVal, onConfigChange, onChange, disabled }: {
    globalVarName: string | undefined | null,
    initVal: any,
    onConfigChange: (fn: (cfg: any) => any) => any,
    onChange: (val: any) => any,
    disabled?: boolean
  }
) {
  const onConfigChangeMemo = useMemoizedFn(onConfigChange)
  const onChangeMemo = useMemoizedFn(onChange)

  useEffect(() => {
    const isSnapshotPage = _.includes(window.location.search, 'arhType')
    const finalGlobalVarName = globalVarName && `gv_${globalVarName}`
    if (disabled || isSnapshotPage || !finalGlobalVarName) {
      return
    }
    // 如果有初始值，就初始化全局变量
    if (initVal && !_.isEqual(initVal, getGlobalVarValue(globalVarName))) {
      window.localStorage[finalGlobalVarName] = JSON.stringify(initVal)
    }
    let lastVal = window.localStorage[finalGlobalVarName]
    // 监听全局变量变更
    const listener = ev => {
      if (ev.key !== finalGlobalVarName) {
        return
      }
      const newValue = ev.newValue

      if (newValue !== lastVal) {
        lastVal = newValue
        const newStoredVal = newValue && tryJsonParse(newValue, []) || []
        onConfigChangeMemo(cfg => ({ ...cfg, value: newStoredVal })) // 修改属性
        onChangeMemo(newValue) // 触发 onEvents.change，主要是为了通知筛选器组件
      }
    }
    window.addEventListener('storage', listener)
    return () => {
      window.removeEventListener('storage', listener)
    }
  }, [])
}


/**
 * 渲染器，根据 loadMode，name 等信息进行渲染组件
 * 纯函数组件，这个组件禁止连接 redux
 */
function RendererContainer(props: RendererContainrProps) {
  const { define, eventDefine, component, chartData, previewRuntimeAPI, idPrefix = '', releaseConfig } = props
  const { onUpdateLinkage, onEnterOrLeave, onConfigChange, onStyleChange } = props
  const { forceRender = false, renderTabLayoutContent } = props
  // 在页面导出PDF/Word时会进行强制渲染
  const { forceRenderOnExport } = useTopQueryParams()

  // 如果是数据筛选组件那么不需要懒加载，下面是强制渲染的控件
  const forceComponentGroup = useMemo(() => [
    'filterRule', // 数据筛选器
    'numPicker', // 数值输入控件
    'textInput', // 文本输入控件
    'timePicker', // 时间输入控件
    'selector' // 下拉选择控件
  ], [])

  // 这里通过特殊的方法修复先，如果为 undefined 则判断 url
  const isPreview = useMemo(() => {
    if (props.isPreview === undefined) {
      return /\/preview\/\w+/.test(window.location.href)
    }
    return props.isPreview
  }, [props.isPreview])

  // 可见区域加载
  const compHeight = component.style.height && parseFloat(component.style.height) || 500
  const { ref: observeRef, inView } = useInView({
    threshold: compHeight <= 250 ? 0.1 : _.clamp(_.round(25 / compHeight, 3), 0, 1), // 能见 25px，就加载
    rootMargin: '0px 0px 0px 0px',
    onChange: isInView => onEnterOrLeave?.(isInView),
    triggerOnce: true
  })
  const [eventHandlerMap, unknownEventHandlerMap] = useMemo(
    // 打印的时候会出现重复 key，所以 getComponentByKey 其实不能拿到实际的当前组件，不过因为打印时用不上事件，所以可以简单返回空对象
    () => _.isEmpty(component.eventAction.entities) ? [{}, {}] : [
      previewRuntimeAPI?.getComponentByKey(component.key)?.genEventHandler('dom'),
      previewRuntimeAPI?.getComponentByKey(component.key)?.genEventHandler('unknown')
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [eventDefine, component.eventAction, component.key]
  )

  // 渲染的节点
  const View = useMemo(() => {
    let el: React.ComponentType<any> | null = null

    if (define?.loadMode === 'local') el = ELEMENT_MAP[define?.name]
    if (define?.loadMode === 'live') el = SafeComponentNotebookAdapter

    return el || (p => <div style={p.style} />)
  }, [define?.name, define?.loadMode])

  const dataSource = component.dataSource || {}

  // isComponentHasDataSource 表示配置了数据源，不能用默认值
  const rawData = useMemo(
    () => isComponentHasDataSource({ dataSource }) ? (chartData || []) : getDemoData(define.dataSourceDefine),
    [chartData, define.dataSourceDefine]
  )
  const { data, carouselControl, hasDataCarousel, handleMouseEvent } = useDataCarousel({
    carousel: dataSource.carousel,
    theme: component.config?.options?.theme || component.config?.options?.color,
    rawData
  })

  // 是否有配置点击事件
  const hasPointer = useMemo(() => !_.isEmpty(component?.eventAction?.entities?.click), [component?.eventAction?.entities])

  // 如果有绑定全局变量，监听全局变量变更
  useStorageListener({
    globalVarName: component.config.globalVarName,
    initVal: component.config.value,
    onConfigChange: next => onUpdateLinkage?.(next, 'configLinkage'),
    onChange: unknownEventHandlerMap?.onChange || _.noop,
    disabled: !isPreview
  })

  // 注入到组件的 props
  const injectProps = {
    meta: _.pick(component, ['key', 'title', 'alias', 'description', 'name', 'version']),
    // TODO: width/height/fons-size/padding/margin 等需要做 rem 转换
    // TODO: 边框，圆角，边距，背景，阴影，不传进去
    style: getInjectElementStyle(component.style, component.key, isPreview),
    config: transformConfigOptions(component.config, data),
    dataSource: cloneDeep(component.dataSource),
    eventAction: cloneDeep(component.eventAction),
    deviceType: define.device, // 这个组件适合在什么设备上使用
    isPreview: isPreview || false,
    idPrefix,
    isMobile: false,
    onConfigChange,
    onStyleChange,
    onUpdateLinkage,
    define, // 自定义图表需要
    data,
    onEvents: unknownEventHandlerMap,
    // 数据筛选器，查询按钮依赖 runtimeAPI
    runtimeAPI: previewRuntimeAPI,
    renderTabContent: undefined as any,
    releaseConfig: undefined as any
  }

  if (component.name.indexOf('component-iframe') > -1) {
    // 补充发布配置
    injectProps.releaseConfig = releaseConfig
  }

  const events = {
    onClick: e => e.stopPropagation(),
    onDoubleClick: e => e, // e.stopPropagation()
    ...handleMouseEvent,
    ...(isPreview ? eventHandlerMap : {})
  }

  // 定义表为空

  // tabs layout 布局组件用到
  if (TABS_LAYOUT_NAMES.includes(component.name)) {
    injectProps.renderTabContent = renderTabLayoutContent
  }

  // 强行渲染时，自动请求接口
  useEffect(() => {
    if (forceRender || forceRenderOnExport === 'true') {
      return onEnterOrLeave?.(true)
    }
    // 如果是筛选器组件，不进行懒加载
    if (forceComponentGroup.includes(define.group)) {
      onEnterOrLeave?.(true)
    }
  }, [forceRender, onEnterOrLeave, forceRenderOnExport, define.group, forceComponentGroup])

  if (!define) return <span>组件缺少定义信息</span>
  if (!View) return <span>组件不存在</span>

  const isLoadingData = chartData === null // _.isNil(chartData) && _.isEmpty(data)

  // console.log(chartData, data)

  return (
    <Suspense fallback={<LoadingOutlined size={10} />}>
      <div
        className={cn({
          'renderer-container': true,
          'has-carousel': hasDataCarousel,
          'has-pointer': hasPointer
        })}
        ref={observeRef}
        {...events}
      >
        {(inView || forceRender || forceRenderOnExport === 'true' || forceComponentGroup.includes(define.group)) && (
          <ErrorWrap>
            {isLoadingData ?
              <Skeleton active key={`dev-${component.key}`} style={injectProps.style} loading={isLoadingData} /> :
              <View key={`dev-${component.key}`} {...injectProps} />
            }
          </ErrorWrap>
        )}
      </div>
      {carouselControl}
    </Suspense>
  )
}

// 更新排除的字段
const omitField = [
  'component.config.lock',
  'component.style.transform',
  'component.style.zIndex',
  'renderTabLayoutContent'
]

// 特殊处理（忽略更新字段）
const SPECIAL_OMIT_FIELD_DICT = {
  // 筛选器需要监听位置改变，然后修改内部组件位置
  'component-filter-rule': _.without(omitField, 'component.style.transform')
}

/**
 * 元素的渲染器
 * 性能优化
 */
export default memo(RendererContainer, (prev, next) => {
  // 优化更新
  const paths = SPECIAL_OMIT_FIELD_DICT[prev.define.name] || omitField // define.name 不会变
  return isEqual(_.omit(prev, paths), _.omit(next, paths))
})
