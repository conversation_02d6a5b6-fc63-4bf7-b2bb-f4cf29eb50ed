
.cursor-coordinate-y-line,
.cursor-coordinate-x-line {
  position: absolute;
  border: 1px dashed rgba(#f45, 0.5);
  margin-top: 1px;
  z-index: 300;
  top: 0;
  left: 0;
  transform-origin: top left;
  pointer-events: none;

  &.hide {
    &::after {
      display: none !important;
    }
  }
}

.cursor-coordinate-x-line::after,
.cursor-coordinate-y-line::after {
  position: absolute;
  font-size: 11px * 2;
  content: attr(data-index) "px" ;
  color: #222;
  z-index: 1;
}

.cursor-coordinate-x-line::after {
  left: 5px;
  top: -32px;
}

.cursor-coordinate-y-line::after {
  top: 0px;
  left: -40px;
}
