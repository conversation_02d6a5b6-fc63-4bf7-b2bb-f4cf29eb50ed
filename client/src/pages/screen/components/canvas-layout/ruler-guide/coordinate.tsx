// 高亮显示鼠标位置
import './coordinate.less'

import { useMouse } from 'ahooks'
import type { CursorState } from 'ahooks/es/useMouse'
import cn from 'classnames'
import isEqual from 'fast-deep-equal'
import _ from 'lodash'
import React, { memo, useEffect } from 'react'

export interface CoordinateProps {
  offset?: [number, number]
  target?: HTMLElement | null

  zoom?: number
  onChange?: (data: CursorState) => any

  styleWidth: any
  styleHeight: any
}

function Coordinate(props: CoordinateProps) {
  const { offset = [0, 0], target, onChange, zoom = 1 } = props
  const { styleWidth, styleHeight } = props

  const mouse = useMouse(target)
  const { pageY, elementX: x, elementY } = mouse
  const y = pageY - 125

  useEffect(() => {
    onChange?.(mouse)
  })

  if (Number.isNaN(y) || Number.isNaN(x)) return null

  const xIndex = _.floor((x - 2) / zoom, 0)
  const yIndex = _.floor((elementY - 2) / zoom, 0)

  return (
    <div
      style={{
        position: 'absolute',
        width: styleWidth,
        height: styleHeight,
        top: 0,
        left: 0,
        transformOrigin: 'top left'
      }}
    >
      <div
        className={cn('cursor-coordinate-y-line', { hide: xIndex < 0 })}
        style={{ height: '200%', transform: `translate(${(x + offset[0])}px, 0) scale(0.6)` }}
        data-index={xIndex}
      />
      <div
        className={cn('cursor-coordinate-x-line', { hide: yIndex < 0 })}
        style={{ width: '200%', transform: `translate(0, ${(y + offset[1])}px) scale(0.6)` }}
        data-index={yIndex}
      />
    </div>
  )
}

export default memo(Coordinate, isEqual)

