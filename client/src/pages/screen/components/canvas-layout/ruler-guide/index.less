@val: 25px;
@color: #fcfefe;

.ruler-guide {
  .ruler {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    background-color: @color;

    .scena-guide {
      background-color: var(--primary-color);
    }
  }
  .ruler.horizontal {
    left: 0;
    top: 1px;
    width: 100%;
    height: @val;
    // border-top: 1px solid var(--border-color-base);
    border-bottom: 1px solid var(--border-color-base);
  }
  .ruler.vertical {
    top: 0;
    width: @val;
    left: 2px;
    height: 100%;
    // border-left: 1px solid var(--border-color-base);
    border-right: 1px solid var(--border-color-base);
  }

  .rule-eye {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    z-index: 10;
    background-color: @color;
    width: @val;
    height: @val;
    text-align: center;
    line-height: @val;
    font-size: 14px;
    color: #333;
    // border-top: 1px solid var(--border-color-base);
    // border-right: 1px solid var(--border-color-base);
    border-bottom: 1px solid #e8e8e8;

    &::after,
    &::before {
      position: absolute;
      content: '';
      height: 15px;
      width: 1px;
      background-color: #555;
      left: 2px;
      top: 5px;
      z-index: 1;
      display: none;
    }
    &::before {
      height: 1px;
      width: 15px;
    }
  }

  &.hide-scena-guide {
    .scena-guide {
      display: none;
    }
  }
}

.ruler-guide-setting-overlay {
  background-color: #fff;
  border-radius: 5px;
  box-shadow:
  1px 2px 8px rgba(1, 1, 1, 0.15),
  0 0 3px rgba(1, 1, 1, 0.1)
  ;
  // border: 1px solid #f4f4f4;
  user-select: none;
  padding: 0;
  .ant-popover-inner {
    box-shadow: none;
  }
  .ant-popover-inner-content {
    padding: 8px;
  }
}

.ruler-guide-setting-overlay-content {
  display: flex;
  flex-direction: column;
  .ant-checkbox-wrapper {
    margin-left: 0;
  }
}
