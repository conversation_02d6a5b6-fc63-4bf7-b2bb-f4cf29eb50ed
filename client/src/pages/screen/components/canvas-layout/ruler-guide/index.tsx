import './index.less'

import { SettingOutlined } from '@ant-design/icons'
import Guides, { GuidesOptions } from '@scena/guides'
import { useDeepCompareEffect, useMemoizedFn } from 'ahooks'
import { Popover } from 'antd'
import isEqual from 'fast-deep-equal'
import React, { memo, useEffect, useRef, useState } from 'react'
import ResizeObserver from 'resize-observer-polyfill'

export interface RulerGuideProps {
  zoom?: number
  keyId: string // 用来保存唯一性的
  defaultOptions?: Partial<GuidesOptions>
  containerRef: { current: HTMLElement | null }
  verticalStyle?: any
  horizontalStyle?: any
  renderSettingContent?: () => JSX.Element | null
}

/**
 * 通用的标尺与对齐线组件
 * @param props
 */
export function RulerGuide(props: RulerGuideProps) {
  const { zoom = 1, keyId, defaultOptions, containerRef, verticalStyle, horizontalStyle } = props
  const guidesHorizontal = useRef<any>(null)
  const guidesVertical = useRef<any>(null)

  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    const container = containerRef.current

    if (!container) {
      console.error('找不到容器节点')
      return
    }

    const H = '25px'
    const defaultConfig: Partial<GuidesOptions> = {
      dragPosFormat: v => v - 9, // 拖动位置？
      backgroundColor: '#fcfefe',
      textColor: '#999',
      lineColor: '#aaa',
      textOffset: [0, 5],
      longLineSize: 7,
      shortLineSize: 7,
      font: '12px sans-serif',
      unit: 50,
      range: [-100000, 100000],
      negativeRuler: false,
      zoom: zoom || 1,
      // 默认的对齐线
      displayDragPos: true, // 拖拽时是否显示位置
      showGuides: true, // // 是否显示对齐线
      dragGuideStyle: {
        fontWeight: 'normal'
      },
      ...defaultOptions
    }

    const node1: any = document.querySelector(`.${keyId}.ruler.horizontal`)
    const node2: any = document.querySelector(`.${keyId}.ruler.vertical`)

    if (!node1 || !node2) {
      console.error('找不到 ruler 节点')
      return
    }

    const gh = new Guides(node1, {
      type: 'horizontal', // 水平的
      ...defaultConfig,
      textOffset: [0, 5],
      rulerStyle: { left: '39.5px', width: `calc(100% - ${H})`, height: '100%', ...horizontalStyle }
    })
    const gv = new Guides(node2, {
      type: 'vertical',
      ...defaultConfig,
      textOffset: [5, 0],
      rulerStyle: { top: '39.5px', height: `calc(100% - ${H})`, width: '100%', ...verticalStyle }
    })

    guidesHorizontal.current = gh
    guidesVertical.current = gv

    const robserver = new ResizeObserver(() => {
      gh.resize()
      gv.resize()
    })
    robserver.observe(container)

    setIsLoaded(true)

    return () => {
      robserver.disconnect()
      gh.destroy()
      gv.destroy()
    }
  }, [keyId])

  // 设置配置
  useDeepCompareEffect(() => {
    if (isLoaded) {
      guidesHorizontal.current?.setState(defaultOptions)
      guidesVertical.current?.setState(defaultOptions)
    }
  }, [defaultOptions, isLoaded])

  const onScroll = useMemoizedFn((container, gh, gv) => {
    const x = container.scrollLeft / zoom
    const y = container.scrollTop / zoom
    gh.scroll(x)
    gv.scroll(y)
    gh.scrollGuides(y)
    gv.scrollGuides(x)
  })

  // 缩放重置标尺
  useEffect(() => {
    if (!isLoaded) return
    const gh = guidesHorizontal.current
    const gv = guidesVertical.current
    const container = containerRef.current

    const scroll = () => onScroll(container, gh, gv)
    container?.addEventListener('scroll', scroll)
    return () => {
      container?.removeEventListener('scroll', scroll)
    }
  }, [isLoaded, keyId])

  // 缩放
  useEffect(() => {
    const options = { zoom, unit: 75 }
    if (zoom < 0.9) options.unit = 100
    if (zoom < 0.6) options.unit = 150
    if (zoom > 2.0) options.unit = 40
    if (zoom > 2.5) options.unit = 30
    if (zoom > 3) options.unit = 20

    guidesHorizontal.current?.setState(options)
    guidesVertical.current?.setState(options)
    guidesVertical.current?.resize()
    guidesHorizontal.current?.resize()
  }, [zoom, keyId])

  return (
    <div className='ruler-guide' key={keyId}>
      <div className={`ruler horizontal ${keyId}`} onContextMenu={e => e.stopPropagation()} />
      <div className={`ruler vertical ${keyId}`} onContextMenu={e => e.stopPropagation()} />

      <div className='rule-eye'>
        {props.renderSettingContent ?
          <Popover
            placement='bottomLeft'
            trigger={['click']}
            content={props.renderSettingContent?.() || <span />}
            showArrow={false}
            overlayClassName='ruler-guide-setting-overlay'
          >
            <SettingOutlined title='画布设置' />
          </Popover>
          :
          <SettingOutlined title='画布设置' />
        }
        {/* {dragPos ? <EyeOutlined title='隐藏对齐线' /> : <EyeInvisibleOutlined title='显示对齐线' />} */}
      </div>
    </div>
  )
}

export default memo(RulerGuide, isEqual)
