.canvas-size-ruler {
  position: absolute;
  background-color: transparent;
  z-index: -1;
  font-size: 11px;
  text-align: center;
  color: var(--tint-color-50);

  &.horizontal,
  &.vertical {
    border-width: 0;
    border-style: solid;
    border-color: var(--tint-color-80);
    &::after,
    &::before {
      content: ' ';
      position: absolute;
    }
  }

  &.horizontal {
    left: 0;
    right: -1px;
    bottom: -20px;
    border-left-width: 1px;
    border-right-width: 1px;

    &::after,
    &::before {
      height: 0;
      width: calc(50% - 40px);
      border-top: 1px dashed var(--tint-color-80);
      top: 50%;
      left: 10px;
    }
    &::before {
      left: auto;
      right: 10px;
    }
  }

  &.vertical {
    top: 0;
    bottom: -1px;
    right: -20px;
    writing-mode: vertical-rl;
    border-top-width: 1px;
    border-bottom-width: 1px;

    &::after,
    &::before {
      width: 0;
      height: calc(50% - 40px);
      border-left: 1px dashed var(--tint-color-80);
      top: 10px;
      left: 50%;
    }
    &::before {
      top: auto;
      bottom: 10px;
    }
  }
}
