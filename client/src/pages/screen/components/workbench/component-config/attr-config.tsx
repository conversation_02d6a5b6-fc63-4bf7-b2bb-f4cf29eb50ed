import isEqual from 'fast-deep-equal'
import React, { Fragment, memo } from 'react'

import { FormSchema } from '@/components/form-schema'
import { Config } from '@/types/editor-core/config'

import { BaseConfigProps } from './base-config'
// import type { ComponentType } from '@/types/editor-core/component'

const FormSchemaMemo = memo(FormSchema, isEqual)

export interface AttrConfigProps extends Pick<BaseConfigProps, 'style' | 'onStyleChange'> {
  configSchema: any
  config: Config
  onConfigChange: (config: any) => any
  extraMap: Record<string, JSX.Element | undefined>
  // styleSchema?: Record<string, any>
  // defineType?: ComponentType // 定义表类型
}

/**
 * 属性配置面板
 * @param props
 */
export default function AttrConfig(props: AttrConfigProps) {
  const { configSchema = {}, config = {}, onConfigChange, extraMap } = props

  return (
    <div className='element-attr-config-panel'>
      <FormSchemaMemo
        className='element-attr-config-schema pt-2'
        schema={configSchema}
        value={config}
        onChange={onConfigChange}
      // 用于动态修饰单个 schema
      // schemaModifyFn={(key, schema) => {
      //   if (key === 'userName' && schema.type === 'select') {
      //     schema.options = [...]
      //   }
      //   return schema
      // }}
      />

      {/* 遍历扩展内容 */}
      {Object.keys(extraMap).filter(k => extraMap[k]).map(key => (
        <Fragment key={key}>{extraMap[key]}</Fragment>
      ))}
    </div>
  )
}

export type AttrConfigType = typeof AttrConfig
