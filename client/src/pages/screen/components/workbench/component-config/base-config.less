.element-base-config-panel {
  border-bottom: 1px solid var(--border-color-base);
  padding-top: 6px;

  .items-center {
    .anticon-unlock,
    .anticon-lock {
      font-size: 16px;
      margin-left: 2px;
    }
  }

  .title-setting {
    display: flex;
    align-items: center;
    margin-top: 10px;

    > .anticon {
      cursor: pointer;
      font-size: 20px;
      margin-right: 12px;
      color: #444;
      &:first-of-type {
        margin-left: 12px;
      }
      &:last-of-type {
        margin-right: 4px;
      }
    }
  }

  // x，y 输入
  .width-height-input {
    .ant-input-number-group-wrapper {
      margin: 5px;
      width: 48%;
    }
    .ant-input-number {
      border-left: none;
    }
    .ant-input-number,
    .ant-input-number-group-addon {
      box-shadow: none;
    }
    .ant-input-number-group-addon {
      color: #888;
      padding: 0 6px;
    }
    .ant-input-number-input {
      padding-left: 4px;
    }
  }

  .ant-input-number-group-addon {
    padding: 0 5px;
    background-color: #fff;
    width: 28px;
  }

  .ant-slider {
    margin: 0 16px;
    margin-right: 12px;
  }
}
