import './base-config.less'

import { EyeInvisibleOutlined, EyeOutlined, LockOutlined, UnlockOutlined } from '@ant-design/icons'
import { useDeepCompareEffect } from 'ahooks'
import { InputNumber, Radio, Slider } from 'antd'
import _ from 'lodash'
import React, { CSSProperties, useEffect, useMemo, useState } from 'react'
import * as transform from 'transform-parser'

import DebounceInput from '@/components/debounce-input'
import Icon from '@/components/icons/iconfont-icon'
import { ELEMENT_OMIT_FIELD } from '@/consts/screen'
import Item from '@/pages/screen/components/workbench/page-config/config-item-warp'
import { memoryStorage } from '@/storages/memory'

function MySlider({ value, onChange }) {
  const [val, setVal] = useState(value || 100)

  useEffect(() => {
    setVal(value === undefined ? 100 : _.floor(value * 100, 0))
  }, [value])

  return (
    <>
      <span>可见度</span>
      <Slider
        className='flex-1'
        value={val}
        onChange={v => setVal(v)}
        onAfterChange={va => onChange(_.floor(va / 100, 2))}
      />
      <span>{val}%</span>
    </>
  )
}

export interface BaseConfigProps {
  // 组件样式
  style: Pick<CSSProperties, 'transform' | 'width' | 'height' | 'borderRadius' | 'position' | 'opacity'>
  // 组件 title
  title: string
  // 组件 alias
  alias?: string
  lock?: boolean
  // 默认显示组件
  visible?: boolean
  // 锁定比例
  lockRatio?: number
  // 浮动等
  position?: 'absolute' | 'fixed' | (string & {})

  onStyleChange: (style: any) => any
  onAliasChange: (alias: string) => any
  onLockChange: (lock: boolean) => any
  onVisibleChange: (visible: boolean) => any
  onPositionChange: (position: string) => any
  onLockRatioChange: (lockRatio: number | undefined) => any
}

/**
 * 组件的基础配置
 * 位置大小，别名等
 */
export default function BaseConfig(props: BaseConfigProps) {
  const { style, title, alias = '', lock, visible = true, position, lockRatio } = props
  const { onStyleChange, onAliasChange, onLockChange, onVisibleChange, onPositionChange, onLockRatioChange } = props
  const { width, height, borderRadius } = style

  const name = alias?.length > 0 ? alias : title
  const radius = borderRadius === 'none' ? 0 : Number.parseFloat((borderRadius || '')?.toString()) || 0

  const tf = useMemo(() => transform.parse(style.transform || ''), [style.transform])
  const rotate = Number.parseFloat(tf.rotate?.toString() || '0') % 360
  const [x = 0, y = 0] = (Array.isArray(tf.translate) ? tf.translate : [tf.translate] || []).map(i =>
    Math.ceil(Number.parseFloat(i?.toString() || '0'))
  )

  const update = (key: string, val: any) => {
    if (lock) return
    if (key === 'rotate' || key === 'top' || key === 'left') {
      if (!tf.translate) tf.translate = []
      if (key === 'top') tf.translate[0] = val || 0
      if (key === 'left') tf.translate[1] = val || 0
      if (key === 'rotate') tf.rotate = `${val}deg`
      onStyleChange({ ...style, transform: transform.stringify(tf) })
    } else {
      // 比例锁，联动两个值
      if ((key === 'width' || key === 'height') && lockRatio !== undefined) {
        if (key === 'width') {
          return onStyleChange({ ...style, width: val, height: _.round(val * lockRatio, 0) })
        }
        if (key === 'height') {
          return onStyleChange({ ...style, height: val, width: _.round(val / lockRatio, 0) })
        }
      }

      onStyleChange({ ...style, [key]: val })
    }
  }

  const onLock = () => onLockChange(!lock)
  const onVisible = () => onVisibleChange(!visible)
  const onLockRatio = () => {
    const newLockRatio = lockRatio !== undefined ? undefined : _.round(_.toNumber(height) / _.toNumber(width), 4)
    onLockRatioChange(newLockRatio)
  }

  useDeepCompareEffect(() => {
    requestAnimationFrame(() => memoryStorage.get('moveable')?.forceUpdate())
  }, [_.pick(style, ELEMENT_OMIT_FIELD.concat(['transform'])), lock, visible])

  return (
    <div className='element-base-config-panel'>
      <Item title='组件名称' contentClassName='title-setting'>
        <DebounceInput
          placeholder='输入组件名称'
          maxLength={16}
          mode='enter'
          value={name}
          onChange={val => onAliasChange((val || '').trim())}
        />
        {visible ? (
          <EyeOutlined title='隐藏' onClick={onVisible} />
        ) : (
          <EyeInvisibleOutlined title='显示' onClick={onVisible} />
        )}

        {lock ? <LockOutlined title='解锁' onClick={onLock} /> : <UnlockOutlined title='锁定' onClick={onLock} />}
        {/* <Icon pointer name={lock ? '锁定' : '解锁'} size={18} onClick={() => onLockChange(!lock)} /> */}
      </Item>
      <Item title='位置' contentClassName='width-height-input'>
        <div className='flex items-center'>
          <InputNumber
            step={1}
            disabled={lock}
            addonBefore='X'
            value={x}
            onChange={val => update('top', val)}
            precision={0}
          />
          <div className='w-[22px]' />
          <InputNumber
            step={1}
            disabled={lock}
            addonBefore='Y'
            value={y}
            onChange={val => update('left', val)}
            precision={0}
          />
        </div>
        <div className='flex items-center'>
          <InputNumber
            disabled={lock}
            step={1}
            min={1}
            max={20000}
            addonBefore='W'
            precision={0}
            value={width}
            onChange={val => update('width', val)}
          />
          <div className='w-[22px]'>
            {lockRatio ? (
              <LockOutlined title='解锁' onClick={onLockRatio} />
            ) : (
              <UnlockOutlined title='锁定' onClick={onLockRatio} />
            )}
            {/* <Icon
              pointer
              name={lockRatio !== undefined ? '锁定' : '解锁'}
              size={18}
              onClick={onLockRatio}
            /> */}
          </div>
          <InputNumber
            disabled={lock}
            step={1}
            min={1}
            max={20000}
            precision={0}
            addonBefore='H'
            value={height}
            onChange={val => update('height', val)}
          />
        </div>
        <div className='flex items-center'>
          <InputNumber
            step={5}
            disabled={lock}
            addonBefore={<Icon name='旋转' />}
            value={_.round(rotate, 2)}
            precision={2}
            formatter={v => `${v}°`}
            parser={v => v?.replace('°', '') as any}
            onChange={val => update('rotate', val)}
          />
          <div className='w-[22px]' />
          <InputNumber
            step={2}
            disabled={lock}
            min={0}
            addonBefore={<Icon name='圆角' />}
            value={_.round(radius, 2)}
            precision={2}
            formatter={v => `${v}%`}
            parser={v => v?.replace('%', '') as any}
            onChange={val => update('borderRadius', val)}
          />
        </div>
        <div className='flex items-center mt-1.5'>
          <span className='mr-4 text-sm'>定位</span>
          <Radio.Group value={position || 'absolute'} onChange={e => onPositionChange(e.target.value)}>
            <Radio value='absolute'>固定</Radio>
            <Radio value='fixed'>浮动</Radio>
          </Radio.Group>
        </div>
        <div className='flex items-center mt-1.5'>
          <MySlider value={style.opacity} onChange={val => update('opacity', val)} />
        </div>
      </Item>
    </div>
  )
}
