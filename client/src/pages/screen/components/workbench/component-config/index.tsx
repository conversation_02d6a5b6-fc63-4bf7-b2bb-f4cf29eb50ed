import cn from 'classnames'
import React, { FunctionComponentElement, useMemo } from 'react'

import CustomTabs from '@/components/customs/custom-tabs'
import { COM_CONFIG_PANEL_TABS } from '@/consts/screen'
import { AttrConfigType } from '@/pages/screen/components/workbench/component-config/attr-config'
import { StyleConfigType } from '@/pages/screen/components/workbench/component-config/style-config'
import { DataSourceConfigPanelType } from '@/pages/screen/components/workbench/data-config'
import { EventConfigType } from '@/pages/screen/components/workbench/event-config'

export interface ComponentConfigTabsProps {
  activeKey: string
  onActiveChange: (key: string) => any
  tabsMap: {
    style?: FunctionComponentElement<StyleConfigType> | null | false
    event?: FunctionComponentElement<EventConfigType> | null | false
    attr?: FunctionComponentElement<AttrConfigType> | null | false
    datasource?: FunctionComponentElement<DataSourceConfigPanelType> | null | false
    columnPicker?: FunctionComponentElement<DataSourceConfigPanelType> | null | false
  }
  renderTitle?: (item: any) => string | JSX.Element
  isLock?: boolean
  contentMaxHeight?: string
}

/**
 * 组件配置管理
 */
export default function ComponentConfigTabs(props: ComponentConfigTabsProps) {
  const { tabsMap, renderTitle, isLock, contentMaxHeight, onActiveChange } = props

  // 将空的隐藏掉
  const options = useMemo(() => {
    const _opts = new Set<string>([])
    Object.keys(tabsMap).forEach(key => {
      if (!tabsMap[key]) _opts.add(key)
    })
    return COM_CONFIG_PANEL_TABS.filter(i => !_opts.has(i.key))
  }, [tabsMap])

  const activeKey = useMemo(() => {
    const item = options.find(i => i.key === props.activeKey)
    if (!item) return options[0]?.key || 'style'
    return props.activeKey
  }, [props.activeKey, options])

  return (
    <CustomTabs
      activeKey={activeKey}
      onChange={onActiveChange}
      className={cn({
        'fix-tabs-style': true,
        'tabs-is-lock': isLock
      })}
      renderTitle={renderTitle}
      contentMaxHeight={contentMaxHeight}
      options={options}
      tabsMap={tabsMap as any}
    />
  )
}
