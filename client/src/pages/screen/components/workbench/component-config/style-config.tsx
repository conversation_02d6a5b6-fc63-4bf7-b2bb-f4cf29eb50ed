import './style-config.less'

import { useMemoizedFn } from 'ahooks'
import isEqual from 'fast-deep-equal'
import _ from 'lodash'
import React, { memo, useMemo } from 'react'

import { FormSchema } from '@/components/form-schema'
import { ELEMENT_OMIT_FIELD } from '@/consts/screen'
import { store } from '@/stores'

import BaseConfig, { BaseConfigProps } from './base-config'

const FormSchemaMemo = memo(FormSchema, isEqual)
const BaseConfigMemo = memo(BaseConfig, isEqual)

export interface StyleConfigProps extends BaseConfigProps {
  title: string
  alias?: string
  config: Record<string, any>
  styleSchema?: Record<string, any>
  baseSchema?: Record<string, any>
  onConfigChange: (config: any) => any
  componentKey?: string
}

/**
 * 组件的样式配置面板
 * @param props
 */
export default function StyleConfig(props: StyleConfigProps) {
  const { alias, title, config = {}, style = {}, styleSchema = {}, baseSchema = {}, componentKey } = props
  const { onAliasChange, onLockChange, onStyleChange, onConfigChange, onVisibleChange } = props
  const { onLockRatioChange, onPositionChange } = props

  const onFormSchemaChange = useMemoizedFn(val => onStyleChange?.(val))
  const onFormSchemaChangeConfig = useMemoizedFn(val => onConfigChange(val))

  // 获取组件的数据传给
  const getComponentData = () => {
    if (componentKey) {
      const editorCore = store.getState().editorCore
      const component = editorCore.components.entities[componentKey]
      const define = editorCore.componentDefine.entities[component?.defineKey]
      const data = editorCore.chartData.entities[componentKey] || define.dataSourceDefine?.demoData
      const fieldMap = component?.dataSource?.indicesTable?.fieldsBinding || _.keyBy(define.dataSourceDefine?.fields, 'name') as any
      return { data, fieldMap }
    }
  }

  const _styleSchema = useMemo(() => {
    if (_.isEmpty(baseSchema)) return styleSchema
    return {
      ...styleSchema,
      beautify: {
        ...styleSchema.beautify,
        unfold: false
      }
    }
  }, [styleSchema, baseSchema])

  return (
    <div className='element-style-config-panel'>
      <BaseConfigMemo
        style={_.pick(style, ELEMENT_OMIT_FIELD)}
        alias={alias}
        title={title}
        lock={config?.lock}
        lockRatio={config?.lockRatio}
        visible={config?.visible}
        position={style.position}
        onAliasChange={onAliasChange}
        onLockChange={onLockChange}
        onStyleChange={onStyleChange}
        onVisibleChange={onVisibleChange}
        onPositionChange={onPositionChange}
        onLockRatioChange={onLockRatioChange}
      />

      {/* {defineType === 'chart' && (
        <FormSchemaMemo
          schema={{
            echartBase: {
              title: '基础设置',
              type: 'echart-base-config',
              defaultValue: {},
              valuePath: 'options'
            }
          }}
          value={config}
          onChange={onFormSchemaChange2}
        />
      )} */}

      {!_.isEmpty(_styleSchema) && (
        <FormSchemaMemo
          className='style-schema-form'
          schema={_styleSchema}
          // 排除其他的位置信息影响重新渲染
          value={_.omit(style, ELEMENT_OMIT_FIELD)}
          onChange={onFormSchemaChange}
        // className='fix-border-bottom'
        />
      )}

      {!_.isEmpty(baseSchema) && (
        <FormSchemaMemo
          className='base-schema-form'
          schema={baseSchema}
          // 排除其他的位置信息影响重新渲染
          value={config}
          onChange={onFormSchemaChangeConfig}
          getComponentData={getComponentData}
        />
      )}

      <div className='h-[20px]' />
    </div>
  )
}

export type StyleConfigType = typeof StyleConfig
