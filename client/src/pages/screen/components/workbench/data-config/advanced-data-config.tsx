import { PlusOutlined } from '@ant-design/icons'
import { useReactive } from 'ahooks'
import { Button, Form, ModalProps, Radio, Select, Space, Tooltip } from 'antd'
import classNames from 'classnames'
import _ from 'lodash'
import React, { useEffect, useMemo } from 'react'

import Modal from '@/components/customs/custom-modal'
import Icon from '@/components/icons/iconfont-icon'
import { GRANULARITY_OPTIONS, GRANULARITY_OPTIONS_FOR_DB, INDEX_TIME_PERIOD } from '@/consts/data-source'
import type {
  DataLoaderConfig,
  DataSourceConfig,
  GRANULARITY_OPTIONS_ID,
  OrderConfig
} from '@/types/editor-core/data-source'
import { DataSourceQueryConfig } from '@/types/editor-core/data-source'
import { switchToGroupByMode, switchToSelectMode } from '@/utils/query'

const Option = Select.Option

interface CustomSortOrderModalProps extends ModalProps {
  type: DataSourceConfig['dataSourceType']
  timeBucket?: string
  queryMode?: DataSourceQueryConfig['queryMode']
  initOrders: OrderConfig[]
  bindingFields: { label: string; value: string }[]
  onClose: () => any
  value: Partial<DataLoaderConfig>
  onChange: (v: Partial<DataLoaderConfig>) => any
  validTimeBuckets?: string[] | null
}

interface UseCustomSortOrderOpts {
  value: DataLoaderConfig
  onChange: (next: DataLoaderConfig) => any
  validTimeBuckets?: string[] | null
}

interface UseCustomSortOrderState {
  modalVisible: boolean
}

/** 高级数据配置 modal */
function AdvancedDataConfig(props: CustomSortOrderModalProps) {
  const {
    bindingFields, value, onChange, queryMode,
    initOrders, type, timeBucket, onClose, validTimeBuckets
  } = props
  const [form] = Form.useForm()

  useEffect(() => {
    form.setFieldsValue({
      orderBy: initOrders
    })
  }, [initOrders])

  const validTimeBucketsSet = useMemo(() => new Set(validTimeBuckets), [validTimeBuckets])
  return (
    <Modal
      {...props}
      onCancel={onClose}
      bodyStyle={{ padding: 15, height: 400 }}
      centered
      width={450}
      destroyOnClose
      title='高级数据配置'
      onOk={() => form.submit()}
    >
      <div className='font-semibold mb-3'>数据查询模式</div>
      <Radio.Group
        className='mb-3'
        options={[
          { label: '聚合查询', value: 'groupBy', disabled: type === 'dataApi' },
          { label: '源数据查询', value: 'select' }
        ]}
        onChange={e => {
          const nextMode = e.target.value || 'groupBy'
          const next = nextMode === 'groupBy' ? switchToGroupByMode(value) : switchToSelectMode(value)
          onChange(next)
        }}
        value={type === 'dataApi' ? 'select' : (queryMode || 'groupBy')}
        optionType='button'
        buttonStyle='solid'
      />
      {/static|combine|repeater|dataApi/.test(type) ? null : (
        <>

          <div className='font-semibold mb-3'>{type !== 'indicesTable' ? '时间维粒度' : '数据展示粒度'}</div>
          <Radio.Group
            className='!w-full mb-3'
            value={timeBucket}
            size='small'
            // getPopupContainer={() => window.rootElement!}
            // getPopupContainer={triggerNode => triggerNode.parentNode}
            onChange={ev => {
              const next = ev.target.value
              if (type === 'indicesTable') {
                return onChange({
                  ...value,
                  timeBucket: next as GRANULARITY_OPTIONS_ID
                })
              }
              return onChange({ timeBucket: (next as GRANULARITY_OPTIONS_ID) || 'DAY' })
            }}
            disabled={_.some(value?.filters, flt => flt.col === INDEX_TIME_PERIOD)}
            options={_(value.type === 'indicesTable' ? GRANULARITY_OPTIONS : GRANULARITY_OPTIONS_FOR_DB)
              .map(opt => ({
                value: opt.id,
                label: opt.name,
                disabled: validTimeBuckets ? !validTimeBucketsSet.has(opt.id) : false
              })
              )
              .orderBy(o => o.disabled ? 2 : 1)
              .value()}
            optionType='button'
            buttonStyle='solid'
          />
        </>
      )}

      <div className='font-semibold mb-3'>排序规则</div>
      <Form
        form={form}
        onFinish={v => {
          onChange(v || { orderBy: [] })
          onClose()
        }}
        validateTrigger='onSubmit'
      >
        <Form.List name='orderBy'>
          {(fields, { add, remove }) => (
            <>
              {fields.map(field => (
                <Space key={field.key} align='baseline'>
                  <Form.Item noStyle shouldUpdate>
                    {() => {
                      const orderByValue = form.getFieldValue('orderBy')
                      return (
                        <Form.Item
                          {...field}
                          label='字段'
                          name={[field.name, 'field']}
                          rules={[{ required: true, message: '请选择排序字段' }]}
                        >
                          <Select
                            allowClear
                            placeholder='排序字段'
                            style={{ width: 150 }}
                            dropdownMatchSelectWidth={false}
                          >
                            {bindingFields.map(item => (
                              <Option
                                key={item.value}
                                value={item.value}
                                disabled={_.some(orderByValue, or => or?.field === item?.value)}
                              >
                                {item.label}
                              </Option>
                            ))}
                          </Select>
                        </Form.Item>
                      )
                    }}
                  </Form.Item>
                  <Form.Item
                    noStyle
                    shouldUpdate={(prevValues, curValues) =>
                      prevValues.field !== curValues.field || prevValues.dir !== curValues.dir
                    }
                  >
                    {() => (
                      <Form.Item
                        {...field}
                        label='规则'
                        name={[field.name, 'dir']}
                        rules={[{ required: true, message: '请选择排序规则' }]}
                      >
                        <Radio.Group buttonStyle='solid' size='small'>
                          <Radio.Button value='asc'>升序</Radio.Button>
                          <Radio.Button value='desc'>降序</Radio.Button>
                        </Radio.Group>
                      </Form.Item>
                    )}
                  </Form.Item>
                  <Icon name='删除' className='mr-2' onClick={() => remove(field.name)} />
                </Space>
              ))}
              <Form.Item>
                <Button
                  disabled={_.size(form.getFieldValue('orderBy')) >= bindingFields.length}
                  type='dashed'
                  onClick={() => add({ field: undefined, dir: 'desc' })}
                  block
                  icon={<PlusOutlined />}
                >
                  新增
                </Button>
              </Form.Item>
            </>
          )}
        </Form.List>
      </Form>
    </Modal>
  )
}

/** 高级数据配置 */
export default function useAdvancedDataConfig(opts: UseCustomSortOrderOpts) {
  const { onChange, value, validTimeBuckets } = opts
  const state = useReactive<UseCustomSortOrderState>({
    modalVisible: false
  })

  const bindingFields: { label: string; value: string }[] = useMemo(
    () =>
      _(value.fieldsBinding)
        .keys()
        .map(name => ({
          value: name,
          label: value.fieldsBinding?.[name]?.title || name
        }))
        .value(),
    [value.fieldsBinding]
  )

  const sortAscendingOutlined = (
    <Tooltip title='高级数据配置'>
      <Icon
        name='条件配置'
        className={classNames('mr-1.5', {
          '!text-primary-500 font-bold': !_.isEmpty(value.orderBy),
          '!text-gray-500': _.isEmpty(value.orderBy)
        })}
        onClick={() => (state.modalVisible = true)}
      />
    </Tooltip>
  )

  if (!state.modalVisible) return sortAscendingOutlined

  return (
    <>
      <AdvancedDataConfig
        type={value.type}
        bindingFields={bindingFields}
        queryMode={value.queryMode}
        timeBucket={value?.timeBucket}
        open={state.modalVisible}
        initOrders={value.orderBy || []}
        onClose={() => (state.modalVisible = false)}
        value={value}
        onChange={v => onChange({ ...value, ...v } as DataLoaderConfig)}
        validTimeBuckets={validTimeBuckets}
      />
      {sortAscendingOutlined}
    </>
  )
}
