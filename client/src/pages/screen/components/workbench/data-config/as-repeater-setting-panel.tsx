import { QuestionCircleOutlined } from '@ant-design/icons'
import { message, Switch, Tooltip } from 'antd'
import _ from 'lodash'
import React from 'react'

import { AsRepeaterSettings } from '@/types/editor-core/data-source'


interface RepeaterSettingPanelParams {
  onChange: (val) => any
  value: AsRepeaterSettings | undefined
  disabled?: boolean
  chartData?: any
}

/** 中继器设置面板 */
export function AsRepeaterSettingPanel(props: RepeaterSettingPanelParams) {
  const { onChange, value, chartData, disabled } = props || {}
  return (
    <div className='p-2 border-gray-100 border-solid border-0 border-b data-carousel-panel'>
      <div className='flex items-center'>
        <span className='flex-1'>
          作为数据中继器
          <Tooltip title='组件的数据能被其他组件作为源数据读取，并且保持同步'>
            <QuestionCircleOutlined className='ml-1' />
          </Tooltip>
        </span>
        <Switch
          checkedChildren='开启'
          unCheckedChildren='关闭'
          disabled={disabled}
          checked={value?.enable}
          onChange={val => {
            if (val && _.isEmpty(chartData)) {
              message.error('能查到图表数据的图表，才能作为中继器使用，请先查询一下数据吧')
              return
            }
            // demo 数据, 只需要取 5 条数据，主要是为了拿列名
            const demoData = val ? _.take(chartData, 5) : undefined
            onChange({ ...value, enable: val, demoData } as AsRepeaterSettings)
          }}
        />
      </div>
    </div>
  )
}
