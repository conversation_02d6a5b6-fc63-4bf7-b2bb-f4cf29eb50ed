import './code-editor-for-workbench.less'

import { useReactive } from 'ahooks'
import { Button } from 'antd'
import React, { useEffect, useState } from 'react'

import CodeEditor from '@/components/code-editor'
import Modal from '@/components/customs/custom-modal'

export interface CodeEditorModalProps {
  title: string
  value: string
  onChange: (next: string) => any
  defaultValue?: string
  extra?: React.ReactNode
}

// 加载过图表后，编辑器直接加载不了，需要屏蔽掉 window.define 再加载编辑器，这个变量用于临时存放屏蔽前的值
let originalDefine: Function | null | undefined = null

/** 代码编辑器 */
export function useCodeEditorModal(props: CodeEditorModalProps) {
  const { title, value, onChange, defaultValue, extra} = props
  const reactiveState = useReactive({ visible: false })
  const [pendingCode, setPendingCode] = useState<string>(value || defaultValue || '')
  useEffect(() => {
    setPendingCode(value || defaultValue || '')
  }, [value, defaultValue])

  const handleConfirmSave = () => {
    onChange(pendingCode)
    reactiveState.visible = false
  }

  const handleCodeChange = (data: string) => {
    setPendingCode(data)
  }

  const renderFooter = () => (
    <div className='footer'>
      <div>
        {extra}
      </div>
      <div>
        <Button onClick={() => (reactiveState.visible = false)}>取消</Button>
        <Button type='primary' onClick={handleConfirmSave}>
          确定
        </Button>
      </div>
    </div>
  )

  const modalDom = !reactiveState.visible ? null : (
    <Modal
      title={title}
      open
      width='60vw'
      bodyStyle={{ height: '50vh', padding: '0px' }}
      centered
      maskClosable={false}
      onCancel={() => (reactiveState.visible = false)}
      onOk={handleConfirmSave}
      footer={renderFooter()}
      className='code-editor-for-workbench'
    >
      <CodeEditor
        value={pendingCode}
        onChange={handleCodeChange}
        onMount={() => {
          window.define = originalDefine
          originalDefine = null
        }}
      />
    </Modal>
  )

  return {
    showModal: () => {
      originalDefine = window.define
      window.define = undefined
      reactiveState.visible = true
    },
    modalDom
  }
}
