import { ArrowDownOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons'
import { useReactive } from 'ahooks'
import { Button, Form, Input, PopoverProps } from 'antd'
import { produce } from 'immer'
import _ from 'lodash'
import React, { useState } from 'react'

import DataSourceColumnPicker from '@/components/data-filter-config/data-source-column-picker'
import { DataSourceInfo, DataSourceInfoType } from '@/types/data-source'
import { ChartField } from '@/types/editor-core/component'
import { DataSourceQueryConfig, DrillConfig } from '@/types/editor-core/data-source'

interface FancyDrillDownStepEditorProps {
  queryCfg: DataSourceQueryConfig
  value: DrillConfig
  onChange: (next: DrillConfig) => any
  dataSourcePickerInfo: DataSourceInfo
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id: string) => Promise<any>
}

/** 下钻步骤编辑组件 */
function FancyDrillDownStepEditor(props: FancyDrillDownStepEditorProps) {
  const { value, onChange, queryCfg, dataSourcePickerInfo, loadMoreDataSourceInfo } = props

  return (
    <>
      {[value.baseCol, ...value.steps].map((c, i, arr) => {
        if (c) {
          // 设置完维度后，进入修改名称的模式
          return (
            <React.Fragment key={i}>
              <Input.Group compact>
                <Input className='!w-8' defaultValue={i + 1} readOnly />
                <Input
                  className='!w-[calc(100%_-_2rem)]'
                  value={c.title}
                  onChange={ev => {
                    const val = ev.target.value
                    onChange(
                      produce(value, draft => {
                        if (i === 0) {
                          draft.baseCol.title = val
                        } else {
                          draft.steps[i - 1].title = val
                        }
                      })
                    )
                  }}
                  suffix={
                    i === 0 ? null : (
                      <DeleteOutlined
                        className='text-danger-500 cursor-pointer'
                        onClick={() => {
                          onChange(
                            produce(value, draft => {
                              draft.steps.splice(i - 1, 1)
                            })
                          )
                        }}
                      />
                    )
                  }
                />
              </Input.Group>
              {i < arr.length - 1 && <ArrowDownOutlined className='my-2 mx-auto block text-lg text-[#aeb0e6]' />}
            </React.Fragment>
          )
        }
        // 未设置维度，进入选择维度的模式
        return (
          <React.Fragment key={i}>
            <Input.Group compact>
              <Input className='!w-8' defaultValue={i + 1} readOnly />
              <DataSourceColumnPicker
                treeIcon
                showArrow={false}
                fieldType='string' // 目前只支持指标项目，除列指标列其他都是字符串列
                className='w-[calc(100%-2rem)]'
                dataSourceConfigInfo={queryCfg}
                dataSourcePickerInfo={dataSourcePickerInfo}
                loadMoreDataSourceInfo={loadMoreDataSourceInfo}
                dropdownStyle={{ width: '180px', whiteSpace: 'nowrap' }}
                disabledPredicate={node => node.key === value.baseCol.id}
                disabled={i === 0}
                allowClear
                value={c}
                onChange={val => {
                  const targetCol = _.isArray(val) ? val[0] : val
                  onChange({
                    ...value,
                    steps: _(_.map(value.steps, (col, j) => (i - 1 === j ? targetCol : col)))
                      .compact()
                      .uniqBy('id')
                      .value()
                  })
                }}
              />
            </Input.Group>
            {i < arr.length - 1 && <ArrowDownOutlined className='my-2 mx-auto block text-lg text-[#aeb0e6]' />}
          </React.Fragment>
        )
      })}
      <Button
        icon={<PlusOutlined />}
        className='w-full my-6'
        onClick={() => onChange({ ...value, steps: [...value.steps, null!] })}
        type='ghost'
      >
        添加
      </Button>
    </>
  )
}

interface ColumnDrillDownConfigPanelProps {
  field: ChartField
  value: DataSourceQueryConfig
  onChange: (next: DataSourceQueryConfig) => any
  dataSourcePickerInfo: DataSourceInfo
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id: string) => Promise<any>
  onClose: () => any
}

/** 获得初始状态 */
function getInitialState(value: DataSourceQueryConfig, field: ChartField): DrillConfig {
  const baseCol = value.fieldsBinding![field.name]!
  if (value.drillConfig?.baseFieldName === field.name && value.drillConfig?.baseCol.id === baseCol.id) {
    return { ...value.drillConfig, baseCol } // 总是更新 col.title
  }
  return { baseFieldName: field.name, baseCol, steps: [] }
}

/** 下钻配置面板 */
function ColumnDrillDownConfigPanel(props: ColumnDrillDownConfigPanelProps) {
  const { dataSourcePickerInfo, loadMoreDataSourceInfo, field, value, onChange, onClose } = props
  const [formVal, setFormVal] = useState<DrillConfig>(() => getInitialState(value, field))

  return (
    <div className='w-[19rem]'>
      <Form labelCol={{ span: 6 }} wrapperCol={{ span: 24 }} layout='vertical'>
        <Form.Item
          label='下钻顺序'
          help={
            value.drillConfig?.baseCol && value.drillConfig.baseCol.id !== formVal.baseCol.id ? (
              <span className='text-danger-500'>即将覆盖 {value.drillConfig.baseCol.title} 的下钻配置</span>
            ) : null
          }
        >
          <FancyDrillDownStepEditor
            value={formVal}
            onChange={setFormVal}
            queryCfg={value}
            dataSourcePickerInfo={dataSourcePickerInfo}
            loadMoreDataSourceInfo={loadMoreDataSourceInfo}
          />
        </Form.Item>
        <Form.Item wrapperCol={{ offset: 6, span: 16 }}>
          <Button
            type='primary'
            onClick={() => {
              onChange({ ...value, drillConfig: _.isEmpty(formVal.steps) ? undefined : formVal })
              onClose()
            }}
          >
            提交
          </Button>
          <Button onClick={onClose} className='ml-4'>
            取消
          </Button>
        </Form.Item>
      </Form>
    </div>
  )
}

/** 下钻配置弹窗 hook */
export function useColumnDrillDownConfigPanelForPopover(
  opts: Omit<ColumnDrillDownConfigPanelProps, 'field' | 'onClose'>
) {
  const { value, onChange, dataSourcePickerInfo, loadMoreDataSourceInfo } = opts
  const { fieldsBinding } = value

  const reactiveState = useReactive<{ field: ChartField | null }>({
    field: null
  })
  const fieldName = reactiveState.field?.name
  const columnInfo = fieldName ? fieldsBinding?.[fieldName] : null
  const onShow = (f: ChartField) => (reactiveState.field = f)

  return {
    targetFieldName: reactiveState.field?.name,
    onShow,
    popoverProps: !reactiveState.field
      ? null
      : ({
          placement: 'left',
          trigger: 'click',
          title: `列 ${columnInfo?.title || columnInfo?.name} 下钻配置`,
          content: (
            <ColumnDrillDownConfigPanel
              field={reactiveState.field}
              value={value}
              onChange={onChange}
              dataSourcePickerInfo={dataSourcePickerInfo}
              loadMoreDataSourceInfo={loadMoreDataSourceInfo}
              onClose={() => (reactiveState.field = null)}
            />
          ),
          open: !!reactiveState.field,
          onOpenChange: _.noop
        } as PopoverProps)
  }
}
