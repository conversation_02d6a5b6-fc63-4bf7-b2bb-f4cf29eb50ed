import { SettingOutlined } from '@ant-design/icons'
import { useDebounceEffect, useDeepCompareEffect, useMemoizedFn, useReactive, useRequest } from 'ahooks'
import { Form, Input, InputNumber, PopoverProps, Select, Switch } from 'antd'
import classNames from 'classnames'
import { FormatSpecifier, formatSpecifier } from 'd3-format'
import isEqual from 'fast-deep-equal'
import { produce } from 'immer'
import _ from 'lodash'
import React, { useEffect, useMemo, useState } from 'react'

import { AGG_MODE_TRANSLATE_DICT } from '@/consts/data-source'
import { COMPARE_SPEC_TRANSLATE_DICT } from '@/consts/define'
import { useFormFieldsAdapter } from '@/hooks/use-form-fields-adapter'
import { composeDynamicFieldName } from '@/pages/screen/components/workbench/data-config/utils'
import { IndicesTableService, MutCloud } from '@/services'
import { Chart<PERSON>ield, CompareSpec, ManualCompareSpec } from '@/types/editor-core/component'
import { ColumnInfo, DataLoaderConfig } from '@/types/editor-core/data-source'
import { SqlMappingModel } from '@/types/sql-mapping-model'
import { enableSelectSearch } from '@/utils'
import { parseDynamicFieldName } from '@/utils/query'
import { mutRequest } from '@/utils/request'


interface ColumnExtraConfigPanelProps {
  field: ChartField
  value: DataLoaderConfig
  onChange: (next: DataLoaderConfig) => any
  pendingColumnInfo: ColumnInfo | null | undefined
}

interface ColumnExtraConfigFormState extends Partial<FormatSpecifier> {
  versions?: string[]
  aggMode?: ColumnInfo['aggMode']
  spec?: CompareSpec | ManualCompareSpec
  title: string
  direction: 'none' | 'asc' | 'desc'
  hideField: boolean
}

/** 根据查询配置生成表单状态 */
function getInitFormState(queryConfig: DataLoaderConfig, field: ChartField): ColumnExtraConfigFormState {
  const { orderBy, fieldsBinding, hideFields } = queryConfig
  const { name: fieldName, formatter, type } = field

  const colInf = fieldsBinding?.[fieldName]
  const formatStr: string =
    (_.isString(colInf?.columnFormatter) && colInf?.columnFormatter) || (_.isString(formatter) ? formatter : '')
  const formatInfo = (() => {
    try {
      return formatSpecifier(formatStr)
    } catch (err) {
      console.error(err)
    }
    return {}
  })()
  const myOrderInfo = _.find(orderBy, o => o.field === fieldName)
  const fieldInf = parseDynamicFieldName(fieldName)
  const spec = fieldInf.spec || 'CUR_VALUE'
  return {
    // 同环比取本期值的版本
    versions: spec === 'CUR_VALUE' ? colInf?.versions : fieldsBinding?.[fieldInf.nameWithIdx]?.versions,
    aggMode: type === 'number' ? colInf?.aggMode : undefined,
    spec: spec as CompareSpec | ManualCompareSpec,
    title: colInf?.title || '',
    direction: myOrderInfo ? myOrderInfo.dir : 'none',
    hideField: _.includes(hideFields, fieldName),
    ...formatInfo
  }
}

/** 根据表单状态生成新状态 */
function mergeFormState(
  field: ChartField,
  formState: ColumnExtraConfigFormState,
  queryCfg: DataLoaderConfig,
  pendingColumnInfo: ColumnInfo | null | undefined
) {
  const { direction, comma, precision, trim, type, title, spec, aggMode, versions } = formState
  const nextFormatStr = new FormatSpecifier({
    comma: comma ? ',' : undefined,
    precision: _.isNumber(precision) ? `${precision}` : undefined,
    trim: trim ? '~' : undefined,
    type: type || 'f'
  }).toString()

  const prevDir = getInitFormState(queryCfg, field).direction

  const currFieldName = field.name
  const fieldInfo = parseDynamicFieldName(currFieldName)
  const nextFieldName =
    (fieldInfo.spec || 'CUR_VALUE') !== spec
      ? composeDynamicFieldName(fieldInfo.name, fieldInfo.idx, spec, fieldInfo.isDynamicField)
      : currFieldName
  if (currFieldName !== nextFieldName && queryCfg.fieldsBinding?.[nextFieldName]) {
    // 不该执行到这里，界面上已经做了 disable 的检查
    throw new Error(`${pendingColumnInfo?.title} 的 ${COMPARE_SPEC_TRANSLATE_DICT[spec!]} 口径已经存在`)
  }
  // 本期值的标题
  const curValColTitle =
    spec === 'CUR_VALUE' ? pendingColumnInfo?.title : queryCfg.fieldsBinding?.[fieldInfo.nameWithIdx]?.title

  return produce(queryCfg, draft => {
    if (!draft.fieldsBinding) {
      draft.fieldsBinding = {}
    }
    if (currFieldName !== nextFieldName) {
      // 如果口径变了，先清掉之前的绑定
      delete draft.fieldsBinding[currFieldName]
      draft.hideFields = _.map(draft.hideFields, n => (n === currFieldName ? nextFieldName : n))
      draft.sortedFieldNames = _.map(draft.sortedFieldNames, n => (n === currFieldName ? nextFieldName : n))
      draft.orderBy = _.map(draft.orderBy, o => (o.field === currFieldName ? { ...o, field: nextFieldName } : o))
    }
    // 更新额外配置
    draft.hideFields = _.uniq(
      formState.hideField ? [...(draft.hideFields || []), nextFieldName] : _.without(draft.hideFields, nextFieldName)
    )
    const columnInfo = draft.fieldsBinding?.[nextFieldName]
    if (!columnInfo) {
      if (!pendingColumnInfo) {
        return
      }
      draft.fieldsBinding[nextFieldName] = { ...pendingColumnInfo }
    }
    const draftCol = draft.fieldsBinding[nextFieldName]!
    // 只允许本期值切换版本，因为同环比的版本跟本期值需要保持一致
    draftCol.versions = spec === 'CUR_VALUE' ? versions : undefined
    draftCol.columnFormatter = field.type === 'string' ? undefined : nextFormatStr
    draftCol.title = title
    draftCol.aggMode = aggMode
    if (prevDir !== direction) {
      draft.orderBy = direction === 'none' ? [] : [{ field: nextFieldName, dir: direction }]
    }
  })
}

/** 获取度量值版本列表 */
function useMetricVersions(specCol: ColumnInfo | null | undefined) {
  const { data: versions, loading } = useRequest(
    async () => {
      const [curIndiceId, specId] = specCol?.id.split(':') || []
      const vers = await IndicesTableService.getMeasuresVersionList(curIndiceId)
      return _.filter(vers, v => (v.specId || 'default_spec') === specId)
    },
    {
      ready: !!specCol?.id,
      refreshDeps: [specCol?.id]
    }
  )
  return { versions, loadingVersions: loading }
}

/** 字段额外设置（排序、格式化、...）配置面板 */
function ColumnExtraConfigPanel(props: ColumnExtraConfigPanelProps) {
  const { field, value, onChange, pendingColumnInfo } = props
  const [formVal, setFormVal] = useState<ColumnExtraConfigFormState>(getInitFormState(value, field))
  const { fields, onFieldsChange } = useFormFieldsAdapter(formVal, setFormVal)

  const saveOnUnmount = useMemoizedFn(() => {
    // 确保 unmount 的时候写入状态
    const next = mergeFormState(field, formVal, value, pendingColumnInfo)
    if (!isEqual(next, value)) onChange(next)
  })

  const fieldInfo = useMemo(() => parseDynamicFieldName(field.name), [field])

  useDeepCompareEffect(() => {
    setFormVal(getInitFormState(value, field))
  }, [value, field])

  useDebounceEffect(
    () => {
      const next = mergeFormState(field, formVal, value, pendingColumnInfo)
      if (!isEqual(next, value)) onChange(next)
    },
    [formVal],
    { wait: 500 }
  )

  useEffect(() => {
    const next = mergeFormState(field, formVal, value, pendingColumnInfo)
    if (!isEqual(next, value)) onChange(next)
  }, [])

  useEffect(() => saveOnUnmount, [])
  const colInfo = value.fieldsBinding?.[field.name]
  const {versions, loadingVersions} = useMetricVersions(colInfo)

  if (field.type === 'string') {
    return (
      <div className='w-[19rem]'>
        <Form
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 16 }}
          layout='horizontal'
          fields={fields}
          onFieldsChange={onFieldsChange}
        >
          <Form.Item label='是否隐藏' name='hideField' valuePropName='checked'>
            <Switch checkedChildren='开启' unCheckedChildren='关闭' />
          </Form.Item>
          <Form.Item label='别名' name='title'>
            <Input placeholder='浮点数精度' />
          </Form.Item>
        </Form>
      </div>
    )
  }

  const formatOpts = [
    { label: '整数', value: 'd' },
    { label: '小数', value: 'f' },
    { label: '百分数', value: '%' }
  ]

  return (
    <div className='w-[19rem]'>
      <Form
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 16 }}
        layout='horizontal'
        fields={fields}
        onFieldsChange={onFieldsChange}
        initialValues={{ type: 'd' }}
      >
        {value.type !== 'indicesTable' ? null : (
          <Form.Item
            label='版本'
            name='versions'
            tooltip={(fieldInfo.spec || 'CUR_VALUE') !== 'CUR_VALUE' ? '同环比列跟本期值版本一致' : undefined}
          >
            <Select
              loading={loadingVersions}
              getPopupContainer={triggerNode => triggerNode.parentNode}
              {...enableSelectSearch}
              placeholder='最新版本'
              mode='multiple'
              disabled={(fieldInfo.spec || 'CUR_VALUE') !== 'CUR_VALUE'}
              options={[
                ..._.map(versions, v => ({ label: v.name, value: v.name })),
                { label: '全部版本', value: '*' }
              ]}
              onChange={v => {
                // 如果选择了全部版本，则清除其他选项，只保留 *
                if (_.includes(v, '*')) {
                  setFormVal(prev => ({ ...prev, versions: ['*'] }))
                }
              }}
            />
          </Form.Item>
        )}

        {value.queryMode === 'select' ? null : (
          <Form.Item label='聚合类型' name='aggMode'>
            <Select
              getPopupContainer={triggerNode => triggerNode.parentNode}
              {...enableSelectSearch}
              placeholder='请选择聚合类型'
              options={_.map(
                _.keys(AGG_MODE_TRANSLATE_DICT).filter(
                  colInfo?.dataType !== 'number' ? k => _.startsWith(k, 'count') : k => k !== 'unknown'
                ),
                aggMode => ({ label: AGG_MODE_TRANSLATE_DICT[aggMode], value: aggMode })
              )}
              onChange={v => {
                setFormVal(prev => ({
                  ...prev,
                  title: colInfo?.title?.replace(/（.*）/, `（${AGG_MODE_TRANSLATE_DICT[v] || '未知'}）`) || ''
                }))
              }}
            />
          </Form.Item>
        )}

        <Form.Item label='度量值' name='spec'>
          <Select
            {...enableSelectSearch}
            placeholder='本期值'
            disabled={formVal.spec === 'CUR_VALUE'}
            options={_.map(COMPARE_SPEC_TRANSLATE_DICT, (label, specName) => ({
              label,
              value: specName,
              disabled:
                specName === 'CUR_VALUE' ||
                !!value.fieldsBinding?.[
                composeDynamicFieldName(fieldInfo.name, fieldInfo.idx, specName, fieldInfo.isDynamicField)
                ]
            }))}
            onChange={v => {
              setFormVal(prev => ({
                ...prev,
                title: colInfo?.title?.replace(/_.*$/, `_${COMPARE_SPEC_TRANSLATE_DICT[v] || '未知'}`) || ''
              }))
            }}
          />
        </Form.Item>

        <Form.Item label='是否隐藏' name='hideField' valuePropName='checked'>
          <Switch checkedChildren='开启' unCheckedChildren='关闭' />
        </Form.Item>
        <Form.Item label='别名' name='title'>
          <Input placeholder='浮点数精度' />
        </Form.Item>

        <Form.Item label='格式' name='type'>
          <Select placeholder='数值格式' options={formatOpts} />
        </Form.Item>
        <Form.Item label='精度' name='precision'>
          <InputNumber placeholder='浮点数精度' min={0} step={1} max={9} style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item label='逗号分隔' name='comma' valuePropName='checked'>
          <Switch checkedChildren='开启' unCheckedChildren='关闭' />
        </Form.Item>
        <Form.Item label='省略零' name='trim' valuePropName='checked'>
          <Switch checkedChildren='开启' unCheckedChildren='关闭' />
        </Form.Item>
      </Form>
    </div>
  )
}

type UseColumnExtraConfigPanelForPopoverOpts = ColumnExtraConfigPanelProps

/** 列额外配置面板 hook 封装 */
export function useColumnExtraConfigPanelForPopover(opts: UseColumnExtraConfigPanelForPopoverOpts) {
  const { field, value, onChange, pendingColumnInfo } = opts

  const reactiveState = useReactive({
    showExtraConfigPanel: false
  })
  const { fieldsBinding } = value
  const { name } = field
  const columnInfo = fieldsBinding?.[name] || pendingColumnInfo

  const hasExtraCfg = fieldsBinding?.[name]?.columnFormatter
  const onShow = () => (reactiveState.showExtraConfigPanel = true)
  const sortBtnIcon = (
    <SettingOutlined
      className={classNames('cursor-not-allowed ml-1.5', {
        '!text-gray-300': !hasExtraCfg,
        '!text-black-500': hasExtraCfg
      })}
      title='额外设置'
      onClick={onShow}
    />
  )

  const title = `列 ${columnInfo?.title || columnInfo?.name} 显示配置`

  return {
    sortBtnIcon,
    onShow,
    popoverProps: !reactiveState.showExtraConfigPanel
      ? null
      : ({
        placement: 'left',
        trigger: 'click',
        title: title.length > 20 ? `${title.slice(0, 20)}...` : title,
        content: (
          <ColumnExtraConfigPanel
            field={field}
            value={value}
            onChange={onChange}
            pendingColumnInfo={pendingColumnInfo}
          />
        ),
        open: reactiveState.showExtraConfigPanel,
        onOpenChange: visible => (reactiveState.showExtraConfigPanel = visible)
      } as PopoverProps)
  }
}
