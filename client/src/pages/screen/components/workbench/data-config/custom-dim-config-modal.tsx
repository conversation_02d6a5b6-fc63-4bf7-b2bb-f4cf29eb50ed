import './custom-dim-config-modal.less'

import { FunctionOutlined } from '@ant-design/icons'
import { useReactive } from 'ahooks'
import { Button, Form, FormInstance, Input, message, ModalProps, Radio, Tabs, Tooltip } from 'antd'
import classNames from 'classnames'
import { produce } from 'immer'
import _ from 'lodash'
import { nanoid } from 'nanoid'
import React, { useEffect, useState } from 'react'

import Modal from '@/components/customs/custom-modal'
import { ColumnValuePicker } from '@/components/data-filter-config/column-value-picker'
import DataSourceColumnPicker from '@/components/data-filter-config/data-source-column-picker'
import DebounceInput from '@/components/debounce-input'
import { DruidColumnType } from '@/consts/data-source'
import { useFormFieldsAdapter } from '@/hooks/use-form-fields-adapter'
import type { DataSourceInfo, DataSourceInfoType } from '@/types/data-source'
import type { ColumnInfo, CustomDim, DataSourceQueryConfig } from '@/types/editor-core/data-source'

interface CustomDimConfigFormProps extends ModalProps {
  dataSourcePickerInfo: DataSourceInfo
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id: string) => Promise<any>
  dataSourceConfigInfo: DataSourceQueryConfig

  value: CustomDim
  onChange: (next: CustomDim | ((nextDim: CustomDim) => any)) => any
  form: FormInstance<any>
}

const groupRuleHint = (
  <>
    格式为：
    <br />
    组名1=源维度值1,源维度值2,……
    <br />
    组名2=源维度值3,源维度值4,……
    <br />
    如果值里含有逗号：
    <br />
    {`组名2=${['x,1', 'y,2', 'z,3'].map(v => `"${v}"`).join(',')}`}
  </>
)

/** 自定义分组维度计算表单 */
export function CustomGroupDimConfigForm(props: CustomDimConfigFormProps) {
  const { value, onChange, form, dataSourcePickerInfo, loadMoreDataSourceInfo, dataSourceConfigInfo } = props
  const { fields, onFieldsChange } = useFormFieldsAdapter(value, onChange, 'params')
  const columnInfo = value?.params?.dimension?.id
    ? ({ ..._.pick(value.params.dimension, ['id', 'name', 'title']), type: 'indicesDims' } as ColumnInfo)
    : null
  const reactiveState = useReactive({ pickingVals: [] })
  const onSelectSrcDim = nextVals => {
    const next = _.isArray(nextVals) ? nextVals[0] : nextVals
    onChange(
      produce(value, draft => {
        const params = draft.params
        if (!params) {
          draft.params = { type: 'group' }
        }
        draft.params!.dimension = next
          ? { id: next?.id, name: next.name!, title: next?.title, type: DruidColumnType.String }
          : undefined
      })
    )
  }
  return (
    <Form
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 16 }}
      layout='horizontal'
      form={form}
      fields={fields}
      autoComplete='off'
      onFieldsChange={onFieldsChange}
    >
      <Form.Item label='名称' name='name' required rules={[{ required: true }, { type: 'string', max: 32 }]}>
        <DebounceInput placeholder='请输入' />
      </Form.Item>

      <Form.Item label='别名' name='title' required rules={[{ required: true }, { type: 'string', max: 20 }]}>
        <DebounceInput placeholder='请输入' />
      </Form.Item>

      <Form.Item label='类型' name={['params', 'type']} required rules={[{ required: true }]}>
        <Radio.Group>
          <Radio.Button value='calc'>计算维度</Radio.Button>
          <Radio.Button value='group'>分组维度</Radio.Button>
        </Radio.Group>
      </Form.Item>

      <Form.Item label='源维度' required rules={[{ required: true }]}>
        <DataSourceColumnPicker
          className='w-full'
          fieldType='string' // 目前只支持指标项目，除列指标列其他都是字符串列
          dataSourceConfigInfo={dataSourceConfigInfo}
          dataSourcePickerInfo={dataSourcePickerInfo}
          loadMoreDataSourceInfo={loadMoreDataSourceInfo}
          value={columnInfo}
          onChange={onSelectSrcDim}
        />
      </Form.Item>

      <Form.Item label='未分组组名' name={['params', 'othersGroupName']} required rules={[{ required: true }]}>
        <DebounceInput placeholder='其他/未分组/...' />
      </Form.Item>

      <Form.Item
        label='分组规则'
        name={['params', 'groupRule']}
        required
        rules={[{ required: true }]}
        tooltip={groupRuleHint}
      >
        <DebounceInput Component={Input.TextArea} placeholder='组1=aaa,bbb,ccc' autoSize={{ minRows: 3, maxRows: 6 }} />
      </Form.Item>

      <Form.Item
        label='值选取器'
        help={
          _.isEmpty(reactiveState.pickingVals) ? null : (
            <Button
              type='link'
              onClick={() => {
                onChange(
                  produce(value, draft => {
                    if (!draft.params) {
                      draft.params = { type: 'group', groupRule: '' }
                    }
                    const preAdd = _.map(reactiveState.pickingVals, v => `"${v}"`).join(',')
                    draft.params.groupRule = `${draft.params.groupRule || ''}${preAdd},`
                  })
                )
                reactiveState.pickingVals = []
              }}
            >
              已选取 {_.size(reactiveState.pickingVals)} 个，点此写入
            </Button>
          )
        }
      >
        {!columnInfo ? (
          <span className='text-gray-500'>未选择源维度</span>
        ) : (
          <ColumnValuePicker
            columnInfo={columnInfo}
            dataSourceQueryConfig={dataSourceConfigInfo}
            queryAllVals
            value={reactiveState.pickingVals}
            onChange={vals => (reactiveState.pickingVals = vals)}
          />
        )}
      </Form.Item>
    </Form>
  )
}

/** 自定义计算维度配置表单 */
export function CustomCalcDimConfigForm(props: CustomDimConfigFormProps) {
  const { value, onChange, form, dataSourcePickerInfo, loadMoreDataSourceInfo, dataSourceConfigInfo } = props
  const { fields, onFieldsChange } = useFormFieldsAdapter(value, onChange, 'params')
  const [columnInfo, setColumnInfo] = useState<ColumnInfo | null>(null)

  return (
    <Form
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 16 }}
      layout='horizontal'
      form={form}
      fields={fields}
      autoComplete='off'
      onFieldsChange={onFieldsChange}
    >
      <Form.Item label='名称' name='name' required rules={[{ required: true }, { type: 'string', max: 32 }]}>
        <DebounceInput placeholder='请输入' />
      </Form.Item>

      <Form.Item label='别名' name='title' required rules={[{ required: true }, { type: 'string', max: 20 }]}>
        <DebounceInput placeholder='请输入' />
      </Form.Item>

      <Form.Item label='类型' name={['params', 'type']} required rules={[{ required: true }]}>
        <Radio.Group>
          <Radio.Button value='calc'>计算维度</Radio.Button>
          <Radio.Button value='group'>分组维度</Radio.Button>
        </Radio.Group>
      </Form.Item>

      <Form.Item
        label='公式'
        name='formula'
        required
        rules={[{ required: true }]}
        tooltip={
          <>
            表达式语法请参考：
            <a href='https://plywood.imply.io/expressions' target='_blank' rel='noreferrer'>
              表达式 API 文档
            </a>
          </>
        }
      >
        <DebounceInput
          Component={Input.TextArea}
          autoSize={{ minRows: 3, maxRows: 6 }}
          placeholder='请输入 plywood 公式，如：$dim0.substr(0, 50)'
        />
      </Form.Item>

      <Form.Item label='添加维度'>
        <DataSourceColumnPicker
          className='w-full'
          fieldType='string' // 目前只支持指标项目，除列指标列其他都是字符串列
          dataSourceConfigInfo={dataSourceConfigInfo}
          dataSourcePickerInfo={dataSourcePickerInfo}
          loadMoreDataSourceInfo={loadMoreDataSourceInfo}
          value={columnInfo}
          onChange={nextVals => {
            const next = _.isArray(nextVals) ? nextVals[0] : nextVals
            setColumnInfo(next)
            if (next) {
              onChange({ ...value, formula: `${value.formula || ''}$${next?.name}` })
            }
          }}
        />
      </Form.Item>
    </Form>
  )
}

interface CustomDimConfigModalProps extends ModalProps {
  dataSourcePickerInfo: DataSourceInfo
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id: string) => Promise<any>
  dataSourceConfigInfo: DataSourceQueryConfig

  value: DataSourceQueryConfig['customDims']
  onChange: (next: DataSourceQueryConfig['customDims']) => any
}

const { TabPane } = Tabs

/** 自定义维度配置对话框 */
function CustomDimConfigModal(props: CustomDimConfigModalProps) {
  const { value, onChange, dataSourcePickerInfo, loadMoreDataSourceInfo, dataSourceConfigInfo } = props
  const [pendingDims, setPendingDims] = useState<CustomDim[] | undefined>(() => value)
  const [form] = Form.useForm()
  const reactiveState = useReactive<{ activeTabKey: string | undefined }>({
    activeTabKey: undefined
  })
  useEffect(() => {
    setPendingDims(value)
    reactiveState.activeTabKey = value?.[0]?.id
  }, [value])

  const onEditTab = (targetKey: any, action: 'add' | 'remove') => {
    if (action !== 'add') {
      setPendingDims(_.filter(pendingDims, d => d.id !== targetKey))
      return
    }
    const name = `customDim-${nanoid(5)}`
    const preAdd: CustomDim = {
      id: name, // 为了跟 IndicesDimension 保持一致加的
      name,
      type: DruidColumnType.String,
      title: '未命名维度',
      params: { type: 'calc' }
    }
    setPendingDims([...(pendingDims || []), preAdd])
    if (!reactiveState.activeTabKey) {
      reactiveState.activeTabKey = preAdd.id
    }
  }
  return (
    <Modal
      title='自定义维度'
      width={600}
      onOk={async () => {
        try {
          await form.validateFields()
          onChange(pendingDims)
        } catch (e: any) {
          message.error(e.errorFields[0].errors[0])
        }
      }}
      centered
      maskClosable={false}
      bodyStyle={{ padding: '0px' }}
      className='custom-dim-config-modal'
      {...props}
    >
      <Tabs
        className='h-[62.8vh]'
        type='editable-card'
        tabPosition='left'
        onEdit={onEditTab}
        activeKey={reactiveState.activeTabKey}
        onChange={async key => {
          try {
            await form.validateFields()
            reactiveState.activeTabKey = key
          } catch (e: any) {
            message.error(e.errorFields[0].errors[0])
          }
        }}
      >
        {_.map(pendingDims, pendingDim => {
          const Comp =
            (pendingDim.params?.type || 'calc') === 'calc' ? CustomCalcDimConfigForm : CustomGroupDimConfigForm
          return (
            <TabPane
              tab={<div className='elli max-w-[80px]'>{pendingDim.title || pendingDim.name}</div>}
              key={pendingDim.id}
              closable
              className='pt-6 overflow-y-auto'
            >
              {reactiveState.activeTabKey !== pendingDim.id ? null : (
                <Comp
                  dataSourcePickerInfo={dataSourcePickerInfo}
                  loadMoreDataSourceInfo={loadMoreDataSourceInfo}
                  dataSourceConfigInfo={dataSourceConfigInfo}
                  form={form}
                  value={pendingDim}
                  onChange={next => {
                    if (_.isFunction(next)) {
                      setPendingDims(prev => _.map(prev, p => (p.name === pendingDim.name ? next(p) : p)))
                      return
                    }
                    setPendingDims(prev => _.map(prev, p => (p.name === pendingDim.name ? next : p)))
                  }}
                />
              )}
            </TabPane>
          )
        })}
      </Tabs>
    </Modal>
  )
}

interface UseCustomDimConfigModalOpts {
  dataSourcePickerInfo: DataSourceInfo
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id: string) => Promise<any>
  dataSourceConfigInfo: DataSourceQueryConfig

  onDsCfgChange: (next: DataSourceQueryConfig) => any
}

const dsTypeSetCanUseCustomDims = new Set(['indicesTable', 'dataCenter', 'dataset'])

/** 使用自定义维度配置对话框 hook */
export function useCustomDimConfigModal(opts: UseCustomDimConfigModalOpts) {
  const { onDsCfgChange: onChange, dataSourcePickerInfo, loadMoreDataSourceInfo, dataSourceConfigInfo } = opts

  const reactiveState = useReactive({
    modalVisible: false
  })
  if (!dsTypeSetCanUseCustomDims.has(dataSourceConfigInfo?.type)) {
    return null
  }
  return (
    <>
      {!reactiveState.modalVisible ? null : (
        <CustomDimConfigModal
          dataSourcePickerInfo={dataSourcePickerInfo}
          loadMoreDataSourceInfo={loadMoreDataSourceInfo}
          dataSourceConfigInfo={dataSourceConfigInfo}
          value={dataSourceConfigInfo.customDims}
          onChange={next => {
            onChange({ ...dataSourceConfigInfo, customDims: next })
            reactiveState.modalVisible = false
          }}
          open
          onCancel={() => (reactiveState.modalVisible = false)}
        />
      )}

      <Tooltip title='自定义维度'>
        <FunctionOutlined
          className={classNames({
            '!text-primary-500 font-bold': !_.isEmpty(dataSourceConfigInfo.customDims),
            '!text-gray-500': _.isEmpty(dataSourceConfigInfo.customDims)
          })}
          onClick={() => (reactiveState.modalVisible = true)}
          title='自定义维度'
        />
      </Tooltip>
    </>
  )
}
