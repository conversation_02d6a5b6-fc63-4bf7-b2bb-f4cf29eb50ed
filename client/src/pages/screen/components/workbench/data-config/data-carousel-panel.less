.data-carousel-panel {
  .group-addon {
    border: 1px solid var(--border-color-base);
    border-radius: var(--border-radius-base) 0 0 var(--border-radius-base);
    padding: 0 11px;
    border-right: none;
    background-color: var(--tint-color-92);
    border-color: var(--tint-color-40);
    border-radius: 20px 0 0 20px;
    height: 32px;
    line-height: 32px;

    + .group-addon-select {
      width: calc(100% - 95px);
      display: block;

      > .ant-select-selector {
        border-radius: 0 20px 20px 0 !important;
        // border-radius: 0 var(--border-radius-base) var(--border-radius-base) 0;
        border-color: var(--tint-color-40);
      }
    }
  }
}
