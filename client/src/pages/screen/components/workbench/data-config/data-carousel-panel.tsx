import './data-carousel-panel.less'

import { QuestionCircleOutlined } from '@ant-design/icons'
import { useDeepCompareEffect } from 'ahooks'
import { InputNumber, Select, Switch, Tooltip } from 'antd'
import _ from 'lodash'
import React, { useMemo } from 'react'

export interface DataCarouselPanelProps {
  dimensions?: any[]
  value?: Partial<{
    dimName: string
    enable: boolean
    interval: number
    limit?: number // 分页数量
  }>
  onChange?: (val: DataCarouselPanelProps['value']) => any
}

/**
 * 数据分页播放
 */
export default function DataCarouselPanel(props: DataCarouselPanelProps) {
  const { value = {}, onChange, dimensions } = props
  const options = useMemo(() => dimensions?.map(i => ({ ...i, label: i.title, value: i.key })), [dimensions])

  const defaultValue = {
    dimName: options?.[0]?.value || undefined,
    interval: 5,
    limit: 3
  }

  const setValueField = (key: string, val: any) => {
    const newVal = _.merge(defaultValue, value, { [key]: val })
    onChange?.(newVal)
  }

  // option 改变时重置默认选择
  useDeepCompareEffect(() => {
    if (!options?.find(i => i.value === value.dimName)) {
      setValueField('dimName', options?.[0]?.value || '')
    }
  }, [options])

  return (
    <div className='p-2 border-gray-100 border-solid border-0 border-b data-carousel-panel'>
      <div className='flex items-center'>
        <span className='flex-1'>
          数据分页播放
          <Tooltip title='按照选定维度，对查询后的数据进行分页，在设定时间间隔进行轮播'>
            <QuestionCircleOutlined className='ml-1' />
          </Tooltip>
        </span>
        <Switch
          checkedChildren='开启'
          unCheckedChildren='关闭'
          checked={value.enable}
          onChange={val => setValueField('enable', val)}
        />
      </div>

      <div className='box-panel mt-3 mb-2' style={{ display: value.enable ? 'block' : 'none' }}>
        <div className='mb-3 flex items-center'>
          <span className='group-addon'>分页维度</span>
          <Select
            getPopupContainer={triggerNode => triggerNode.parentNode}
            className='group-addon-select'
            placeholder='请选择'
            defaultValue={defaultValue.dimName}
            options={options}
            value={!!value?.dimName ? value?.dimName : undefined}
            onChange={val => setValueField('dimName', val)}
          />
        </div>

        <div className='mb-3'>
          <InputNumber
            min={1}
            max={1000}
            step={1}
            placeholder='请输入'
            defaultValue={defaultValue.limit}
            addonBefore='分页大小'
            addonAfter='项'
            value={value?.limit}
            onChange={val => setValueField('limit', val)}
          />
        </div>
        <div className=''>
          <InputNumber
            max={3600 * 24}
            step={0.1}
            placeholder='请输入'
            defaultValue={defaultValue.interval}
            addonBefore='变化间隔'
            addonAfter='秒'
            value={value?.interval}
            onChange={val => setValueField('interval', val)}
          />
        </div>
      </div>
    </div>
  )
}
