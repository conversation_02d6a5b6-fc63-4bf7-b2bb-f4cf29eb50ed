import { ArrowDownOutlined, CloseOutlined, DeleteOutlined, PlusOutlined, SettingOutlined } from '@ant-design/icons'
import { useCreation, useDebounceFn, useDeepCompareEffect, useReactive, useSessionStorageState } from 'ahooks'
import { Dropdown, Input, Tooltip, TreeSelect } from 'antd'
import classNames from 'classnames'
import { csvFormat } from 'd3-dsv'
import { produce } from 'immer'
import _ from 'lodash'
import { nanoid } from 'nanoid'
import React, { useEffect, useMemo } from 'react'

import { CustomPopover } from '@/components/customs/custom-popover'
import HighlightText from '@/components/highlight'
import { COMBINE_MODE_TRANSLATE_DICT } from '@/pages/screen/components/workbench/data-config/const'
import DataSourceConfigPanel from '@/pages/screen/components/workbench/data-config/index'
import {
  DataSourcePickerTreeNode,
  toSimpleDataSourcePickerTree
} from '@/pages/screen/components/workbench/data-config/utils'
import { DataSourceInfo, DataSourceInfoType } from '@/types/data-source'
import { ChartField } from '@/types/editor-core/component'
import {
  ColumnInfo,
  CombineQuery,
  DataLoaderConfig,
  DataSourceConfig,
  DataSourceQueryConfig
} from '@/types/editor-core/data-source'
import smartSearch from '@/utils'

interface DataSourcePickerProps {
  dataSourcePickerInfo: DataSourceInfo
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id?: string, other?: any) => Promise<any>
  value: DataSourceConfig
  onChange: (next: DataSourceConfig) => any
  defaultStaticData?: any
  onSelected?: (id?: string, node?: any) => any
  // 刷新数据，主要用于子查询
  onRefreshData?: (query?: DataSourceConfig) => Promise<any>
  // 图表字段，透传给子查询
  chartFields?: ChartField[] | undefined | null
  // 是否记录最后选择的数据源
  recordLastDataLoadConfig?: boolean
  // 是否只允许选择数据源，不允许创建为合并查询
  selectOnly?: boolean
  title?: string
}

function getNameOfDsCfgInCombineCfg(currDsCfg: DataSourceConfig) {
  if (currDsCfg.dataSourceType === 'repeater') return '数据中继器'
  if (currDsCfg.dataSourceType === 'static') return 'csv'
  if (currDsCfg.dataSourceType === 'api') return 'api'
  if (currDsCfg.dataSourceType === 'combine') return '组合查询'
  const query = currDsCfg[currDsCfg.dataSourceType]
  return query?.tableName || '未命名'
}

/** 往组合查询中添加一个查询 */
const appendQueryInCombine = (currDsCfg: DataSourceConfig): DataSourceConfig => {
  const newCombineCfgId = nanoid(5)
  if (currDsCfg.dataSourceType !== 'combine') {
    const wrap: DataSourceConfig = {
      dataSourceType: 'combine',
      combine: {
        type: 'combine',
        tableId: 'combine',
        combineOrders: [newCombineCfgId],
        combineCfgDict: {
          [newCombineCfgId]: {
            key: newCombineCfgId,
            name: getNameOfDsCfgInCombineCfg(currDsCfg),
            query: currDsCfg
          }
        },
        queryMode: 'select',
        // 复制字段映射，避免用户需要手动重新映射
        fieldsBinding: _.mapValues(
          currDsCfg[currDsCfg.dataSourceType]?.fieldsBinding,
          (colInfo, field): ColumnInfo | null =>
            colInfo && { ...colInfo, id: field, name: field, type: 'field' }
        )
      }
    }
    return appendQueryInCombine(wrap)
  }
  return produce(currDsCfg, combineDraft => {
    const q = combineDraft.combine!
    const lastCombineCfgId = q.combineOrders[q.combineOrders.length - 1]
    q.combineOrders.push(newCombineCfgId)
    q.combineCfgDict[newCombineCfgId] = {
      key: newCombineCfgId,
      name: `数据源${q!.combineOrders.length}`,
      // 默认先复制前一个查询
      query: q.combineCfgDict[lastCombineCfgId].query,
      combineMode: 'concatByName'
    } as CombineQuery
  })
}

/** 根据 tableId 创建新的查询配置 */
const tableIdToNewDsCfg = (
  { tableId, currDsCfg, dataSourcePickerInfo, defaultStaticData, selectedTreeItem }: {
    tableId: string,
    currDsCfg: DataSourceConfig,
    dataSourcePickerInfo: DataSourceInfo,
    defaultStaticData: any,
    selectedTreeItem: Omit<DataSourcePickerTreeNode, 'title'> & { title: JSX.Element, label: string }
  }
): DataSourceConfig => {
  if (!tableId) {
    return { dataSourceType: 'dataCenter' }
  }

  if (tableId === 'combine') {
    return appendQueryInCombine(currDsCfg)
  }

  const datasetInfo = dataSourcePickerInfo.datasetMap.entities[tableId]
  if (datasetInfo) {
    return {
      dataSourceType: 'dataset',
      dataset: {
        type: 'dataset',
        dbId: datasetInfo?.dbId,
        datasetId: tableId,
        datasetName: datasetInfo?.title,
        tableId,
        tableName: datasetInfo?.title,
        dbType: datasetInfo?.dbType,
        groupId: datasetInfo?.groupId,
        sqlVariable: datasetInfo?.sqlVariable
      } as DataSourceQueryConfig
    }
  }

  if (_.startsWith(tableId, 'static_')) {
    return {
      dataSourceType: 'static',
      static: {
        type: 'static',
        tableId: 'static_csv',
        tableName: '静态数据',
        data: csvFormat(defaultStaticData || [])
      }
    }
  }

  const dataApi = dataSourcePickerInfo.dataApiMap.entities[tableId]
  if (dataApi) {
    // console.log(dataApi)
    return {
      dataSourceType: 'dataApi',
      dataApi: {
        type: 'dataApi',
        tableId,
        tableName: 'DATA_API',
        apiUrl: `/app/abi/data-api/v1/${dataApi.sign}`,
        apiParams: {}
      }
    }
  }

  const api = dataSourcePickerInfo.apiMap.entities[tableId]
  if (api) {
    // TODO: 这里需要异步查询字段信息
    return {
      dataSourceType: 'api',
      api: {
        type: 'api',
        tableId,
        tableName: 'API',
        fields: api.fields,
        request: api.request
      }
    }
  }

  // 中继器，将目标组件的图表数据，作为数据源
  if (_.includes(tableId, '@')) {
    const [screenId, compKey] = tableId.split('/')
    const { dataSource } = dataSourcePickerInfo.repeatersMap.entities[compKey]
    const query = dataSource[dataSource.dataSourceType]
    const demoData = dataSource.asRepeater?.demoData || [] // js对象数组，只有一行
    return {
      dataSourceType: 'repeater',
      repeater: {
        type: 'repeater',
        tableId,
        tableName: `数据中继器：${selectedTreeItem.title}`,
        fieldsBinding: query?.fieldsBinding,
        sortedFieldNames: query?.sortedFieldNames,
        hideFields: query?.hideFields,
        data: csvFormat(demoData)
      }
    }
  }

  if (!Number.isFinite(+tableId)) {
    const indicesTable = dataSourcePickerInfo.indicesTableMap.entities[tableId]
    // 指标系统大宽表
    const nextIndicesQueryCfg: DataSourceQueryConfig = {
      ...(currDsCfg?.indicesTable || {}),
      type: 'indicesTable',
      tableId: tableId as string, // 这个是 datasource_id
      tableName: indicesTable?.datasource_name,
      timeBucket: 'MONTH'
    }
    return {
      dataSourceType: 'indicesTable',
      indicesTable: _.pick(nextIndicesQueryCfg, [
        'type', 'tableId', 'tableName', 'timeBucket', 'queryMode'
      ])
    }
  }

  // 数据开发中心的表
  const table = dataSourcePickerInfo.tableMap.entities[tableId]
  const db = dataSourcePickerInfo.databaseMap.entities[table.dbId]
  const conn = dataSourcePickerInfo.connectionMap.entities[db.connectId]
  const dbTypeDict = {
    MYSQL: 'mysql',
    ORACLE: 'oracle',
    SQLSERVER: 'mssql',
    POSTGRESQL: 'postgres',
    TINDEX: 'tindex'
  }
  const nextDbQueryCfg: DataSourceQueryConfig = {
    ...(currDsCfg?.dataCenter || {}),
    type: 'dataCenter',
    dbType: dbTypeDict[conn.type],
    connId: db.connectId,
    connName: conn.name,
    dbId: db.id,
    dbName: db.dbName,
    tableId: +tableId,
    tableName: table.tableName
  }
  return {
    dataSourceType: 'dataCenter',
    dataCenter: _.pick(nextDbQueryCfg, [
      'type',
      'dbType',
      'connId',
      'connName',
      'dbId',
      'dbName',
      'tableId',
      'tableName',
      'queryMode'
    ])
  }
}

/** 数据源选择器 */
export function DataSourcePicker(props: DataSourcePickerProps) {
  const { dataSourcePickerInfo, loadMoreDataSourceInfo, value, selectOnly, title = '数据源' } = props
  const { onChange, defaultStaticData, onSelected, recordLastDataLoadConfig = true } = props

  // 初始化的时候加载上次的图表的数据源配置
  const [lastDataLoadCfg, onLastQueryCfgChange] = useSessionStorageState<Partial<DataLoaderConfig>>(
    'last-data-source',
    { defaultValue: () => ({}) }
  )


  useEffect(() => {
    if (!recordLastDataLoadConfig) return
    const selectedTableId = value[value.dataSourceType]?.tableId
    if (selectedTableId || !lastDataLoadCfg?.tableId) {
      return
    }
    const dataSourceType = lastDataLoadCfg.type!
    onChange({
      ...value,
      dataSourceType,
      [dataSourceType]: { ...value[dataSourceType], ...lastDataLoadCfg }
    })
  }, [])

  const onChangeWithMemo = useMemo(
    () => (next: DataSourceConfig) => {
      onChange(next)
      const q = next[next.dataSourceType]
      if (q && next.dataSourceType !== 'combine') {
        onLastQueryCfgChange(q)
      }
    },
    [onLastQueryCfgChange, onChange]
  )

  const reactiveState = useReactive({ search: '' })
  const keyword = reactiveState.search
  const dataSourcePickerSimpleTree = useCreation(() =>
    _(toSimpleDataSourcePickerTree(dataSourcePickerInfo))
      .orderBy(n => (keyword && smartSearch(keyword, n.title as string) ? 0 : 1), 'asc')
      .map(n => {
        const dom = _.isString(n.title)
          ? (
            <HighlightText className='ml-1.5' text={n.title} highlight={keyword || ''} />
          )
          : n.title
        return {
          ...n,
          label: n.title,
          // 为了点击的时候也触发加载下级，带上额外信息
          title: !n.loadType
            ? dom
            : (
              <span data-id={n.id} data-load-type={n.loadType || ''}>{dom}</span>
            )
        }
      })
      .value(),
    [dataSourcePickerInfo, keyword]
  )

  // 搜索时展开第一级节点，不搜索时如果已选了表的话，展开到表级
  const queryCfg: any = value[value.dataSourceType]
  const currConn = queryCfg?.connId ? dataSourcePickerInfo.connectionMap.entities[queryCfg.connId] : null

  const treeDefaultExpandedKeys = useCreation(() => {
    if (reactiveState.search) {
      return dataSourcePickerSimpleTree.filter(n => !n.pId).map(i => i.value)
    }
    return value.dataSourceType === 'dataCenter'
      ? (currConn && [currConn.type, queryCfg!.dbId!, queryCfg!.tableId!]) || []
      : ['indicesTable']
  }, [reactiveState.search, dataSourcePickerSimpleTree, queryCfg, currConn])

  useEffect(() => {
    // 初始化时即加载数据源
    loadMoreDataSourceInfo('connection')
  }, [])


  // 查询已选表名，全部层级都加载，避免下级展示在顶层
  const asyncInitDataSource = async () => {
    // combine 类型的子查询，chartFields 都是一样的，但是最终汇总后的数据，当作静态数据处理
    if (value.dataSourceType === 'dataCenter') {
      loadMoreDataSourceInfo('dataBase', `${queryCfg!.connId}`)
      loadMoreDataSourceInfo('table', `${queryCfg!.dbId}`, { connectId: queryCfg?.connId, dbId: queryCfg?.dbId })
      loadMoreDataSourceInfo('field', `${queryCfg!.tableId}`, { connectId: queryCfg?.connId, dbId: queryCfg?.dbId })
    } else if (value.dataSourceType === 'dataset') {
      loadMoreDataSourceInfo('datasetGroup')
      loadMoreDataSourceInfo('dataset', queryCfg!.groupId)
      loadMoreDataSourceInfo('datasetDetail', queryCfg!.datasetId, { groupId: queryCfg?.groupId })
    } else if (value.dataSourceType === 'api') {
      // ... 加载 api 列表
      loadMoreDataSourceInfo('api')
    } else if (value.dataSourceType === 'dataApi') {
      loadMoreDataSourceInfo('dataApi', queryCfg?.tableId)
    } else if (value.dataSourceType === 'indicesTable') {
      loadMoreDataSourceInfo('indicesTable')
      loadMoreDataSourceInfo('indicesDims', `${queryCfg!.tableId}`)
    } else if (value.dataSourceType === 'repeater') {
      loadMoreDataSourceInfo('repeaters')
    }
  }

  useDeepCompareEffect(() => {
    if (!queryCfg?.tableId) return
    asyncInitDataSource()
  }, [queryCfg])

  const { run: loadMoreDataSourceInfoDebounced } = useDebounceFn(loadMoreDataSourceInfo, { leading: true })
  const onSearch = val => {
    reactiveState.search = val
    if (!val) {
      return
    }
    // 搜索时加载第一层数据
    if (_.isEmpty(dataSourcePickerInfo.connectionMap.keys)) {
      loadMoreDataSourceInfoDebounced('connection')
    }
    if (_.isEmpty(dataSourcePickerInfo.indicesTableMap.keys)) {
      loadMoreDataSourceInfoDebounced('indicesTable')
    }
    if (_.isEmpty(dataSourcePickerInfo.datasetGroupMap.keys)) {
      loadMoreDataSourceInfoDebounced('datasetGroup')
    }
    if (_.isEmpty(dataSourcePickerInfo.repeatersMap.keys)) {
      loadMoreDataSourceInfoDebounced('repeaters')
    }
    if (_.isEmpty(dataSourcePickerInfo.dataApiMap.keys)) {
      loadMoreDataSourceInfoDebounced('dataApi')
    }
  }

  const selectedTableId = useMemo(() => {
    if (!_.some(dataSourcePickerSimpleTree, n => n.value === queryCfg?.tableId)) {
      return queryCfg?.tableName // 修复 id 闪烁一下的情况
    }
    return queryCfg?.type === 'dataCenter' ? queryCfg?.tableId && `table:${queryCfg.tableId}` : queryCfg?.tableId
  }, [queryCfg?.type, queryCfg?.tableId, queryCfg?.tableName, dataSourcePickerSimpleTree])

  return (
    <div className='data-source-select p-2 border-gray-100 border-solid border-0 border-b'>
      <div className='data-source-select-title mb-3'>
        <span>{title}</span>
        {selectOnly ? null : (
          <span className='float-right'>
            <Tooltip title='添加数据源'>
              <PlusOutlined className='cursor-pointer' onClick={() => onChange(appendQueryInCombine(value))} />
            </Tooltip>
          </span>
        )}
      </div>
      <TreeSelect
        treeData={dataSourcePickerSimpleTree}
        treeDataSimpleMode
        treeIcon
        showSearch
        onSearch={onSearch}
        treeNodeFilterProp='label'
        className='w-full'
        dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
        placeholder='搜索指标库名/数据源连接名...'
        allowClear
        treeDefaultExpandedKeys={treeDefaultExpandedKeys}
        loadData={({ id, loadType }) => loadMoreDataSourceInfo(loadType, _.includes(id, ':') ? _.last(id.split(':')) : id)}
        autoClearSearchValue={false}
        value={selectedTableId}
        onChange={(tableIdWithPrefix: string) => {
          const selectedTreeItem = dataSourcePickerSimpleTree.find(i => i.value === tableIdWithPrefix)!
          const tableId = _.includes(tableIdWithPrefix, ':') ? _.last(tableIdWithPrefix.split(':'))! : tableIdWithPrefix
          const dsCfg = tableIdToNewDsCfg({
            tableId,
            currDsCfg: value,
            dataSourcePickerInfo,
            defaultStaticData,
            selectedTreeItem
          })
          onChangeWithMemo({ carousel: value.carousel, ...dsCfg })
          const node = dataSourcePickerSimpleTree.find(i => i.id === tableIdWithPrefix)
          onSelected?.(tableId, node)
        }}
        onClick={(ev: React.MouseEvent<HTMLSpanElement>) => {
          ev.stopPropagation()
          // 搜索时没有出现展开按钮，点击则展开
          const parent = (ev.target as HTMLSpanElement).parentElement!
          const span = (parent.querySelector('[data-id]') || parent) as HTMLSpanElement
          const id = span.getAttribute('data-id')
          const loadType = span.getAttribute('data-load-type')
          if (id && loadType) {
            const idWithoutPrefix = _.includes(id, ':') ? _.last(id.split(':'))! : id
            loadMoreDataSourceInfo(loadType as DataSourceInfoType, idWithoutPrefix)
          }
        }}
      />
    </div>
  )
}

/** 组合查询配置面板 */
export function CombineDataSourcePicker(props: DataSourcePickerProps) {
  const { dataSourcePickerInfo, loadMoreDataSourceInfo, value, onChange, defaultStaticData } = props
  const { chartFields, onRefreshData } = props

  const reState = useReactive({ editingSubQueryKey: '' })
  const editingSubQueryKey = reState.editingSubQueryKey

  if (value.dataSourceType !== 'combine') {
    return <DataSourcePicker {...props} />
  }

  const query = value.combine || { type: 'combine', tableId: 'combine', combineOrders: [], combineCfgDict: {} }
  const subQuery = query.combineCfgDict[editingSubQueryKey]
  const popOverTitle = subQuery && (
    <div className='relative py-1'>
      编辑子查询-{subQuery.name}
      <CloseOutlined
        className='absolute right-0 !text-gray-500 !leading-6 cursor-pointer'
        onClick={() => (reState.editingSubQueryKey = '')}
      />
    </div>
  )

  const popOverContent = subQuery && (
    <DataSourceConfigPanel
      className='w-[300px]'
      dataSourcePickerInfo={dataSourcePickerInfo}
      loadMoreDataSourceInfo={loadMoreDataSourceInfo}
      value={subQuery.query}
      onChange={next => {
        onChange(
          produce(value, combineDraft => {
            combineDraft.combine!.combineCfgDict[editingSubQueryKey].query = next
          })
        )
      }}
      chartFields={chartFields}
      onRefreshData={onRefreshData}
    />
  )

  return (
    <div className='p-2 border-gray-100 border-solid border-0 border-b'>
      <CustomPopover
        placement='left'
        open={!!editingSubQueryKey}
        title={popOverTitle}
        content={popOverContent}
        overlayClassName='[&_.ant-popover-inner-content]:p-0 !z-[900]'
      >
        <div className='mb-3'>
          <span>数据源</span>
          <span className='float-right'>
            <Tooltip title='添加数据源'>
              <PlusOutlined className='cursor-pointer' onClick={() => onChange(appendQueryInCombine(value))} />
            </Tooltip>
          </span>
        </div>
      </CustomPopover>
      {_.map(query.combineOrders, (key, i) => {
        const cfg = query.combineCfgDict[key]
        return (
          <React.Fragment key={key}>
            {i > 0 && (
              <div className='text-center py-2'>
                <ArrowDownOutlined className='text-lg text-[#aeb0e6] mr-2 align-top' />
                <Dropdown
                  menu={{
                    items: _.map(COMBINE_MODE_TRANSLATE_DICT, (v, k) => ({ key: k, label: v })),
                    onClick: ({ key: nextMode }) => {
                      onChange(
                        produce(value, combineDraft => {
                          const q = combineDraft.combine
                          if (q) q.combineCfgDict[key].combineMode = nextMode as CombineQuery['combineMode']
                        })
                      )
                    }
                  }}
                >
                  <span>{COMBINE_MODE_TRANSLATE_DICT[cfg.combineMode || ''] || '未知'}</span>
                </Dropdown>
              </div>
            )}
            <Input.Group compact>
              <Input className='!w-8 px-0 py-1 !text-center' defaultValue={i + 1} readOnly />
              <Input
                className='!w-[calc(100%_-_2rem)]'
                value={cfg.name}
                onChange={ev => {
                  const val = ev.target.value
                  onChange(
                    produce(value, draft => {
                      draft.combine!.combineCfgDict[key].name = val
                    })
                  )
                }}
                suffix={
                  <span>
                    <SettingOutlined
                      className={classNames('cursor-pointer', { 'text-primary-500': editingSubQueryKey === key })}
                      onClick={() => {
                        reState.editingSubQueryKey = key === reState.editingSubQueryKey ? '' : key
                      }}
                    />
                    {i === 0 ? null : (
                      <DeleteOutlined
                        className='text-danger-500 cursor-pointer ml-2'
                        onClick={() => {
                          const next = produce(value, draft => {
                            const q = draft.combine!
                            delete q.combineCfgDict[key]
                            q.combineOrders = _.filter(q.combineOrders, k => k !== key)
                          })
                          const q = next.combine
                          if (q?.combineOrders.length === 1) {
                            // 如果只剩 1 个，则回退到普通查询界面
                            return onChange(q.combineCfgDict[q.combineOrders[0]].query)
                          }
                          onChange(next)
                        }}
                      />
                    )}
                  </span>
                }
              />
            </Input.Group>
          </React.Fragment>
        )
      })}
    </div>
  )
}

/** 使用数据源配置面板 */
export function useDataSourcePicker(props: DataSourcePickerProps) {
  const { dataSourceType } = props.value
  if (dataSourceType === 'combine') {
    return <CombineDataSourcePicker {...props} />
  }
  return <DataSourcePicker {...props} />
}
