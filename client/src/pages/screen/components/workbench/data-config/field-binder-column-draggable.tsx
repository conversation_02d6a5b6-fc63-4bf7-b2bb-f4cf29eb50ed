import {
  CloseOutlined,
  DeleteOutlined,
  <PERSON>lip<PERSON>Outlined,
  <PERSON><PERSON><PERSON>berOutlined,
  FieldStringOutlined,
  FieldTimeOutlined,
  SearchOutlined,
  SettingOutlined, WarningOutlined,
  ZoomInOutlined
} from '@ant-design/icons'
import { fastMemo } from '@sugo/design/functions'
import { useInViewport, useMemoizedFn, useReactive, useSessionStorageState } from 'ahooks'
import { Dropdown, Input, message, PopoverProps, Select, Tag, Tooltip } from 'antd'
import classNames from 'classnames'
import _, { Dictionary } from 'lodash'
import React, { HTMLProps, MutableRefObject, useEffect, useMemo, useRef } from 'react'

import { CustomPopover } from '@/components/customs/custom-popover'
import { inflateMeasureTreeNode, toFieldPickerTree, treeNodeToColumnInfo } from '@/components/data-filter-config/utils'
import HighlightText from '@/components/highlight'
import { useVerticalListDnd } from '@/hooks/use-vertical-list-dnd'
import { useFieldColumnChange } from '@/pages/screen/components/workbench/data-config/field-binder'
import { ALL_ID } from '@/services/indices-table'
import type { DataSourceInfo, DataSourceInfoType, FieldNode } from '@/types/data-source'
import { ChartField, CompareSpec, ManualCompareSpec } from '@/types/editor-core/component'
import type { ColumnInfo, DataLoaderConfig, DataSourceQueryConfig } from '@/types/editor-core/data-source'
import { move } from '@/utils'
import { tryJsonParse } from '@/utils/json-utils'
import { parseDynamicFieldName, parseIndicesSpecTreeNodeKey } from '@/utils/query'

import { useColumnDrillDownConfigPanelForPopover } from './column-drill-down-config'
import { useColumnExtraConfigPanelForPopover } from './column-extra-config-panel'
import { useColumnSelectorTree } from './field-binding-panel'
import { composeDynamicFieldName, resolveDynamicChartField } from './utils'


/** hook，生成回调函数，用于获取下一个未绑定数据列的 ChartField */
export function useNextValidFieldGetter(
  fieldBindings: Record<string, ColumnInfo | null>,
  fieldNameDict: Dictionary<ChartField>
) {
  return (preAddCol: ColumnInfo, treeNodeKey?: string | number) => {
    const createDynamicFieldInst = (preUseField: ChartField, idx: number) => ({
      ...preUseField,
      required: false,
      dynamic: false,
      name: composeDynamicFieldName(preUseField.name, idx),
      title: `${preUseField.title}${idx + 1}`
    })

    // 假设 dynamic 都是只配置到了最后一个 fields
    // 匹配规则，从上到下匹配，如果是 dynamic 则根据现有的绑定列生成新的 field
    // 如果指标已存在，则共用 fieldName，spec 则根据 treeNode.key 获取

    const treeNodeKeySafe = `${treeNodeKey || preAddCol.id}`
    const preAddSpec = _.includes(treeNodeKeySafe, '$') ? treeNodeKeySafe.split('$')[1] : 'CUR_VALUE'

    // 判断是否共用
    if (preAddCol.type === 'indicesSpec' || preAddCol.type === 'customAgg') {
      // 找最新的本期值列，如果是加入本期值，则判断是否加入过无版本限制的本期值；如果加入同环比口径，则不判断版本
      const curValFieldName = _.findLastKey(fieldBindings, (col, fieldName) => (
        col?.id === preAddCol.id &&
        col.type === preAddCol.type &&
        !parseDynamicFieldName(fieldName).spec &&
        (preAddSpec !== 'CUR_VALUE' || _.isEmpty(col.versions))
      ))
      if (curValFieldName) {
        // 判断目标口径是否已存在
        const fieldInf = parseDynamicFieldName(curValFieldName)
        const preCheck = composeDynamicFieldName(fieldInf.name, fieldInf.idx, preAddSpec, fieldInf.isDynamicField)
        if (fieldBindings?.[preCheck]) {
          return { field: null, spec: null, error: '此口径已经加入过了' }
        }
        const preUseField = fieldNameDict[fieldInf.name]
        return {
          field: !preUseField.dynamic ? preUseField : createDynamicFieldInst(preUseField, fieldInf.idx),
          spec: preAddSpec as CompareSpec
        }
      }
      if (preAddSpec !== 'CUR_VALUE') {
        return { field: null, spec: null, error: '请先加入本期值列' }
      }
    }

    // 找到本期值列（其他度量）
    if (preAddCol.type === 'field' && preAddCol.dataType === 'number' && preAddSpec === 'CUR_VALUE') {
      const curValFieldNameCrossed = _.findKey(fieldBindings, (col, fieldName) => {
        const { spec, name } = parseDynamicFieldName(fieldName)
        if (spec || col?.type !== preAddCol.type || col?.id === preAddCol.id) {
          return false
        }
        // 当 withCompareSpecs 非空，可以作为其他度量的本期值
        return !_.isEmpty(fieldNameDict[name]?.withCompareSpecs)
      })
      if (curValFieldNameCrossed) {
        const { name, idx, isDynamicField } = parseDynamicFieldName(curValFieldNameCrossed)
        const preUseField = fieldNameDict[name]
        const notUsedSpec = _.find(preUseField.withCompareSpecs, s => {
          const preCheck = composeDynamicFieldName(name, idx, _.isString(s) ? s : s.spec, isDynamicField)
          return !fieldBindings?.[preCheck]
        })
        return {
          field: !preUseField.dynamic ? preUseField : createDynamicFieldInst(preUseField, idx),
          spec: _.isString(notUsedSpec) ? notUsedSpec : notUsedSpec?.spec
        }
      }
    }

    // 不可共用的情况，找到可用的下一个 field
    const preUseField = _.find(fieldNameDict, f => f.dynamic || !fieldBindings[f.name])
    if (!preUseField) {
      const targetFieldType = _.first(_.values(fieldNameDict))?.type || 'string'
      return {
        field: null,
        spec: null,
        error: `此图表的 ${targetFieldType === 'string' ? '维度' : '度量'} 已经达到上限`
      }
    }
    return {
      field: !preUseField.dynamic
        ? preUseField
        : createDynamicFieldInst(
          preUseField,
          _.filter(fieldBindings, (_c, k) => {
            const fieldInfo = parseDynamicFieldName(k)
            return !fieldInfo.spec && fieldInfo.name === preUseField.name
          }).length
        ),
      spec: preAddSpec as CompareSpec
    }
  }
}

interface UseFieldPanelDropHandlerParams {
  label: string
  fieldNameDict: Dictionary<ChartField>
  fieldBindings: Record<string, ColumnInfo | null>
  queryMode?: DataSourceQueryConfig['queryMode']
  onFieldColumnChange: (field: ChartField, spec: CompareSpec | ManualCompareSpec, colInfo: ColumnInfo | null) => any
  timeBucket: DataSourceQueryConfig['timeBucket']
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id: string) => Promise<any>
}

/** 拖拽创建相关逻辑 */
function useFieldPanelDropHandler({
  label,
  fieldNameDict,
  fieldBindings,
  queryMode,
  onFieldColumnChange,
  timeBucket,
  loadMoreDataSourceInfo
}: UseFieldPanelDropHandlerParams) {
  const getNextValidField = useNextValidFieldGetter(fieldBindings, fieldNameDict)

  const onDragOver = ev => {
    const fromPayload = ev.dataTransfer.getData('text/x-add-field-info')
    if (!fromPayload) {
      return
    }
    ev.preventDefault()
    ev.stopPropagation()
    // TODO 变更 UI ?
  }

  const onDrop = async ev => {
    ev.preventDefault()
    ev.stopPropagation()
    const fromPayload = ev.dataTransfer.getData('text/x-add-field-info')
    let treeNode = tryJsonParse(fromPayload)
    if (!treeNode) {
      console.error('tree node is empty')
      message.warn({
        key: 'field-binder-onDrop',
        content: '请拖拽到正确并合适的区域里',
        style: { float: 'right' }
      })
      return
    }
    // 如果度量 node 不包含 name，则需要查询
    if (treeNode.type === 'indicesSpec' && !treeNode.name) {
      treeNode = await inflateMeasureTreeNode(treeNode, timeBucket, loadMoreDataSourceInfo)
    }
    // add field
    const targetFieldType = _.first(_.values(fieldNameDict))?.type || 'string'
    const col = treeNodeToColumnInfo(treeNode, targetFieldType, queryMode)
    const { field: f, spec, error } = getNextValidField(col, treeNode.key)

    // 指标模式下：指标只能拖到度量上，维度只能拖到维度上
    // 数据库模式下：指标可以拖到度量上，维度都可以拖到度量上（默认按 count 聚合，转换成度量，相当于 customMetric）
    // 静态数据模式下：没有指标，维度都可以拖到度量上（直接把数值列看作聚合后的结果）
    // 总结：
    // indicesSpec|staticField(number)|field -> number
    // indicesDims|staticField(number，string)|field -> string
    if (
      !(
        targetFieldType === col.dataType?.replace('date', 'string') ||
        (targetFieldType === 'number' && col.type === 'customAgg' && treeNode.type === 'field') ||
        (targetFieldType === 'string' && col.type === 'field')
      )
    ) {
      console.error('拖入类型不一致，请检查节点')
      message.warn({
        key: 'field-binder-onDrop',
        content: '请拖拽到正确并合适的区域里',
        style: { float: 'right' }
      })
      return
    }

    if (!f) {
      message.error({
        key: 'field-binder-onDrop',
        content: error || `此图表的 ${label} 已经达到上限`,
        style: { float: 'right' }
      })
      return
    }
    onFieldColumnChange(f, spec as CompareSpec | ManualCompareSpec, col)
  }
  return { onDragOver, onDrop }
}

export interface FieldBinderColumnDraggableProps {
  dataSourcePickerInfo: DataSourceInfo
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id?: string) => Promise<any>
  chartFields: ChartField[] | undefined | null
  dataSourceConfigInfo: DataLoaderConfig
  onDsCfgChange: (next: DataLoaderConfig) => any
}

/** 列拖拽选择器，用于将列拖拽到字段面板里 */
export function useFieldBinderColumnDraggable(props: FieldBinderColumnDraggableProps): {
  onShowToggle: (flag?: boolean) => any
  inViewPortDetectRef: MutableRefObject<HTMLElement | null>
  fieldBinderColumnDraggableProps: PopoverProps
} {
  const { dataSourcePickerInfo, loadMoreDataSourceInfo, chartFields } = props
  const { dataSourceConfigInfo: dsQueryCfg, onDsCfgChange } = props

  const [lastState, onLastStateChange] = useSessionStorageState<{ search: string }>(
    'last-field-binder-column-draggable-state',
    { defaultValue: () => ({ search: '' }) }
  )
  const { type: dsType, tableId, fieldsBinding, orderBy } = dsQueryCfg || {}

  const onDragStart = e => {
    const payload = e.target.getAttribute('data-payload')
    if (!payload) return
    e.dataTransfer.setData('text/x-add-field-info', payload)
  }

  // 生成指标/维度列选择树（支持 选择 和 拖拽）
  const specTitleRender = (node: Partial<FieldNode>, keyword?: string | null) => {
    const type = node.type
    if (type !== 'indicesSpec' && type !== 'indicesDims' && type !== 'field') {
      return node.title
    }
    const payload = JSON.stringify(_.omit(node, 'hints'))
    // hints: 字段禁用的原因
    const hints = node.hints
    const dom = (
      <span draggable data-payload={payload} onDragStart={onDragStart}>
        {_.isFunction(node.icon) ? node.icon(node) : node.icon}
        {_.isString(node.title)
          ? (
            <HighlightText className='ml-1.5' text={node.title} highlight={keyword || ''} />
          )
          : node.title
        }
        {_.isEmpty(hints) ? null : (
          <WarningOutlined className='ml-1' />
        )}
      </span>
    )

    return _.isEmpty(hints) ? dom : (
      <Tooltip title={hints!.join('，')}>
        {dom}
      </Tooltip>
    )
  }

  // 除了可以通过拖拽绑定列，也可以通过选中来绑定
  const onFieldColumnChange = useFieldColumnChange(dsQueryCfg, onDsCfgChange)
  const { numFieldsDict, strFieldsDict } = useMemo(() => {
    const [numFields, strFields] = _.partition(chartFields, field => field.type === 'number')
    return {
      numFieldsDict: _.keyBy(numFields, 'name'),
      strFieldsDict: _.keyBy(strFields, 'name')
    }
  }, [chartFields])

  const getNextValidNumField = useNextValidFieldGetter(fieldsBinding || {}, numFieldsDict)
  const getNextValidStrField = useNextValidFieldGetter(fieldsBinding || {}, strFieldsDict)

  // 计算树高度，尽量占满窗口（40 是 popover title 和搜索框、32 是树的标题、5 是留白）
  const singleTreeHeight = window.innerHeight - 40 * 2 - 32 - 5
  const dualTreeHeight = (window.innerHeight - 40 * 2 - 32 * 2) / 2 - 5

  const chartFieldsDict = useMemo(() => _.keyBy(chartFields, 'name'), [chartFields])

  // 生成指标列选择树（支持 选择 和 拖拽）
  const { reactiveState: specTreeReactiveState, treeDom: measureTreeDom } = useColumnSelectorTree(
    dataSourcePickerInfo,
    loadMoreDataSourceInfo,
    dsQueryCfg || { type: 'indicesTable' },
    'number',
    _.pickBy(
      fieldsBinding,
      (_col, fieldName) => chartFieldsDict[parseDynamicFieldName(fieldName).name]?.type === 'number'
    ),
    (_cols, { col: deltaCol, node, selected }) => {
      const targetSpec = parseIndicesSpecTreeNodeKey(`${node.key}`).spec as CompareSpec | ManualCompareSpec
      if (!selected) {
        const exactFieldName = _.findKey(
          fieldsBinding,
          (c, k) =>
            (c?.type === 'indicesSpec' || c?.type === 'customAgg') &&
            c.id === deltaCol.id &&
            parseDynamicFieldName(k).spec === targetSpec
        )

        if (!exactFieldName) {
          // 匹配不到，说明是本期值不对应的同环比度量，按 id 匹配
          const fieldNameById = _.findKey(
            fieldsBinding,
            (c: ColumnInfo | null) => c?.type === 'field' && c.id === deltaCol.id
          )
          if (!fieldNameById) return

          // 适配动态 ChartField
          const f = resolveDynamicChartField(numFieldsDict, fieldNameById) || { name: exactFieldName, type: 'number' }
          onFieldColumnChange(f, parseDynamicFieldName(fieldNameById).spec || 'CUR_VALUE', null)
          return
        }

        // 适配动态 ChartField
        const f = resolveDynamicChartField(numFieldsDict, exactFieldName) || { name: exactFieldName, type: 'number' }
        onFieldColumnChange(f, targetSpec || 'CUR_VALUE', null)
        return
      }
      const { field: f, spec, error } = getNextValidNumField(deltaCol, node.key)

      if (!f) {
        message.error({
          content: error || '此图表的 度量 已经达到上限',
          style: { float: 'right' }
        })
        return
      }
      onFieldColumnChange(f, spec as CompareSpec | ManualCompareSpec, deltaCol)
    },
    { height: dualTreeHeight, showIcon: false, multiple: true, titleRender: specTitleRender },
    true
  )

  const dimTitleRender = (node: Partial<FieldNode>, keyword?: string | null) => {
    const type = node.type
    if (!_.includes(['indicesSpec', 'indicesDims', 'field', 'staticField'], type)) {
      return node.title
    }
    const payload = JSON.stringify(node)
    return (
      <span draggable data-payload={payload} onDragStart={onDragStart}>
        {_.isFunction(node.icon) ? node.icon(node) : node.icon}
        {_.isString(node.title)
          ? (
            <HighlightText className='ml-1.5' text={node.title} highlight={keyword || ''} />
          )
          : node.title
        }
      </span>
    )
  }

  // 生成维度列树（只可用于拖拽）
  const { reactiveState: dimTreeReactiveState, treeDom: dimTreeDom } = useColumnSelectorTree(
    dataSourcePickerInfo,
    loadMoreDataSourceInfo,
    dsQueryCfg || { type: 'indicesTable' },
    'string',
    _.pickBy(
      fieldsBinding,
      (_c, fieldName) => chartFieldsDict[parseDynamicFieldName(fieldName).name]?.type !== 'number'
    ),
    (_cols, { col: deltaCol, node, selected }) => {
      if (!selected) {
        const fieldName = _.findKey(
          fieldsBinding,
          (c, k) =>
            (c?.type === 'indicesDims' || c?.type === 'field') && c.id === deltaCol.id && !parseDynamicFieldName(k).spec
        )
        if (!fieldName) return

        // 适配动态 ChartField
        const f = resolveDynamicChartField(strFieldsDict, fieldName) || { name: fieldName, type: 'string' }
        onFieldColumnChange(f, 'CUR_VALUE', null)
        return
      }
      const { field: f, spec, error } = getNextValidStrField(deltaCol, node.key)

      if (!f) {
        message.error({
          content: error || '此图表的 维度 已经达到上限',
          style: { float: 'right' }
        })
        return
      }
      onFieldColumnChange(f, spec as CompareSpec | ManualCompareSpec, deltaCol)
    },
    {
      height: /static|api|combine/.test(dsType) ? singleTreeHeight : dualTreeHeight,
      showIcon: false,
      multiple: true,
      titleRender: dimTitleRender
    },
    true
  )

  useEffect(() => {
    specTreeReactiveState.search = lastState?.search || ''
    dimTreeReactiveState.search = lastState?.search || ''
  }, [])

  const indicesMajorsDict = dataSourcePickerInfo.indicesMajorMap.entities

  useEffect(() => {
    if (dsType !== 'indicesTable') {
      specTreeReactiveState.majorId = undefined
      return
    }
    if (specTreeReactiveState.majorId) {
      return
    }
    // 默认选择一个度量主题
    const defaultMajor =
      _.find(indicesMajorsDict, c => c.isDefault === 1 && !c.parentId) || _.find(indicesMajorsDict, c => !c.parentId)
    specTreeReactiveState.majorId = defaultMajor?.id
    loadMoreDataSourceInfo('indicesBase', defaultMajor?.id)
  }, [indicesMajorsDict, dsType])

  const content = (
    <div className='h-full'>
      <Input
        className='mx-4 !w-[calc(100%-32px)] my-1'
        prefix={<SearchOutlined />}
        value={lastState?.search}
        placeholder='输入关键字搜索'
        onChange={ev => {
          const keyword = ev.target.value
          specTreeReactiveState.search = keyword
          dimTreeReactiveState.search = keyword
          onLastStateChange({ search: keyword })
        }}
        allowClear
        onFocus={() => loadMoreDataSourceInfo('indicesBase', ALL_ID)}
      />
      {/static|api|combine/.test(dsType) ? null : (
        <>
          <div className='h-8 leading-8 pl-4 bg-gray-100 value-fields mb-1'>
            <div>
              {/dataApi|api/.test(dsType) ? '接口字段' : '度量'}
            </div>

            {dsType !== 'indicesTable' ? null : (
              <Select
                value={specTreeReactiveState.majorId}
                onChange={majorId => {
                  specTreeReactiveState.majorId = majorId
                  loadMoreDataSourceInfo('indicesBase', majorId)
                }}
                className='w-[140px] float-right'
                dropdownMatchSelectWidth={false}
                bordered={false}
                options={_.filter(indicesMajorsDict, c => !c.parentId).map(c => ({
                  label: (
                    <span>
                      {c.title || c.name}
                      {c.isDefault === 1 ? (
                        <Tag color='blue' className='!ml-2'>
                          默认
                        </Tag>
                      ) : null}
                    </span>
                  ),
                  value: c.id
                }))}
              />
            )}
          </div>
          <div className='border-gray-100 border-solid border-0 border-b'>{measureTreeDom}</div>
        </>
      )}

      {/dataApi/.test(dsType) ? null : (
        <>
          <div className='h-8 leading-8 pl-4 bg-gray-100 dim-fields mb-1'>维度</div>
          <div className=''>{dimTreeDom}</div>
        </>
      )}
    </div>
  )

  const reactiveState = useReactive({ showPopover: false })
  const onShowToggle = useMemoizedFn((flag?: boolean) => {
    reactiveState.showPopover = flag !== undefined ? !!flag : !reactiveState.showPopover
  })
  const ref = useRef<HTMLElement | null>(null)
  const [inViewport] = useInViewport(ref)
  const popoverVisible = inViewport && reactiveState.showPopover

  // 显示时如果有关键字搜索，则加载基础指标
  useEffect(() => {
    if (popoverVisible && lastState?.search) {
      loadMoreDataSourceInfo('indicesBase', ALL_ID)
    }
  }, [popoverVisible])

  return {
    onShowToggle,
    inViewPortDetectRef: ref,
    fieldBinderColumnDraggableProps: {
      title: (
        <div className='relative py-1'>
          新增数据配置
          <CloseOutlined
            className='absolute right-0 !text-gray-500 !leading-6 cursor-pointer'
            onClick={() => onShowToggle(false)}
          />
        </div>
      ),
      content,
      overlayClassName: '[&_.ant-popover-inner-content]:p-0 w-[306px] !z-[1009]',
      overlayInnerStyle: { padding: '0px' },
      placement: 'left',
      trigger: 'click',
      open: popoverVisible,
      // 只能通过 onShowToggle 打开
      onOpenChange: _.noop
    }
  }
}

const iconDict = {
  number: FieldNumberOutlined,
  string: FieldStringOutlined,
  date: FieldTimeOutlined
}

interface BindingColumnProps extends HTMLProps<HTMLDivElement> {
  iconClsName?: string
  columnInfo: ColumnInfo
  hints?: string[]
  fieldName: string
  field: ChartField
  onFieldColumnChange: (field: ChartField, spec: CompareSpec | ManualCompareSpec, col: ColumnInfo | null) => any
  dataSourceQueryConfig: DataLoaderConfig
  onDataSourceQueryConfigChange: (next: DataLoaderConfig) => any
  onChangeFieldNamesOrder: (updater: (curr: string[]) => string[]) => any
  onShowDrillCfg?: (field: ChartField) => any
  extraPopoverProps?: PopoverProps
}

/** 需要查询的列的可视化呈现 */
const BindingColumn = fastMemo((props: BindingColumnProps) => {
  const { className: itemClsName, iconClsName: itemIconClsName, columnInfo: col, hints } = props
  const { field, fieldName, dataSourceQueryConfig, onDataSourceQueryConfigChange } = props
  const { onFieldColumnChange, onChangeFieldNamesOrder, onShowDrillCfg, extraPopoverProps } = props

  const {
    hoverState: dndHoverState,
    onDragStart,
    onDragOver,
    onDragLeave,
    onDrop
  } = useVerticalListDnd({
    transferFormat: `text/x-move-field-${field.type}`,
    onDragEnd: (from, to, pos) => {
      // curValFieldNames 这个不一定有值，需要调用 changeCurValFieldNames
      if (pos === 'current') {
        return
      }
      onChangeFieldNamesOrder(currOrders => {
        const currIdx = _.findIndex(currOrders, n => n === from)
        const nextIdx = _.findIndex(currOrders, n => n === to)
        // 跨了类型拖拽则会出现找不到的情况
        return currIdx === -1 || nextIdx === -1 ? currOrders : move(currOrders, currIdx, nextIdx)
      })
    }
  })

  // 由于此配置面板会修改 fieldName，修改过后难以跟踪显示状态，所以这个 hook 放外层不合适
  const { onShow, popoverProps: columnExtraConfigPanelProps } = useColumnExtraConfigPanelForPopover({
    field: { ...field, name: fieldName },
    value: dataSourceQueryConfig,
    onChange: onDataSourceQueryConfigChange,
    pendingColumnInfo: col
  })

  const getExtraOperationDropdown = () => {
    const getMenu = () => ({
      theme: 'dark' as const,
      onClick: ev => {
        const { key } = ev
        if (key === 'drill') {
          onShowDrillCfg?.(field)
        } else if (key === 'setting') {
          onShow()
        } else if (key === 'delete') {
          const spec = parseDynamicFieldName(fieldName).spec || 'CUR_VALUE'
          onFieldColumnChange(field, spec, null)
        }
      },
      items: [
        onShowDrillCfg ? { key: 'drill', label: '下钻', icon: <ZoomInOutlined /> } : null,
        { key: 'setting', label: '配置', icon: <SettingOutlined /> },
        { key: 'delete', label: '删除', icon: <DeleteOutlined />, danger: true }
      ].filter(Boolean)
    })
    return (
      <Dropdown menu={getMenu()} destroyPopupOnHide trigger={['click']} placement='bottom'>
        <EllipsisOutlined
          className={classNames(
            'absolute right-2 !leading-6 cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity'
          )}
        />
      </Dropdown>
    )
  }

  const Icon = (col!.dataType && iconDict[col!.dataType]) || FieldNumberOutlined
  const fieldTitle = field.dynamic ? `${field.title}${parseDynamicFieldName(fieldName).idx + 1}` : field.title
  const versions = col!.versions

  const el = (
    <div
      key={col!.id}
      className={classNames(
        itemClsName,
        'relative group px-2 mb-1 leading-6 border-solid border rounded-sm elli hover:pr-6 cursor-move',
        {
          'border-t-2': dndHoverState === 'upper',
          'border-b-2': dndHoverState === 'lower'
        }
      )}
      title={`${fieldTitle}：${col!.title || col!.name}`}
      draggable
      data-drag-payload={fieldName}
      onDragStart={onDragStart}
      onDragOver={onDragOver}
      onDragLeave={onDragLeave}
      onDrop={onDrop}
    >
      <Icon className={`${itemIconClsName || ''} mr-2`} />
      {col!.title || col!.name}
      {!_.isEmpty(versions) && (
        <span className='ml-2 text-xs text-gray-400'>
          {versions![0] === '*' ? '全部版本' : _.map(versions, v => /v/i.test(v) ? v : `v${v}`).join(', ')}
        </span>
      )}
      {_.isEmpty(hints) ? null : (
        <Tooltip title={hints?.join('，')}>
          <WarningOutlined className='ml-2 text-danger-500' />
        </Tooltip>
      )}
      {getExtraOperationDropdown()}
    </div>
  )
  const popoverProps = extraPopoverProps || columnExtraConfigPanelProps
  if (popoverProps?.open) {
    return <CustomPopover {...popoverProps}>{el}</CustomPopover>
  }
  return el
})

interface FieldsPanelOpts {
  label: string
  className?: string
  itemClsName?: string
  itemIconClsName?: string
  chartFields: ChartField[]
  value: DataLoaderConfig
  onChange: (next: DataLoaderConfig) => any
  columnsPredicate?: (col: ColumnInfo | null, fieldName: string) => boolean
  onShowDragPanelToggle: () => any
  dataSourcePickerInfo: DataSourceInfo
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id: string) => Promise<any>
}

/** 使用字段编辑面板 */
const useFieldsPanel = (opts: FieldsPanelOpts) => {
  const { label, className, itemClsName, itemIconClsName, chartFields, value, onChange, columnsPredicate } = opts
  const { onShowDragPanelToggle, dataSourcePickerInfo, loadMoreDataSourceInfo } = opts

  const fieldType = _.first(chartFields)?.type || 'string'
  // 获取字段选择树，主要是为了获取 hints
  const originalFieldNodesDict = useMemo(
    () => {
      // 非指标库，度量可以用作维度
      const fieldNodes = value.type === 'indicesTable' || fieldType === 'number'
        ? toFieldPickerTree(dataSourcePickerInfo, value, fieldType)
        : [
          ...toFieldPickerTree(dataSourcePickerInfo, value, 'string'),
          ...toFieldPickerTree(dataSourcePickerInfo, value, 'number')
        ]
      return _.keyBy(fieldNodes, n => n.value || n.key)
    },
    [dataSourcePickerInfo, value, fieldType])

  // 筛选出当前面板的列
  const columnsPredicateMemo = useMemoizedFn(columnsPredicate || _.constant(true))
  const partialFieldBindings = useMemo(
    () => _.pickBy(value.fieldsBinding, columnsPredicateMemo) as Record<string, ColumnInfo>,
    [value.fieldsBinding, columnsPredicateMemo]
  )

  const fieldNameDict = useMemo(() => _.keyBy(chartFields, 'name'), [chartFields])
  const onFieldColumnChange = useFieldColumnChange(value, onChange)

  const sortedFieldNames = useMemo(() => {
    const currFieldNames = _.keys(value.fieldsBinding)
    const fieldNameOrderDict = _.zipObject(value.sortedFieldNames || [], _.range(_.size(value.sortedFieldNames)))
    return _.isEmpty(fieldNameOrderDict) ? currFieldNames : _.orderBy(currFieldNames, n => fieldNameOrderDict[n] ?? 999)
  }, [value.fieldsBinding, value.sortedFieldNames])

  const onChangeFieldNamesOrder = (updater: (curr: string[]) => string[]) => {
    // TODO 如果是维度调整顺序，则需要更新绑定的列 ?
    const nextFieldName = updater(sortedFieldNames)
    return onChange({ ...value, sortedFieldNames: nextFieldName })
  }

  const { onDragOver: onDragOverRoot, onDrop: onDropRoot } = useFieldPanelDropHandler({
    label,
    fieldNameDict,
    fieldBindings: partialFieldBindings,
    queryMode: (value as DataSourceQueryConfig).queryMode,
    onFieldColumnChange,
    timeBucket: (value as DataSourceQueryConfig).timeBucket,
    loadMoreDataSourceInfo
  })

  const {
    onShow: onShowDrillCfg,
    popoverProps: colDrillCfgPanelProps,
    targetFieldName
  } = useColumnDrillDownConfigPanelForPopover({
    // 暂时只有查询类型的才支持下钻
    value: value as DataSourceQueryConfig,
    onChange,
    dataSourcePickerInfo,
    loadMoreDataSourceInfo
  })

  return (
    <div className={`${className || ''} bg-gray-100 pb-1.5 px-2`} onDragOver={onDragOverRoot} onDrop={onDropRoot}>
      <div className='mb-0.5'>{label}</div>
      {!_.isEmpty(partialFieldBindings) ? null : (
        <div className='w-full py-2 text-center text-gray-400 cursor-pointer' onClick={() => onShowDragPanelToggle()}>
          拖动{label}至此处
        </div>
      )}
      {_.map(sortedFieldNames, fieldName => {
        const col = partialFieldBindings[fieldName]
        if (!col) {
          return null
        }
        const field = resolveDynamicChartField(fieldNameDict, fieldName) || {
          name: fieldName,
          type: fieldType
        }
        // 获取报错提示，csv 模式下 key 为 fieldName
        const originalFieldNode = originalFieldNodesDict[col.id]
          || originalFieldNodesDict[fieldName] // csv
          || _.find(originalFieldNodesDict, n => n.name === fieldName) // 合并查询
        const hints = originalFieldNode?.hints
          || (_.isEmpty(originalFieldNodesDict) || originalFieldNode ? undefined : ['字段不存在'])
        return (
          <BindingColumn
            key={fieldName}
            className={itemClsName}
            iconClsName={itemIconClsName}
            columnInfo={col}
            hints={hints}
            fieldName={fieldName}
            field={field}
            onFieldColumnChange={onFieldColumnChange}
            dataSourceQueryConfig={value}
            onDataSourceQueryConfigChange={onChange}
            onChangeFieldNamesOrder={onChangeFieldNamesOrder}
            onShowDrillCfg={
              // 暂时只有查询类型的才支持下钻
              fieldType === 'string' && _.includes(['indicesTable', 'dataCenter'], value.type)
                ? onShowDrillCfg
                : undefined
            }
            extraPopoverProps={targetFieldName === field.name ? colDrillCfgPanelProps! : undefined}
          />
        )
      })}
    </div>
  )
}

export interface FieldConfigPanelProps {
  dataSourcePickerInfo: DataSourceInfo
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id: string) => Promise<any>
  chartFields: ChartField[] | undefined | null
  value: DataLoaderConfig
  onChange: (next: DataLoaderConfig) => any
  onShowDragPanelToggle: () => any
}

/** 字段配置面板 */
export const FieldConfigPanel = fastMemo((props: FieldConfigPanelProps) => {
  const { onShowDragPanelToggle, chartFields, value, onChange, dataSourcePickerInfo, loadMoreDataSourceInfo } = props

  const chartFieldsDict = useMemo(() => _.keyBy(chartFields || [], 'name'), [chartFields])
  const metricsPanel = useFieldsPanel({
    label: '度量',
    className: 'mb-2',
    itemClsName: 'bg-[#cce2ff] border-[#0065ee]',
    itemIconClsName: '!text-[#0065ee]',
    chartFields: _.filter(chartFields, f => f.type === 'number'),
    value,
    onChange,
    columnsPredicate: (_col, fieldName) => chartFieldsDict[parseDynamicFieldName(fieldName).name]?.type === 'number',
    onShowDragPanelToggle,
    dataSourcePickerInfo,
    loadMoreDataSourceInfo
  })

  const dimsPanel = useFieldsPanel({
    label: '维度',
    itemClsName: 'bg-[#e7fff1] border-[#009f43]',
    itemIconClsName: '!text-[#009F43]',
    chartFields: _.filter(chartFields, f => f.type === 'string'),
    value,
    onChange,
    columnsPredicate: (_col, fieldName) => chartFieldsDict[parseDynamicFieldName(fieldName).name]?.type !== 'number',
    onShowDragPanelToggle,
    dataSourcePickerInfo,
    loadMoreDataSourceInfo
  })
  return (
    <>
      {metricsPanel}
      {dimsPanel}
    </>
  )
})
