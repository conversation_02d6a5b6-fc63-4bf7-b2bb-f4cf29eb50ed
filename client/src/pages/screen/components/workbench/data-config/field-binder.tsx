import _ from 'lodash'

import { ChartField, CompareSpec, ManualCompareSpec } from '@/types/editor-core/component'
import { ColumnInfo, DataLoaderConfig, DataSourceQueryConfig } from '@/types/editor-core/data-source'
import { expandCompareSpec, parseDynamicFieldName } from '@/utils/query'

import { composeDynamicFieldName, getDefaultColumnInfoForCompareSpecField } from './utils'

/** 为图表字段删除列 */
function delCol(
  spec: CompareSpec | ManualCompareSpec,
  name: string,
  fieldName: string,
  fieldsBinding: DataSourceQueryConfig['fieldsBinding'],
  onChange: (next: DataSourceQueryConfig['fieldsBinding']) => any
) {
  if (spec !== 'CUR_VALUE') {
    // 如果删除的是对比口径，则屏蔽（不屏蔽的话会按图表 fields 设置，查询对比口径）
    // nextBinding = { ...(fieldsBinding || {}), [name]: null }
    return onChange(_.omit(fieldsBinding, name))
  }
  // 如果删除的是动态字段，则需要重排索引
  const nextBinding = _(fieldsBinding)
    .omitBy((_v, k) => parseDynamicFieldName(k).nameWithIdx === fieldName)
    .thru(nextFieldBinding => {
      const { isDynamicField, name: baseFieldName, idx: deletedIdx } = parseDynamicFieldName(fieldName)
      if (!isDynamicField) {
        return nextFieldBinding
      }
      return _.mapKeys(nextFieldBinding, (_c, k) => {
        const fieldInfo = parseDynamicFieldName(k)
        if (fieldInfo.name !== baseFieldName || fieldInfo.idx < deletedIdx) {
          return k
        }
        return composeDynamicFieldName(baseFieldName, fieldInfo.idx - 1, fieldInfo.spec, fieldInfo.isDynamicField)
      })
    })
    .value() as DataSourceQueryConfig['fieldsBinding']

  return onChange(nextBinding)
}

/** 添加列到图表字段 */
function addCol(
  spec: CompareSpec | ManualCompareSpec,
  field: ChartField,
  colInfo: ColumnInfo,
  name: string,
  fieldsBinding: DataSourceQueryConfig['fieldsBinding'],
  onChange: (next: DataSourceQueryConfig['fieldsBinding']) => any
) {
  // 默认指标名称逻辑
  // 1. 本期值：指标原名（可自定义）
  // 2. 同环比：指标原名_口径 （可自定义）

  // 如果展开了同环比，则只能编辑同环比翻译名称，否则编辑指标名
  // 如果 spec 不是本期值，且 设置了跟本期值是同一个指标，则需要在 title 补上对比口径

  const isCompareSpecs = spec !== 'CUR_VALUE'
  // const curValColumnInfo = fieldsBinding?.[fieldName] || colInfo
  // if (curValColumnInfo?.name !== colInfo.name) {
  //   // 对比口径源指标跟本期值只能是同一个指标，不应该会走到这里
  //   throw new Error('指标列不能变更')
  // }

  let nextColumnInfo = isCompareSpecs
    ? getDefaultColumnInfoForCompareSpecField(expandCompareSpec(field, spec), colInfo)!
    : colInfo

  // 如果是数字类型，则默认格式化为 2 位小数
  if (nextColumnInfo.dataType === 'number') {
    nextColumnInfo = { columnFormatter: '.2~f', ...nextColumnInfo }
  }
  // 避免重复字段名
  if (_.some(fieldsBinding, c => c?.title === nextColumnInfo.title)) {
    const nextUnusedIdx = _.range(2, 20)
      .find(i => !_.some(fieldsBinding, c => c?.title === `${nextColumnInfo.title}_${i}`))
    nextColumnInfo = { ...nextColumnInfo, title: `${nextColumnInfo.title}_${nextUnusedIdx}` }
  }

  return onChange({ ...(fieldsBinding || {}), [name]: nextColumnInfo })
}

/** hook，封装了修改字段绑定列方法 */
export function useFieldColumnChange<T extends DataLoaderConfig>(value: T, onChange: (next: T) => any) {
  const { fieldsBinding, orderBy } = value || {}

  return (field: ChartField, spec: CompareSpec | ManualCompareSpec, colInfo: ColumnInfo | null) => {
    const { name: fieldName } = field
    const name = spec === 'CUR_VALUE' ? fieldName : `${fieldName}$${spec}`

    const cb = (next: DataSourceQueryConfig['fieldsBinding']) => onChange({ ...value, fieldsBinding: next })
    if (!colInfo) {
      return delCol(spec, name, fieldName, fieldsBinding, cb)
    }
    return addCol(spec, field, colInfo, name, fieldsBinding, cb)
  }
}
