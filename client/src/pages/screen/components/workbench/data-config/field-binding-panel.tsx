/* eslint-disable no-nested-ternary */
import { OrderedListOutlined, PlusOutlined } from '@ant-design/icons'
import { useDeepCompareEffect } from 'ahooks'
import { Tooltip, Tree, TreeDataNode, TreeProps } from 'antd'
import isEqual from 'fast-deep-equal'
import _ from 'lodash'
import React, { useEffect, useMemo, useState } from 'react'

import { CustomPopover } from '@/components/customs/custom-popover'
import { inflateMeasureTreeNode, treeNodeToColumnInfo, useColumnPickerLazyInit, useDataSourceTableColumnTree } from '@/components/data-filter-config/utils'
import { DataSourceInfo, DataSourceInfoType, FieldNode } from '@/types/data-source'
import { ChartField } from '@/types/editor-core/component'
import { ColumnInfo, DataLoaderConfig, DataSourceConfig, DataSourceQueryConfig } from '@/types/editor-core/data-source'
import { parseDynamicFieldName } from '@/utils/query'

import useAdvancedDataConfig from './advanced-data-config'
import { useCustomDimConfigModal } from './custom-dim-config-modal'
import { FieldConfigPanel, useFieldBinderColumnDraggable } from './field-binder-column-draggable'


/** 列选择树 hook */
export function useColumnSelectorTree(
  dataSourcePickerInfo: DataSourceInfo,
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id?: string) => Promise<any>,
  dataSourceConfigInfo: DataLoaderConfig,
  fieldType: ChartField['type'],
  value: Record<string, ColumnInfo | null | undefined>,
  onChange: (next: ColumnInfo[], delta: { col: ColumnInfo; node: FieldNode; selected: boolean }) => any,
  extraProps: Partial<TreeProps> & { titleRender?: (node: FieldNode, keyword?: string) => React.ReactNode },
  searchCache = false,
  filterNode: ((n: FieldNode) => boolean) | undefined = undefined
) {
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([])

  const { reactiveState, treeData, flatTree, keyword } = useDataSourceTableColumnTree(
    dataSourcePickerInfo, // 整个源信息
    dataSourceConfigInfo, // 激活的节点信息
    fieldType, // 字段类型
    searchCache,
    filterNode
  )
  const onInitTree = useColumnPickerLazyInit(
    dataSourceConfigInfo as DataSourceQueryConfig,
    fieldType,
    loadMoreDataSourceInfo
  )

  const showTreeData = useMemo(() => {
    // console.log(treeData)
    let arr = treeData
    const firstNode = treeData?.[0]
    const isIndiceList = firstNode?.value === 'UNGROUPED_ALL'
    if (treeData?.[0] && isIndiceList && _.size(treeData) === 1) {
      arr = _.slice(treeData[0].children, 0, 200)
    }
    return arr
  }, [treeData])

  useEffect(() => {
    // 渲染时即刻加载列数据
    onInitTree()
  }, [dataSourceConfigInfo?.tableId])

  const selectedColumnKeys = _.compact(
    _.flatMap(value, (c, k) => {
      const { nameWithIdx, spec } = parseDynamicFieldName(k)
      return [
        // 根据 fieldsBinding 的 key 判断是否选中对比指标
        // 也有可能本期值是其他指标，这个时候选择自己的本期值列
        spec ? (value![nameWithIdx]?.name !== c?.name ? c?.id : `${c?.id}$${spec}`) : c?.id
      ]
    })
  )
  const onSelectColumn = async (_nextKeys, { selected, selectedNodes, node }) => {
    // 如果度量 node 不包含 name，则需要查询
    const key = node.key as string
    if (selected && node.type === 'indicesSpec' && !_.some(dataSourcePickerInfo.indicesSpecMap.entities, d => d.indiceId === key)) {
      node = await inflateMeasureTreeNode(
        node,
        (dataSourceConfigInfo as DataSourceQueryConfig).timeBucket || 'MONTH',
        loadMoreDataSourceInfo
      )
    }
    const queryMode = (dataSourceConfigInfo as DataSourceQueryConfig).queryMode || 'groupBy'
    onChange(
      _.map(selectedNodes as FieldNode[], n => treeNodeToColumnInfo(n, fieldType, queryMode)),
      { col: treeNodeToColumnInfo(node as FieldNode, fieldType, queryMode), node, selected }
    )
  }

  useDeepCompareEffect(() => {
    const next = _.union(expandedKeys, _.map(treeData, 'key'))
    if (isEqual(next, expandedKeys)) {
      return
    }
    setExpandedKeys(next)
  }, [treeData])

  return {
    reactiveState,
    onInitTree,
    flatTree,
    treeDom: !dataSourceConfigInfo.tableId ? (
      <div className='w-full py-8 text-center text-gray-400'>请先选择数据源</div>
    ) : !_.isEmpty(showTreeData) ? (
      <Tree
        className='[&_.ant-tree-list-holder]:w-full'
        loadData={(node: TreeDataNode & { loadType?: DataSourceInfoType; value?: string }) =>
          loadMoreDataSourceInfo(node.loadType!, node.value || `${node.key}`)
        }
        treeData={fieldType === 'number' || dataSourceConfigInfo?.type !== 'indicesTable'
          ? showTreeData
          : _.filter(showTreeData, n => !n._hideForGroupBy)}
        selectedKeys={selectedColumnKeys}
        onSelect={onSelectColumn}
        showIcon
        expandedKeys={expandedKeys}
        onExpand={setExpandedKeys}
        {...(extraProps || {})}
        titleRender={
          extraProps?.titleRender
            ? n => extraProps.titleRender!(n as FieldNode, reactiveState.search)
            : undefined
        }
      />
    ) : (
      <div className='w-full py-8 text-center text-gray-400'>{keyword ? <span>没有匹配的结果</span> : '暂无数据'}</div>
    )
  }
}

/** 根据已选指标计算有效时间粒度 */
function calcValidTimeBucket(
  dataSourceConfigInfo: DataLoaderConfig,
  dataSourcePickerInfo: DataSourceInfo
) {
  if (dataSourceConfigInfo.type !== 'indicesTable') {
    return null
  }
  const metrics = _.filter(dataSourceConfigInfo.fieldsBinding, c => c?.type === 'indicesSpec')
  if (!metrics.length) {
    return null
  }
  const gr2d = _.map(metrics, m => {
    const spec = dataSourcePickerInfo.indicesSpecMap.entities[m!.id]
    return spec?.fixType.split(';')
  }).filter(Boolean)

  return _.intersection(...gr2d)
}

/** 渲染数据配置面板 */
export function useColumnsConfig(
  dataSourcePickerInfo: DataSourceInfo,
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id?: string) => Promise<any>,
  chartFields: ChartField[] | undefined | null,
  value: DataSourceConfig,
  onChange: (next: DataSourceConfig) => any
) {
  const dataSourceType = value.dataSourceType
  const dataSourceConfigInfo: DataLoaderConfig = value[dataSourceType] || { type: 'indicesTable' }
  if (dataSourceConfigInfo.type === 'indicesTable') {
    // 按照需求，指标系统，先让用户选择指标列
    chartFields = _.orderBy(chartFields, f => (f.type === 'number' ? 0 : 1))
  }
  const onQueryConfigChange = nextQueryConfig => {
    onChange({ ...value, [dataSourceType]: nextQueryConfig })
  }

  const resetCustomFieldOrder = _.isEmpty(dataSourceConfigInfo.sortedFieldNames) ? null : (
    <OrderedListOutlined
      title='重置手动排序'
      className='!text-primary-500 mr-1.5'
      onClick={() => onQueryConfigChange(_.omit(dataSourceConfigInfo, 'sortedFieldNames'))}
    />
  )

  // 自定义维度配置，计算维度，分组维度等
  const customDimConfigBtn = useCustomDimConfigModal({
    dataSourcePickerInfo,
    loadMoreDataSourceInfo,
    dataSourceConfigInfo: dataSourceConfigInfo as DataSourceQueryConfig,
    onDsCfgChange: onQueryConfigChange
  })

  // 高级数据配置，时间粒度、排序等
  const validTimeBuckets = useMemo(
    () => calcValidTimeBucket(dataSourceConfigInfo, dataSourcePickerInfo),
    [dataSourceConfigInfo, dataSourcePickerInfo])

  const advancedDataConfig = useAdvancedDataConfig({
    value: dataSourceConfigInfo,
    onChange: onQueryConfigChange,
    validTimeBuckets
  })

  const { onShowToggle, inViewPortDetectRef, fieldBinderColumnDraggableProps } = useFieldBinderColumnDraggable({
    dataSourcePickerInfo,
    loadMoreDataSourceInfo,
    chartFields,
    dataSourceConfigInfo,
    onDsCfgChange: onQueryConfigChange
  })

  // 点击刷新数据按钮时，关闭面板
  useEffect(() => {
    const id = 'refresh-data-btn'
    const el = document.getElementById(id)
    const onclick = () => onShowToggle(false)
    el?.addEventListener('click', onclick)
    return () => {
      el?.removeEventListener('click', onclick)
    }
  }, [onShowToggle])

  const queryCfg = value[value.dataSourceType]
  if (!queryCfg?.tableId) return null

  const columnsConfigPanel = (
    <div className='p-2 border-gray-100 border-solid border-0 border-b'>
      <div className='mb-2'>
        <CustomPopover {...fieldBinderColumnDraggableProps}>数据配置</CustomPopover>
        <span className='float-right leading-[1.375rem]'>
          {resetCustomFieldOrder}
          <Tooltip title='新增数据配置'>
            <PlusOutlined
              ref={inViewPortDetectRef}
              className='mr-1.5'
              title='新增数据配置'
              onClick={() => onShowToggle(true)}
            />
          </Tooltip>
          {advancedDataConfig}
          {customDimConfigBtn}
        </span>
      </div>
      <FieldConfigPanel
        dataSourcePickerInfo={dataSourcePickerInfo}
        loadMoreDataSourceInfo={loadMoreDataSourceInfo}
        chartFields={chartFields}
        value={dataSourceConfigInfo}
        onChange={onQueryConfigChange}
        onShowDragPanelToggle={onShowToggle}
      />
    </div>
  )

  return columnsConfigPanel
}
