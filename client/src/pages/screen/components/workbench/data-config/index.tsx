import './index.less'

import { RedoOutlined, SyncOutlined } from '@ant-design/icons'
import { useRequest } from 'ahooks'
import { Button, Col, message, Row, Tooltip } from 'antd'
import produce from 'immer'
import _ from 'lodash'
import React, { useEffect, useMemo } from 'react'

import { useTempFilterSettingPanel } from '@/components/data-filter-config/temp-filter-config-panel'
import { TIME_DATE } from '@/consts/data-source'
import { AsRepeaterSettingPanel } from '@/pages/screen/components/workbench/data-config/as-repeater-setting-panel'
import { useDataCacheSettings } from '@/pages/screen/components/workbench/data-config/use-data-cache-settings'
import { useDataLimitInput } from '@/pages/screen/components/workbench/data-config/use-data-limit-input'
import { DataSourceInfo, DataSourceInfoType } from '@/types/data-source'
import { ChartField } from '@/types/editor-core/component'
import { DataSourceConfig, DataSourceQueryConfig } from '@/types/editor-core/data-source'
import { parseDynamicFieldName } from '@/utils/query'

import DataCarouselPanel from './data-carousel-panel'
import { useDataSourcePicker } from './data-source-picker'
import { useColumnsConfig } from './field-binding-panel'
import { usePreProcessSettingPanel } from './pre-process-config-panel'
import { useSqlVariable } from './sql-variable'


export interface DataSourceConfigPanelProps {
  className?: string
  style?: React.CSSProperties
  value: DataSourceConfig
  onChange: (next: DataSourceConfig) => any
  dataSourcePickerInfo: DataSourceInfo
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id?: string, other?: any) => Promise<any>
  chartFields: ChartField[] | undefined | null
  onRefreshData?: (dsCfg?: DataSourceConfig) => Promise<any>
  chartData?: any
  chartDemoData?: any
  onSelected?: (id?: string, node?: any) => any
}

/** 如果查同环比，但是没有选择时间列且筛选时间范围，则提示 */
function hintAboutTimeDimCheck(indicesTable: DataSourceQueryConfig | undefined) {
  if (!indicesTable) {
    return
  }
  const timeFlt = _.find(indicesTable?.filters, flt => flt.col === TIME_DATE)
  const [since, until] = (timeFlt?.eq || []) as (string | null | undefined)[]

  const hasTimeFlt = since || until
  const fieldsBinding = indicesTable?.fieldsBinding || {}
  const hasSpec = _.some(fieldsBinding, (_c, k) => !!parseDynamicFieldName(k).spec)
  if (!hasSpec) {
    return
  }
  const hasPlanSpec = _.some(fieldsBinding, (_c, k) => _.startsWith(parseDynamicFieldName(k).spec, 'PLAN'))
  const hasTimeDimInGroup = _.some(fieldsBinding, c => c?.name === TIME_DATE)

  if (hasPlanSpec) {
    const hasSortPlan = _.some(indicesTable.orderBy, o => _.includes(o.field, 'PLAN'))
    if (!hasTimeFlt) {
      message.warn('查询了目标值，但是没有筛选时间，可能会导致查询很慢')
    } else if (hasSortPlan && (indicesTable.limit ?? 100) <= 100) {
      message.warn(
        '查询了目标值，并且设置了排序，因为是前端排序，所以需要保证数据限制（limit）足够大，否则数据可能不对'
      )
    }
    return
  }
  if (!hasTimeDimInGroup) {
    message.warn('查询了同环比，但是没有添加时间维度，查询结果会自动加上时间维')
  }
}

/** 数据源配置面板 */
export default function DataSourceConfigPanel(props: DataSourceConfigPanelProps) {
  const { value, onChange, dataSourcePickerInfo, chartFields, loadMoreDataSourceInfo, onRefreshData, chartData } = props
  const { chartDemoData, onSelected, className, style } = props
  const { run: doRefreshData, loading } = useRequest(
    () => {
      if (!onRefreshData) {
        return Promise.resolve()
      }
      return onRefreshData(produce(value, draft => {
        const q = draft[draft.dataSourceType] as DataSourceQueryConfig
        q.onError = err => message.error({
          content: `请求错误：${err?.message || '接口请求失败'}`,
          duration: 5,
          key: 'cloud-error'
        })
      }))
    },
    { manual: true }
  )
  const queryCfg = value[value.dataSourceType]

  const dataSourcePickerPanel = useDataSourcePicker({
    dataSourcePickerInfo,
    loadMoreDataSourceInfo,
    value,
    onChange,
    defaultStaticData: chartData,
    onSelected,
    onRefreshData,
    chartFields
  })
  const columnsConfigPanel = useColumnsConfig(
    dataSourcePickerInfo,
    loadMoreDataSourceInfo,
    chartFields,
    value,
    onChange
  )

  const sqlVariableList = useSqlVariable({
    value,
    onChange,
    dataSourcePickerInfo
  })

  const tempFilterSettingPanel = useTempFilterSettingPanel({
    value,
    dataSourcePickerInfo,
    onChange,
    loadMoreDataSourceInfo,
    chartData, // 静态数据筛选设置值时需要用到
    props: { mode: 'default' }
  })
  const dataLimitInput = useDataLimitInput(value, onChange)
  const dataCacheSettings = useDataCacheSettings(value, onChange)
  const preProcessSettingPanel = usePreProcessSettingPanel({
    value,
    onChange,
    chartData,
    chartDemoData
  })

  const dataSourceConfigInfo = value[value.dataSourceType]
  const selectdDims = useMemo(
    () =>
      // ...
      _(dataSourceConfigInfo?.fieldsBinding)
        .mapValues((v, key) => ({ ...v, key }))
        .filter(i => i?.dataType === 'string')
        .value(),
    [dataSourceConfigInfo?.fieldsBinding]
  )

  const onResizeData = () => onChange({ dataSourceType: 'dataCenter' })

  const onRefreshDataWithCheck = () => {
    // 如果查同环比，但是没有选择时间列且筛选时间范围，则提示
    if (value.dataSourceType === 'indicesTable') {
      hintAboutTimeDimCheck(value[value.dataSourceType])
    }
    return doRefreshData()
  }

  // 判断是否是使用了sql变量的数据视图
  const isDatasetVariable = !_.isEmpty(value?.dataset?.sqlVariable)

  useEffect(() => {
    const dataset = value?.dataset
    // 当前数据源的 id
    const currentId = dataset?.datasetId

    if (currentId) {
      // 当前数据源的变量
      const dataSourceVariable = dataSourcePickerInfo.datasetMap.entities?.[currentId]?.sqlVariable

      const isUpdateVariable = !_.isEqual(_.keys(dataSourceVariable).sort(), _.keys(dataset?.sqlVariable).sort()) &&
        !_.isEmpty(dataSourceVariable) &&
        !_.isEmpty(dataset?.sqlVariable)

      // 判断key是否一致 如果一直则不更新 否则更新
      if (isUpdateVariable) {
        onChange(produce(value, draft => {
          if (draft?.dataset) {
            draft.dataset.sqlVariable = dataSourceVariable
          }
        }))
      }
    }
  }, [dataSourcePickerInfo.datasetMap.entities, onChange, value])

  return (
    <div className={`h-full pb-8 data-config-setting ${className || ''}`} style={style}>
      {!onRefreshData ? null : (
        <Row className='w-full px-4 py-2 pt-4'>
          <Col flex={1}>
            <Button
              type='primary'
              className='w-full'
              icon={<SyncOutlined />}
              loading={loading}
              onClick={onRefreshDataWithCheck}
              id='refresh-data-btn'
            >
              刷新数据
            </Button>
          </Col>

          <Col flex={0} className='pl-4'>
            <Tooltip title='重置数据'>
              <Button icon={<RedoOutlined />} onClick={onResizeData} />
            </Tooltip>
          </Col>
        </Row>
      )}

      {dataSourcePickerPanel}
      {columnsConfigPanel}
      {isDatasetVariable && sqlVariableList}
      {tempFilterSettingPanel}
      {dataLimitInput}
      {preProcessSettingPanel}
      {dataCacheSettings}

      {/* 切片播放设置 */}
      {queryCfg?.tableId && (
        <DataCarouselPanel
          value={value.carousel}
          onChange={val => onChange({ ...value, carousel: val })}
          dimensions={selectdDims}
        />
      )}

      {queryCfg?.tableId && (
        <AsRepeaterSettingPanel
          value={value.asRepeater}
          disabled={value.dataSourceType === 'repeater'}
          onChange={val => onChange({ ...value, asRepeater: val })}
          chartData={chartData}
        />
      )}

      {!queryCfg?.tableId && <div className='not-datasource'>提示：请选择一个数据源</div>}
    </div>
  )
}

export type DataSourceConfigPanelType = typeof DataSourceConfigPanel
