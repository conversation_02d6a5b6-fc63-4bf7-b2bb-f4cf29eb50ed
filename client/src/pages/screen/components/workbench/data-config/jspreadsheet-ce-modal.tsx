import { useReactive, useRequest } from 'ahooks'
import { Button } from 'antd'
import _ from 'lodash'
import React, { useEffect, useRef } from 'react'

import Modal from '@/components/customs/custom-modal'
import { i18nDict, importCe } from '@/components/elements/luckysheet/ce-renderer'
import { encodeColMemo } from '@/components/elements/luckysheet/utils'

export interface CEModalOpts {
  title: string
  value: any[]
  onChange: (next: object[]) => any
  readOnly?: boolean
  extra?: React.ReactNode
  sortedColumns?: string[]
}

/** 使用 ce 编辑对话框 */
export function useCEModal(opts: CEModalOpts) {
  const { title, value, onChange, readOnly = false, extra, sortedColumns } = opts
  const reactiveState = useReactive<{ visible: boolean }>({ visible: false })

  const containerDomRef = useRef<HTMLDivElement | null>(null)
  const instRef = useRef<any>()

  const { data: ce, run } = useRequest(importCe, { manual: true, cacheKey: 'import-ce' })
  const handleConfirmSave = () => {
    const ceInst = instRef.current
    reactiveState.visible = false
    instRef.current = null
    if (!ceInst) {
      return
    }
    const jsonData = ceInst.getJson()
    // 清掉无用数据
    const validColNames = ceInst
      .getConfig()
      .columns.filter(c => _.some(jsonData, d => !_.isNil(d[c.name]) && d[c.name] !== ''))
      .map(c => c.name)
    const data = _.map(jsonData, d => _.pick(d, validColNames))
    const trimTailData = _.dropRightWhile(data, d => _.every(validColNames, c => _.isNil(d[c]) || d[c] === ''))
    onChange(trimTailData)
  }

  const onModalContentRefChange = dom => {
    containerDomRef.current = dom
    const keysFields = sortedColumns || _.uniq(_.flatMap(value, obj => _.keys(obj)))
    const safeKeysFields = _.isEmpty(keysFields) ? _.range(8).map(i => encodeColMemo(i)) : keysFields
    if (ce && !instRef.current && dom) {
      instRef.current = ce(dom, {
        data: _.isEmpty(value) ? [{}] : _.map(value, obj => {
          const objKeys = _.keys(obj)
          const missingKeys = _.difference(safeKeysFields, objKeys)
          const missingObj = _.fromPairs(_.map(missingKeys, k => [k, '']))
          return { ...obj, ...missingObj }
        }),
        columns: _.map(safeKeysFields, k => ({ type: 'text', title: k, name: k, width: 160 })),
        minDimensions: readOnly ? [0, 0] : [8, 10],
        text: i18nDict,
        onchangeheader: (_el, _idx, prev, next) => {
          const ceInst = instRef.current
          const nextData = _.map(ceInst.getJson(), d => _.mapKeys(d, (_v, k) => (k === prev ? next : k)))
          ceInst.options.columns = _.map(ceInst.options.columns, c => ({
            ...c,
            title: c.title === prev ? next : c.title,
            name: c.name === prev ? next : c.name
          }))
          ceInst.setData(nextData)
        }
      })
    }
  }

  useEffect(() => {
    // 自动同步 value
    const ceInst = instRef.current
    if (!ceInst) {
      return
    }
    instRef.current.destroy()
    instRef.current = null
    onModalContentRefChange(containerDomRef.current)
  }, [value])

  const onAddMoreRows = () => instRef.current?.insertRow(5)
  const onCancel = () => {
    instRef.current.destroy()
    instRef.current = null
    reactiveState.visible = false
  }
  return {
    showModal: () => {
      reactiveState.visible = true
      if (!ce) {
        return run()
      }
    },
    modalDom: !reactiveState.visible ? null : (
      <Modal
        title={title}
        open
        className='[&_.ant-modal-body]:overflow-auto'
        width='75vw'
        bodyStyle={{ height: '50vh', padding: '0px' }}
        centered
        maskClosable={false}
        onCancel={onCancel}
        onOk={handleConfirmSave}
        footer={
          readOnly
            ? null
            : [
              <Button key='addRows' className='float-left' onClick={onAddMoreRows}>
                添加 5 行
              </Button>,
              extra,
              <Button key='cancel' onClick={onCancel}>
                取消
              </Button>,
              <Button key='confirm' type='primary' onClick={handleConfirmSave}>
                确定
              </Button>
            ]
        }
      >
        <div ref={onModalContentRefChange} />
      </Modal>
    )
  }
}
