import { CloudDownloadOutlined, CodeOutlined, DownOutlined, EditOutlined, EyeOutlined, UndoOutlined } from '@ant-design/icons'
import { Button, Checkbox, Tooltip } from 'antd'
import { csvFormat } from 'd3-dsv'
import _ from 'lodash'
import React, { useMemo, useState } from 'react'

import { DS_PAGE_NAME } from '@/consts/dataset'
import { useCEModal } from '@/pages/screen/components/workbench/data-config/jspreadsheet-ce-modal'
import { DataSourceConfig } from '@/types/editor-core/data-source'
import { csvToJsObjs } from '@/utils/json-utils'

import { useCodeEditorModal } from './code-editor-for-workbench'


export interface PreProcessConfigPanelOpts {
  value: DataSourceConfig
  onChange: (next: DataSourceConfig) => any
  chartData?: any
  chartDemoData?: any
}

/** 数据预处理配置面板 */
export function usePreProcessSettingPanel(opts: PreProcessConfigPanelOpts) {
  const { value, onChange, chartData, chartDemoData } = opts
  const queryCfg: any = value[value.dataSourceType]
  const [unfold, setUnfold] = useState(queryCfg?.preProcess !== undefined)

  const onChangeCode = next => {
    onChange({
      ...value,
      [value.dataSourceType]: { ...queryCfg, preProcess: next || undefined }
    })
  }
  const onChangeData = (obj = {}) => {
    onChange({
      ...value,
      [value.dataSourceType]: { ...queryCfg, ...obj }
    })
  }
  const ceModalVal: any[] = useMemo(
    () => (queryCfg?.type === 'static' ? csvToJsObjs(queryCfg?.data || chartData) : chartData),
    [queryCfg?.type, queryCfg?.data, chartData]
  )

  const [metrics, dims] = _.partition(queryCfg?.fieldsBinding, c => c?.dataType === 'number')
  const metricsToJsConst = _.map(metrics, (c, i) => `const met${i} = t('${c.title}')`).join('\n\t')
  const dimsToJsConst = _.map(dims, (c, i) => `const dim${i} = t('${c.title}')`).join('\n\t')

  const { showModal: showPreProcessCodeEditModal, modalDom: codeEditorModal } = useCodeEditorModal({
    title: '数据预处理配置',
    value: queryCfg?.preProcess || '',
    onChange: onChangeCode,
    extra: (
      <Tooltip title='禁止原来的请求, 只执行这里的代码' placement='topLeft'>
        <Checkbox
          checked={queryCfg?.isDisabledQuery || ''}
          onChange={e => onChangeData?.({ isDisabledQuery: e.target.checked })}
        >
          禁止请求
        </Checkbox>
      </Tooltip>
    ),
    // FIXME: 字符串包含 ' " 的话会导致页面无法保存，改成 ` 临时解决
    defaultValue: `/**
 * 数据预处理配置
 * @params{Array} data 数据数组
 * @params{Object} queryMeta 查询元数据
 * @params{Object} utils 工具函数，包括 loadsh 和 dayjs，例如 utils._
 * @params{Function} fetchData 请求数据: fetchData(queryMeta)
 *
 * @example: 过滤掉维度名称包含'京'的
 * async function (data, queryMeta, utils, fetchData) {
 *   return data.filter(i => i.dim0.indexOf('京') === -1)
 * }
 */
async function preProcess(data, queryMeta, utils, fetchData) {
  const { _, dayjs } = utils
  const t = n => _.findKey(queryMeta.fieldsBinding, c => c && c.title === n)
  /* data csv sample:
${csvFormat(_.take(ceModalVal, 5))}
  */
  // 度量、维度的属性名：
  ${metricsToJsConst}
  ${dimsToJsConst}

  // TODO 请在这里编写数据预处理逻辑

  return data
}`
  })

  const onCeChange = next => {
    if (queryCfg?.type !== 'static') {
      return
    }
    onChange({ ...value, [value.dataSourceType]: { ...queryCfg, data: next && csvFormat(next) } })
  }
  const { showModal: showInspectChartDataModal, modalDom: inspectChartDataModal } = useCEModal({
    title: `${queryCfg?.type !== 'static' ? '查看 csv 数据（需要编辑请转换为静态数据）' : '编辑 csv 数据'}`,
    // 非静态数据模式，则无法编辑
    readOnly: queryCfg?.type !== 'static',
    value: ceModalVal,
    sortedColumns: queryCfg?.sortedFieldNames || _.chain(queryCfg?.fieldsBinding).keys().sort().value(),
    onChange: onCeChange,
    extra: <Button onClick={() => onCeChange(chartDemoData)}>重置{DS_PAGE_NAME}</Button>
  })

  if (!queryCfg) {
    return null
  }

  const resetPreProcessCodeBtn = !queryCfg?.preProcess ? null : (
    <UndoOutlined
      title='去掉处理逻辑'
      className='float-right cursor-pointer !text-primary-500'
      onClick={() => {
        onChangeCode(undefined)
      }}
    />
  )

  const becomeStatic = () => {
    if (queryCfg?.type === 'static') {
      return
    }
    onChange({
      ...value,
      dataSourceType: 'static',
      static: {
        originalType: queryCfg.type,
        type: 'static',
        tableId: 'static_csv',
        fieldsBinding: queryCfg.fieldsBinding,
        sortedFieldNames: queryCfg.sortedFieldNames,
        hideFields: queryCfg.hideFields,
        data: csvFormat(chartData)
      }
    })
  }

  const undoBecomeStatic = () => {
    if (queryCfg?.type !== 'static' || !queryCfg.originalType) {
      return
    }
    onChange({
      ...value,
      dataSourceType: queryCfg.originalType,
      static: undefined
    })
  }

  return (
    <div className='p-2 border-gray-100 border-solid border-0 border-b'>
      <div className='flex items-center cursor-pointer' onClick={() => setUnfold(!unfold)}>
        <span className='flex-1'>
          数据预处理
          {resetPreProcessCodeBtn}
        </span>
        <DownOutlined rotate={unfold ? 0 : -90} />
      </div>

      <div className='my-2' style={{ display: unfold ? 'block' : 'none' }}>
        <Button
          className='w-full mb-3'
          onClick={showInspectChartDataModal}
          icon={queryCfg?.type === 'static' ? <EditOutlined /> : <EyeOutlined />}
        >
          {queryCfg.type === 'static' ? '编辑数据' : '查看数据'}
        </Button>
        {inspectChartDataModal}

        {queryCfg.type === 'static' ? null : (
          <Button className='w-full mb-3' onClick={becomeStatic} icon={<CloudDownloadOutlined />}>
            静态化
          </Button>
        )}

        {queryCfg.type === 'static' && queryCfg.originalType ? (
          <Button className='w-full mb-3' onClick={undoBecomeStatic} icon={<UndoOutlined />}>
            撤销静态化
          </Button>
        ) : null}

        <Button
          className='w-full'
          onClick={showPreProcessCodeEditModal}
          type={queryCfg?.preProcess ? 'primary' : 'default'}
          icon={<CodeOutlined />}
        >
          编辑处理逻辑
        </Button>
      </div>
      {codeEditorModal}
    </div>
  )
}
