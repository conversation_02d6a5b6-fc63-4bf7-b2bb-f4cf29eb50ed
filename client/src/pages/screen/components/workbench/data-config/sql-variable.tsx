import { useDebounceFn, useSetState } from 'ahooks'
import { Dropdown } from 'antd'
import { MenuProps } from 'antd/lib/menu'
import { produce } from 'immer'
import _ from 'lodash'
import React, { useEffect } from 'react'

import DebounceInput from '@/components/debounce-input'
import { DataSourceQueryConfig } from '@/types/editor-core/data-source'

export interface SqlVariableProps {
  value: DataSourceQueryConfig
  onChange: (next: DataSourceQueryConfig) => any
}

function getLocalStorageKeys(prefix: string) {
  const keys: string[] = []
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i)
    if (key?.indexOf(prefix) === 0) {
      keys.push(key)
    }
  }
  return keys
}

/** 生成全局变量菜单 */
function genGlobalVarMenu(keyPrefix: string) {
  return getLocalStorageKeys('gv_').map(varName => ({
    label: `${varName.replace(/^gv_/, '')} = ${localStorage.getItem(varName)}`,
    key: `${keyPrefix}${varName}`
  }))
}

/** 额外参数 key 输入框，支持选择全局变量 */
function LinkQueryKeyPicker(props) {
  const items: MenuProps['items'] = genGlobalVarMenu('')

  const handleMenuClick: MenuProps['onClick'] = e => {
    const varName = e.key.replace(/^gv_/, '')
    // 存一个可以获取的全局变量的代码公式
    props.onChange?.({ target: { value: varName } } as any)
  }

  return (
    <Dropdown.Button
      menu={{ items, onClick: handleMenuClick }}
      buttonsRender={([_leftButton, rightButton]) => [
        <DebounceInput {...props} />,
        rightButton
      ]}
    />
  )
}

export function useSqlVariable({ value, onChange, dataSourcePickerInfo }) {
  const { run: onChangeDebounced } = useDebounceFn(onChange, { wait: 100 })
  const { dataset } = value

  const [sqlVariableState, setSqlVariableState] = useSetState({
    isDataSourceDelete: true,
    variable: dataset?.sqlVariable
  })

  useEffect(() => {

    // 当前数据源的 id
    const currentId = dataset?.datasetId

    // 当前数据源的变量
    const dataSourceVariable = dataSourcePickerInfo.datasetMap.entities?.[currentId]?.sqlVariable


    if (dataSourceVariable) {
      setSqlVariableState({ isDataSourceDelete: false })
    }
    const isUpdateVariable = !_.isEqual(_.keys(dataSourceVariable).sort(), _.keys(dataset?.sqlVariable).sort()) &&
      !_.isEmpty(dataSourceVariable) &&
      !_.isEmpty(dataset?.sqlVariable)

    // 判断dataSourceVariable和dataset?.sqlVariable key是否一致 如果一直则不更新 否则更新
    if (isUpdateVariable) {
      onChangeDebounced(produce(value, draft => {
        draft.dataset.sqlVariable = dataSourceVariable
        setSqlVariableState({ variable: dataSourceVariable })
      }))
    } else {
      setSqlVariableState({ variable: dataset?.sqlVariable })
    }
  }, [
    dataSourcePickerInfo.entities,
    dataset?.sqlVariable,
    onChangeDebounced,
    value,
    setSqlVariableState,
    dataset?.datasetId,
    dataSourcePickerInfo.datasetMap.entities
  ])

  return (
    <div className='p-2 border-gray-100 border-solid border-0 border-b'>
      <div className='mb-2'>
        视图变量
      </div>
      {sqlVariableState.isDataSourceDelete ? <div className='mb-2'>
        当前数据源已被删除
      </div> : <div className='flex flex-col'>
        {_.map(sqlVariableState.variable, (v, k) => (
          <div key={k} className='flex items-center'>
            <span className='mr-2'>{k}:</span>
            <LinkQueryKeyPicker type='text' size='small' value={v.value} onChange={val => {
              onChangeDebounced(produce(value, draft => {
                draft.dataset.sqlVariable[k].value = val.target.value
              }))
            }} className='border rounded px-2 py-1' />
          </div>
        ))}
      </div>}
    </div>
  )
}
