/* eslint-disable no-template-curly-in-string */
import { DownOutlined } from '@ant-design/icons'
import { useDebounceFn } from 'ahooks'
import { InputNumber, Select, Space } from 'antd'
import _ from 'lodash'
import React, { useState } from 'react'

import { DataSourceConfig } from '@/types/editor-core/data-source'

const cacheUnitOptions = [
  { label: '秒', value: 'S', fmt: 'PT${v}S' },
  { label: '分钟', value: 'M', fmt: 'PT${v}M' },
  { label: '小时', value: 'H', fmt: 'PT${v}H' },
  { label: '天', value: 'D', fmt: 'P${v}D' }
]

/** 数据缓存配置面板 */
export function useDataCacheSettings(value: DataSourceConfig, onChange: (next: DataSourceConfig) => any) {
  const queryCfg = value[value.dataSourceType]
  const { run: onChangeDebounced } = useDebounceFn(onChange, { wait: 1000 })
  const [unfold, setUnfold] = useState(_.has(queryCfg, 'staleAfter'))

  if (!_.includes(['indicesTable', 'dataCenter', 'combine', 'dataset'], queryCfg?.type)) {
    return null
  }
  // 解析数值和单位
  const staleAfter = (queryCfg && ('staleAfter' in queryCfg)) ? queryCfg.staleAfter ?? 'PT4H' : 'PT4H'
  const staleAfterVal = _.isFinite(+staleAfter) ? +staleAfter : `${staleAfter}`.match(/\d+/)?.[0] ?? 4
  const staleAfterUnit = _.isFinite(+staleAfter) ? 'S' : `${staleAfter}`.match(/[A-Z]$/)?.[0] || 'H'

  return (
    <div className='p-2 border-gray-100 border-solid border-0 border-b'>
      <div className='flex items-center cursor-pointer' onClick={() => setUnfold(!unfold)}>
        <span className='flex-1'>缓存过期时间</span>
        <DownOutlined rotate={unfold ? 0 : -90} />
      </div>
      <div className='my-2' style={{ display: unfold ? 'block' : 'none' }}>
        <Space.Compact>
          <InputNumber
            placeholder='请输入'
            min={0}
            max={100000}
            step={1}
            value={staleAfterVal ?? 1}
            onChange={val => {
              const selectedUnit = _.find(cacheUnitOptions, o => o.value === staleAfterUnit)
              const finalVal = _.template(selectedUnit?.fmt || 'PT{{v}}S')({ v: val })
              onChangeDebounced({ ...value, [value.dataSourceType]: { ...queryCfg, staleAfter: finalVal } })
            }}
          />
          <Select
            options={cacheUnitOptions}
            value={staleAfterUnit}
            onChange={val => {
              const selectedUnit = _.find(cacheUnitOptions, o => o.value === val)
              const finalVal = _.template(selectedUnit?.fmt || 'PT{{v}}S')({ v: staleAfterVal })
              onChangeDebounced({ ...value, [value.dataSourceType]: { ...queryCfg, staleAfter: finalVal } })
            }}
          />
        </Space.Compact>
      </div>
    </div>
  )
}
