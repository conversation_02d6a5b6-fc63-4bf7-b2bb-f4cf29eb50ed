import { DownOutlined } from '@ant-design/icons'
import { useDebounceFn } from 'ahooks'
import { InputNumber } from 'antd'
import _ from 'lodash'
import React, { useState } from 'react'

import { DataSourceConfig } from '@/types/editor-core/data-source'


/** 渲染 limit 配置面板 */
export function useDataLimitInput(value: DataSourceConfig, onChange: (next: DataSourceConfig) => any) {
  const queryCfg = value[value.dataSourceType]
  const { run: onChangeDebounced } = useDebounceFn(onChange, { wait: 1000 })
  const [unfold, setUnfold] = useState(queryCfg?.limit !== undefined)

  if (!_.includes(['indicesTable', 'dataCenter', 'static', 'combine', 'dataset'], queryCfg?.type)) {
    return null
  }

  return (
    <div className='p-2 border-gray-100 border-solid border-0 border-b'>
      <div className='flex items-center cursor-pointer' onClick={() => setUnfold(!unfold)}>
        <span className='flex-1'>数据限制</span>
        <DownOutlined rotate={unfold ? 0 : -90} />
      </div>
      <div className='my-2' style={{ display: unfold ? 'block' : 'none' }}>
        <InputNumber
          placeholder='请输入'
          min={1}
          max={100000}
          className='!w-full'
          step={1}
          value={queryCfg?.limit || 100}
          addonAfter='项'
          onChange={val => {
            onChangeDebounced({
              ...value,
              [value.dataSourceType]: { ...queryCfg, limit: val }
            })
          }}
        />
      </div>
    </div>
  )
}
