import { ApiOutlined, DatabaseOutlined, TableOutlined } from '@ant-design/icons'
import arrayToTree from 'array-to-tree'
import _, { Dictionary } from 'lodash'
import React from 'react'

import { toFieldPickerTree } from '@/components/data-filter-config/utils'
import { DS_PAGE_NAME } from '@/consts/dataset'
import { COMPARE_SPEC_TRANSLATE_DICT } from '@/consts/define'
import { DataSourceInfo, DataSourceInfoType } from '@/types/data-source'
import { ChartField } from '@/types/editor-core/component'
import { ColumnInfo, DataLoaderConfig } from '@/types/editor-core/data-source'
import smartSearch from '@/utils'
import { parseDynamicFieldName } from '@/utils/query'


/** 生成列选择树节点数据 */
export function getColumnTreeDataWithoutAll(
  dataSourcePickerInfo: DataSourceInfo,
  dataSourceConfigInfo: DataLoaderConfig,
  fieldType: 'string' | 'number',
  search = ''
) {
  const flatData = toFieldPickerTree(dataSourcePickerInfo, dataSourceConfigInfo, fieldType)
  const filtered = search ? _.filter(flatData, d => smartSearch(search, d.title as string)) : flatData
  return arrayToTree(filtered, { parentProperty: 'pId', customID: 'key' })
}

/** 去掉对比指标的后缀，单价_本期实际 -> 单价 */
export function dropCompareSpecTitle(columnTitle: string | undefined | null) {
  return (columnTitle || '').replace(/(.+?)(_[^_]*)$/, '$1')
}

/** 取得指标对比口径的默认 columnInfo */
export function getDefaultColumnInfoForCompareSpecField(
  chartField: ChartField,
  curValColumnInfo: ColumnInfo | null | undefined // 本期值的 columnInfo
) {
  const { spec } = parseDynamicFieldName(chartField.name)
  const hasCompareSpecs = !!spec
  // 默认指标名称逻辑
  // 1. 不能配置同环比：指标原名（可自定义）
  // 2. 可配置同环比：指标原名_口径 （可自定义口径）

  return (
    curValColumnInfo &&
    ({
      ...curValColumnInfo,
      title:
        chartField.columnTitle ||
        (hasCompareSpecs
          ? `${dropCompareSpecTitle(curValColumnInfo.title)}_${COMPARE_SPEC_TRANSLATE_DICT[spec!]}`
          : curValColumnInfo.title),
      aggMode: spec ? 'unknown' : curValColumnInfo.aggMode
      // 注意不用传 compareSpec，因为 fieldsBinding 的 key 已经有此信息
    } as ColumnInfo)
  )
}

/** 根据 fieldName 和 图表定义的 ChartField 获取实际动态生成的 ChartField */
export function resolveDynamicChartField(fieldsDict: Dictionary<ChartField>, fieldName: string) {
  const fieldInf = parseDynamicFieldName(fieldName)
  const baseField: ChartField | undefined = fieldsDict[fieldInf.name]
  return baseField?.dynamic
    ? {
      ...baseField,
      name: fieldInf.nameWithIdx,
      required: fieldInf.idx === 0 ? baseField.required : false,
      dynamic: false,
      title: `${baseField.title}${fieldInf.idx + 1}`
    }
    : baseField
}

/** 构造动态 fieldName */
export function composeDynamicFieldName(fieldName: string, idx: number, spec?: string, dynamic = true) {
  const v = dynamic ? `${fieldName}_idx_${idx}` : fieldName
  return spec && spec !== 'CUR_VALUE' ? `${v}$${spec}` : v
}

export type DataSourcePickerTreeNode = {
  id: string
  value: string
  pId?: string
  title: string
  loadType?: DataSourceInfoType
  isLeaf?: boolean
  selectable?: boolean
}

const icoApiOutlined = <ApiOutlined />
const icoDbOutlined = <DatabaseOutlined />
const icoTableOutlined = <TableOutlined />
const connIcon = () => icoApiOutlined
const dbIcon = () => icoDbOutlined
const tableIcon = () => icoTableOutlined

/** 生成数据源选择器所需的简单树结构（打平的树） */
export function toSimpleDataSourcePickerTree(dataSourcePickerInfo: DataSourceInfo) {
  const {
    connectionMap, databaseMap, tableMap, indicesTableMap,
    datasetGroupMap, datasetMap, apiMap, repeatersMap, dataApiMap
  } = dataSourcePickerInfo
  const connDict = connectionMap.entities
  const dbDict = databaseMap.entities
  const tableDict = tableMap.entities
  const indicesTableDict = indicesTableMap.entities
  const apiDict = apiMap.entities
  const dataApiDict = dataApiMap.entities
  // 需求规定，最顶层是数据库类型
  // 预先指定类型，避免查询完才出现类型；并且要避免出现类型为空的情况
  // const dbConnTypes = ['MYSQL', 'SQLSERVER', 'POSTGRESQL', 'ORACLE', 'TINDEX']
  const typeGroup = _.chain(connDict)
    // 兼容旧版本没有showType的情况
    .map(v => v?.showType ? v?.showType : _.toUpper(v.type))
    .filter(Boolean)
    .uniq()
    .orderBy()
    .value()
  const validTypesSet = new Set(typeGroup)

  const enableIndicesDatasource = window?.initialState?.enableIndicesDatasource ?? true

  // 类型信息
  const tree = [
    // 有指标库的情况下才显示
    ...(enableIndicesDatasource ? [{
      id: 'indicesTable',
      value: 'indicesTable',
      title: '数智云指标库',
      loadType: 'indicesTable',
      selectable: false
    }] : []),
    ..._.map(typeGroup, connType => ({
      id: connType,
      value: connType,
      title: connType,
      loadType: 'connection',
      selectable: false
    })),
    { id: 'static', value: 'static', title: '静态数据', selectable: false },
    { id: 'dataset_group', value: 'dataset_group', title: DS_PAGE_NAME, loadType: 'datasetGroup', selectable: false },
    { id: 'repeater', value: 'repeater', title: '数据中继器', loadType: 'repeaters', selectable: false },
    // 上面是类型信息，下面是具体的子类

    // 静态数据
    { id: 'static_csv', value: 'static_csv', pId: 'static', title: 'csv', isLeaf: true, selectable: true, icon: tableIcon },

    // 指标系统
    ..._.map(indicesTableMap.keys, dsId => {
      const project = indicesTableDict[dsId]
      return {
        id: project.datasource_id,
        value: project.datasource_id,
        pId: 'indicesTable',
        title: project.name,
        isLeaf: true,
        selectable: true,
        icon: tableIcon
      }
    }),

    // 数据开发中心的连接
    ..._.map(connectionMap.keys, connId => {
      const conn = connDict[connId]
      // 兼容旧版本没有showType的情况
      const type = conn?.showType || conn.type
      if (!validTypesSet.has(type)) {
        return null
      }
      // 为了避免重复，这里的 id 加上前缀
      const uniqId = `connection:${connId}`
      return {
        id: uniqId,
        value: uniqId,
        pId: type,
        title: conn.name,
        loadType: 'dataBase',
        selectable: false,
        icon: connIcon
      }
    }).filter(Boolean),

    // 数据库
    ..._.map(databaseMap.keys, dbId => {
      const db = dbDict[dbId]
      // 为了避免重复，这里的 id 加上前缀
      const uniqId = `dataBase:${dbId}`
      return {
        id: uniqId,
        value: uniqId,
        pId: `connection:${db.connectId}`,
        title: db.dbAlias || db.dbName || db?.schemaName,
        loadType: 'table',
        selectable: false,
        icon: dbIcon
      }
    }),
    // 表
    ..._.map(tableMap.keys, tableId => {
      const table = tableDict[tableId]
      // 为了避免重复，这里的 id 加上前缀
      const uniqId = `table:${tableId}`
      return {
        id: uniqId,
        value: uniqId,
        pId: `dataBase:${table.dbId}`,
        title: table.tableAlias || table.tableName,
        isLeaf: true,
        selectable: true,
        icon: tableIcon
      }
    }),

    // // 接口数据
    // {
    //   id: 'api',
    //   value: 'api',
    //   title: 'API 数据',
    //   loadType: 'api',
    //   isLeaf: false,
    //   selectable: false
    // },
    // ..._.map(apiMap.keys, id => {
    //   const api = apiDict[id]
    //   return {
    //     id,
    //     value: id,
    //     pId: 'api',
    //     loadType: 'api',
    //     title: api.title,
    //     isLeaf: true,
    //     selectable: true,
    //     icon: () => <ApiOutlined style={{ color: '#f56' }} />
    //   }
    // }),


    // 数据集分组
    ..._.map(datasetGroupMap.keys, groupId => {
      const group = datasetGroupMap.entities[groupId]
      return {
        id: groupId,
        value: groupId,
        pId: 'dataset_group',
        title: group?.title,
        selectable: false,
        loadType: 'dataset',
        icon: dbIcon
      }
    }),

    // 数据集
    ..._.map(datasetMap.keys, datasetId => {
      const dataset = datasetMap.entities[datasetId]
      return {
        id: datasetId,
        value: datasetId,
        pId: dataset?.groupId,
        title: dataset?.title,
        isLeaf: true,
        selectable: true,
        icon: tableIcon
      }
    }),

    // 中继器组（当前项目的页面）
    ..._.chain(repeatersMap.entities)
      .groupBy('screenId')
      .map((arr, screenId) => ({
        id: screenId,
        value: screenId,
        pId: 'repeater',
        title: arr[0].screenName,
        isLeaf: false,
        selectable: false,
        icon: dbIcon
      }))
      .value(),
    // 中继器
    ..._.compact(_.map(repeatersMap.keys, repeaterId => {
      const repeaterComp = repeatersMap.entities[repeaterId]
      if (!repeaterComp) {
        return null
      }
      const { key, title, screenId, alias } = repeaterComp
      const k = `${screenId}/${key}`
      return {
        id: k,
        value: k,
        pId: screenId,
        title: alias || title,
        isLeaf: true,
        selectable: true,
        icon: tableIcon
      }
    }))
  ] as DataSourcePickerTreeNode[]

  // 数据 API
  if (_.get(window, 'initialState.dataApiEnable') || window.isDev) {
    tree.push({ id: 'dataApi', value: 'dataApi', title: '数据 API（我的分析）', loadType: 'dataApi', selectable: false })

    tree.push(..._.map(dataApiMap.keys, id => {
      const api = dataApiDict[id]
      return {
        id,
        value: id,
        key: id,
        pId: 'dataApi',
        loadType: 'dataApi',
        title: `${api.title}（${api.sign}）`,
        isLeaf: true,
        selectable: true
      }
    }))
  }

  return tree
}
