import { useLocalStorageState, useSessionStorageState } from 'ahooks'
import { produce } from 'immer'
import _ from 'lodash'
import { nanoid } from 'nanoid'
import React, { useMemo } from 'react'

import { AGG_MODE_TRANSLATE_DICT } from '@/consts/data-source'
import { useDataSourcePicker } from '@/pages/screen/components/workbench/data-config/data-source-picker'
import { useColumnSelectorTree } from '@/pages/screen/components/workbench/data-config/field-binding-panel'
import type { DataSourceInfo, DataSourceInfoType, FieldNode } from '@/types/data-source'
import type { ChartField, ComponentDefineMap } from '@/types/editor-core/component'
import type { ColumnInfo, DataSourceConfig } from '@/types/editor-core/data-source'
import { generateDropData } from '@/utils/editor-core/drop-data'
import { tryJsonParse } from '@/utils/json-utils'


/** 数据筛选器筛选组件选择器 */
function useFilterComponentPicker(componentDefineMap: ComponentDefineMap) {
  const compGroup = useMemo(
    () => _.groupBy(componentDefineMap.entities, def => def.group),
    [componentDefineMap.entities]
  )
  const candidateComponentDict = useMemo(
    () => ({
      number: _.find(compGroup.numPicker, { title: '数值范围' })?.key
        || _.maxBy(compGroup.numPicker, c => c.updatedAt)?.key,
      string: _.find(compGroup.selector, { title: '下拉选择框' })?.key
        || _.maxBy(compGroup.selector, c => c.updatedAt)?.key,
      date: _.find(compGroup.timePicker, { title: '日期选择' })?.key
        || _.maxBy(compGroup.timePicker, c => c.updatedAt)?.key
    }),
    [compGroup]
  )

  const filterCompDict = useMemo(
    () => _.mapValues(candidateComponentDict, compKey => compKey && componentDefineMap.entities[compKey]),
    [componentDefineMap, candidateComponentDict]
  )

  return { filterCompDict }
}

export interface ColumnPickerProps {
  componentDefineMap: ComponentDefineMap
  dataSourcePickerInfo: DataSourceInfo
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id?: string) => Promise<any>
}

/** 简单地根据数据库字段生产单个聚合指标（只适用于 DataCenter 表），参考 treeNodeToColumnInfo */
function simpleConvertFieldColumnToAggColumn(fieldCol: ColumnInfo): ColumnInfo {
  const aggModeForDataCenter = fieldCol.dataType === 'number' ? 'sum' : 'count'

  const name = `_tempMetric_${fieldCol.name}_${nanoid(5)}`
  return {
    ...fieldCol,
    id: name,
    type: 'customAgg',
    dataType: 'number',
    aggMode: aggModeForDataCenter,
    name,
    title: `${fieldCol.title}（${AGG_MODE_TRANSLATE_DICT[aggModeForDataCenter]}）`
  }
}

/** 指标系统列拖拽选择器，用于将列拖拽到数据选择器里 */
export default function TableColumnDraggable(props: ColumnPickerProps) {
  const { dataSourcePickerInfo, loadMoreDataSourceInfo, componentDefineMap } = props
  const [dataSourceConfig, onChange] = useLocalStorageState<DataSourceConfig>('table-column-draggable', {
    defaultValue: () => ({ dataSourceType: 'indicesTable' } as DataSourceConfig)
  })

  const { filterCompDict } = useFilterComponentPicker(componentDefineMap)

  const dataSourcePickerDom = useDataSourcePicker({
    dataSourcePickerInfo,
    loadMoreDataSourceInfo,
    value: dataSourceConfig || ({ dataSourceType: 'indicesTable' } as DataSourceConfig),
    onChange,
    selectOnly: true
  })

  const dsQueryCfg = dataSourceConfig?.[dataSourceConfig?.dataSourceType]
  const { fieldsBinding } = dsQueryCfg || {}

  const mockNumField: ChartField = { name: 'y', type: 'number' }
  const mockStringField: ChartField = { name: 'x', type: 'string' }

  /** 如果是数据中心表，因为没有指标，需要预先构造聚合指标 */
  function presetNumCol(columnInfo: ColumnInfo) {
    onChange({
      dataSourceType: 'dataCenter',
      ...dataSourceConfig,
      dataCenter: {
        ...(dsQueryCfg || {}),
        type: 'dataCenter',
        fieldsBinding: produce(dsQueryCfg?.fieldsBinding || {}, draft => {
          draft.y = simpleConvertFieldColumnToAggColumn(columnInfo)
        })
      }
    })
  }

  const onDragStart = e => {
    const payload = e.target.getAttribute('data-payload')
    const node: Partial<FieldNode> = tryJsonParse(payload)
    if (!node) {
      return
    }
    const dimType = node.dataType
    const compDefine = dimType && filterCompDict[dimType]
    if (!compDefine) {
      return
    }
    const columnInfo: ColumnInfo = {
      type: node.type as 'indicesSpec' | 'indicesDims' | 'field',
      id: `${node.value || node.key}`,
      dataType: node.dataType || (node.type === 'indicesSpec' ? 'number' : 'string'),
      name: node.name!,
      title: `${node.title}`
    }
    // 创建的组件，需要预先设置好名称以便区分
    generateDropData(e, { ...compDefine, title: `${compDefine.title}-${node.title || node.name}` }, !window.isDev)
    e.dataTransfer.setData('text/x-column-info', JSON.stringify(columnInfo))
    e.dataTransfer.setData('text/x-keep-curr-active-elem', '1')

    // 如果是数据中心表，需要预设聚合指标（经过 session 传递信息）
    if (dsQueryCfg!.type === 'dataCenter') {
      presetNumCol(columnInfo)
    }
  }

  // 生成指标列选择树（支持 选择 和 拖拽）
  const dimTitleRender = (node: Partial<FieldNode>) => {
    // TODO 支持指标 having 筛选
    if (node.type !== 'indicesDims' && node.type !== 'field') {
      return node.title
    }
    const payload = JSON.stringify(_.pick(node, ['type', 'key', 'name', 'title', 'dataType']))
    return (
      <span draggable data-payload={payload} onDragStart={onDragStart}>
        {_.isFunction(node.icon) ? node.icon(node) : node.icon}
        <span className='ml-1.5'>{node.title}</span>
      </span>
    )
  }

  const { treeDom: measureTreeDom } = useColumnSelectorTree(
    dataSourcePickerInfo,
    loadMoreDataSourceInfo,
    dsQueryCfg || { type: 'indicesTable' },
    mockNumField.type,
    _.pick(fieldsBinding, mockNumField.name),
    (_colInfos, { col: deltaCol, selected }) => {
      if (!dsQueryCfg) {
        return
      }
      if (selected) {
        loadMoreDataSourceInfo('indicesSpec', deltaCol.id)
      }
      const nextBinding = selected
        ? { ...(fieldsBinding || {}), [mockNumField.name]: deltaCol }
        : _.omit(fieldsBinding, mockNumField.name)
      onChange({
        ...dataSourceConfig,
        [dsQueryCfg.type]: {
          ...(dsQueryCfg || {}),
          fieldsBinding: nextBinding
        }
      })
    },
    { height: 240 },
    false,
    // 不显示同环比口径
    node => !(node.isLeaf && node.type === 'indicesSpec')
  )

  // 生成维度列树（只可用于拖拽）
  const { treeDom: dimTreeDom } = useColumnSelectorTree(
    dataSourcePickerInfo,
    loadMoreDataSourceInfo,
    dsQueryCfg || { type: 'indicesTable' },
    mockStringField.type,
    _.pick(fieldsBinding, mockStringField.name),
    (_colInfos, { col: deltaCol, selected }) => {
      if (!dsQueryCfg) {
        return
      }

      // 基本上是通过拖拽的方式设置的维度列，这块逻辑没啥用
      const nextBinding = selected
        ? { ...(fieldsBinding || {}), [mockStringField.name]: deltaCol }
        : _.omit(fieldsBinding, mockStringField.name)
      onChange({
        ...dataSourceConfig,
        [dsQueryCfg.type]: {
          ...(dsQueryCfg || {}),
          fieldsBinding: nextBinding
        }
      })
    },
    {
      height: dataSourceConfig?.dataSourceType !== 'indicesTable' ? 540 : 300,
      showIcon: false,
      titleRender: dimTitleRender
    }
  )
  // 生成度量列树（只可用于拖拽）
  const { treeDom: metricTreeDom } = useColumnSelectorTree(
    dataSourcePickerInfo,
    loadMoreDataSourceInfo,
    dsQueryCfg || { type: 'indicesTable' },
    mockNumField.type,
    _.pick(fieldsBinding, mockStringField.name),
    (_colInfos, { col: deltaCol, selected }) => {
      if (!dsQueryCfg) {
        return
      }

      // 基本上是通过拖拽的方式设置的维度列，这块逻辑没啥用
      const nextBinding = selected
        ? { ...(fieldsBinding || {}), [mockStringField.name]: deltaCol }
        : _.omit(fieldsBinding, mockStringField.name)
      onChange({
        ...dataSourceConfig,
        [dsQueryCfg.type]: {
          ...(dsQueryCfg || {}),
          fieldsBinding: nextBinding
        }
      })
    },
    {
      height: dataSourceConfig?.dataSourceType !== 'indicesTable' ? 540 : 300,
      showIcon: false,
      titleRender: dimTitleRender
    }
  )
  return (
    <div className='h-full pb-8'>
      {dataSourcePickerDom}
      {dataSourceConfig?.dataSourceType !== 'indicesTable' ? null : (
        <div className='p-2 border-gray-100 border-solid border-0 border-b'>
          <div className='mb-3'>选择指标列</div>
          {measureTreeDom}
        </div>
      )}

      {dataSourceConfig?.dataSourceType === 'indicesTable' ? null : (
        <div className='p-2 border-gray-100 border-solid border-0 border-b'>
          <div className='mb-3'>拖拽度量列</div>
          {metricTreeDom}
        </div>
      )}

      <div className='p-2 border-gray-100 border-solid border-0 border-b'>
        <div className='mb-3'>拖拽维度列</div>
        {dimTreeDom}
      </div>
    </div>
  )
}
