.screen-elements-chart-list {
  overflow-y: auto;
  min-height: 200px;
  width: 100%;

  .chart-list-chunk-panel {
    > .chunk-row {
      display: flex;
      padding: 0 8px;
    }

    .chart-item-two,
    .chart-item-three {
      position: relative;
      display: flex;
      flex-direction: column;
      width: 32%;
      text-align: center;
      // background-color: rgba(#f8f9f9, 0.75);
      // background-image: linear-gradient(to right top, #fff, #f7f7f7);
      border: 1px solid #e9e9ef;
      border-radius: 5px;
      margin: 6px 0 0 6px;
      padding: 8px 4px;
      line-height: 1.2;
      text-align: center;
      min-height: 55px;
      font-size: 14px;

      .anticon {
        margin: 0;
        margin-bottom: 4px;
        font-size: 15px;
        color: #444;
      }
      &:first-child {
        margin-left: 0;
      }
      .thumb {
        width: 60px;
        height: 60px;
        margin: auto;
        margin-bottom: 6px;
        border-radius: 2px;
        border: 1px solid #f4f4f4;
      }

      > span {
        color: #676767;
      }
      &:hover {
        cursor: pointer;
        background-color: var(--tint-color-90);
        background-image: none;

        > .anticon,
        > span {
          color: var(--primary-color) !important;
        }
      }
    }

    .chart-item-two {
      width: 48%;
    }

    .mobile-device {
      position: absolute;
      top: 0;
      right: -4px;
      z-index: 10;
      color: var(--primary-color-80) !important;
      padding: 1px 3px 2px;
      border-radius: 3px;
      font-size: 15px;
    }
  }
  .chart-list-collapse {
    > header {
      padding: 3px 6px;
      cursor: pointer;

      > span {
        font-size: 14px;
      }

      .anticon {
        color: #aaa;
        font-size: 13px;
        margin-right: 5px;
      }
    }
    > div {
      padding-top: 0;
      padding-bottom: 8px;
    }
  }

  .not-data {
    text-align: center;
    color: #777;
    font-size: 14px;
  }
}
