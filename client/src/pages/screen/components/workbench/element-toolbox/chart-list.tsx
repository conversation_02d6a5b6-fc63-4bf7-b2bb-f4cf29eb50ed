import './chart-list.less'

import { CaretDownOutlined } from '@ant-design/icons'
import { useReactive } from 'ahooks'
import isEqual from 'fast-deep-equal'
import _ from 'lodash'
import React, { CSSProperties, memo, useMemo } from 'react'

import Icon from '@/components/icons/iconfont-icon'
import { generateDropData } from '@/utils/editor-core/drop-data'
import { getRootCompGroup } from '@/utils/editor-core/element'


export interface ChartListProps {
  style?: CSSProperties
  type: string
  groupMap: Record<
    string,
    {
      key: string
      title: string
      [key: string]: any
    }
  >
  value: {
    id: string
    name: string
    title?: string
    type: string // 类型
    group?: string | null // 分组
    icon?: string | null // 图标
    thumb?: string | null // 缩略图
  }[]
  onSelect: (item: ChartListProps['value'][number]) => any
}

// 不用 antd 的 Collapse 是因为性能太差了，不好用，太重
function Collapse(props: { title: string; defaultUnfold: boolean; children: any }) {
  const { title, defaultUnfold, children } = props
  const state = useReactive({ unfold: defaultUnfold || false })

  return (
    <div className='chart-list-collapse'>
      <header onClick={() => (state.unfold = !state.unfold)}>
        <CaretDownOutlined rotate={state.unfold ? 0 : -90} />
        <span>{title}</span>
      </header>
      <div style={{ display: state.unfold ? 'block' : 'none' }}>{children}</div>
    </div>
  )
}

/** 拆分结构 */
function ChunkLayout(props: { list: any[]; size: number; renderItem: (data: any) => JSX.Element }) {
  const { list, size, renderItem } = props

  return (
    <div className='chart-list-chunk-panel'>
      {_.chunk(list, size).map((arr, index) => (
        <div key={index} className='chunk-row'>
          {arr.map(renderItem)}
        </div>
      ))}
    </div>
  )
}

/**
 * 元素列表
 * @param props
 */
function ChartList(props: ChartListProps) {
  const { value = [], onSelect, groupMap, style } = props

  const group = useMemo(() => _.groupBy(value, c => getRootCompGroup(c.group)), [value])
  const keys = useMemo(() => _.sortBy(_.keys(group), key => key === 'undefined'), [group])
  // 没有分组
  const isNotGroup = !value.some(i => !!i.group)

  const getTnnerProps = (item: any) => ({
    key: item.id,
    onClick: () => onSelect(item),
    draggable: true,
    onDragStart: e => generateDropData(e, item, !window.isDev)
  })

  const renderIcon = item => {
    if (_.isString(item.icon) && item.icon) return <Icon name={item.icon as any} type={item.icon} />
    if (_.isString(item.thumb) && item.thumb) return <img className='thumb' src={item.thumb} alt='' />
  }

  const renderList = () => (
    <ChunkLayout
      list={value}
      size={3}
      renderItem={item => (
        <div {...getTnnerProps(item)} className='chart-item-three'>
          {renderIcon(item)}
          <span>{item.title || item.name}</span>
        </div>
      )}
    />
  )

  const renderCollapse = () =>
    keys.map((key, index) => (
      <Collapse
        key={key}
        title={`${groupMap[key]?.title || '其他'}（${group[key].length}）`}
        defaultUnfold={index === 0}
      >
        <ChunkLayout
          list={group[key]}
          size={2}
          renderItem={item => (
            <div className='chart-item-two' {...getTnnerProps(item)}>
              {renderIcon(item)}
              <span>{item.title || item.name}</span>

              {
                item.device === 'mobile' && <Icon className='mobile-device' name='移动端' />
                // <div className='mobile-device'>移动端</div>
              }
            </div>
          )}
        />
      </Collapse>
    ))

  return (
    <div className='screen-elements-chart-list' style={style}>
      {value.length === 0 && <div className='not-data'>暂无数据</div>}
      {isNotGroup ? renderList() : renderCollapse()}
    </div>
  )
}

export default memo(ChartList, isEqual)
