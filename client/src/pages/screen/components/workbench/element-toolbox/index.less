// 工具箱
.screen-element-toolbox {
  flex: 1;
  width: var(--screen-left-width);
  background-color: #fff;
  min-height: 100px;
  position: relative;
  height: 100%;

  &.is-fixed {
    box-shadow: 0 0 6px rgba(#111, 0.05);
    border: 1px solid #f4f4f4;
    position: fixed;
    left: 120px;
    top: 360px;
    z-index: 1001;
  }

  .loading-panel {
    margin: 12px auto;
    color: #777;
    text-align: center;
    font-size: 14px;
    .anticon {
      margin-right: 6px;
    }
  }

  .star-component {
    margin: 0;
    padding: 0;
    list-style: none;
    overflow-y: auto;
    height: 80px;
    padding: 6px 12px;
    .anticon-delete {
      display: none;
    }

    > li {
      margin: 1px;
      float: left;
      cursor: pointer;
      border-radius: 3px;
      padding: 0 3px;
      &:hover {
        background-color: #f4f4f4;
      }
    }
  }

  &.has-star {
    .screen-project-toolbox-search-panel {
      box-shadow: 1px 2px 3px rgba(17, 17, 17, 0.06);
      border-bottom: none;
      height: 32px;

      .star {
        margin-bottom: 0;
        flex: 10;
      }
    }

    .my-tabs {
      border-top: 1px solid #f1f1f1;
    }
  }

  .my-tabs {
    .ant-tabs-ink-bar {
      display: block !important;
      transform: scale(0.75);
    }
  }
}
