import './index.less'

import { LoadingOutlined } from '@ant-design/icons'
import { useMemoizedFn, useReactive } from 'ahooks'
import cn from 'classnames'
import isEqual from 'fast-deep-equal'
import _ from 'lodash'
import React, { useEffect, useMemo } from 'react'

import CustomTabs from '@/components/customs/custom-tabs'
import { ELEMENT_GROUP_MAP, ELEMENT_TOOL_TABS, MOBILE_DEVICE } from '@/consts/screen'
import { useModelState as useCoreModelState } from '@/stores/models/editor-core'
import { useModelState as usePageModelState } from '@/stores/models/editor-page'
import type { ComponentDefine } from '@/types/editor-core/component'

import ChartList from './chart-list'
import SearchPanel from './search-panel'
import StarPanel from './star-panel'

export interface ElementToolboxProps {
  keys: string[]
  entities: Record<string, ComponentDefine>
  maxHeight: string
  screenType?: number
  loading?: boolean
}

/**
 * 组件选择器
 */
export default function ElementToolbox(props: ElementToolboxProps) {
  const { keys, entities, maxHeight, screenType, loading } = props

  // 收藏的列表
  const defineMap = useCoreModelState(s => s.componentDefine.entities)
  const starList = usePageModelState(s =>
    _.filter(s.starComponents, (i => defineMap[i.component?.defineKey])),
    isEqual
  )

  const hasStar = !_.isEmpty(starList)
  const $maxHeight = hasStar ? 'calc(100vh - 300px)' : maxHeight

  const state = useReactive({ keyword: '', showMobile: screenType === MOBILE_DEVICE })

  // base 类型的进行排序
  const group = useMemo(() => {
    const list = keys.map(k => entities[k]).filter(i => i.status === 'available')
    // 只展示最新版本，排除名称中含有 WIP 的组件
    const latestVerList = _(list)
      .filter(c => !/wip/i.test(c.title))
      .groupBy(c => c.name)
      .mapValues(arr => _.maxBy(arr, d => d.versionCode || 0))
      .values()
      .compact()
      .orderBy('title', 'desc') // 排序
      .value()
    const obj = _.groupBy(latestVerList, 'category') as Record<string, any[]>
    _.forEach(obj, (val, key) => {
      if (key === 'base') {
        const titleOrderMap = ['矩形', '三角形', '圆形', '文字', '按钮', '竖线', '横线'].reduce((o, v, i) => ({
          ...o,
          [v]: i + 1 // 不能为 0
        }), {}) // 排序
        obj[key] = _(val).map(i => ({ ...i, order: titleOrderMap[i.title] || 999 })).orderBy('order', 'asc').value()
      }
    })
    return obj
  }, [entities, keys])

  const searchAction = title => title.toLocaleLowerCase().indexOf(state.keyword.toLocaleLowerCase()) > -1
  const devices = ['none'].concat(state.showMobile ? ['mobile'] : ['pc', 'screen'])

  const onSelect = useMemoizedFn(item => item)
  const renderContent = tab => (
    <>
      {loading && (
        <div className='loading-panel'>
          <LoadingOutlined />
          <span>加载中...</span>
        </div>
      )}
      <ChartList
        groupMap={ELEMENT_GROUP_MAP}
        type={tab.key}
        onSelect={onSelect}
        style={{ maxHeight: $maxHeight }}
        // [{ id: 'a', type: 'a', icon: '收藏', title: '6666' },]
        value={_.filter(group[tab.key], i => {
          let flag = i.device ? devices.includes(i.device) : true
          if (i.category === 'base' || i.category === 'other') flag = true
          if (state.keyword) flag = flag && searchAction(i.title)
          return flag
        })}
      />
    </>
  )

  useEffect(() => {
    state.showMobile = screenType === MOBILE_DEVICE
  }, [screenType])

  return (
    <div className={cn({
      'screen-element-toolbox': true,
      'has-star': hasStar
    })}>
      <SearchPanel
        showStar={hasStar}
        starTitle='我的收藏'
        keyword={state.keyword}
        onKeywordChange={val => (state.keyword = val)}
        showMobile={state.showMobile}
        onDeviceChange={showMobile => (state.showMobile = showMobile)}
      />

      <StarPanel starList={starList} defineMap={defineMap} />
      <CustomTabs
        className='fix-element-tabs-style my-tabs'
        animated={false}
        options={ELEMENT_TOOL_TABS.map(i => ({ ...i, tabProps: { forceRender: true } }))}
        renderContent={renderContent}
      />
    </div>
  )
}
