import '../project-toolbox/search-panel.less'

import { useReactive } from 'ahooks'
import { Input, Tooltip } from 'antd'
import React from 'react'

import Icon from '@/components/icons/iconfont-icon'

export interface SearchPanelProps {
  keyword: string
  showMobile?: boolean
  onKeywordChange: (keyword: string) => any
  onDeviceChange?: (showMobile: boolean) => any
  showStar?: boolean
  starTitle?: string
}

/**
 * 搜索栏
 */
export default function SearchPanel(props: SearchPanelProps) {
  const { keyword, onKeywordChange, showMobile, onDeviceChange, showStar, starTitle } = props
  const state = useReactive({ isSearch: false })

  return (
    <div className='screen-project-toolbox-search-panel'>
      {showStar && state.isSearch === false &&
        <h4 className='star'>{starTitle}</h4>
      }
      {state.isSearch === true && (
        <div className='flex items-center flex-1'>
          <Icon name='返回' pointer onClick={() => (state.isSearch = false)} />
          <Input
            placeholder='关键词搜索'
            value={keyword}
            className='search-input'
            maxLength={30}
            onChange={e => onKeywordChange(e.target.value)}
            autoFocus
            size='small'
            prefix={<Icon name='搜索' size={14} />}
          />
        </div>
      )}

      {state.isSearch === false && (
        <div className='flex items-center flex-1'>
          {/* <Icon name='商城' pointer title='商城' /> */}

          <Tooltip title='切换图表设备类型' placement='left'>
            {showMobile ? (
              <Icon name='移动端' pointer title='移动端' onClick={() => onDeviceChange?.(false)} />
            ) : (
              <Icon name='电脑' pointer title='桌面端' onClick={() => onDeviceChange?.(true)} />
            )}
          </Tooltip>
          <div className='flex flex-1' />
          <Icon name='搜索' title='搜索' pointer onClick={() => (state.isSearch = true)} />
        </div>
      )}
    </div>
  )
}
