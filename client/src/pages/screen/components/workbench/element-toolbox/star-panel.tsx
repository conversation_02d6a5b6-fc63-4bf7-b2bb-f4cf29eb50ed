import { DeleteOutlined } from '@ant-design/icons'
import { <PERSON><PERSON>, Toolt<PERSON> } from 'antd'
import dayjs from 'dayjs'
import _ from 'lodash'
import React from 'react'

import Icon from '@/components/icons/iconfont-icon'
import { useCommit as useCoreCommit } from '@/stores/models/editor-core'
import { useCommit as usePageCommit } from '@/stores/models/editor-page'
import { useModelState as useWorkbenchModelState } from '@/stores/models/workbench-config'
import { parseStarComponentCreate } from '@/utils/editor-core/drop-data'

/**
 * 我的收藏组件
 */
export default function StarPanel(props) {
  const { starList = [], defineMap } = props
  const coreCommit = useCoreCommit()
  const pageCommit = usePageCommit()
  const zoom = useWorkbenchModelState(s => s.canvas.zoom)
  // 收藏的列表

  const onDragEnd = (e, item) => {
    const params = parseStarComponentCreate({
      component: item,
      event: e,
      zoom,
      layout: document.querySelector('#abi-canvas')!
    })
    coreCommit('createComponent', params)
  }

  if (_.isEmpty(starList)) return null

  return (
    <ul className='star-component'>
      {_.filter<any>(starList, (i => defineMap[i.component?.defineKey]))
        .map(({ component, key, time }, index) => (
          <Tooltip placement='top' title={
            <div style={{ textAlign: 'center', width: 190 }}>
              <span>收藏于 {dayjs(time).format('YYYY-MM-DD HH:mm:ss')}</span>
              <Button
                type='primary'
                size='small'
                icon={<DeleteOutlined />}
                onClick={e => {
                  e.stopPropagation()
                  pageCommit('asyncRemoveStarComponent', key)
                }}
              >
                移除
              </Button>
            </div>
          }>
            <li
              key={key + index}
              draggable
              onDragEnd={e => onDragEnd(e, component)}
            >
              {defineMap[component.defineKey].icon &&
                <Icon name={defineMap[component.defineKey].icon} />
              }
              <span className='ml-1'>{component?.alias || component?.title}</span>
            </li>
          </Tooltip>
        ))
      }
    </ul>
  )
}
