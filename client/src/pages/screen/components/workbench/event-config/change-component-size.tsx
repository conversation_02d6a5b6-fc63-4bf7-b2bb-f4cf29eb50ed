import React from 'react'

import type { Action } from '@/cores/evaction/types/action.d'

import { useConfigChange } from './hooks/use-action'
import { BaseStyleSetting } from './modals/base-style-setting'
import { SettingModal } from './modals/setting-modal'
import type { ComponentBaseProps } from './type'

export type ChangeComponentSizePanelProps = ComponentBaseProps<Action.ChangeComponentSize>

/**
 * 改变组件的样式
 * @param props
 */
export default function ChangeComponentSizePanel(props: ChangeComponentSizePanelProps) {
  const { componentKey, value, onChange, componentMap, componentDefineMap } = props
  // value 里的 componentKey 是目标组件

  const { modalRef } = useConfigChange({
    value,
    onChange,
    onSaveData: data => ({
      isTrigger: data.isTrigger,
      style: data.value,
      isMerge: false,
      enableMap: data.enableMap || {}
    })
  })

  return (
    <SettingModal
      ref={modalRef}
      title='改变组件的大小位置'
      initData={value}
      selfComponentKey={componentKey} // 本身的组件 key
      componentMap={componentMap}
      componentDefineMap={componentDefineMap}
      schemaType='style'
      renderContent={(state, update) => (
        <BaseStyleSetting
          value={state.value}
          onChange={val => update({ value: val })}
          enableMap={state.enableMap}
          onEnableChange={val => update({ enableMap: val })}
        />
      )}
    />
  )
}
