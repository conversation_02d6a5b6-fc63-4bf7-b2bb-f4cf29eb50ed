import isEqual from 'fast-deep-equal'
import _ from 'lodash'
import React, { memo } from 'react'

import { FormSchema } from '@/components/form-schema'
import type { Action } from '@/cores/evaction/types/action.d'

import { useConfigChange } from './hooks/use-action'
import { SettingModal } from './modals/setting-modal'
import type { ComponentBaseProps } from './type'

export type ChangeComponentStylePanelProps = ComponentBaseProps<Action.ChangeComponentStyle>

const FormSchemaMemo = memo(FormSchema, isEqual)
/**
 * 改变组件的样式
 * @param props
 */
export default function ChangeComponentStylePanel(props: ChangeComponentStylePanelProps) {
  const { componentKey, value, onChange, componentMap, componentDefineMap } = props
  // value 里的 componentKey 是目标组件

  const { modalRef } = useConfigChange({
    value,
    onChange,
    onSaveData: data => ({
      isTrigger: data.isTrigger,
      style: data.value,
      isMerge: false
    })
  })

  return (
    <SettingModal
      ref={modalRef}
      title='改变组件的样式'
      initData={value}
      selfComponentKey={componentKey} // 本身的组件 key
      componentMap={componentMap}
      componentDefineMap={componentDefineMap}
      schemaType='style'
      renderContent={(state, update) => {
        const schema = _.mapValues(state.schema, i => ({ ...i, unfold: true }))
        return (
          <FormSchemaMemo value={state.value} schema={schema} onChange={val => update({ value: val })} />
        )
      }}
    />
  )
}
