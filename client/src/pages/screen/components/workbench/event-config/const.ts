import React, { lazy } from 'react'

import { ComponentBaseProps } from '@/pages/screen/components/workbench/event-config/type'
import type { ActionName } from '@/types/editor-core/events'


/**
 * 行为配置面板映射字典，{ 行为名: 配置组件 }
 * 如果配置比较简单可以不用代码实现，直接到 client/src/consts/define.ts 配置动作参数 schema
 */
export const ActionConfigPanelDict: Record<ActionName, React.ComponentType<ComponentBaseProps<any>>> = {
  openUrl: lazy(() => import('./open-url-config-panel')),
  setVisibility: lazy(() => import('./set-visibility-config-panel')),
  gotoComponent: lazy(() => import('./goto-component')),
  changeComponentConfig: lazy(() => import('./change-component-config')),
  changeComponentStyle: lazy(() => import('./change-component-style')),
  changeComponentSize: lazy(() => import('./change-component-size')),
  changeComponentDataSource: lazy(() => import('./change-component-data-source')),
  customActionAdapt: lazy(() => import('./custom-action-adapt-panel')), // 这个面板解析动作参数 schema
  setExternalFilter: lazy(() => import('./set-external-filter')),
  triggerEvent: lazy(() => import('./trigger-event-config-panel')),
  triggerAction: lazy(() => import('./trigger-action-config-panel')),
  printPage: lazy(() => import('./print-page')),
  timedRefresh: lazy(() => import('./timed-refresh'))
}

/** 单个事件配置对象 */
export interface EventActionItemProps {
  // 事件名
  eventName: string
  // 动作
  actions: Array<any>
}
