import { CodeOutlined } from '@ant-design/icons'
import { Button, InputNumber, Select, Switch } from 'antd'
import _ from 'lodash'
import React, { useMemo } from 'react'

import DebounceInput from '@/components/debounce-input'
import { useCodeEditorModal } from '@/pages/screen/components/workbench/data-config/code-editor-for-workbench'
import { enableSelectSearch } from '@/utils'

import { CustomActionAdaptPanelPanelProps } from './index'


export interface CustomActionAdaptPanelPanelParamsInputProps extends CustomActionAdaptPanelPanelProps {
  limit?: number
  optionsFilter?: any
  value: any
  onChange: (next: any) => any
  defaultValue?: any
}

/**
 * 自定义行为面板输入控件字典
 * 支持类型: 'componentKey' | 'pageKey' | 'dataSourceId' | 'dimName' | 'specId' | 'expr' | 'text' | 'number' | 'bool' | 'code'
 */
export const PARAMS_INPUT_TYPE_DICT = {
  componentKey: function ComponentKeyInput(props: CustomActionAdaptPanelPanelParamsInputProps) {
    const { value, onChange, componentKey, componentMap, limit, optionsFilter } = props

    const validComponents = useMemo(() => {
      const allComponentDefines1 = _.values(componentMap.entities)
      return _.orderBy(allComponentDefines1, d => (d.key === componentKey ? 0 : 1))
    }, [])

    return (
      <Select
        getPopupContainer={triggerNode => triggerNode.parentNode}
        placeholder='请选择控件'
        value={value}
        onChange={onChange}
        mode={limit === 1 ? undefined : 'multiple'}
        {...enableSelectSearch}
        dropdownMatchSelectWidth={false}
      >
        {_.map(validComponents, comp => (
          <Select.Option key={comp.key}>{comp.alias || comp.title}</Select.Option>
        ))}
      </Select>
    )
  },
  pageKey: function PagePicker(props: CustomActionAdaptPanelPanelParamsInputProps) {
    const { value, onChange, pageNodes } = props

    return (
      <Select
        placeholder='请选择页面'
        value={value}
        onChange={onChange}
        {...enableSelectSearch}
        options={_.map(pageNodes, page => ({ label: page.title, value: page.id }))}
        dropdownMatchSelectWidth={false}
      />
    )
  },
  bool: function BoolInput(props: CustomActionAdaptPanelPanelParamsInputProps) {
    const { value, onChange } = props
    return <Switch checkedChildren='是' unCheckedChildren='否' checked={value} onChange={onChange} />
  },
  number: function NumberInput(props: CustomActionAdaptPanelPanelParamsInputProps) {
    const { value, onChange } = props
    return <DebounceInput Component={InputNumber} value={value} onChange={onChange} />
  },
  text: function TextInput(props: CustomActionAdaptPanelPanelParamsInputProps) {
    const { value, onChange } = props
    return <DebounceInput value={value} onChange={onChange} />
  },
  code: function Code(props: CustomActionAdaptPanelPanelParamsInputProps) {
    const { value, onChange, defaultValue } = props
    const { showModal: showPreProcessCodeEditModal, modalDom: codeEditorModal } = useCodeEditorModal({
      title: '数据预处理配置',
      value,
      onChange,
      defaultValue
    })

    return (
      <>
        <Button
          className='w-full'
          onClick={showPreProcessCodeEditModal}
          type={value ? 'primary' : 'default'}
          icon={<CodeOutlined />}
        >
          编辑处理逻辑
        </Button>
        {codeEditorModal}
      </>
    )
  }
}

/** 自定义行为面板已选项格式化 */
export const PARAMS_VALUE_FORMATTER = {
  componentKey(props: CustomActionAdaptPanelPanelParamsInputProps, _paramKey: string, value: string | string[]) {
    const { componentMap } = props
    const values = _.castArray(value)
    const comp = _.map(values, v => {
      const com = componentMap.entities[v]
      return com?.alias || com?.title
    }).join(', ')

    return comp
  },
  pageKey(props: CustomActionAdaptPanelPanelParamsInputProps, _paramKey: string, value: string) {
    const { pageNodes } = props
    const page = _.find(pageNodes, p => p.id === value)
    return page?.title
  },
  bool(_props: CustomActionAdaptPanelPanelParamsInputProps, _paramKey: string, value: boolean) {
    return value ? '是' : '否'
  },
  number(_props: CustomActionAdaptPanelPanelParamsInputProps, _paramKey: string, value: number) {
    return _.truncate(`${value}`, { length: 10 })
  },
  text(_props: CustomActionAdaptPanelPanelParamsInputProps, _paramKey: string, value: string) {
    return _.truncate(value, { length: 10 })
  },
  code(_props: CustomActionAdaptPanelPanelParamsInputProps, _paramKey: string, value: string) {
    return value ? '代码省略' : '未配置代码'
  }
}
