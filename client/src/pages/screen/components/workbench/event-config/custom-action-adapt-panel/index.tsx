import { Button, Form } from 'antd'
import _ from 'lodash'
import React, { useMemo } from 'react'

import { DefaultActionDefines } from '@/consts/define'
import type { ComponentDefineMap, ComponentKey, ComponentMap } from '@/types/editor-core/component'
import { ActionConfig, ActionDefineInfo } from '@/types/editor-core/events'
import { EditorPage } from '@/types/editor-page'

import { PARAMS_INPUT_TYPE_DICT, PARAMS_VALUE_FORMATTER } from './const'

/** 自定义行为配置信息定义 */
export type CustomActionConfig = ActionConfig

export interface CustomActionAdaptPanelPanelProps {
  componentKey: ComponentKey
  componentMap: ComponentMap
  componentDefineMap: ComponentDefineMap
  pageNodes: EditorPage['directory']['nodes']
  value: CustomActionConfig
  onChange: (config: CustomActionConfig | null) => any
}

/**
 * 自定义行为配置适配面板
 * 需要解析 actionDefine.actionName.params 来实现动态表单配置
 */
export default function CustomActionAdaptPanel(props: CustomActionAdaptPanelPanelProps) {
  const { componentKey, componentMap, componentDefineMap, pageNodes, value, onChange } = props
  const [form] = Form.useForm()

  const { name, paramKeys, params } = useMemo<ActionDefineInfo>(() => {
    const defineKey = componentMap.entities[componentKey]?.defineKey
    const customActDef = componentDefineMap.entities[defineKey]?.eventActionDefine?.actionDefine?.[value?.actionName]
    return customActDef || DefaultActionDefines.actions[value?.actionName] || { name: '', title: '' }
  }, [componentMap, componentKey, componentDefineMap, value?.actionName])

  if (!name) {
    return (
      <div className='text-center text-gray-500 py-4'>
        未定义动作 {value?.actionName} 或定义被删除，
        <Button type='link' onClick={() => onChange(null)}>
          知道了
        </Button>
      </div>
    )
  }
  const inputProps = _.omit(props, ['value', 'onChange'])
  return (
    <Form
      layout='vertical'
      form={form}
      initialValues={_.defaults({}, value)}
      className='border border-solid rounded border-gray-300 !px-4 !py-2'
    >
      {_.isEmpty(paramKeys) || value.notParams ? (
        <div className='text-center text-gray-500 py-4'>无需参数设置</div>
      ) : (
        _.map(paramKeys, paramKey => {
          const {
            title, type, limit, optionsFilter,
            tooltip, defaultValue
          } = params?.[paramKey] || {}

          const Comp = type && PARAMS_INPUT_TYPE_DICT[type]
          if (!Comp) {
            return null
          }
          return (
            <Form.Item label={title} name={paramKey} key={paramKey} tooltip={tooltip}>
              <Comp {...inputProps} limit={limit} optionsFilter={optionsFilter} defaultValue={defaultValue} />
            </Form.Item>
          )
        })
      )}

      <Form.Item>
        <Button
          type='primary'
          className='mr-4'
          onClick={() => {
            const nextParams = form.getFieldsValue()
            const descArr = _.map(paramKeys, k => {
              const param = params?.[k]
              return param && `${param.title}: ${PARAMS_VALUE_FORMATTER[param.type]?.(props, k, nextParams[k])}`
            }).filter(_.identity)
            onChange({
              ..._.omit(value, 'editing'),
              ...nextParams,
              description: descArr.join('\n') || '无参数'
            })
          }}
        >
          保存
        </Button>
        <Button type='ghost' onClick={() => onChange(null)}>
          取消
        </Button>
      </Form.Item>
    </Form>
  )
}
