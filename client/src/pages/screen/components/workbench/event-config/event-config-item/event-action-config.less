.event-config-action-item {
  display: flex;
  width: 100%;
  padding: 6px 16px;
  position: relative;

  &:hover {
    background-color: var(--primary-color-10);
    .event-config-item-head-extra {
      display: block;
      width: 45px;
    }
  }

  .event-config-item-head-extra {
    position: absolute;
    right: 18px;
    top: 7px;
    display: none;
    flex-shrink: 0;
  }

  .action-item-content {
    color: var(--primary-color);
    cursor: pointer;
    // display: flex;
    // align-items: center;
    overflow: hidden;
    // white-space: nowrap;
    font-size: 14px;
    // text-overflow: ellipsis;

    .anticon {
      margin-right: 3px;
    }
  }

  .event-config-action-left {
    flex: 1;
  }
}
