import './event-action-config.less'

import { ApiOutlined, DeleteOutlined, SettingOutlined } from '@ant-design/icons'
import { Button, Col, Popconfirm, Row } from 'antd'
import _ from 'lodash'
import React, { useMemo } from 'react'

import type { Component } from '@/types/editor-core/component'
import { DataFilterCondition } from '@/types/editor-core/data-source'
import type { ActionConfig, ActionName, ActionParamEditStatus, EventName } from '@/types/editor-core/events'

import EventDropDown, { EventDropDownOptionItem } from './event-drop-down'


export interface ActionConfigItemProps extends ActionConfig {
  // 事件名
  eventName: EventName
  // 动作列表
  actionList: EventDropDownOptionItem[]
  // 动作名
  actionName: ActionName
  editing: ActionParamEditStatus
  // 下标
  index: number
  // 是否是回旋模式
  isTrigger?: boolean
  // 切换动作
  onChangeAction: (newAction: ActionName, index: number, currentEvent: EventName) => any
  // 配置参数
  onSettingActionParam: (currentEvent: EventName, index: number) => any
  // 删除动作
  onDeleteAction: (eventName: EventName, actionIndex: number) => any
  // 显示动作配置面板
  onShowActionSettingPanel?: (eventName: EventName, actionIndex: number) => any
  // 动作参数配置器
  renderActionEditor?: React.ReactNode

  triggerComponentKey?: string | string[]
  getComponent?: (key: string) => Component

  precondition?: DataFilterCondition[]
}

/**
 *  动作配置项
 * @param {ActionConfigItemProps} props
 */
function ActionConfigItem(props: ActionConfigItemProps) {
  const {
    eventName,
    actionName,
    actionList = [],
    description = '',
    editing,
    isTrigger = false,
    index = -1,
    onChangeAction,
    onSettingActionParam,
    onDeleteAction,
    onShowActionSettingPanel,
    renderActionEditor,
    triggerComponentKey,
    getComponent,
    precondition
  } = props

  const handleChangeAction = (actName: ActionName) => onChangeAction?.(actName, index, eventName)
  // 动作参数配置
  const handleActionParamSetting = () => {
    onSettingActionParam?.(eventName, index)
  }
  // 删除动作
  const handleDeleteAction = () => onDeleteAction?.(eventName, index)
  // 显示动作配置面板
  const handleShowActionSettingPanel = () => onShowActionSettingPanel?.(eventName, index)

  const components = useMemo(
    () => _.compact(_.castArray(triggerComponentKey).map(c => getComponent?.(c!))),
    [triggerComponentKey, getComponent])

  const renderDescription = () => {
    if (editing === 'creating') return <Button type='dashed' size='small' danger>请配置参数</Button>
    if (triggerComponentKey && _.isEmpty(components)) return <span style={{ color: '#f45' }}>组件已删除，请重新设置</span>
    if (isTrigger) return <><ApiOutlined /> {description}</>
    return description
  }

  return (
    <>
      <div className='event-config-action-item'>
        <div className='event-config-action-left'>
          <EventDropDown value={actionName || ''} option={actionList} onChange={handleChangeAction} />
          <div
            className='action-item-content inline-block align-top'
            title={description}
            onClick={handleActionParamSetting}
          >
            {renderDescription()}
          </div>
        </div>

        <div className='event-config-item-head-extra'>
          <Row>
            <Col className='cursor-pointer' onClick={handleShowActionSettingPanel}>
              <SettingOutlined className={_.isEmpty(precondition) ? '' : 'text-primary'} />
            </Col>
            <Col className='ml-[16px] cursor-pointer'>
              <Popconfirm title='确认删除此动作？' onConfirm={handleDeleteAction} okText='确认' cancelText='取消'>
                <DeleteOutlined />
              </Popconfirm>
            </Col>
          </Row>
        </div>
      </div>

      {editing === 'editing' && renderActionEditor && (
        <div className='px-[12px] pb-[12px]'>{renderActionEditor}</div>
      )}
    </>
  )
}

export default ActionConfigItem
