@import '~@/styles/mixin/index.less';

.event-drop-down {
  .dark-drop-down();

  .custom-disabled {
    cursor: pointer;
  }

  &-item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &-item-text {
    flex: 1;
    white-space: normal;
  }

  &-item-icon {
    flex-shrink: 0;
  }

  &-trigger {
    // display: inline-block; 如果下面的描述较短，不应该合并行
    cursor: pointer;
    &:hover,
    &:focus {
      background: rgba(0, 0, 0, 0.018);
    }

    .icon-container {
      margin-right: 7px;
      display: inline-block;
    }
  }

  &-disabled {
    cursor: not-allowed;
  }
}
