import './event-drop-down.less'

import { CheckOutlined, DownOutlined, MoreOutlined } from '@ant-design/icons'
import { Dropdown,Menu } from 'antd'
import classNames from 'classnames'
import _ from 'lodash'
import React, { useState } from 'react'

import { ActionName } from '@/types/editor-core/events'

export type EventDropDownOptionItem = {
  // 图标
  icon?: string | React.ReactNode
  // 标题
  label: string
  // 值
  value: any
  // 是否可用
  disabled?: boolean
}

export interface EventDropDownProps {
  value?: any
  // 是否禁用
  disabled?: boolean
  // 选项数据
  option: EventDropDownOptionItem[]
  // 默认显示选项条数
  defaultShowOptionLength?: number
  onChange?: (data: ActionName) => any
}

interface RenderMenuItemProps extends EventDropDownOptionItem {
  // 选中值
  selectedKeys: any
}

/**
 * 渲染下拉菜单项
 * @param {RenderMenuItemProps} props
 * @returns
 */
function renderMenuItem(props: RenderMenuItemProps) {
  const { selectedKeys, value, label, icon, disabled } = props
  return (
    <Menu.Item key={value} icon={icon || null} disabled={disabled}>
      <div className='event-drop-down-item-content'>
        <span className='event-drop-down-item-text'>{label}</span>
        {selectedKeys === value && <CheckOutlined className='event-drop-down-item-icon' />}
      </div>
    </Menu.Item>
  )
}

/**
 * 获取显示标题
 * @param value 选中的value值
 * @param option 选项列表
 * @returns
 */
const getValueObj = (value: any, option: Array<EventDropDownOptionItem>): EventDropDownOptionItem => {
  if (_.isNil(value) || value === '') return { icon: null, label: '请选择' } as EventDropDownOptionItem
  return (_.find(option, v => v?.value === value) || { icon: '', label: '' }) as EventDropDownOptionItem
}

/**
 * 自定义下拉选择器
 * @param {EventDropDownProps} props
 */
function EventDropDown(props: EventDropDownProps) {
  const { value = null, defaultShowOptionLength = 6, disabled = false, option = [], onChange } = props
  const [isShowMore, setIsShowMore] = useState(false)
  const showOptionLengh = isShowMore ? option.length : defaultShowOptionLength

  const handleClickMenu = e => {
    if (e.key === value) return
    onChange?.(e.key)
  }

  const handleMore = () => {
    setIsShowMore(v => !v)
  }

  const menu = (
    <Menu theme='dark' selectedKeys={[value]} onClick={handleClickMenu}>
      {option.slice(0, showOptionLengh).map(v => renderMenuItem({ ...v, selectedKeys: value }))}
      {option.length > defaultShowOptionLength ? (
        <>
          <Menu.Divider />
          <Menu.Item key='more' disabled icon={<MoreOutlined rotate={90} />} className='custom-disabled'>
            <div className='event-drop-down-item-content' onClick={handleMore}>
              {isShowMore ? '收起' : '更多'}
            </div>
          </Menu.Item>
        </>
      ) : null}
    </Menu>
  )

  const { icon, label } = getValueObj(value as string, option)

  return (
    <Dropdown overlay={menu} trigger={['click']} disabled={disabled} overlayClassName='event-drop-down'>
      <div className={classNames('event-drop-down-trigger', { 'event-drop-down-disabled': disabled })}>
        {icon ? <div className='icon-container'>{icon}</div> : null}

        <span>{label || '请选择'}</span>
        <DownOutlined className='ml-[16px]' />
      </div>
    </Dropdown>
  )
}

export default EventDropDown
