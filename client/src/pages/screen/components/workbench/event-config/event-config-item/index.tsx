/**
 * @description 交互组件项
 */

import './index.less'

import { DeleteOutlined, PlusCircleOutlined, SettingOutlined } from '@ant-design/icons'
import { <PERSON><PERSON>, Col, Popconfirm, Row } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React from 'react'

import type { Component } from '@/types/editor-core/component'
import type {
  ActionConfig,
  ActionName,
  ActionParamEditStatus,
  EventName,
  EventSettings
} from '@/types/editor-core/events'

import ActionConfigItem from './event-action-config'
import EventDropDown, { EventDropDownOptionItem } from './event-drop-down'

export type EventActionListProps = EventDropDownOptionItem[]

export interface EventConfigItemProps {
  // 数据源
  selectedEvent: EventName
  // 事件列表
  eventList: EventActionListProps
  // 动作列表
  actionList: EventActionListProps
  // 动作配置列表
  actionConfigs: ActionConfig[]
  // 事件配置
  eventSettings?: EventSettings
  // 新增动作
  onAddNewAction: (currentEventName: string, currentActionConfigs?: ActionConfig[]) => any
  // 切换事件名
  onChangeEvent: (newEventName: EventName, oldEventName: EventName) => any
  // 切换动作
  onChangeAction: (newAction: ActionName, index: number, currentEvent: EventName) => any
  // 配置参数
  onSettingActionParam: (currentEvent: EventName, index: number) => any
  // 删除事件
  onDeleteEvent: (eventName: EventName) => any
  // 删除动作
  onDeleteAction: (eventName: EventName, actionIndex: number) => any
  // 显示事件配置面板
  onShowEventSettingModal?: (eventName: EventName) => any
  // 显示动作配置面板
  onShowActionSettingPanel?: (eventName: EventName, actionIndex: number) => any
  // 动作参数配置器
  renderActionEditor: (currentEventName: EventName, actionConfig: ActionConfig, index: number) => React.ReactNode
  // 获取组件信息
  getComponent?: (key: string) => Component
}

// 事件配置
function EventConfigItem(props: EventConfigItemProps) {
  const {
    selectedEvent,
    eventList = [],
    actionList = [],
    actionConfigs = [],
    eventSettings,
    onAddNewAction,
    onChangeEvent,
    onChangeAction,
    onSettingActionParam,
    // 删除事件
    onDeleteEvent,
    // 删除动作
    onDeleteAction,
    onShowEventSettingModal,
    onShowActionSettingPanel,
    renderActionEditor,
    getComponent
  } = props

  // 添加新动作
  const handleClickNewAction = () => onAddNewAction?.(selectedEvent, actionConfigs)
  // 切换事件
  const handleChangeEvent = (eventName: string) => onChangeEvent?.(eventName, selectedEvent)
  // 删除事件
  const handleDeleteEvent = () => onDeleteEvent?.(selectedEvent)
  // 显示事件配置面板
  const handleShowEventSettingModal = () => onShowEventSettingModal?.(selectedEvent)

  return (
    <div className='event-config-item-wrap'>
      <div className='event-config-item-header-wrap'>
        <div className='event-config-item-title'>
          <EventDropDown value={selectedEvent} option={eventList} onChange={handleChangeEvent} />
        </div>

        <div className='event-config-item-head-extra'>
          <Row>
            <Col
              className={cn('icon-ml cursor-pointer', { 'text-primary': _.some(eventSettings?.precondition) })}
              onClick={handleShowEventSettingModal}
            >
              <SettingOutlined />
            </Col>

            <Col className='icon-ml cursor-pointer'>
              <Popconfirm title='确认删除此事件？' onConfirm={handleDeleteEvent} okText='确认' cancelText='取消'>
                <DeleteOutlined />
              </Popconfirm>
            </Col>
          </Row>
        </div>
      </div>

      <div className='event-config-item-body'>
        {actionConfigs.map((v, idx) => (
          <ActionConfigItem
            key={`actionKey_${idx}`}
            eventName={selectedEvent}
            actionName={v.actionName}
            isTrigger={v.isTrigger}
            index={idx}
            editing={v.editing as ActionParamEditStatus}
            precondition={v.precondition}
            description={v.description}
            triggerComponentKey={(v as any).componentKey}
            actionList={actionList}
            onChangeAction={onChangeAction}
            onSettingActionParam={onSettingActionParam}
            onDeleteAction={onDeleteAction}
            onShowActionSettingPanel={onShowActionSettingPanel}
            renderActionEditor={renderActionEditor?.(selectedEvent, v, idx)}
            getComponent={getComponent}
          />
        ))}

        <div className='btn-add-action-box'>
          <Button
            type='link'
            disabled={_.some(actionConfigs, v => v.editing === 'creating' || v.editing === 'editing')}
            icon={<PlusCircleOutlined />}
            onClick={handleClickNewAction}
          >
            添加新动作
          </Button>
        </div>
      </div>
    </div>
  )
}

export default EventConfigItem
