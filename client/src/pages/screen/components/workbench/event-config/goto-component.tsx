import _ from 'lodash'
import React from 'react'

import DebounceInput from '@/components/debounce-input'
import type { Action } from '@/cores/evaction/types/action.d'
import { getElementTransformXY } from '@/utils/dom'

import { useConfigChange } from './hooks/use-action'
import { SettingModal } from './modals/setting-modal'
import type { ComponentBaseProps } from './type'

export type ConfigPanelProps = ComponentBaseProps<Action.GotoComponent>

/**
 * 去到组件
 * @param props
 */
export default function GotoComponentConfigPanel(props: ConfigPanelProps) {
  const { componentKey, value, onChange, componentMap, componentDefineMap } = props

  const { modalRef } = useConfigChange({
    value,
    onChange,
    onSaveData: data => ({ offset: data.value.offset })
  })

  return (
    <SettingModal
      ref={modalRef}
      title='滚动到某组件位置'
      initData={value}
      selfComponentKey={componentKey} // 本身的组件 key
      componentMap={componentMap}
      componentDefineMap={componentDefineMap}
      schemaType='gotoComponent'
      showTrigger={false}
      renderContent={(state, update) => {
        let offset = _.parseInt(state.value.offset || 0)
        const { y: componentTop = 0 } = getElementTransformXY(state.component?.style)
        if (_.isNaN(offset)) offset = 0
        return (
          <div className='goto-component-content'>
            <div className='tip'>实际值 = 目标值 + 偏移量 = {componentTop + offset}px</div>
            <div className='tip'>目标值：{componentTop}px</div>
            <DebounceInput
              prefix='偏移量：'
              placeholder='输入顶偏移量'
              suffix='px'
              value={offset.toString()}
              onChange={e => update({ value: { ...state.value, offset: e.target.value } })}
              style={{ width: 200 }}
            />
          </div>
        )
      }}
    />
  )
}
