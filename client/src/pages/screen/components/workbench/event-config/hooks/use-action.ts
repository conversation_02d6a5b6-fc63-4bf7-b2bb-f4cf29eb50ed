import { useDeepCompareEffect } from 'ahooks'
import _ from 'lodash'
import { useRef } from 'react'

interface Params {
  onChange: any
  value: any
  onSaveData: (data: any) => any
}

export const useConfigChange = ({ onChange, value, onSaveData }: Params) => {
  const modalRef = useRef<{ show: Function; hide: Function }>(null)

  const onSubmit = data => {
    onChange({
      ..._.omit(value, 'editing'),
      componentKey: data.selectedKey,
      description: data.componentName,
      ...onSaveData?.(data)
      // offset: data.value.offset
    })
  }

  useDeepCompareEffect(() => {
    if (value.editing === 'editing') {
      modalRef.current?.show({
        onCancel: () => onChange(null),
        onOk: onSubmit
      })
    }
  }, [value.componentKey, value.editing])

  return { onSubmit, modalRef }
}
