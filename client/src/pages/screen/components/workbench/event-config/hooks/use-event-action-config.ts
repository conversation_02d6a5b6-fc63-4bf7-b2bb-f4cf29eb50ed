import { produce } from 'immer'
import { klona as cloneDeep } from 'klona/lite'
import _ from 'lodash'
import { useCallback, useState } from 'react'

import { EventActionListProps } from '@/pages/screen/components/workbench/event-config/event-config-item'
import {
  ActionConfig,
  ActionDefine,
  ActionName,
  EventHandleActionMap,
  EventName,
  EventsDefine
} from '@/types/editor-core/events'

export interface EventActionConfigInitProps {
  eventDefine: EventsDefine
  actionDefine: ActionDefine
  onChange: (data: EventHandleActionMap) => any
}

/**
 * 事件，动作，参数交互配置逻辑--自定义hook
 * @param {EventActionConfigInitProps} props
 */
export default function useEventActionConfig(props: EventActionConfigInitProps) {
  const { eventDefine, actionDefine, onChange } = props
  const [currentEventActions, setCurrentEventActions] = useState<EventHandleActionMap>({ entities: {}, keys: [] })

  // 更新数据
  const updateData = useCallback((data: EventHandleActionMap) => {
    setCurrentEventActions(cloneDeep(data || { entities: {}, keys: [] }))
  }, [])

  const emitOnchange = (data: EventHandleActionMap) => {
    onChange?.(data)
  }

  // 事件列表
  const eventList: EventActionListProps = _.chain(eventDefine)
    .values()
    .map(v => ({
      label: v.title,
      value: v.name,
      disabled: _.some(currentEventActions.keys, item => item === v.name)
    }))
    .value()

  // 新增任务
  const handleAddNewTask = () => {
    if (_.isEmpty(eventList)) return
    const defaultSelectedEvent = _.find(eventList, v => !v.disabled)
    const preAddEvent: EventName = defaultSelectedEvent?.value
    if (!preAddEvent) {
      console.error('error: 添加新任务失败，请检查事件列表是否存在！')
      return
    }

    const newEventActions = produce(currentEventActions, draft => {
      if (!draft.entities) draft.entities = {}
      draft.entities[preAddEvent] = []
      draft.keys = [...(currentEventActions?.keys || []), preAddEvent] as EventName[]
    }) as EventHandleActionMap
    setCurrentEventActions(newEventActions)
  }

  // 重置
  const handleResizeTask = () => {
    setCurrentEventActions({
      entities: {},
      keys: []
    })
  }

  // 切换事件
  const handleChangeEvent = (newEventName: EventName, oldEventName: EventName) => {
    const newEventActions = produce(currentEventActions, draft => {
      if (!draft.entities) draft.entities = {}
      draft.keys = _.map(currentEventActions.keys, v => (v === oldEventName ? newEventName : v)) as EventName[]
      draft.entities[newEventName] = cloneDeep(currentEventActions.entities[oldEventName])
      draft.entities[oldEventName] = []
    }) as EventHandleActionMap
    setCurrentEventActions(newEventActions)
    emitOnchange(newEventActions)
  }

  // 删除事件
  const handleDeleteEvent = (eventName: EventName) => {
    const newEventActions = produce(currentEventActions, draft => {
      draft.keys = _.filter(currentEventActions.keys, v => v !== eventName) as EventName[]
      draft.entities = _.omit(currentEventActions.entities, [eventName])
    }) as EventHandleActionMap
    setCurrentEventActions(newEventActions)
    emitOnchange(newEventActions)
  }

  // 添加动作
  const handleAddNewAction = (currentEventName: EventName) => {
    const defaultSelectedActionObj = _.chain(actionDefine).values().get(0).value()
    const preAddAction: ActionName = defaultSelectedActionObj?.name
    const nextEvAct = { actionName: preAddAction, editing: 'creating' }

    const nextEventActions = produce(currentEventActions, draft => {
      if (!draft.entities) draft.entities = {}
      draft.entities[currentEventName] = [
        ..._.get(currentEventActions.entities, currentEventName, []),
        nextEvAct
      ] as ActionConfig[]
    })
    setCurrentEventActions(nextEventActions)
  }

  // 切换动作
  const handleChangeAction = (newAction: ActionName, index: number, currentEvent: EventName) => {
    const action = actionDefine[newAction]
    if (!action) {
      console.error('action is not define')
      return
    }

    const nextEventActions = produce(currentEventActions, draft => {
      if (!draft.entities) draft.entities = {}
      const targetActions = draft.entities[currentEvent] as ActionConfig[]
      targetActions[index] = {
        actionName: newAction,
        editing: action.notParams ? false : 'creating',
        description: action.description,
        notParams: action.notParams
      }
    })
    setCurrentEventActions(nextEventActions)

    if (action.notParams) {
      emitOnchange(nextEventActions)
    }
  }

  // 删除动作
  const handleDeleteAction = (eventName: EventName, actionIndex: number) => {
    const preEventActionConfig = produce(currentEventActions, draft => {
      if (!draft.entities) draft.entities = {}
      draft.entities[eventName] = _.filter(
        currentEventActions.entities[eventName],
        (_v, idx: number) => (idx !== actionIndex) as boolean
      )
    })
    setCurrentEventActions(preEventActionConfig)
    emitOnchange(preEventActionConfig)
  }

  // 配置动作参数
  const handleSettingActionParam = (currentEvent: EventName, index: number) => {
    const nextEventActions = produce(currentEventActions, draft => {
      if (!draft.entities) draft.entities = {}
      draft.entities[currentEvent] = _.map(
        currentEventActions.entities[currentEvent] as ActionConfig[],
        (actionItem: ActionConfig, actIdx: number) => {
          const item = cloneDeep(actionItem)
          if (actIdx === index) item.editing = 'editing'
          else item.editing = actionItem?.description ? false : 'creating'
          if (item.notParams) item.editing = false
          return item
        }
      )
    })
    setCurrentEventActions(nextEventActions)
  }

  // 保存配置
  const handleConfirmAction = (save, { index, eventName }, param) => {
    if (save) {
      const preEventActionConfig = produce(currentEventActions, draft => {
        if (!draft.entities) draft.entities = {}
        const targetEventConfig = draft.entities[eventName] as ActionConfig[]
        // 不使用 merge
        if (param.isMerge === false) {
          _.keys(param).forEach(key => {
            targetEventConfig[index][key] = param[key]
          })
          targetEventConfig[index].editing = false
        } else {
          targetEventConfig[index] = _.merge({}, targetEventConfig[index], { editing: false }, param)
        }
      })
      setCurrentEventActions(preEventActionConfig)
      emitOnchange(preEventActionConfig)
    } else {
      const preEventActionConfig = produce(currentEventActions, draft => {
        if (!draft.entities) draft.entities = {}
        const targetEventConfig = draft.entities[eventName] as ActionConfig[]
        targetEventConfig[index].editing = targetEventConfig[index]?.description ? false : 'creating'
      })
      setCurrentEventActions(preEventActionConfig)
    }
  }

  return {
    handleAddNewTask,
    handleResizeTask,
    handleChangeEvent,
    handleDeleteEvent,
    handleAddNewAction,
    handleChangeAction,
    handleDeleteAction,
    handleSettingActionParam,
    handleConfirmAction,
    eventList,
    // 更新事件列表值
    setValue: updateData,
    value: currentEventActions
  }
}
