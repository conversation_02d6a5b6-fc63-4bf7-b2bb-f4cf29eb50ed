import { FieldDataIcon, IndiceRulePanel, TreeLayerSelectProps } from '@sugo/design'
import { Rule } from '@sugo/design/dist/esm/components/indice-rule/type'
import type { BaseNode } from '@sugo/design/dist/esm/components/tree-layer-select/panel'
import { useMemoizedFn } from 'ahooks'
import { Form, Modal } from 'antd'
import { produce } from 'immer'
import _ from 'lodash'
import type { DefaultOptionType } from 'rc-tree-select/lib/TreeSelect'
import React, { useMemo, useState } from 'react'

import { getLocalStorageKeys } from '@/components/elements/luckysheet/utils'
import { ELEMENT_GROUP_MAP } from '@/consts/screen'
import { ComponentDefineMap, ComponentMap } from '@/types/editor-core/component'
import { DataFilterCondition } from '@/types/editor-core/data-source'
import { EventHandleActionMap, EventName, EventSettings } from '@/types/editor-core/events'


/** 获取指标相关的上下文数据 */
export function useIndiceRuleContext(options: DefaultOptionType[] | null | undefined) {
  const indiceTreeLayerProps: TreeLayerSelectProps = useMemo(() => {
    const dimsTypeDict = _.groupBy(options, o => _.startsWith(`${o.value}`, 'gv_') ? 'globalVar' : 'componentValue')
    return {
      title: '请选择 全局变量/输入控件',
      treeData: [
        { key: 'globalVar', title: '全局变量', count: _.size(dimsTypeDict.globalVar), children: dimsTypeDict.globalVar },
        { key: 'componentValue', title: '输入控件', count: _.size(dimsTypeDict.componentValue), children: dimsTypeDict.componentValue }
      ] as TreeLayerSelectProps['treeData'],
      renderValue: (_s, info: BaseNode[]) => _.last(info)?.title || '',
      renderIcon: ({ level, dataType }) => ((level || 0) > 0 ? <FieldDataIcon dataType={dataType || 'string'} /> : null)
    }
  }, [options?.length])

  // 用于获取指标的 code，dataType
  const onGenerateRuleCol = useMemoizedFn((_value: string[], infos: any[]) => {
    const item = _.last(infos)
    return {
      col: item?.key,
      colName: item?.label,
      dataType: item.dataType || 'string'
    }
  })

  const onFieldQueryHotwords = useMemoizedFn(async (_rule: Rule, _keyword: string) => [])

  return {
    indiceTreeLayerProps,
    onGenerateRuleCol,
    onFieldQueryHotwords
  }
}

/** 事件配置表单 */
function EventSettingForm(props: {
  value: EventSettings
  onChange: (next: EventSettings) => any
  componentMap: ComponentMap
  componentDefineMap: ComponentDefineMap
}) {
  const { value, onChange, componentMap, componentDefineMap } = props
  const filterComps = useMemo(() => {
    const keysSet = new Set(
      [
        ELEMENT_GROUP_MAP.timePicker,
        ELEMENT_GROUP_MAP.textInput,
        ELEMENT_GROUP_MAP.numPicker,
        ELEMENT_GROUP_MAP.selector
      ].map(v => v.key)
    )
    return _.filter(componentMap?.entities, v => keysSet.has(componentDefineMap.entities[v.defineKey].group))
  }, [componentMap?.entities, componentDefineMap?.entities])
  const opts = useMemo(() => {
    const dataTypeDict = {
      [ELEMENT_GROUP_MAP.timePicker.key]: 'string',
      [ELEMENT_GROUP_MAP.textInput.key]: 'string',
      [ELEMENT_GROUP_MAP.numPicker.key]: 'number',
      [ELEMENT_GROUP_MAP.selector.key]: 'string'
    }
    return [
      ...getLocalStorageKeys('gv_').map(p => {
        const label = p.replace(/^gv_/, '')
        return ({ label, title: label, value: p, key: p })
      }),
      ..._.map(filterComps, c => ({
          label: c.alias || c.title,
          title: c.alias || c.title,
          value: c.key,
          key: c.key,
          dataType: dataTypeDict[componentDefineMap.entities[c.defineKey].group]
        }))
    ]
  }, [filterComps])

  const { indiceTreeLayerProps, onGenerateRuleCol, onFieldQueryHotwords } = useIndiceRuleContext(opts)

  return (
    <Form
      layout='vertical'
      labelCol={{ span: 24 }}
      wrapperCol={{ span: 24 }}
    >
      <Form.Item label='前置条件' tooltip='符合条件才执行，一般使用“含有”或“不含有”'>
        <IndiceRulePanel
          treeLayerProps={indiceTreeLayerProps}
          onGenerateRuleCol={onGenerateRuleCol}
          onFieldQueryHotwords={onFieldQueryHotwords}
          value={value.precondition as Rule[]}
          onChange={next => {
            onChange({ ...value, precondition: _.map(next, r => ({ ...r, eq: _.castArray(r.eq) })) as DataFilterCondition[] })
          }}
        />
      </Form.Item>
    </Form>
  )
}

/** 事件配置弹窗封装 */
export function useEventSettingModal(props: {
  componentMap: ComponentMap
  componentDefineMap: ComponentDefineMap
  onChange: (data: EventHandleActionMap) => void
  value: EventHandleActionMap
}) {
  const { value, onChange, componentMap, componentDefineMap } = props
  const [targetEventName, setTargetEventName] = useState<EventName>('')
  const [targetActionIndex, setTargetActionIndex] = useState<number>(-1)
  const [pendingSetting, setPendingSetting] = useState<EventSettings>(() => ({}))

  const onCancel = () => {
    setTargetEventName('')
    setTargetActionIndex(-1)
    setPendingSetting({})
  }
  const eventSettingModal = (
    <Modal
      title='事件配置'
      open={!!targetEventName}
      width={830}
      onCancel={onCancel}
      onOk={() => {
        if ((targetActionIndex ?? -1) === -1) {
          onChange({ ...value, settings: { ...value.settings, [targetEventName]: pendingSetting } })
        } else {
          const precondition = _.cloneDeep(pendingSetting.precondition) // 解决 immer 跟组件内部 proxy 冲突的问题
          const next = produce(value, draft => {
            draft.entities[targetEventName] = draft.entities[targetEventName] || []
            const act = draft.entities[targetEventName]![targetActionIndex]
            act.precondition = precondition
          })
          onChange(next)
        }
        onCancel()
      }}
      destroyOnClose
    >
      <EventSettingForm
        value={pendingSetting}
        onChange={setPendingSetting}
        componentMap={componentMap}
        componentDefineMap={componentDefineMap}
      />
    </Modal>
  )

  return {
    eventSettingModal,
    showEventSettingModal: (eventName: EventName, actIndex = -1) => {
      setTargetEventName(eventName)
      setTargetActionIndex(actIndex ?? -1)
      if ((actIndex ?? -1) === -1) {
        setPendingSetting(value.settings?.[eventName] || {})
      } else {
        setPendingSetting(value.entities?.[eventName]?.[actIndex] || {})
      }
    }
  }
}
