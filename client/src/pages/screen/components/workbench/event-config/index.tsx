import { PlusOutlined, RedoOutlined } from '@ant-design/icons'
import { Button, Col, Row, Tooltip } from 'antd'
import _ from 'lodash'
import React, { Suspense, useCallback, useEffect } from 'react'

import { ActionConfigPanelDict } from '@/pages/screen/components/workbench/event-config/const'
import { useEventSettingModal } from '@/pages/screen/components/workbench/event-config/hooks/use-event-setting-modal'
import { ComponentBaseProps } from '@/pages/screen/components/workbench/event-config/type'
import type { DataSourceInfo, DataSourceInfoType } from '@/types/data-source'
import { ComponentDefineMap, ComponentKey, ComponentMap } from '@/types/editor-core/component'
import { ActionConfig, ActionDefine, EventHandleActionMap, EventName, EventsDefine } from '@/types/editor-core/events'
import type { EditorPage } from '@/types/editor-page'

import EventConfigItem from './event-config-item'
import useEventActionConfig from './hooks/use-event-action-config'


export interface EventConfigProps {
  eventDefine: EventsDefine
  actionDefine: ActionDefine
  componentKey: ComponentKey
  value: EventHandleActionMap
  onChange: (next: EventHandleActionMap) => any
  componentMap: ComponentMap
  componentDefineMap: ComponentDefineMap
  dataSourcePickerInfo: DataSourceInfo
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id?: string) => Promise<any>
  pageNodes: EditorPage['directory']['nodes']
}

// 渲染事件响应行为编辑器
function renderActionEditor(
  eventName: string,
  idx: number,
  actionConfig: ActionConfig,
  componentKey: ComponentKey,
  onConfirmAction,
  pageNodes: EditorPage['directory']['nodes'],
  componentMap: ComponentMap,
  componentDefineMap: ComponentDefineMap,
  dataSourcePickerInfo: DataSourceInfo,
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id?: string) => Promise<any>
) {
  const { actionName } = actionConfig
  const Comp: React.ComponentType<ComponentBaseProps<any>> =
    ActionConfigPanelDict[actionName] || ActionConfigPanelDict.customActionAdapt

  return (
    <Suspense key={idx} fallback={<div className='py-4 text-center text-base text-gray-400'>Loading...</div>}>
      <Comp
        rawKey={componentKey || 'screen'}
        componentKey={componentKey}
        componentMap={componentMap}
        componentDefineMap={componentDefineMap}
        dataSourcePickerInfo={dataSourcePickerInfo}
        loadMoreDataSourceInfo={loadMoreDataSourceInfo}
        value={actionConfig}
        pageNodes={pageNodes}
        onChange={next => {
          if (!next) return onConfirmAction(false, { index: idx, eventName, actionName })
          onConfirmAction(true, { index: idx, eventName, actionName }, next)
        }}
      />
    </Suspense>
  )
}

/**
 * 事件配置面板
 * @param {EventConfigProps} props
 * @returns
 */
export default function EventConfig(props: EventConfigProps) {
  const { eventDefine, actionDefine, componentKey, pageNodes, value, onChange } = props
  const { componentMap, componentDefineMap, dataSourcePickerInfo, loadMoreDataSourceInfo } = props
  const actionList = _.values(actionDefine).map(v => ({ label: v.title, value: v.name }))

  const {
    handleAddNewTask,
    handleChangeEvent,
    handleDeleteEvent,
    handleAddNewAction,
    handleChangeAction,
    handleDeleteAction,
    handleResizeTask,
    handleSettingActionParam,
    handleConfirmAction,
    eventList,
    value: renderEventActionObj,
    setValue
  } = useEventActionConfig({ eventDefine, actionDefine, onChange })

  const {
    showEventSettingModal,
    eventSettingModal
  } = useEventSettingModal({
    value: renderEventActionObj,
    onChange,
    componentMap,
    componentDefineMap
  })

  // 使用 useMemoizedFn 会导致 切换事件 tab，再切换页面后，动作配置显示“组件已删除”
  const getComponent = useCallback(key => componentMap.entities[key], [componentMap?.entities])

  useEffect(() => {
    setValue?.(value as EventHandleActionMap)
  }, [value, setValue])

  return (
    <div className='h-full px-4 mt-4'>
      <Row className='w-full mb-4'>
        <Col flex={1}>
          <Button type='primary' className='w-full' icon={<PlusOutlined />} onClick={handleAddNewTask}>
            添加新任务
          </Button>
        </Col>

        <Col flex={0} className='pl-4'>
          <Tooltip title='重置事件'>
            <Button icon={<RedoOutlined />} onClick={handleResizeTask} />
          </Tooltip>
        </Col>
      </Row>

      <div className='mb-4'>
        {_.map(renderEventActionObj?.keys || [], eventName => {
          const actionConfigs = renderEventActionObj.entities[eventName as EventName] as ActionConfig[]
          return (
            <EventConfigItem
              key={eventName as EventName}
              selectedEvent={eventName as EventName}
              eventList={eventList}
              actionList={actionList}
              actionConfigs={actionConfigs}
              eventSettings={renderEventActionObj.settings?.[eventName as EventName]}
              onChangeEvent={handleChangeEvent}
              onDeleteEvent={handleDeleteEvent}
              onAddNewAction={handleAddNewAction}
              onChangeAction={handleChangeAction}
              onDeleteAction={handleDeleteAction}
              onSettingActionParam={handleSettingActionParam}
              onShowEventSettingModal={showEventSettingModal}
              onShowActionSettingPanel={showEventSettingModal}
              getComponent={getComponent}
              renderActionEditor={(currentEventName: string, actionConfig, index: number) =>
                renderActionEditor(
                  currentEventName,
                  index,
                  actionConfig,
                  componentKey,
                  handleConfirmAction,
                  pageNodes,
                  componentMap,
                  componentDefineMap,
                  dataSourcePickerInfo,
                  loadMoreDataSourceInfo
                )
              }
            />
          )
        })}
      </div>

      {eventSettingModal}
    </div>
  )
}

export type EventConfigType = typeof EventConfig
