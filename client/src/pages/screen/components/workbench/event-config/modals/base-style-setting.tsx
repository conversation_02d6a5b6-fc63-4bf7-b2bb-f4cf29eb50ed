import './base-style-setting.less'

import { Checkbox, InputNumber } from 'antd'
import _ from 'lodash'
import parseCssTransition from 'parse-css-transition'
import React, { CSSProperties, useMemo } from 'react'
import * as transform from 'transform-parser'

import Icon from '@/components/icons/iconfont-icon'

export interface BaseStyleSettingProps {
  value: CSSProperties
  // width, height, transform, border-radius
  onChange: (style: CSSProperties) => any
  enableMap: Partial<Record<'width' | 'height' | 'transform' | 'rotate' | 'radius', boolean>>
  onEnableChange: (enableMap: BaseStyleSettingProps['enableMap']) => any
}

/**
 * 基础的样式设置
 * width, height, transform, border-radius
 */
export function BaseStyleSetting(props: BaseStyleSettingProps) {
  const { value, onChange, enableMap } = props

  const getValue = (val: number | string | undefined, num) => {
    const _val = _.round(_.isString(val) ? Number.parseFloat(val) : val || 0, num)
    if (_.isNaN(_val)) return 0
    return _val
  }

  const transition = useMemo(() => {
    if (!value.transition) return undefined
    return parseCssTransition(value.transition || '')[0]
  }, [value.transition])

  const width = getValue(value.width, 0)
  const height = getValue(value.height, 0)
  const radius = getValue(value.borderRadius, 2)
  const tf = useMemo(() => transform.parse(value.transform || ''), [value.transform])
  const rotate = Number.parseFloat(tf.rotate?.toString() || '0') % 360 || 0
  const [x = 0, y = 0] = (_.isArray(tf.translate) ? tf.translate : [tf.translate] || []).map(i =>
    Math.ceil(Number.parseFloat(i?.toString() || '0'))
  )

  const update = (key: string, val) => {
    if (key === 'rotate' || key === 'top' || key === 'left') {
      if (!tf.translate) tf.translate = []
      if (key === 'left') tf.translate[0] = val || 0
      if (key === 'top') tf.translate[1] = val || 0
      if (key === 'rotate') tf.rotate = `${val}deg`
      onChange({ ...value, transform: transform.stringify(tf) })
    } else {
      onChange({ ...value, [key]: val })
    }
  }

  const onEnableChange = (key: string, e) => {
    props.onEnableChange({ ...enableMap, [key]: e.target.checked })
  }

  return (
    <div className='change-style-action-base-style-setting'>
      <div className='title'>基础设置</div>

      <div className='item-row-layout'>
        <Checkbox checked={enableMap?.transform} onChange={e => onEnableChange('transform', e)} />
        <InputNumber
          step={1}
          min={-100000}
          max={100000}
          addonBefore='X'
          placeholder='请输入'
          value={x}
          onChange={val => update('left', val)}
        />
        <InputNumber
          step={1}
          min={-100000}
          max={100000}
          addonBefore='Y'
          placeholder='请输入'
          value={y}
          onChange={val => update('top', val)}
        />
      </div>

      <div className='item-row-layout'>
        <Checkbox checked={enableMap?.width} onChange={e => onEnableChange('width', e)} />
        <InputNumber
          step={1}
          min={1}
          addonBefore='W'
          value={width}
          onChange={val => update('width', val)}
          placeholder='请输入'
        />
      </div>

      <div className='item-row-layout'>
        <Checkbox checked={enableMap?.height} onChange={e => onEnableChange('height', e)} />
        <InputNumber
          step={1}
          min={1}
          addonBefore='H'
          value={height}
          onChange={val => update('height', val)}
          placeholder='请输入'
        />
      </div>

      <div className='item-row-layout'>
        <Checkbox checked={enableMap?.rotate} onChange={e => onEnableChange('rotate', e)} />
        <InputNumber
          step={5}
          addonBefore={<Icon name='旋转' />}
          value={rotate}
          precision={2}
          formatter={v => `${v}°`}
          parser={v => v?.replace('°', '') as any}
          onChange={val => update('rotate', val)}
        />
      </div>

      <div className='item-row-layout'>
        <Checkbox checked={enableMap?.radius} onChange={e => onEnableChange('radius', e)} />
        <InputNumber
          step={2}
          min={0}
          addonBefore={<Icon name='圆角' />}
          value={radius}
          precision={2}
          formatter={v => `${v || 0}%`}
          parser={v => v?.replace('%', '') as any}
          onChange={val => update('borderRadius', val)}
        />
      </div>

      <div className='item-row-layout'>
        <Checkbox
          checked={!!transition}
          onChange={e => {
            const checked = e.target.checked
            const duration = transition?.duration || 500
            update('transition', checked ? `all ${duration}ms linear` : undefined)
          }}
        >
          启用变化动画效果
        </Checkbox>
        <InputNumber
          step={100}
          min={100}
          addonAfter='ms'
          disabled={!transition}
          value={transition?.duration || 500}
          placeholder='动画持续时间'
          style={{ width: 172 }}
          onChange={val => update('transition', `all ${val}ms linear`)}
        />
      </div>
    </div>
  )
}
