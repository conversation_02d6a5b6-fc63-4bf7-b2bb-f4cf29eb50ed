import { Input } from 'antd'
import cn from 'classnames'
import _ from 'lodash'
import React, { useMemo,useState } from 'react'

import Icon from '@/components/icons/iconfont-icon'
import { ELEMENT_TOOL_TABS } from '@/consts/screen'
import type { Component, ComponentDefine } from '@/types/editor-core/component'

export interface ComponentListProps {
  list: (Component & { define?: ComponentDefine })[]
  active: string
  onSelect: (key: string, data: any) => any
  selfComponentKey?: string
}

/**
 * 组件列表
 */
export default function ComponentList(props: ComponentListProps) {
  const { list = [], active, onSelect, selfComponentKey } = props
  const [keyword, setKeyword] = useState('')
  const [groupMap, setGroupMap] = useState<Record<string, any>>({}) // 组过滤

  const newList = useMemo(() => {
    let arr = list
    if (keyword)
      arr = arr.filter(i => (i.alias || i.title).toLocaleLowerCase().indexOf(keyword.toLocaleLowerCase()) > -1)
    if (!_.isEmpty(groupMap)) arr = arr.filter(i => groupMap[i.define?.category || ''])
    return arr
  }, [keyword, list, groupMap])

  const groups = ELEMENT_TOOL_TABS.map(i => ({
    label: i.title === '更多' ? '其他' : i.title,
    value: i.key
  }))

  const onClick = item => e => {
    e.stopPropagation()
    if (groupMap[item.value]) {
      setGroupMap(_.omit(groupMap, item.value))
    } else {
      setGroupMap({ ...groupMap, [item.value]: item })
    }
  }

  return (
    <>
      <div className='search-panel'>
        <Input value={keyword} placeholder='搜索组件' onChange={e => setKeyword(e.target.value)} />
      </div>
      <div className='tag-list'>
        {/* 类型 */}
        {groups.map(item => (
          <span
            key={item.value}
            onClick={onClick(item)}
            className={cn({
              'tag-list-item': true,
              'active': !!groupMap[item.value]
            })}
          >
            {item.label}
          </span>
        ))}
      </div>
      {newList
        .filter(i => i && i.define)
        .map(item => (
          <div
            key={item.key}
            className={cn({
              'component-list-item': true,
              'active': active === item.key
            })}
            onClick={() => onSelect(item.key || '', {})}
          >
            {item.define?.icon && <Icon name={item.define?.icon} />}
            <span>{item.alias || item.title}</span>
            {selfComponentKey === item.key && <span className='self-mark'>（自己）</span>}
          </div>
        ))}
      {newList.length === 0 && <div className='no-schema'>没有数据</div>}
    </>
  )
}
