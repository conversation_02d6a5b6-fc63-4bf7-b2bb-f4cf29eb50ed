.change-component-config-modal {
  width: 640px;

  .ant-modal-body {
    padding: 0;
  }

  .footer {
    width: 100%;
  }

  &-content {
    display: flex;
  }

  .component-list {
    width: 200px;
    border-right: 1px solid var(--border-color-base);
    height: 500px;
    overflow-y: auto;

    > .component-list-item {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      padding: 8px 12px;
      cursor: pointer;

      .anticon {
        margin-right: 8px;
        color: var(--primary-color);
      }

      &.active {
        background-color: var(--tint-color-92);
      }
      &:hover {
        background-color: var(--tint-color-98);
      }
    }

    .search-panel {
      padding: 6px;
      border-bottom: 1px solid var(--border-color-base);
      > input {
        padding-top: 3px;
        padding-bottom: 3px;
      }
    }

    .tag-list {
      display: flex;
      align-items: center;
      overflow-x: auto;
      width: 100%;
      justify-content: space-around;
      border-bottom: 1px solid var(--border-color-base);
      padding: 4px 1px;

      .tag-list-item {
        font-size: 13px;
        cursor: pointer;
        border-radius: 3px;
        padding: 1px 3px;
        line-height: 1.2;
        border: 1px solid transparent;
        user-select: none;
        &:hover {
          color: var(--primary-color);
        }
        &.active {
          color: var(--primary-color);
          border: 1px solid var(--primary-color-50);
        }
      }
    }
  }

  .component-base-schema {
    flex: 1;
    padding: 0px;
    height: 500px;
    overflow-y: auto;
  }

  .no-schema {
    padding: 12px;
    color: #777;
  }

  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .self-mark {
    font-size: 13px;
    color: #999;
  }

  .tip {
    color: #999;
    margin: 8px 12px;
  }

  // .ant-input-affix-wrapper-readonly {
  //   border: none;
  //   box-shadow: none;
  // }

  .goto-component-content {
    padding: 8px;
  }

}
