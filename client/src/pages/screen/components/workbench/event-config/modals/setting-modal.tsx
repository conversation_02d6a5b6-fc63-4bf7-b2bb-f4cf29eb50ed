import './setting-modal.less'

import { <PERSON><PERSON>, Checkbox, Tooltip } from 'antd'
import isEqual from 'fast-deep-equal'
import { klona as cloneDeep } from 'klona/lite'
import _ from 'lodash'
import React, { memo, useEffect, useMemo, useState } from 'react'

import Modal from '@/components/customs/custom-modal'
import withRefModal from '@/components/with-ref-modal'
import { FIXED_SCHEMA } from '@/consts/elements'
import type { Action } from '@/cores/evaction/types/action.d'
import type { Component, ComponentDefine, ComponentDefineMap, ComponentMap } from '@/types/editor-core/component'
import type { ActionConfig } from '@/types/editor-core/events'

import type { ComponentBaseProps } from '../type'
import ComponentList from './setting-component-list'

export type ChangeComponentConfigPanelProps = ComponentBaseProps<Action.ChangeComponentConfig>

export interface State {
  selectedKey: string
  componentName: string
  value: Record<string, any>
  schema?: Record<string, any>
  component?: Component
  isTrigger?: boolean
  enableMap?: any
}

export interface SettingModalProps {
  componentMap: ComponentMap
  componentDefineMap: ComponentDefineMap
  onCancel?: () => any
  onOk?: (data: any) => any
  // 初始化数据
  initData: Record<string, any> & ActionConfig
  title: string
  schemaType: 'config' | 'style' | 'dataSource' | 'gotoComponent' | 'external-filter'
  selfComponentKey?: string
  showTrigger?: boolean
  renderContent?: (
    state: State,
    update: (newState: Partial<State>) => any,
    getComponent: (key: string) => {
      define?: ComponentDefine
      component?: Component
    }
  ) => JSX.Element
}

/**
 * 配置设置
 */
export const SettingModal = withRefModal<SettingModalProps>(props => {
  const { componentMap, componentDefineMap } = props
  const { visible, onCancel, modal, onOk, initData, title, schemaType, selfComponentKey } = props
  const { renderContent, showTrigger = true } = props

  const [fixedSchema] = useState(() => cloneDeep(FIXED_SCHEMA))

  const [state, setState] = useState({
    selectedKey: '', // 选中的组件 key
    componentName: '', // 选中的组件名称
    value: {} as Record<string, any>, // 修改的值
    schema: {} as Record<string, any>, // 表单模型变量
    component: undefined as Component | undefined, // 选中的组件
    isTrigger: false, // 是否是正反模式
    enableMap: {}
  })

  const update = (newState: Partial<typeof state>) => setState({ ...state, ...newState })

  const getComponent = (key: string) => {
    const comp = componentMap.entities[key] as Component | undefined
    const define = componentDefineMap.entities[comp?.defineKey || '']
    return { component: comp, define }
  }

  // 组件列表
  const componentList = useMemo(
    () =>
      componentMap.keys
        .map(key => {
          const data = getComponent(key)
          if (!data.component) return null
          return { ...data.component, define: data.define }
        })
        .filter(i => i),
    [componentMap, componentDefineMap]
  )

  // 选中时触发
  const onSelect = (key: string, initValue: any) => {
    const data = getComponent(key)
    if (!data.component) {
      update({ selectedKey: '', isTrigger: initData.isTrigger })
      return
    }
    const schemaDict = {
      dataSource: undefined,
      config: _.assign(
        {},
        _.mapKeys(data.define.baseSchema, (_val, k) => `base_${k}`),
        _.mapKeys(data.define.configSchema, (_val, k) => `config_${k}`)
      ),
      style: _.assign({}, data.define.styleSchema, fixedSchema)
    }
    // 不是 config 类型时，给一个空
    if (schemaType !== 'config') schemaDict.config = undefined as any

    // 获取表单数据
    let _value: any = {}
    if (schemaType === 'dataSource') {
      if (!_.isEmpty(initValue)) _value = initValue
      else _value = data.component[schemaType]
    } else if (schemaType === 'style' || schemaType === 'config') {
      _value = _.merge({}, data.component[schemaType], initValue)
    } else {
      _value = initData
    }

    update({
      selectedKey: key,
      value: _value,
      componentName: data.component.alias || data.component.title,
      component: data.component,
      isTrigger: initData.isTrigger,
      enableMap: initData.enableMap,
      schema: schemaDict[schemaType]
    })
  }

  const _onCancel = () => {
    onCancel?.()
    modal.hide()
  }
  // 确定
  const _onSubmit = () => {
    if (!state.selectedKey) return _onCancel()
    onOk?.(state)
    modal.hide()
  }

  const renderFooter = () => (
    <div className='footer'>
      <div>
        {showTrigger && (
          <Tooltip title='记忆模式会记住上一次触发的状态，再次点击时会进行反转状态' placement='topLeft'>
            <Checkbox checked={state.isTrigger} onChange={e => update({ isTrigger: e.target.checked })}>
              Trigger 记忆模式
            </Checkbox>
          </Tooltip>
        )}
      </div>
      <div>
        <Button onClick={_onCancel}>取消</Button>
        <Button type='primary' onClick={_onSubmit} disabled={!state.selectedKey}>
          确定
        </Button>
      </div>
    </div>
  )

  // 渲染内容
  const renderRightContent = () => {
    if (!state.selectedKey) return <div className='tip'>请选择要设置的组件</div>
    return renderContent?.(state, update, getComponent)
  }

  useEffect(() => {
    if (!visible) return
    onSelect(initData.componentKey, initData[schemaType])
  }, [visible, schemaType, initData])

  return (
    <Modal
      title={state.componentName ? `${title} - ${state.componentName}` : title}
      width={680}
      open={visible}
      destroyOnClose
      onCancel={_onCancel}
      onOk={_onSubmit}
      className='change-component-config-modal'
      footer={renderFooter()}
      bodyStyle={{ padding: 0 }}
    >
      <div className='change-component-config-modal-content'>
        <div className='component-list'>
          <ComponentList
            list={componentList as any}
            active={state.selectedKey}
            selfComponentKey={selfComponentKey}
            onSelect={onSelect}
          />
        </div>
        <div className='component-base-schema'>{renderRightContent()}</div>
      </div>
    </Modal>
  )
})

export default memo(SettingModal, isEqual)
