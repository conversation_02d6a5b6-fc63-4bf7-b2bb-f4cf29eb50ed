import { DeleteOutlined, PlusCircleOutlined } from '@ant-design/icons'
import { useMemoizedFn } from 'ahooks'
import { Button, Checkbox, Col, Form, Input, Popconfirm, Radio, Row, Select, Space } from 'antd'
import _ from 'lodash'
import React, { useMemo, useState } from 'react'

import DataSourceColumnPicker from '@/components/data-filter-config/data-source-column-picker'
import {
  defaultPopWindowParams,
  EXTERNAL_LINK_URL_KEY,
  GO_BACK_URL_KEY,
  INDICES_FUNCTION_URL_KEY
} from '@/consts/event'
import { Action, OpenWay, VariablesOption } from '@/cores/evaction/types/action.d'
import { ComponentBaseProps } from '@/pages/screen/components/workbench/event-config/type'
import { ColumnInfo } from '@/types/editor-core/data-source'
import { enableSelectSearch } from '@/utils'


/** 变量默认值 */
const defaultVariableOption = {
  queryKey: '',
  componentKey: ''
}


/**
 * 打开页面配置面板
 * @param props
 * @constructor
 */
export default function OpenUrlConfigPanel(props: ComponentBaseProps<Action.OpenUrlActionConfig>) {
  const {
    value, onChange, pageNodes = [], componentMap,
    componentKey, componentDefineMap, dataSourcePickerInfo, loadMoreDataSourceInfo
  } = props
  const [currentPageUrl, setCurrentPageUrl] = useState<string>(value?.pageUrl)
  const [currentOpenWay, setCurrentOpenWay] = useState<OpenWay>(value?.openWay || 'self')
  const [isWindwoCenter, setIsWindowCenter] = useState<boolean>(
    _.isNil(value?.popWindowParams?.center) ? defaultPopWindowParams.center : value?.popWindowParams?.center
  )
  const [variableInputs, setVariableInputs] = useState<VariablesOption[]>([
    ..._.get(value, 'variables', [defaultVariableOption])
  ] as VariablesOption[])

  const [form] = Form.useForm()

  const validPages = _.concat(
    _.map(pageNodes, v => ({ url: v.screenId, title: v.title })),
    [
      { url: EXTERNAL_LINK_URL_KEY, title: '外部链接' },
      { url: INDICES_FUNCTION_URL_KEY, title: '指标功能页' },
      { url: GO_BACK_URL_KEY, title: '返回上页' }
    ]
  )

  const openOpts = useMemo(() => [
    { label: '本页打开', value: 'self' },
    { label: '新标签打开', value: 'blank' },
    { label: '弹出窗口', value: 'popWindow' },
    { label: '父窗口（限 Iframe）', value: 'parent' }
  ], [])

  const openAfterOpts = useMemo(() => [
    { label: '默认', value: 'default' },
    { label: '关闭当前页面', value: 'closePage' }
  ], [])

  const filterComponents = useMemo(() => {
    const filterRuleBtnComps = _.values(componentMap.entities).filter(c => {
      const compDefine = componentDefineMap.entities[c.defineKey]
      return ['timePicker', 'selector'].includes(compDefine?.group) ||
        // 如果有value字段 不管有没有值
        _.has(compDefine.configDefine, 'value')
    })
    const orderByFilerRuleComps = _.orderBy(filterRuleBtnComps, d => (d.key === componentKey ? 0 : 1))
    return orderByFilerRuleComps
  }, [])


  const currComponent = useMemo(() => componentMap.entities[componentKey], [componentKey])

  /** 表单值变更 */
  const handleFormValueChange = useMemoizedFn(({ pageUrl, openWay, popWindowParams }) => {
    if (openWay) setCurrentOpenWay(openWay)
    if (pageUrl) setCurrentPageUrl(pageUrl)
    if (popWindowParams) {
      setIsWindowCenter(popWindowParams?.center)
      if (!popWindowParams?.center) return

      form.setFieldValue(['popWindowParams', 'left'], 100)
      form.setFieldValue(['popWindowParams', 'top'], 100)
    }
  })

  /** 新增变量  */
  const handleAddVariable = useMemoizedFn(() => {
    const res = [...variableInputs, defaultVariableOption]
    setVariableInputs(res)
  })

  /** 删除变量 */
  const handleDeleteVariable = useMemoizedFn((i: number) => {
    const res = variableInputs.filter((_v, idx) => idx !== i)
    setVariableInputs(res)

    const formStore = form.getFieldsValue()
    if (formStore?.variables && i <= _.get(formStore, 'variables.length', -1) - 1) {
      form.setFieldsValue({ ...formStore, variables: _.get(formStore, 'variables', []).filter((_v, idx) => idx !== i) })
    }
  })

  /** 取消配置 */
  const handleCancelSetting = useMemoizedFn(() => {
    if (variableInputs.length !== 1) {
      const list = variableInputs.filter(v => Boolean(v.queryKey))
      setVariableInputs(list)
    }
    onChange(null)
  })

  const filterOptionFnc = useMemoizedFn((val, option) => new RegExp(val, 'ig').test(option.children))

  return (
    <Form
      layout='vertical'
      form={form}
      initialValues={_.defaults({}, value, {
        pageUrl: _.first(validPages)?.url,
        openWay: 'self',
        openAfter: 'default',
        popWindowParams: defaultPopWindowParams,
        _targetMetric: value.targetMetric ? { type: 'indicesSpec', id: value.targetMetric, name: '???' } as ColumnInfo : undefined
      })}
      onValuesChange={handleFormValueChange}
      className='border border-solid rounded border-gray-300 !px-4 !py-2'
    >
      <Form.Item name='pageUrl' label='跳转链接'>
        <Select
          getPopupContainer={() => window.rootElement!}
          placeholder='请选择页面'
          {...enableSelectSearch}
        >
          {_.map(validPages, page => (
            <Select.Option key={page.url}>{page.title}</Select.Option>
          ))}
        </Select>
      </Form.Item>

      {currentPageUrl === EXTERNAL_LINK_URL_KEY && (
        <Form.Item required name='externalLink' rules={[{ required: true, message: '请输入外部链接地址' }]}>
          <Input placeholder='输入网址' />
        </Form.Item>
      )}

      {currentPageUrl === INDICES_FUNCTION_URL_KEY && (
        <>
          <Form.Item
            required
            name='indicesFunction'
            label='指标功能页'
            rules={[{ required: true, message: '请选择指标功能页' }]}
            initialValue='factor-analysis'
          >
            <Select
              placeholder='请选择指标功能页'
              options={[
                { label: '归因分析', value: 'factor-analysis' },
                { label: '指标预警', value: 'monitor-alarms' }
              ]}
            />
          </Form.Item>

          <Form.Item
            required
            name='_targetMetric'
            label='目标指标'
            rules={[{ required: true, message: '请选择指标' }]}
          >
            <DataSourceColumnPicker
              className='w-[130px]'
              dataSourcePickerInfo={dataSourcePickerInfo}
              loadMoreDataSourceInfo={loadMoreDataSourceInfo}
              fieldType='number' // 选度量
              dataSourceConfigInfo={{
                type: 'indicesTable',
                tableId: currComponent.dataSource.indicesTable?.tableId || dataSourcePickerInfo.indicesTableMap.keys[0]
              }}
              dropdownMatchSelectWidth={false}
              allowClear
              dropdownStyle={{ width: '180px', whiteSpace: 'nowrap' }}
            />
          </Form.Item>
        </>
      )}

      {currentPageUrl !== GO_BACK_URL_KEY && (
        <Form.Item label='变量'>
          <Space direction='vertical' size='middle' style={{ display: 'flex' }}>
            {variableInputs.map((v, i) => (
              <Row key={i + v.queryKey + v.componentKey}>
                <Col span={8}>
                  <Form.Item name={['variables', i, 'queryKey']} noStyle>
                    <Input placeholder='请输入' />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={['variables', i, 'componentKey']} noStyle>
                    <Select
                      getPopupContainer={triggerNode => triggerNode.parentNode}
                      showSearch
                      filterOption={filterOptionFnc}
                      placeholder='请选择'
                      dropdownMatchSelectWidth={false}
                    >
                      {filterComponents.map(comp => (
                        <Select.Option key={comp.key}>{comp.alias || comp.title}</Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>

                <Col span={4}>
                  <Popconfirm title='是否删除当前变量' onConfirm={() => handleDeleteVariable(i)}>
                    <Button type='link' icon={<DeleteOutlined />} />
                  </Popconfirm>
                </Col>
              </Row>
            ))}
          </Space>

          <div className='btn-add-action-box'>
            <Button type='link' icon={<PlusCircleOutlined />} onClick={handleAddVariable}>
              添加新变量
            </Button>
          </div>
        </Form.Item>
      )}

      {currentPageUrl !== GO_BACK_URL_KEY && (
        <Form.Item label='页面打开方式' name='openWay'>
          <Select getPopupContainer={() => window.rootElement!} options={openOpts} />
        </Form.Item>
      )}

      <Form.Item label='打开后' name='openAfter'>
        <Radio.Group options={openAfterOpts} />
      </Form.Item>

      {currentOpenWay === 'popWindow' && (
        <>
          <Row gutter={10}>
            <Col span={12}>
              <Form.Item label='上位置' name={['popWindowParams', 'top']}>
                <Input disabled={isWindwoCenter} min={0} type='number' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='左位置' name={['popWindowParams', 'left']}>
                <Input disabled={isWindwoCenter} min={0} type='number' />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={10}>
            <Col span={12}>
              <Form.Item label='宽度' name={['popWindowParams', 'width']}>
                <Input min={0} placeholder='支持百分比' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='高度' name={['popWindowParams', 'height']}>
                <Input min={0} placeholder='支持百分比' />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item name={['popWindowParams', 'center']} valuePropName='checked'>
            <Checkbox>居中</Checkbox>
          </Form.Item>
        </>
      )}

      <Form.Item>
        <Button
          type='primary'
          className='mr-4'
          onClick={async () => {
            const data = await form.validateFields()
            const page = _.find(validPages, p => p.url === data.pageUrl)
            // 是否为外链
            const isExternalLink = data.pageUrl === EXTERNAL_LINK_URL_KEY

            let next = {
              ..._.omit(value, ['editing', 'linkType', isExternalLink ? 'pageUrl' : 'externalLink']),
              ...data,
              description: isExternalLink ? data?.externalLink : `${page?.title || data.pageUrl}`,
              targetMetric: data._targetMetric?.id
            }

            if (data.pageUrl === GO_BACK_URL_KEY) {
              next = _.omit(next, ['variables', 'openWay'])
            }

            // 当删除最后一个变量时，提交表单会form实例会保留原来的变量。
            if (next?.variables && _.isEmpty(variableInputs)) {
              next.variables = [defaultVariableOption]
            }

            if (next.openWay !== 'popWindow') {
              next = _.omit(next, ['popWindowParams'])
            }

            onChange(next)
          }}
        >
          保存
        </Button>
        <Button type='ghost' onClick={handleCancelSetting}>
          取消
        </Button>
      </Form.Item>
    </Form>
  )
}
