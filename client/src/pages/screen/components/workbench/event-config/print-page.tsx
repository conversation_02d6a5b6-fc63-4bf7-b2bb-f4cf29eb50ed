/* eslint-disable no-template-curly-in-string */
import './print-page.less'

// import { DeleteOutlined, PlusCircleOutlined } from '@ant-design/icons'
import { useDeepCompareEffect } from 'ahooks'
import { Modal } from 'antd'
import _ from 'lodash'
import React, { useMemo, useRef, useState } from 'react'

import withRefModal from '@/components/with-ref-modal'
import { PRINT_DIRECTION_OPTIONS, PRINT_SIZE_OPTIONS } from '@/consts/screen'
import type { Action } from '@/cores/evaction/types/action.d'
import SettingForm from '@/pages/preview/print/setting-form'
import { getDefaultConfig } from '@/pages/preview/print/utils'

import type { ComponentBaseProps } from './type'

export type ConfigPanelProps = ComponentBaseProps<Action.PagePrint>

/** 打印设置 */
const NewSettingModal = withRefModal(props => {
  const { visible, modal, data, pageNodes } = props

  const [state, setState] = useState<any>({
    cursors: [],
    ...getDefaultConfig()
  })
  const update = (newState: Partial<typeof state>) => setState(s => ({ ...s, ...newState }))

  // 选择的页面
  const screenOpts = useMemo(() =>
    _.filter(pageNodes, i => !!i.screenId).map(i => ({ label: i.title, value: i.screenId })),
    [pageNodes]
  )

  const onOk = () => {
    props.onOk({
      ...state,
      cursors: state.cursors.filter(i => i * 1 > 0).slice().sort()
    })
    modal.hide()
  }

  useDeepCompareEffect(() => {
    setState(s => {
      const st = { ...s }
      const params = data
      const val = getDefaultConfig()
      _.keys(val).forEach(key => {
        st[key] = params[key] ?? val[key]
      })
      return st
    })
  }, [data])

  return (
    <Modal
      open={visible}
      onCancel={() => {
        modal.hide()
        props.onCancel()
      }}
      onOk={onOk}
      title='打印设置'
      className='print-page-action'
    >

      <SettingForm
        warning={undefined}
        sizeOptions={PRINT_SIZE_OPTIONS}
        diectionOpts={PRINT_DIRECTION_OPTIONS}
        value={state}
        screenOpts={screenOpts} // 要补上
        onChange={val => update(val)}
      />

      <p className='msg'>*具体效果，需要发布后，触发打印事件，在打印预览里查看</p>
    </Modal>
  )
})

/**
 * 打印设置
 */
export default function PagePrint(props: ConfigPanelProps) {
  const { componentKey, value, onChange, pageNodes } = props
  const modalRef = useRef<ModalDefaultRef>()

  const onSubmit = data => {
    onChange({
      ..._.omit(value, 'editing'),
      description: `纸张 ${data.size}`,
      rawKey: componentKey,
      isMerge: false,
      ...data
    })
  }

  useDeepCompareEffect(() => {
    if (value.editing === 'editing') {
      modalRef.current?.show({
        onCancel: () => onChange(null),
        onOk: onSubmit
      })
    }
  }, [value.componentKey, value.editing])

  return <NewSettingModal ref={modalRef} data={value} pageNodes={pageNodes} />
}
