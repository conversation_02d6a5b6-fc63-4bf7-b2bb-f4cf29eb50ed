import './set-external-filter.less'

import isEqual from 'fast-deep-equal'
import { klona as cloneDeep } from 'klona/lite'
import _ from 'lodash'
import React from 'react'

import { useTempFilterSettingPanel } from '@/components/data-filter-config/temp-filter-config-panel'
import type { Action } from '@/cores/evaction/types/action.d'
import { useCommit as useDataSourceCommit, useModelState as useDataSourceModelState } from '@/stores/models/data-source'

import { useConfigChange } from './hooks/use-action'
import { SettingModal } from './modals/setting-modal'
import type { ComponentBaseProps } from './type'

export type ConfigPanelProps = ComponentBaseProps<Action.ExternalFilter>

/** 筛选面板 */
function FilterPanel({ dataSource, value, onChange }) {
  const dataSourceCommit = useDataSourceCommit()
  const loadMoreDataSourceInfo = (type: string, id?: string) => dataSourceCommit('loadMoreDataSourceInfo', { type, id })
  const dataSourcePickerInfo = useDataSourceModelState(s => s, isEqual)

  // 绑定的外部维度在 filter[n].bind 字段上
  const onFilterChange = val => {
    onChange(_.get(val, `${dataSource.dataSourceType}.filters`, []))
  }

  const _dataSource = cloneDeep(dataSource)
  _.set(_dataSource, `${dataSource.dataSourceType}.filters`, value)

  const tempFilterSettingPanel = useTempFilterSettingPanel({
    value: _dataSource,
    dataSourcePickerInfo,
    onChange: onFilterChange,
    loadMoreDataSourceInfo,
    props: { mode: 'external', title: '外部添加筛选', addText: '添加绑定条件' }
  })

  if (tempFilterSettingPanel === null) {
    return <div className='mt-2 text-gray-400 text-sm text-center'>请先设置此组件的数据源，再设置外部筛选条件</div>
  }

  return (
    <>
      {tempFilterSettingPanel}
      <div className='p-2 text-gray-400 text-sm'>
        提示：多值数组条件用英文逗号（,）分隔，例如：?date=2022-11-07,2022-12-07&org=ming
      </div>
    </>
  )
}

/**
 * 去到组件
 * @param props
 */
export default function ExternalFilterConfigPanel(props: ConfigPanelProps) {
  const { componentKey, value, onChange, componentMap, componentDefineMap } = props

  const { modalRef } = useConfigChange({
    value,
    onChange,
    onSaveData: data => ({
      // 只存 filter
      filter: data.value?.filter
    })
  })

  return (
    <SettingModal
      ref={modalRef}
      title='外部数据筛选'
      initData={value}
      selfComponentKey={componentKey} // 本身的组件 key
      componentMap={componentMap}
      componentDefineMap={componentDefineMap}
      schemaType='external-filter'
      showTrigger={false}
      renderContent={(state, update) => (
        <div className='set-external-filter'>
          {state.component ? (
            <FilterPanel
              dataSource={state.component?.dataSource || {}}
              value={state.value?.filter}
              onChange={val => update({ value: { ...state.value, filter: val } })}
            />
          ) : (
            <div>请选择组件</div>
          )}
        </div>
      )}
    />
  )
}
