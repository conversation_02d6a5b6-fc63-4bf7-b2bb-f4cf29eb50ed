import { useMemoizedFn } from 'ahooks'
import { Button, Form, message, Select } from 'antd'
import _ from 'lodash'
import React, { useMemo } from 'react'

import { Action } from '@/cores/evaction/types/action.d'
import { ComponentBaseProps } from '@/pages/screen/components/workbench/event-config/type'


/**
 * 设置可见性配置面板
 * @param props
 */
export default function SetVisibilityConfigPanel(props: ComponentBaseProps<Action.SetVisibility>) {
  const { componentKey, value, onChange, componentMap } = props
  const [form] = Form.useForm()

  const validComponents = useMemo(() => {
    const allComponentDefines1 = _.values(componentMap.entities)
    return _.orderBy(allComponentDefines1, d => (d.key === componentKey ? 0 : 1))
  }, [])

  const filterOptionFnc = useMemoizedFn((val, option) => new RegExp(val, 'ig').test(option.children))

  return (
    <Form
      layout='vertical'
      form={form}
      initialValues={_.defaults({}, value, { componentKey: _.first(validComponents)?.key, visibility: '1' })}
      className='border border-solid rounded border-gray-300 !px-4 !py-2 bg-white'
    >
      <Form.Item label='目标控件' name='componentKey'>
        <Select
          getPopupContainer={triggerNode => triggerNode.parentNode}
          showSearch
          filterOption={filterOptionFnc}
          placeholder='请选择控件'
          mode='multiple'
        >
          {_.map(validComponents, comp => (
            <Select.Option key={comp.key}>{comp.alias || comp.title}</Select.Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item label='可见性' name='visibility'>
        <Select getPopupContainer={triggerNode => triggerNode.parentNode}>
          <Select.Option key='1'>可见</Select.Option>
          <Select.Option key='0'>不可见</Select.Option>
          <Select.Option key='-1'>反转可见性</Select.Option>
        </Select>
      </Form.Item>
      <Form.Item>
        <Button
          type='primary'
          className='mr-4'
          onClick={() => {
            const data = form.getFieldsValue()
            const compKeysSet = new Set(_.castArray(data.componentKey))
            const comps = _.filter(validComponents, p => compKeysSet.has(p.key))
            if (_.isEmpty(comps)) {
              message.warn('请选择有效控件')
              return
            }
            const targetVisibility = { 1: '可见', 0: '不可见', '-1': '反转可见性' }
            onChange({
              ..._.omit(value, 'editing'),
              ...data,
              description: `${_.map(comps, c => c.title || c.key).join(', ')}${targetVisibility[data.visibility]}`
            })
          }}
        >
          保存
        </Button>
        <Button type='ghost' onClick={() => onChange(null)}>
          取消
        </Button>
      </Form.Item>
    </Form>
  )
}
