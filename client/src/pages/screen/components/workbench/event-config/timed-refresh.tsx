import { useDeepCompareEffect, useReactive } from 'ahooks'
import { Input, InputNumber, Modal, Radio, Select } from 'antd'
import _ from 'lodash'
import React, { useMemo, useRef } from 'react'

import withRefModal from '@/components/with-ref-modal'
import type { Action } from '@/cores/evaction/types/action.d'
import { enableSelectSearch } from '@/utils'
import { checkDataConfigValid } from '@/utils/query'

import type { ComponentBaseProps } from './type'

export type Props = ComponentBaseProps<Action.TimedRefresh>

const NewSettingModal = withRefModal(props => {
  const { data, modal, visible, componentOpts = [] } = props
  const { repeat = 1, interval = 5, unit = 'm', componentKeys = ['*'] } = data || {}

  const repateOpts = [
    { label: '无限次', value: 'infinite' },
    { label: '有限次', value: 'finite' }
  ]
  const unitOpts = [
    { label: '秒', value: 's' },
    { label: '分钟', value: 'm' },
    { label: '小时', value: 'h' }
  ]
  const targerOpts = [
    { label: '所有', value: 'all' },
    { label: '部分', value: 'other' }
  ]

  const state = useReactive({
    repateVal: repeat >= 99999 ? 'infinite' : 'finite',
    unit: unit || 'm',
    interval: interval || 5,
    repeat: repeat || 1,
    componentKeys: componentKeys || ['*'],
    target: _.isEqual(componentKeys, ['*']) ? 'all' : 'other'
  })

  const onOk = () => {
    props.onOk({
      ..._.pick(state, ['unit', 'interval', 'repeat', 'componentKeys'])
    })
    modal.hide()
  }

  return (
    <Modal
      title='定时刷新组件数据设置'
      open={visible}
      onCancel={() => {
        props.onCancel()
        modal.hide()
      }}
      onOk={onOk}
    >
      <div>
        <span>定时时间：</span>
        <div className='mt-2'>
          <Input.Group compact>
            <InputNumber
              value={state.interval}
              onChange={val => state.interval = val}
              min={state.unit === 's' ? 10 : 1}
              step={state.unit === 's' ? 5 : 1}
              max={10000}
            />
            <Select
              options={unitOpts}
              value={state.unit}
              onChange={val => {
                state.unit = val
                if (val === 's' && state.interval <= 10) {
                  state.interval = 10
                }
              }}
            />
          </Input.Group>
        </div>
      </div>

      <div className='my-4'>
        <span>重复次数：</span>
        <div className='mt-2'>
          <Radio.Group
            options={repateOpts}
            value={state.repateVal}
            onChange={e => {
              state.repateVal = e.target.value
              if (e.target.value === 'infinite') {
                state.repeat = 99999
              } else {
                state.repeat = 1
              }
            }}
          />
          {state.repateVal === 'finite' &&
            <InputNumber
              min={1}
              max={1000000}
              placeholder='请输入次数'
              style={{ width: 120 }}
              value={state.repeat}
              onChange={val => state.repeat = val}
            />
          }
        </div>
      </div>

      <div>
        <span>目标组件：</span>
        <div className='mt-2'>
          <Radio.Group
            options={targerOpts}
            value={state.target}
            onChange={e => state.target = e.target.value}
          />
          {state.target === 'other' &&
            <div>
              <div className='my-1 text-[#999]'>请选择要刷新数据的组件（已配置数据源的）</div>
              <Select
                mode='multiple'
                allowClear
                options={componentOpts}
                style={{ width: '80%' }}
                placeholder='请选择刷新数据的组件'
                value={_.isEqual(state.componentKeys, ['*']) ? undefined : state.componentKeys}
                onChange={val => state.componentKeys = val}
                {...enableSelectSearch}
              />
            </div>
          }
        </div>
      </div>

    </Modal>
  )
})

/**
 * 组件刷新
 */
export default function TimedRefresh(props: Props) {
  const { rawKey, value, onChange, componentMap, componentDefineMap } = props
  const modalRef = useRef<ModalDefaultRef>()

  const onSubmit = data => {
    const isAll = _.isEqual(data.componentKeys, ['*'])
    const unitStr = ({ s: '秒', m: '分钟', h: '小时' })[data.unit]
    const repeatStr = data.repeat >= 99999 ? '无限次' : `${data.repeat}次`

    onChange({
      ..._.omit(value, 'editing'),
      description: `每隔${data.interval}${unitStr}刷新“${isAll ? '全部' : '部分'}”组件，重复${repeatStr}`,
      rawKey,
      isMerge: false,
      ...data
    })
  }

  useDeepCompareEffect(() => {
    if (value.editing === 'editing') {
      modalRef.current?.show({
        onCancel: () => onChange(null),
        onOk: onSubmit
      })
    }
  }, [value.editing])

  // 获取组件的选项
  const componentOpts = useMemo(() => {
    const list = _.mapValues(componentMap.entities, i => {
      const define = componentDefineMap.entities[i.defineKey]
      const hasDataSource = checkDataConfigValid({ ...i, define })
      return {
        label: i.alias || i.title,
        value: i.key,
        disabled: !hasDataSource
      }
    })
    return _.values(list)
  }, [componentMap.entities, componentDefineMap.entities])

  return (
    <NewSettingModal
      ref={modalRef}
      data={value}
      componentOpts={componentOpts}
    />
  )
}
