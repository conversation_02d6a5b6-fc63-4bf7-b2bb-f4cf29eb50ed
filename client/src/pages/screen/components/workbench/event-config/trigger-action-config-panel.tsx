import { Button, Form, message, Select } from 'antd'
import { produce } from 'immer'
import _ from 'lodash'
import React, { useEffect, useMemo } from 'react'

import { Action } from '@/cores/evaction/types/action.d'
import { useFormFieldsAdapter } from '@/hooks/use-form-fields-adapter'
import { ActionConfigPanelDict } from '@/pages/screen/components/workbench/event-config/const'
import { ComponentBaseProps } from '@/pages/screen/components/workbench/event-config/type'
import { enableSelectSearch } from '@/utils'


/** 触发其他事件配置面板 */
export default function TriggerActionConfigPanel(props: ComponentBaseProps<Action.TriggerAction>) {
  const {
    componentKey, value, onChange, componentMap, componentDefineMap,
    dataSourcePickerInfo, loadMoreDataSourceInfo, pageNodes
  } = props
  const [pendingState, setPendingState] = React.useState(() => value || { params: {} })
  useEffect(() => {
    setPendingState(value || { params: {} })
  }, [value])

  const { fields, onFieldsChange } = useFormFieldsAdapter(pendingState, setPendingState, 'params')

  const validComponents = useMemo(() => {
    const allComponentDefines1 = _.values(componentMap.entities)
    return _.orderBy(allComponentDefines1, d => (d.key === componentKey ? 0 : 1))
  }, [])

  // 动作列表
  const actionDefine = useMemo(() => {
    const targetComp = componentMap.entities[pendingState.componentKey]
    if (!targetComp) return {}
    const define = componentDefineMap.entities[targetComp.defineKey]
    return define.eventActionDefine?.actionDefine
  }, [pendingState.componentKey, componentMap, componentDefineMap])

  const Comp: React.ComponentType<ComponentBaseProps<any>> =
    ActionConfigPanelDict[pendingState.params?.actionName] || ActionConfigPanelDict.customActionAdapt

  return (
    <Form
      layout='vertical'
      className='border border-solid rounded border-gray-300 !px-4 !py-2'
      fields={fields}
      onFieldsChange={onFieldsChange}
    >
      <Form.Item label='目标控件' name='componentKey'>
        <Select
          getPopupContainer={triggerNode => triggerNode.parentNode}
          placeholder='请选择控件'
          options={_.map(validComponents, c => ({ label: c.alias || c.title, value: c.key }))}
          dropdownMatchSelectWidth={false}
          {...enableSelectSearch}
        />
      </Form.Item>
      <Form.Item label='触发动作' name={['params', 'actionName']}>
        <Select
          placeholder='请选择动作'
          getPopupContainer={triggerNode => triggerNode.parentNode}
          options={_.map(actionDefine, d => ({ label: d.title, value: d.name }))}
          dropdownMatchSelectWidth={false}
          {...enableSelectSearch}
        />
      </Form.Item>
      {!Comp || !pendingState.params?.actionName ? null : (
        <Form.Item label='动作参数' name='params'>
          {pendingState.params.description ? (
              <div
                className='action-item-content'
                title={pendingState.params.description}
                onClick={() => {
                  setPendingState(prev => produce(prev, draft => {
                    delete draft.params.description
                  }))
                }}
              >{pendingState.params.description}</div>
            )
            : (
              <Comp
                rawKey={componentKey || 'screen'}
                componentKey={pendingState.componentKey}
                componentMap={componentMap}
                componentDefineMap={componentDefineMap}
                dataSourcePickerInfo={dataSourcePickerInfo}
                loadMoreDataSourceInfo={loadMoreDataSourceInfo}
                pageNodes={pageNodes}
              />
            )}
        </Form.Item>
      )}
      <Form.Item>
        <Button
          type='primary'
          className='mr-4'
          onClick={() => {
            const data = pendingState
            const comp = _.find(validComponents, p => p.key === data.componentKey)
            if (!comp) {
              message.warn('请选择有效控件')
              return
            }
            const next = {
              ...value,
              ...data,
              description: `触发 ${comp.title || comp.key} 的 ${actionDefine?.[data.params.actionName]?.title} 动作`
            }
            onChange(_.omit(next, 'editing'))
          }}
        >
          保存
        </Button>
        <Button type='ghost' onClick={() => onChange(null)}>
          取消
        </Button>
      </Form.Item>
    </Form>
  )
}
