import { Button, Form, message, Select } from 'antd'
import _ from 'lodash'
import React, { useEffect, useMemo } from 'react'

import { DefaultEventDefines } from '@/consts/define'
import { Action } from '@/cores/evaction/types/action.d'
import { useFormFieldsAdapter } from '@/hooks/use-form-fields-adapter'
import { ComponentBaseProps } from '@/pages/screen/components/workbench/event-config/type'
import { enableSelectSearch } from '@/utils'


/** 触发其他事件配置面板 */
export default function TriggerEventConfigPanel(props: ComponentBaseProps<Action.TriggerEvent>) {
  const { componentKey, value, onChange, componentMap, componentDefineMap } = props
  const [pendingState, setPendingState] = React.useState(value)
  useEffect(() => {
    setPendingState(value)
  }, [value])

  const { fields, onFieldsChange } = useFormFieldsAdapter(pendingState, setPendingState)

  const validComponents = useMemo(() => {
    const allComponentDefines1 = _.values(componentMap.entities)
    return _.orderBy(allComponentDefines1, d => (d.key === componentKey ? 0 : 1))
  }, [])

  // 事件列表
  const eventDefine = useMemo(() => {
    const targetComp = componentMap.entities[pendingState.componentKey]
    if (!targetComp) return {}
    const define = componentDefineMap.entities[targetComp.defineKey]
    return {
      ...(define.eventActionDefine?.eventDefine || {}),
      ...DefaultEventDefines.events
    }
  }, [pendingState.componentKey, componentMap, componentDefineMap])

  return (
    <Form
      layout='vertical'
      className='border border-solid rounded border-gray-300 !px-4 !py-2'
      fields={fields}
      onFieldsChange={onFieldsChange}
    >
      <Form.Item label='目标控件' name='componentKey'>
        <Select
          getPopupContainer={triggerNode => triggerNode.parentNode}
          {...enableSelectSearch}
          dropdownMatchSelectWidth={false}
          placeholder='请选择控件'
          options={_.map(validComponents, c => ({ label: c.alias || c.title, value: c.key }))}
        />
      </Form.Item>
      <Form.Item label='触发事件' name='eventName'>
        <Select
          getPopupContainer={triggerNode => triggerNode.parentNode}
          {...enableSelectSearch}
          dropdownMatchSelectWidth={false}
          options={_.map(eventDefine, d => ({ label: d.title, value: d.name }))}
        />
      </Form.Item>
      <Form.Item>
        <Button
          type='primary'
          className='mr-4'
          onClick={() => {
            const data = pendingState
            const comp = _.find(validComponents, p => p.key === data.componentKey)
            if (!comp) {
              message.warn('请选择有效控件')
              return
            }
            const next = {
              ...value,
              ...data,
              description: `触发 ${comp.title || comp.key} 的 ${eventDefine?.[data.eventName]?.title} 事件`
            }
            onChange(_.omit(next, 'editing'))
          }}
        >
          保存
        </Button>
        <Button type='ghost' onClick={() => onChange(null)}>
          取消
        </Button>
      </Form.Item>
    </Form>
  )
}
