import type { Compo<PERSON><PERSON><PERSON>, ComponentMap, ComponentDefineMap } from '@/types/editor-core/component'
import type { DataSourceInfo, DataSourceInfoType } from '@/types/data-source'
import type { ProjectType } from '@/types/entitys/project'

export interface ComponentBaseProps<T> {
  rawKey: string // 原始的组件 key，也就是触发者
  componentKey: ComponentKey
  componentMap: ComponentMap
  componentDefineMap: ComponentDefineMap
  pageNodes: ProjectType['directory']['nodes']
  dataSourcePickerInfo: DataSourceInfo
  loadMoreDataSourceInfo: (type: DataSourceInfoType, id?: string) => Promise<any>

  value: T
  onChange: (config: T | null) => any
}
