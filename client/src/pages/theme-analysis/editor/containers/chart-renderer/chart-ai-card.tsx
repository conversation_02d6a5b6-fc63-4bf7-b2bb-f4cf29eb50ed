import './chart-ai-card.less'

import { ChatMessageCard } from '@sugo/design'
import { fastMemo } from '@sugo/design/functions'
import _ from 'lodash'
import React from 'react'

import { useCompute } from '@/pages/theme-analysis/editor/reactive'

export interface ChartAiCardProps {
  cardId: string
  tabKey: string
  height?: number
}

/**
 * 自定义组件
 * @returns
 */
function _ChartAiCard(props: ChartAiCardProps) {
  const { cardId, tabKey, height } = props
  const params = useCompute(s => s.getCardParams(cardId, tabKey, ['aiAnswerV2']))
  const msg = params?.aiAnswerV2?.context

  return (
    <div className='theme-analysis-editor-chart-ai-card' style={{ maxHeight: height }}>
      {msg ?
        <ChatMessageCard context={msg} /> :
        <div className='px-2'>
          请通过智能助手创建分析卡片
        </div>
      }
    </div>
  )
}

export const ChartAiCard = fastMemo(_ChartAiCard)
