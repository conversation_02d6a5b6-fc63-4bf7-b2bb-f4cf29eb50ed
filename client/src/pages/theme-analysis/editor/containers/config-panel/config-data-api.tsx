import { DeleteOutlined, EditOutlined, LockOutlined, PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import { Tag } from '@sugo/design'
import { copyText, fastMemo } from '@sugo/design/functions'
import { But<PERSON>, Popconfirm, Tooltip } from 'antd'
import _ from 'lodash'
import React, { useRef } from 'react'

import { CreateApiReleaseModal } from '@/pages/theme-analysis/editor/containers/modals/data-api/create-api-release-modal'
import { useDataApiAction, useDataApiCompute } from '@/pages/theme-analysis/editor/reactive/data-api'

export interface ConfigDataApiProps {
  cardId: string
  paramsKey: string
}

/**
 * 数据接口管理
 * @returns
 */
function _ConfigDataApi(props: ConfigDataApiProps) {
  const { cardId, paramsKey } = props

  const createApiRef = useRef<{ show: Function }>()
  const data = useDataApiCompute(s => s.getDataApiDetail(cardId, paramsKey))
  const { delDataApi } = useDataApiAction()

  return (
    <div className='config-base-panel config-page-setting'>
      <b className='title'>
        <span>
          <EditOutlined className='mr-1' />
          数据接口
          <Tooltip title='针对此卡片发布数据接口，提供给数据业务人员使用。'>
            <QuestionCircleOutlined />
          </Tooltip>
        </span>
      </b>

      {data && (
        <div className='mb-3 api-box'>
          <h4 className='mb-0'>
            <Tag text={data.status === 'active' ? '上线' : '下线'} color={data.status === 'active' ? 'green' : 'red'} bordered={false} size='small' />
            <span className='ml-2'>{data.title}</span>
            {data.isLock && <LockOutlined className='ml-2' />}
          </h4>
          <div>
            <Tag text='描述' autoColor bordered={false} size='small' className='!mr-2' />
            {data.description || '暂无描述'}
          </div>
          <div onDoubleClick={e => {
            e.stopPropagation()
            copyText(data.sign, true)
          }}>
            <Tag text='签名' autoColor bordered={false} size='small' className='!mr-2' />
            {data.sign}
          </div>
        </div>
      )}
      <Button type='primary' size='small' icon={data ? <EditOutlined /> : <PlusOutlined />}
        onClick={e => {
          e.stopPropagation()
          createApiRef.current?.show({ cardId, paramsKey, editData: data })
        }}>
        {data ? '编辑接口' : '发布接口'}
      </Button>
      {data && (
        <Popconfirm
          title='确定要删除吗？'
          onConfirm={() => delDataApi(data.id)}
        >
          <Button className='ml-2' danger size='small' icon={<DeleteOutlined />}>删除接口</Button>
        </Popconfirm>
      )}
      <CreateApiReleaseModal ref={createApiRef} />
    </div>
  )
}

export const ConfigDataApi = fastMemo(_ConfigDataApi)
