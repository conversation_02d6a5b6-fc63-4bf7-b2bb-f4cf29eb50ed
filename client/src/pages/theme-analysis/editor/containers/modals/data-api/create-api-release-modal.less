
.theme-analysis-editor-create-api-release-modal {

  .ant-form-item {
    margin-bottom: 20px;
    &:last-of-type {
      margin: 0;
    }
    .ant-form-item-explain {
      transform: translate(2px, 2px);
    }
  }
  .ant-form-item-label {
    padding-bottom: 4px;
  }

  .font-normal {
    font-weight: normal;
  }

  .ant-drawer-body {
    // transform: translateZ(0);
    height: max-content;
    padding: 0;
    display: flex;
    flex-direction: column;
    background-color: rgba(252, 252, 255, 0.9);
  }

  .form-container {
    height: max-content;
    display: flex;
    justify-content: center;
    overflow-y: auto;
    flex: 1;
    padding: 16px;
    // margin-right: 50%;
    position: relative;

    .form-chunk {
      flex: 1;
      max-width: 680px;
      min-width: 520px;
      height: max-content;
      &:first-of-type {
        margin-right: 20px;
      }
    }
  }

  .form-footer {
    position: sticky;
    z-index: 222;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    box-shadow: 0 -1px 6px rgba(@primary-color, 10%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px 12px;
  }

  .box-panel {
    background-color: #fff;
    border-radius: 10px;
    margin-bottom: 16px;
    box-shadow: 0 0 4px rgba(@primary-color, 12%);
    padding: 10px 12px;

    > h4 {
      font-weight: bold;
      border-bottom: 1px solid #f4f4f4;
      padding-bottom: 8px;
      position: sticky;
      top: 0;
      left: 0;
      z-index: 22;
      background-color: #fff;
      margin-bottom: 12px;
    }
  }

  .test-panel {
    max-width: 500px;
  }

  .ant-btn-sm > .anticon  + span {
    margin-left: 4px;
  }


  .doc-panel {
    h4 {
      margin-bottom: 0;
      margin-top: 10px;
    }
    .request-params {
      table {
        margin-top: 4px;
        border: 1px solid #ddd;
      }
      td, th {
        border-color: rgba(@primary-color, 10%);
        width: 120px;
        text-align: left;
        padding: 2px 4px;
        border-radius: 4px;
      }
    }
  }
}

.theme-analysis-editor-data-api-auth-setting {
  height: max-content;
  .selected-panel {
    border-left: 1px solid #f4f4f4;
    padding: 8px 12px;
    flex: 0.8;
  }

  .user-role-tabs {
    .ant-tabs-nav {
      margin-bottom: 0;
    }
    .ant-tabs-tab {
      padding: 6px 8px;
      margin-left: 12px !important;
    }
  }

  .search-input {
    margin: 5px 5px 0;
    width: calc(100% - 10px);
  }


  .selected-panel {
    overflow-y: auto;
    height: 330px;
    max-height: 330px;
  }

  .select-list {
    overflow-y: auto;
    height: 300px;
    max-height: 300px;
    padding: 4px 0;

    .select-list-item {
      padding: 6px 12px;
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        color: rgba(@primary-color, 100%);
        background-color: rgba(@primary-color, 12%);
      }
    }
  }
}
