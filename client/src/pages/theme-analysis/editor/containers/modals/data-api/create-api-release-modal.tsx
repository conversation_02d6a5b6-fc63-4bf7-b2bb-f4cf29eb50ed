import './create-api-release-modal.less'

import {
  ApiOutlined, CaretRightOutlined, CloseOutlined,
  ExperimentOutlined, PlusOutlined, SaveOutlined,
  SwapRightOutlined
} from '@ant-design/icons'
import { FieldDataIcon, HighlightText, MenuDropdown, Tag } from '@sugo/design'
import { fastMemo, withRefModal } from '@sugo/design/functions'
import { useDeepCompareEffect, useMemoizedFn } from 'ahooks'
import { Button, Checkbox, Drawer, Form, Input, message, Select, Switch, Tabs, Tooltip } from 'antd'
import cn from 'classnames'
import htmr from 'htmr'
import _ from 'lodash'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { useDeepCompareMemo } from 'use-deep-compare'

import { CodeEditor } from '@/components/code-editor'
import { getCompute, useCompute } from '@/pages/theme-analysis/editor/reactive'
import { getDataApiCompute, useDataApiAction, useDataApiStore } from '@/pages/theme-analysis/editor/reactive/data-api'
import { getDataApiUrl } from '@/pages/theme-analysis/editor/utils/data-api'

import { loadLanguageSupport } from './skill-code'

const queryAfterHookCode = `import _ from 'lodash'
// 可用库 lodash, dayjs, request
// 运行环境 nodejs v16+

/**
 * 查询后置函数
 * @return {object} { data, fields }
 */
export async function main(data: any[], fields: any[], query: any) {
  // 你的处理逻辑
  return { data, fields }
}
`

const mergeCarryHookCode = `import _ from 'lodash'
// 可用库 lodash, dayjs, request
// 运行环境 nodejs v16+

/**
 * 合并执行函数
 *
 * @parma {object} dataMap 的 key 是接口签名
 * @return {object} { data, fields }
 */
export async function main(dataMap: Record<string, { data: any[], fields: any[] }>, query: any) {
  // 你的处理逻辑
  return {
    data: [{ count: 0 }],
    fields: [{ key: 'count', title: '数量' }]
  }
}
`

const getApiDoc = (sign: string, fields: any[]) => `
<!-- API基础信息 -->
<div class='api-info mb-2'>
  <h4>基础信息</h4>
  <div>请求方法: GET/POST</div>
  <div>请求路径: {host}${getDataApiUrl(sign, false)}</div>
  <div>POST 请求头：Authorization: Bearer {token}</div>
  <div>GET 请求头：url query: jwtSign={token}</div>
</div>

<!-- 请求参数 -->
<div class='request-params mb-2 mt-2'>
  <h4>请求参数</h4>
  <table border='1'>
    <thead>
      <tr>
        <th>参数名</th>
        <th>类型</th>
        <th>描述</th>
      </tr>
    </thead>
    <tbody>
${[
    ['limit', 'number', '数量'],
    ['offset', 'number', '偏移'],
    ['orderBy', 'array', '排序'],
    ['filters', 'array', '筛选'],
    ['cache', 'number', '缓存时间（默认 60000ms）']
  ].map(item => `
<tr>
  <td>${item[0]}</td>
  <td>${item[1]}</td>
  <td>${item[2]}</td>
</tr>
`).join('\n')}
    </tbody>
  </table>
</div>

<div class='request-params mb-2'>
  <h4>接口认证</h4>
  <div>看技术提供的文档，这里不描述</div>
</div>

<!-- 响应结构 -->
<div class='response-structure'>
  <h4 class='mb-1'>响应结构</h4>
<pre>
{
  "code": 1,           // 状态码，1表示成功
  "message": "操作成功" // 状态描述
  "data": any[],       // 响应数据
  "fields": any[],     // 响应字段结构
  "latency": number,   // 耗时
  "timestamp": number  // 时间戳
}
</pre>

<div class='response-structure'>
  <h4 class='mb-1'>请求示例</h4>
<pre>
{
  "limit": 10,
  "offset": 0,
  "cache": 60000,
  "filters": [
    {
      "col": "time_date",
      "op": "in",
      "eq": ['202501'， '202508']
    }
  ],
  "orderBy": [
    { "field": "age", "dir": "desc" }
  ]
}
</pre>

<div>
  <h4 class='mb-1'>输出字段</h4>
  <ul>
    ${_.map(fields, f => (
    `<li>${f.name}（${f.title}）</li>`
  )).join('\n')}
  </ul>
</div>

<div>
  <b style="color: #f56">TODO:</b>
  <div>
  GET 请求参数放在 url query 里，如 ?limit=10&filters=[{ "col": "time_date", "op": "in", "eq": ['202501'， '202508']}]
  </div>
</div>
</div
</div>
`

export const ApiDoc = fastMemo(props => {
  const { value, cardId, paramsKey } = props
  const fields = useCompute(s => s.getCardFields(cardId, paramsKey))

  return (
    <div className='doc-panel'>
      {htmr(getApiDoc(value, fields))}
    </div>
  )
})

export const ShowUrl = fastMemo(props => {
  const { value } = props
  return (
    <div className='mt-2'>
      <a href='##'>{getDataApiUrl(value)}</a>
    </div>
  )
})

export const StatusSwitch = fastMemo(props => {
  const { value, onChange, ...rest } = props

  return (
    <Switch checked={value === 'active'} {...rest}
      onChange={e => onChange(e ? 'active' : 'inactive')}
    />
  )
})

// 字段映射
const FieldColumnMap = fastMemo(props => {
  const { cardId, paramsKey, value = {}, onChange, isMerge, onLoadFielded } = props

  const keys = _.keys(value)
  const [selectedKey, setSelectedKey] = useState(keys)
  const [mergeFields, setMergeFields] = useState<any>([])

  const fields = useCompute(s => s.getCardFields(cardId, paramsKey, undefined, undefined, true))
  const renderFieldLabel = f => (
    <div className='flex-row vcenter'>
      <FieldDataIcon dataType={f.dataType} className='mr-1' />
      <span className='flex-1 mr-2'>{f.name}</span>
      <span className='flex-1'>{f.title}</span>
    </div>
  )

  const opts = useDeepCompareMemo(() => {

    if (!isMerge) {
      onLoadFielded?.(fields)
      return _.map(fields.filter(i => i.key !== '$index'), f => ({
        label: renderFieldLabel(f) as any,
        value: f.name,
        disabled: keys.includes(f.name)
      }))
    }

    const fields2: any[] = []
    const arr = _.map(mergeFields, m => ({
      ...m,
      options: _.map(m.options, f => ({
        ...f,
        name: `${m.sign}_${f.name}`
      })).map(ff => {
        fields2.push(ff)
        return {
          label: renderFieldLabel(ff) as any,
          value: ff.name,
          disabled: keys.includes(ff.name)
        }
      })
    }))

    onLoadFielded?.(fields2)
    return arr
  }, [fields, keys, mergeFields, isMerge])

  useDeepCompareEffect(() => {
    setSelectedKey(_.keys(value))
  }, [value])

  return (
    <div>
      <Form.Item noStyle dependencies={['mainApi', 'subApis']}>
        {({ getFieldsValue }) => {
          const data = getFieldsValue()
          const ids = _.compact([data.mainApi?.id].concat(_.map(data.subApis, i => i.id)))
          const options: any[] = []
          _.forEach(ids, id => {
            const info = getDataApiCompute().getContext(id)
            const _fields = getCompute().getCardFields(info.cardId!, info.paramsKey!, undefined, undefined, true)
            options.push({ label: `${info.sign}（${info.title}）`, sign: info.sign, options: _fields })
          })
          if (!_.isEqual(options, mergeFields)) {
            setTimeout(() => {
              setMergeFields(options)
            }, 100)
          }
          return null
        }}
      </Form.Item>
      {_.map(selectedKey, key => (
        <div className='flex-row vcenter mb-2' key={key}>
          <Select
            options={opts} placeholder='请选择字段'
            value={key === '$' ? undefined : key}
            style={{ maxWidth: '50%' }}
            onChange={val => {
              onChange({ ...value, [val]: value?.[key] || val })
            }}
          />
          <SwapRightOutlined className='ml-2 mr-2' />
          <Input placeholder='请输入映射字段名（变量名）'
            style={{ maxWidth: '50%' }}
            value={value?.[key]}
            onChange={e => {
              const val = _.trim(e.target.value)
              onChange({ ...value, [key]: val })
            }}
          />
          <Button
            icon={<CloseOutlined />} style={{ minWidth: 24 }} size='small' shape='circle' className='ml-2'
            onClick={() => {
              onChange(_.omit(value, key))
              setSelectedKey(keys.filter(i => i !== key))
            }}
          />
        </div>
      ))}
      <Button
        type='primary' size='small'
        onClick={() => setSelectedKey(_.uniq([...keys, '$']))}
      >
        添加映射
      </Button>
    </div>
  )
})

// 选择器
const SelectApi = fastMemo(props => {
  const { value, onChange, placeholder, className, showDel, onDel, ignoreId } = props

  const [selected, setSelected] = useState<any>()
  const fields = useMemo(() => getCompute().getCardFields(selected?.cardId, selected?.paramsKey, undefined, undefined, true), [selected])

  const [apiList, apiListLoading] = useDataApiStore(s => [s.list, s.loading])
  // 获取数据接口列表
  const apiOpts = useMemo(() => _.map(apiList, i => ({
    label: (
      <div className='flex-row vcenter'>
        <ApiOutlined className='mr-2' />
        <span className='flex-1 mr-2'>{i.title}</span>
        <span className='flex-1'>{i.sign}</span>
      </div>
    ),
    value: i.id,
    cardId: i.cardId,
    paramsKey: i.paramsKey
  })).filter(i => i.value !== ignoreId), [apiList, ignoreId])

  const fieldOpts = useMemo(() => _.uniqBy(_.map(fields, f => ({
    value: f.name,
    label: (
      <div className='flex-row vcenter'>
        <FieldDataIcon dataType={f.dataType} className='mr-1' />
        <span className='flex-1 mr-1'>{f.name}</span>
        <span className='flex-1'>{f.title}</span>
      </div>
    )
  })), 'value'), [fields])

  useEffect(() => {
    setSelected(apiList.find(i => i.id === value?.id))
  }, [value?.id])

  return (
    <div className={cn('flex-row vstart w-full', className)}>
      <Select
        value={value?.id}
        placeholder={placeholder} loading={apiListLoading} options={apiOpts}
        onChange={v => {
          setSelected(apiList.find(i => i.id === v))
          const newValue = { ...value, id: v }
          if (!v) newValue.fields = undefined
          onChange(newValue)
        }}
        allowClear
      />
      <Select
        value={_.isEmpty(value?.fields) ? undefined : _.castArray(value?.fields)}
        placeholder='选择链接关系字段' loading={apiListLoading} options={fieldOpts} className='ml-2 w-[50%]'
        mode='multiple'
        onChange={v => {
          onChange({ ...value, fields: v })
        }}
      />
      {showDel ?
        <Button icon={<CloseOutlined />} className='ml-2 mt-1' shape='circle' size='small' title='移除' onClick={onDel} /> :
        <div style={{ width: 66 }} />
      }
    </div>
  )
})

// 选择多个
const SelectApiMult = fastMemo(props => {
  const { value = [], onChange, ignoreId } = props

  return (
    <div className=''>
      {_.map(value, (item, idx) => (
        <div key={item.id}>
          <SelectApi
            key={item.id}
            value={item}
            ignoreId={ignoreId}
            placeholder='选择副属接口'
            className='mb-2'
            showDel
            onDel={() => onChange(_.filter(value, (_v, idx2) => idx2 !== idx))}
            onChange={val => {
              value[idx] = val
              onChange([...value].filter(i => !_.isEmpty(i)))
            }}
          />
        </div>
      ))}
      <Button type='primary' onClick={() => onChange([...value, {}])} icon={<PlusOutlined />} size='small'>
        添加
      </Button>
    </div>
  )
})

const HooksEditor = fastMemo(props => {
  const { value, onChange, defaultCode } = props

  return (
    <div>
      <Switch
        unCheckedChildren='关闭' checkedChildren='启用'
        checked={value?.enable}
        onChange={val => {
          const newVal = { ...value, enable: val }
          if (val && !value?.code) {
            newVal.code = defaultCode
          }
          onChange?.(newVal)
        }}
      />
      {value?.enable &&
        <CodeEditor
          className='mt-2'
          value={value?.code} language='typescript' theme='vs-dark' style={{ height: 300 }}
          onChange={val => onChange?.({ ...value, code: val })}
          onLoadLanguageSupport={loadLanguageSupport}
        />
      }
    </div>
  )
})

const AuthUserOrRoleSetting = fastMemo(props => {
  const { value, onChange } = props
  const { loadUserRole } = useDataApiAction()
  const [userMap, roleMap] = useDataApiStore(s => [s.userMap, s.roleMap])

  // 加载用户，加载角色列表
  const [activeKey, setActiveKey] = useState('user')
  const [keyword, setKeyword] = useState('')

  // 分组
  const group = useMemo(() => {
    const dict: Record<string, any> = {}
    dict.role = _.filter(value, v => String(v).indexOf('role:') > -1)
      .map(i => roleMap[String(i).replace(/role:/, '')]).filter(i => i)
    dict.user = _.filter(value, v => String(v).indexOf('user:') > -1)
      .map(i => userMap[String(i).replace(/user:/, '')]).filter(i => i)

    return dict
  }, [value, userMap, roleMap])

  const renderUserList = (list: any[], type: string) => {
    const arr = keyword ? _.filter(list, i => _.toLower(i.name).indexOf(_.toLower(keyword)) > -1) : list

    return (
      <>
        <Input
          value={keyword} onChange={e => setKeyword(e.target.value)}
          placeholder='输入关键字搜索'
          className='search-input'
          allowClear
        />
        <div className='select-list'>
          {_.map(arr, item => {
            const check = String(value).indexOf(item.id) > -1
            return (
              <div
                className='flex-row select-list-item' key={item.id}
                onClick={e => {
                  e.stopPropagation()
                  if (check) {
                    onChange?.(_.filter(value, v => String(v).indexOf(item.id) === -1))
                  } else {
                    onChange?.([...value || [], `${type}:${item.id}`])
                  }
                }}
              >
                <Checkbox checked={check} className='mr-2' />
                {keyword ?
                  <HighlightText className='flex-1' text={item.name} keyword={keyword} /> :
                  <span className='flex-1'>{item.name}</span>
                }
              </div>
            )
          })}
        </div>
      </>
    )
  }

  const renderSelectedList = list => _.map(list, r => (
    <div key={r.id}>
      <span>{r.name}</span>
    </div>
  ))

  const items = [
    { key: 'user', label: '用户', children: renderUserList(_.values(userMap), 'user') },
    { key: 'role', label: '角色', children: renderUserList(_.values(roleMap), 'role') }
  ]

  const renderContent = () => (
    <div className='theme-analysis-editor-data-api-auth-setting flex-row'>
      <Tabs
        items={items}
        className='flex-1 user-role-tabs'
        activeKey={activeKey}
        onChange={val => {
          setActiveKey(val)
          setKeyword('')
        }}
        size='small'
      />
      <div className='flex-1 selected-panel'>
        {_.isEmpty(value) && '未选择用户或角色'}
        {!_.isEmpty(value) &&
          <div>
            {!_.isEmpty(group.role) && (
              <div>
                <h4 className='mt-0'>角色</h4>
                {renderSelectedList(group.role)}
              </div>
            )}
            {!_.isEmpty(group.user) && (
              <div className='mt-2'>
                <h4 className='mb-0'>用户</h4>
                {renderSelectedList(group.user)}
              </div>
            )}
          </div>
        }
      </div>
    </div>
  )

  const notRestrict = String(value) === '*'
  const label = `${_.size(group.user)} 个用户，${_.size(group.role)} 个角色`
  const options = [{ value: '$', label }]
  const selectValue = (notRestrict || _.isEmpty(value)) ? undefined : '$'

  useEffect(() => {
    loadUserRole()
  }, [])

  return (
    <div>
      <div className='flex-row mb-2'>
        <span>不限制：</span>
        <Switch checked={notRestrict} unCheckedChildren='关闭' checkedChildren='启用'
          onChange={val => {
            if (val) onChange?.(['*'])
            else onChange(undefined)
          }}
        />
      </div>
      <Select disabled={notRestrict} value={selectValue} options={options} dropdownRender={renderContent} placeholder='授权用户或角色' />
    </div>
  )
})

/**
 * 创建发布 API
 * @returns
 */
const _CreateApiReleaseModal = withRefModal(props => {
  const { visible, modal, editData, cardId, paramsKey, mode = 'base', isReal } = props

  const [submitLoading, setSubmitLoading] = useState(false)
  const [form] = Form.useForm()
  const [test, setTest] = useState(false)
  const [latency, setLatency] = useState(0)

  const fieldsRef = useRef<Record<string, any>[]>()
  const domRef = useRef<HTMLDivElement | null>(null)

  const { testQueryDataApi, saveDataApi, testQueryDataApiReal } = useDataApiAction()
  const isMerge = mode === 'merge'

  const initialValues = {
    status: 'active',
    type: 'themeAnalysis',
    sign: `sugo_${Math.random().toString(32).slice(2, 10)}`,
    testCode: isMerge ? `// 查询参数（合并时为 key-map 结构）
({
  '你的签名': {
    limit: 20,
    offset: 0,
    cache: 60000
  }
})
    ` : `// 查询参数
({
  limit: 20,
  offset: 0,
  cache: 60000
})
`
  }

  const onLoadFielded = useMemoizedFn(fields => fieldsRef.current = fields)

  const onCancel = () => {
    modal.hide()
    form.resetFields()
    setLatency(0)
    setTest(false)
    setSubmitLoading(false)
  }

  const onOk = async () => {
    setSubmitLoading(true)
    try {
      await form.validateFields()
    } catch (err) {
      const el = document.querySelector('.theme-analysis-editor-create-api-release-modal .ant-drawer-body')
      if (el) el.scrollTo({ top: 0, behavior: 'smooth' })

      message.error('请填写必填项！')
      setSubmitLoading(false)
      return
    }
    try {
      const data = form.getFieldsValue()
      data.fieldColumnMap = _.omit(data.fieldColumnMap, '$')

      await saveDataApi({
        ..._.omit(data, ['testCode', 'testResult']),
        cardId, paramsKey
      }, editData?.id)
      onCancel()
      message.success('操作成功')
    } catch (err: any) {
      message.error(`操作失败：${err.message}`)
      console.error(err)
    } finally {
      setSubmitLoading(false)
    }
  }

  const onTest = async e => {
    e.stopPropagation()
    try {
      setSubmitLoading(true)

      const el = document.querySelector('.theme-analysis-editor-create-api-release-modal .ant-drawer-body')
      if (el) el.scrollTo({ top: 999999, behavior: 'smooth' })

      await new Promise(rs => setTimeout(rs, 200))

      const data = form.getFieldsValue()
      const res = isReal ?
        await testQueryDataApiReal(data.sign, data.testCode) :
        await testQueryDataApi(cardId, paramsKey, data.testCode, data)

      form.setFieldValue('testResult', JSON.stringify(res, null, 2))
      setLatency(res.latency)
      setTest(true)
    } finally {
      setSubmitLoading(false)
    }
  }


  useEffect(() => {
    if (visible && mode) {
      form.setFieldValue('mode', mode)
    }
  }, [visible, mode, form])

  useEffect(() => {
    if (visible && editData) {
      console.log(editData)
      form.setFieldValue('title', editData.title)
      form.setFieldValue('sign', editData.sign)
      form.setFieldValue('mode', editData.mode)
      form.setFieldValue('description', editData.description)
      form.setFieldValue('isLock', editData.isLock)
      form.setFieldValue('defaultQuery', editData.defaultQuery)
      form.setFieldValue('fieldColumnMap', editData.fieldColumnMap)
      form.setFieldValue('desensitizeConfig', editData.desensitizeConfig)
      form.setFieldValue('authorizes', editData.authorizes)
      form.setFieldValue('status', editData.status)
      form.setFieldValue('mainApi', editData.mainApi)
      form.setFieldValue('subApis', editData.subApis)
      form.setFieldValue('mergeCarryHook', editData.mergeCarryHook)
      form.setFieldValue('queryAfterHook', editData.queryAfterHook)
      form.setFieldValue('includeTotal', editData.includeTotal)
    }
  }, [editData, visible, form])

  return (
    <Drawer
      open={visible}
      title={`${editData ? '编辑' : '发布'}数据 API`}
      width={640}
      placement='bottom'
      height='85%'
      onClose={onCancel}
      className='theme-analysis-editor-create-api-release-modal'
      destroyOnClose
    >
      <Form form={form} layout='vertical' initialValues={initialValues}>
        <div className='form-container' ref={domRef}>

          <div className='flex-1 mr-4 test-panel'>

            <Form.Item name='mode' noStyle hidden />

            <div className='box-panel'>
              <h4>接口文档</h4>
              <Form.Item name='sign' noStyle shouldUpdate>
                <ApiDoc cardId={cardId} paramsKey={paramsKey} />
              </Form.Item>
            </div>
            <div className='box-panel'>
              <h4 className='flex-row vcenter'>
                <span className='flex-1'>接口调试</span>
                {!!latency &&
                  <span className='mr-2 font-normal'>{latency}ms</span>
                }
                <Button
                  type='primary' size='small'
                  icon={<CaretRightOutlined />}
                  loading={submitLoading}
                  onClick={onTest}
                >
                  运行
                </Button>
              </h4>
              <Form.Item name='testCode' noStyle shouldUpdate>
                <CodeEditor language='javascript' theme='vs-dark' style={{ height: 240 }} />
              </Form.Item>
            </div>
            <div className='box-panel'>
              <h4>返回结果</h4>
              <Form.Item name='testResult' noStyle shouldUpdate>
                <CodeEditor language='json' theme='vs-dark' style={{ height: 300 }} readOnly />
              </Form.Item>
            </div>
          </div>

          <div className='form-chunk flex-1'>
            <header className='box-panel'>
              <h3>数据接口</h3>
              <Tag text='GET' autoColor bordered={false} size='small' className='!mr-2' />
              <Tag text='POST' autoColor bordered={false} size='small' />
              <Form.Item name='sign' noStyle shouldUpdate>
                <ShowUrl />
              </Form.Item>
              <Form.Item name='type' hidden />
            </header>

            <div className='box-panel'>
              <h4>基础设置</h4>
              <Form.Item label='接口名称' name='title' rules={[{ required: true, message: '请输入名称' }]}>
                <Input placeholder='请输入名称' showCount maxLength={30} />
              </Form.Item>
              <Form.Item label='接口签名' name='sign'
                help='请求签名，全局唯一，只允许字母、数字、下划线、横杆线和 $'
                rules={[
                  { required: true, message: '请输入签名' },
                  { pattern: /^[a-zA-Z0-9_$-]+$/, message: '只允许字母、数字、下划线、横杆线和 $' }
                ]}
              >
                <Input placeholder='请输入签名' showCount maxLength={30} />
              </Form.Item>
              <Form.Item label='接口描述' name='description' rules={[{ required: true, message: '请输入描述' }]}>
                <Input.TextArea rows={2} placeholder='请输入描述' showCount maxLength={300} />
              </Form.Item>
              {/* <Form.Item label='锁定字段' name='isLock' help='默认不锁定，即用户编辑看板，接口输出也会变化'>
                <Switch checkedChildren='锁定' unCheckedChildren='不锁定' />
              </Form.Item> */}
              {!isMerge &&
                <Form.Item label='查询总数' name='includeTotal' help='多查询一次，花费更多时间。合并接口取主接口的总数。（目前只支持：库表、视图、集合）' required
                  valuePropName='checked'
                >
                  <Switch checkedChildren='启用' unCheckedChildren='关闭' />
                </Form.Item>
              }

              <Form.Item label='上线状态' name='status' help='默认发布即上线' required>
                <StatusSwitch checkedChildren='上线' unCheckedChildren='下线' />
              </Form.Item>
            </div>

            <div className='box-panel'>
              <h4>字段设置</h4>
              {/* <Form.Item label='默认参数' name='defaultQuery' help='默认 limit 100'>
                <Input placeholder='请输入描述' />
              </Form.Item> */}
              <Form.Item label={(
                <div className='flex-row vcenter'>
                  <span>字段映射</span>
                  <MenuDropdown
                    menuItems={[
                      { key: 'pascalCase', label: '大驼峰（Pascal Case）' },
                      { key: 'camelCase', label: '小驼峰（Camel Case）' },
                      { key: 'snakeCase', label: '​下划线（Snake Case）' },
                      { key: 'kebabCase', label: '​烤串法（Kebab Case）' }
                    ]}
                    onMenuClick={e => {
                      e.domEvent.stopPropagation()
                      if (_.isEmpty(fieldsRef.current)) return

                      form.setFieldValue('fieldColumnMap', _.reduce(fieldsRef.current, (acc, f) => {
                        let name = _.camelCase(f.name)
                        if (e.key === 'pascalCase') name = _.upperFirst(_.camelCase(name))
                        if (e.key === 'snakeCase') name = _.snakeCase(f.name)
                        if (e.key === 'kebabCase') name = _.kebabCase(f.name)
                        return {
                          ...acc,
                          [f.name]: name
                        }
                      }, {}))
                    }}
                  >
                    <Tag text='一键别名' color='#3b6' bordered={false} size='small' className='!ml-2' />
                  </MenuDropdown>
                </div>
              )} name='fieldColumnMap' help='输出别名设置，默认使用字段列名输出'>
                <FieldColumnMap cardId={cardId} paramsKey={paramsKey} isMerge={isMerge} onLoadFielded={onLoadFielded} />
              </Form.Item>
              {/* <Form.Item label='字段脱敏' name='desensitizeConfig' help='默认不脱敏'>
                <Input placeholder='请输入描述' />
              </Form.Item> */}
            </div>

            {isMerge &&
              <div className='box-panel'>
                <h4>合并设置</h4>
                <Form.Item label='主要接口' name='mainApi' rules={[{ required: true, message: '请选择主要接口' }]} help='建议业务表等明细数据'>
                  <SelectApi placeholder='选择主要接口' ignoreId={editData?.id} />
                </Form.Item>

                <Form.Item label='副属接口' name='subApis' rules={[{ required: true, message: '请选择副属接口' }]} help='建议指标表等聚合数据'>
                  <SelectApiMult placeholder='选择副属接口' ignoreId={editData?.id} />
                </Form.Item>
              </div>
            }

            <div className='box-panel'>
              <h4>执行钩子</h4>
              {isMerge &&
                <Form.Item label='自定义合并执行函数' name='mergeCarryHook' help='合并执行函数，可手动进行合并数据'>
                  <HooksEditor defaultCode={mergeCarryHookCode} />
                </Form.Item>
              }
              <Form.Item label='查询后置执行函数' name='queryAfterHook' help='查询后置函数，可对数据进行二次处理'>
                <HooksEditor defaultCode={queryAfterHookCode} />
              </Form.Item>
            </div>

            <div className='box-panel'>
              <h4>授权设置</h4>
              <Form.Item label='使用授权' name='authorizes' help='默认仅发布人可使用'>
                <AuthUserOrRoleSetting />
              </Form.Item>
            </div>
          </div>

        </div>
      </Form>

      <footer className='form-footer'>
        <Button className='mr-3' onClick={onCancel} icon={<CloseOutlined />}>取消 </Button>
        <Button className='mr-3' onClick={onTest} loading={submitLoading} icon={<ExperimentOutlined />}>测试 </Button>
        <Tooltip title={test ? undefined : '请先运行测试再发布'}>
          <Button type='primary' disabled={!test} onClick={onOk} icon={<SaveOutlined />}>发布 </Button>
        </Tooltip>
      </footer>
    </Drawer>
  )
})

export const CreateApiReleaseModal = fastMemo(_CreateApiReleaseModal)
