
.theme-analysis-editor-data-api-modal {
  position: absolute;
  z-index: 774;

  .ant-modal-body {
    padding: 0;
    min-height: 600px;

    .ant-pagination {
      padding-right: 16px;
    }
    .design-filter-action-bar {
      padding: 0 16px;
      min-height: 0;
      margin-bottom: 6px;
    }
  }

  .design-filter-action-bar {
    position: sticky;
    left: 0;
    right: 0;
    top: 0;
    z-index: 778;
  }

  .ant-drawer-mask {
    background-color: transparent;
  }

  .ant-drawer-body {
    padding: 10px 12px;
  }

  .ant-drawer-content-wrapper {
    box-shadow: 0 0 12px rgba(@primary-color, 16%);
  }

  .ant-table {
    .ant-table-cell {
      padding: 10px 12px;
    }
  }
}
