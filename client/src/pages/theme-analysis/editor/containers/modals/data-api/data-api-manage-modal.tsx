import './data-api-manage-modal.less'

import { ApartmentOutlined, FrownFilled, SmileOutlined } from '@ant-design/icons'
import { FilterActionBar, Tag } from '@sugo/design'
import { fastMemo, withRefModal } from '@sugo/design/functions'
import { history } from '@umijs/max'
import { useDeepCompareEffect, useMemoizedFn } from 'ahooks'
import { But<PERSON>, Drawer, Modal, Popconfirm, Switch, Table } from 'antd'
import _ from 'lodash'
import React, { useRef } from 'react'

import { useNewBi } from '@/hooks/use-new-bi'
import { CreateApiReleaseModal } from '@/pages/theme-analysis/editor/containers/modals/data-api/create-api-release-modal'
import { useAction } from '@/pages/theme-analysis/editor/reactive'
import { useDataApiAction, useDataApiStore } from '@/pages/theme-analysis/editor/reactive/data-api'

/**
 * 数据 API 管理
 * @returns
 */
const _DataApiManageModal = withRefModal(props => {
  const { visible, modal, useModal, showAll } = props

  const { updateDataApiStatus, loadDataApiList, delDataApi, setDataApiFilter, resetDataApiFilter } = useDataApiAction()
  const [list, total, loading] = useDataApiStore(s => [s.list, s.total, s.loading])
  const filter = useDataApiStore(s => s.filter)
  const totatMap = useDataApiStore(s => s.totatMap)
  const { markNewBi } = useNewBi()

  const mergeModalRef = useRef<{ show: Function }>()

  const { setActiveCardKey } = useAction()

  const onFilterBarChange = useMemoizedFn((val, { key }) => {
    setDataApiFilter(key === 'page' ? val : { ...val, page: 1 })
    // loadDataApiList()
  })

  const columns = [
    {
      title: '类型', dataIndex: 'mode', width: 70, render: v => (
        <Tag
          text={v === 'merge' ? '合并' : '基础'}
          bordered={false} size='small'
          color={v === 'merge' ? '#39f' : '#f80'}
        />
      )
    },
    { title: '接口名称', dataIndex: 'title' },
    { title: '接口签名', dataIndex: 'sign' },
    { title: '接口描述', dataIndex: 'description' },
    {
      title: '接口状态', dataIndex: 'status', width: 90,
      render: (s, r) => <Switch checked={s === 'active'} checkedChildren='上线' unCheckedChildren='下线'
        onChange={val => {
          // if (v)
          if (!val) {
            Modal.confirm({
              title: '你确定要下线该接口吗？',
              onOk: () => updateDataApiStatus(r.id, 'inactive')
            })
          } else {
            updateDataApiStatus(r.id, 'active')
          }
        }}
      />
    },

    { title: '调用次数', dataIndex: 'count', width: 90, render: (_v, r) => `${_.round(Number(totatMap[r.id]?.count), 0) || 0} 次` },
    {
      title: '平均耗时', dataIndex: 'time', width: 125, render: (_v, r) => {
        const ms = _.round(Number(totatMap[r.id]?.avg), 0) || 0
        const s = _.round(ms / 1000, 2)
        const color = s > 3 ? '#f45' : '#3a5'
        const t = s > 60 ? `${_.floor(s / 60, 0)} 分 ${_.round(s % 60, 2)} 秒` : `${s} 秒`
        const icon = s > 3 ? <FrownFilled /> : <SmileOutlined />
        if (ms === 0) return null

        return (
          <span style={{ color }}>{icon} {ms < 10000 ? `${ms} ms` : t}</span>
        )
      }
    },
    {
      title: '操作', dataIndex: '$action', width: showAll ? 110 : 160, render: (_v, r) => (
        <div className={showAll ? 'w-[110px]' : 'w-[160px]'}>

          {showAll && (
            <Button
              size='small' className='mr-2'
              onClick={e => {
                e.stopPropagation()
                history.push(`/theme-analysis/editor/${r.themeId}?${markNewBi}`)
                modal.hide()
              }}
            >进入分析 </Button>
          )}

          {!showAll && (
            <>
              <Button
                size='small' className='mr-2'
                onClick={e => {
                  e.stopPropagation()
                  mergeModalRef.current?.show({ editData: r, mode: r.mode, cardId: r.cardId, paramsKey: r.paramsKey, isReal: showAll })
                }}
              >查看 </Button>
              <Button
                size='small' className='mr-2'
                onClick={e => {
                  e.stopPropagation()
                  setActiveCardKey(r.cardId)
                  modal.hide()
                }}
              >定位 </Button>
              <Popconfirm
                title='确定要删除吗？'
                onConfirm={() => delDataApi(r.id)}
              >
                <Button danger size='small'>删除 </Button>
              </Popconfirm>
            </>
          )
          }
        </div>
      )
    }
  ]

  const Content = (
    <div>
      <FilterActionBar
        schemas={[
          { key: 'title', type: 'Search', wait: 600, trigger: 'change', placeholder: '请输入接口名称', props: { style: { width: 175 } } },
          { key: 'sign', type: 'Search', wait: 600, trigger: 'change', placeholder: '请输入接口签名', props: { style: { width: 175 } } },
          {
            key: 'mode', type: 'Select',
            placeholder: '接口类型',
            options: [
              { value: '', label: '全部' },
              { value: 'base', label: '基础' },
              { value: 'merge', label: '合并' }
            ]
          },
          { key: 'reset', type: 'ResetButton', text: '', props: { title: '刷新' } },
          { key: 'merge', type: 'Button', buttonType: 'primary', text: '合并接口', position: 'right', icon: <ApartmentOutlined />, hideFn: () => showAll }
        ]}
        value={filter}
        onChange={onFilterBarChange}
        onClick={(e, key) => {
          e.stopPropagation()
          if (key === 'reset') resetDataApiFilter()
          if (key === 'merge') mergeModalRef.current?.show({ mode: 'merge' })
        }}
      />
      <Table
        columns={columns}
        dataSource={list}
        loading={loading}
        rowKey='id'
        pagination={{
          total,
          showTotal: () => `共 ${total} 条`,
          pageSize: filter.pageSize,
          current: filter.page,
          showSizeChanger: false,
          onChange: page => onFilterBarChange({ page }, { key: 'page' })
        }}
      />

      <CreateApiReleaseModal ref={mergeModalRef} />
    </div>
  )

  const onCancel = () => {
    modal.hide()
  }

  useDeepCompareEffect(() => {
    if (visible) loadDataApiList()
  }, [visible, filter])

  if (useModal) return (
    <Modal
      open={visible}
      title='数据 API 管理'
      onOk={onCancel}
      onCancel={onCancel}
      width={1250}
      centered
      footer={null}
      style={{ position: 'relative' }}
      className='theme-analysis-editor-data-api-modal'
    >
      {Content}
    </Modal>
  )

  return (
    <Drawer
      open={visible}
      title='数据 API 管理'
      onClose={onCancel}
      placement='left'
      width={1140}
      className='theme-analysis-editor-data-api-modal'
      getContainer={() => document.querySelector('.theme-analysis-editor-content-wrapper') || document.body}
    >
      {Content}
    </Drawer>
  )
})

export const DataApiManageModal = fastMemo(_DataApiManageModal)
