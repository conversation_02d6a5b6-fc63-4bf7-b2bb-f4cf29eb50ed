/* eslint-disable max-len */
export const lodashTypes = `
declare module 'lodash' {
  export interface LoDashStatic {
    // 集合方法
    each<T = any>(collection: T[] | null | undefined, iteratee?: (value: T, index: number, collection: T[]) => any): T[];
    forEach<T = any>(collection: T[] | Dictionary<T> | null | undefined, iteratee?: (value: T, index: number, collection: T[]) => any): T[];
    map<T = any, TResult>(collection: T[] | null | undefined, iteratee?: (value: T, index: number, collection: T[]) => TResult): TResult[];
    filter<T = any>(collection: T[] | null | undefined, predicate?: (value: T, index: number, collection: T[]) => boolean): T[];
    reduce<T = any, TResult>(collection: T[] | null | undefined, iteratee?: (result: TResult, value: T, index: number, collection: T[]) => TResult, accumulator?: TResult): TResult;
    find<T = any>(collection: T[] | null | undefined, predicate?: (value: T, index: number, collection: T[]) => boolean, fromIndex?: number): T | undefined;
    some<T = any>(collection: T[] | null | undefined, predicate?: (value: T, index: number, collection: T[]) => boolean): boolean;
    every<T = any>(collection: T[] | null | undefined, predicate?: (value: T, index: number, collection: T[]) => boolean): boolean;
    groupBy<T = any>(collection: T[] | null | undefined, iteratee?: (value: T) => string): Dictionary<T[]>;
    orderBy<T = any>(collection: T[] | null | undefined, iteratees?: string | ((value: T) => any) | Array<string | ((value: T) => any)>, orders?: string | string[]): T[];

    // 数组方法
    chunk<T = any>(array: T[] | null | undefined, size?: number): T[][];
    compact<T = any>(array: T[] | null | undefined): T[];
    concat<T = any>(array: T[] | null | undefined, ...values: Array<T | T[]>): T[];
    difference<T = any>(array: T[] | null | undefined, ...values: Array<T[] | null | undefined>): T[];
    drop<T = any>(array: T[] | null | undefined, n?: number): T[];
    flatten<T = any>(array: T[][] | null | undefined): T[];
    flattenDeep<T = any>(array: any[] | null | undefined): T[];
    intersection<T = any>(...arrays: Array<T[] | null | undefined>): T[];
    pull<T = any>(array: T[], ...values: T[]): T[];
    push<T = any>(...values: T[]): T[];
    union<T = any>(...arrays: Array<T[] | null | undefined>): T[];
    uniq<T = any>(array: T[] | null | undefined): T[];
    uniqBy<T = any>(array: T[] | null | undefined, fn?: any): T[];
    keyBy<T = any>(array: T[] | null | undefined, fn?: any): T[];
    zip<T1, T2>(arrays1: T1[] | null | undefined, arrays2: T2[] | null | undefined): Array<[T1, T2]>;

    // 对象方法
    assign<T = any, S>(object: T, source: S): T & S;
    clone<T = any>(value: T): T;
    cloneDeep<T = any>(value: T): T;
    defaults<T = any, S>(object: T, source: S): T & S;
    findKey<T = any>(object: T | null | undefined, predicate?: (value: any, key: string, object: T) => boolean): string | undefined;
    forIn<T = any>(object: T | null | undefined, iteratee?: (value: any, key: string, object: T) => any): T;
    get<T = any>(object: T | null | undefined, path: string | string[], defaultValue?: any): any;
    has(object: object | null | undefined, path: string | string[]): boolean;
    invert(object: object | null | undefined): Dictionary<string>;
    keys(object: object | null | undefined): string[];
    merge<T = any, S>(object: T, source: S): T & S;
    omit<T = any>(object: T | null | undefined, ...paths: Array<string | string[]>): Partial<T>;
    pick<T = any>(object: T | null | undefined, ...paths: Array<string | string[]>): Partial<T>;
    set<T = any>(object: T, path: string | string[], value: any): T;
    toPairs(object: object | null | undefined): Array<[string, any]>;

    // 实用方法
    debounce<T extends (...args: any[]) => any>(func: T, wait?: number, options?: DebounceSettings): T & Cancelable;
    throttle<T extends (...args: any[]) => any>(func: T, wait?: number, options?: ThrottleSettings): T & Cancelable;
    random(lower?: number, upper?: number, floating?: boolean): number;
    range(start?: number, end?: number, step?: number): number[];
    times<T = any>(n: number, iteratee?: (index: number) => T): T[];
    uniqueId(prefix?: string): string;

    // 链式调用
    chain<T = any>(value: T): LoDashExplicitWrapper<T>;

    // 其他常用方法
    isEqual(value: any, other: any): boolean;
    isEmpty(value: any): boolean;
    isFunction(value: any): boolean;
    isObject(value: any): boolean;
    isString(value: any): boolean;
    isArray(value: any): boolean;
    isNumber(value: any): boolean;
    isBoolean(value: any): boolean;
    isDate(value: any): boolean;
    isNil(value: any): boolean;
    isNull(value: any): boolean;
    isUndefined(value: any): boolean;

    // 数学方法
    add(augend: number, addend: number): number;
    ceil(number: number, precision?: number): number;
    floor(number: number, precision?: number): number;
    max<T = any>(array: T[] | null | undefined): T | undefined;
    mean(array: number[] | null | undefined): number;
    min<T = any>(array: T[] | null | undefined): T | undefined;
    round(number: number, precision?: number): number;
    sum(array: number[] | null | undefined): number;

    // 字符串方法
    camelCase(string?: string): string;
    capitalize(string?: string): string;
    escape(string?: string): string;
    kebabCase(string?: string): string;
    lowerCase(string?: string): string;
    pad(string?: string, length?: number, chars?: string): string;
    repeat(string?: string, n?: number): string;
    replace(string?: string, pattern: string | RegExp, replacement: string | ((...args: any[]) => string)): string;
    snakeCase(string?: string): string;
    split(string?: string, separator: string | RegExp, limit?: number): string[];
    slice(array: any start?: number, end?: number): any[];
    startCase(string?: string): string;
    trim(string?: string, chars?: string): string;
    upperCase(string?: string): string;
    words(string?: string, pattern?: string | RegExp): string[];

    // 日期方法
    now(): number;

    [key: string]: any
  }

  interface DebounceSettings {
    leading?: boolean;
    maxWait?: number;
    trailing?: boolean;
  }

  interface ThrottleSettings {
    leading?: boolean;
    trailing?: boolean;
  }

  interface Dictionary<T> {
    [index: string]: T;
  }

  interface LoDashExplicitWrapper<T> {
    chain(): this;
    commit(): this;
    plant(value: any): this;
    reverse(): this;
    toJSON(): any;
    value(): T;
    valueOf(): T;
  }

  const _: LoDashStatic;
  export default _;
  export = _;
}
`

export const dayjsTypes = `
declare module 'dayjs' {
  interface Dayjs {
    // 核心方法
    clone(): Dayjs;
    isValid(): boolean;
    format(template?: string): string;
    startOf(unit: OpUnitType): Dayjs;
    endOf(unit: OpUnitType): Dayjs;

    // 获取和设置
    get(unit: UnitType): number;
    set(unit: UnitType, value: number): Dayjs;

    // 操作
    add(value: number, unit?: OpUnitType): Dayjs;
    subtract(value: number, unit?: OpUnitType): Dayjs;

    // 查询
    isBefore(date?: ConfigType, unit?: OpUnitType): boolean;
    isSame(date?: ConfigType, unit?: OpUnitType): boolean;
    isAfter(date?: ConfigType, unit?: OpUnitType): boolean;
    isBetween(a: ConfigType, b: ConfigType, unit?: OpUnitType, inclusivity?: string): boolean;

    // 国际化
    locale(preset?: string | ILocale, object?: Partial<ILocale>): Dayjs;

    // 插件方法
    utc(keepLocalTime?: boolean): Dayjs;
    tz(timezone?: string, keepLocalTime?: boolean): Dayjs;
    duration(value?: number | string | object, unit?: string): Duration;
    relativeTime(withoutSuffix?: boolean): string;
    calendar(referenceTime?: ConfigType, formats?: object): string;

    // 转换
    toDate(): Date;
    toJSON(): string;
    toISOString(): string;
    toString(): string;
    valueOf(): number;

    // 其他实用方法
    diff(date?: ConfigType, unit?: QUnitType | OpUnitType, float?: boolean): number;
    daysInMonth(): number;
    fromNow(withoutSuffix?: boolean): string;
    toNow(withoutSuffix?: boolean): string;
  }

  // 类型定义
  type ConfigType = string | number | Date | Dayjs;
  type UnitType = 'millisecond' | 'second' | 'minute' | 'hour' | 'day' | 'week' | 'month' | 'quarter' | 'year' | 'date';
  type OpUnitType = UnitType | 'weekday' | 'isoWeek' | 'dayOfYear';
  type QUnitType = UnitType | 'week' | 'isoWeek';

  interface Duration {
    milliseconds(): number;
    asMilliseconds(): number;
    seconds(): number;
    asSeconds(): number;
    minutes(): number;
    asMinutes(): number;
    hours(): number;
    asHours(): number;
    days(): number;
    asDays(): number;
    weeks(): number;
    asWeeks(): number;
    months(): number;
    asMonths(): number;
    years(): number;
    asYears(): number;
    toJSON(): string;
    toISOString(): string;
    format(template?: string): string;
    locale(preset?: string): Duration;
  }

  interface ILocale {
    name: string;
    weekdays?: string[];
    weekdaysShort?: string[];
    weekdaysMin?: string[];
    months?: string[];
    monthsShort?: string[];
    ordinal?: (n: number) => number | string;
    formats: {
      LT: string;
      LTS: string;
      L: string;
      LL: string;
      LLL: string;
      LLLL: string;
    };
    relativeTime: {
      future: string;
      past: string;
      s: string;
      m: string;
      mm: string;
      h: string;
      hh: string;
      d: string;
      dd: string;
      M: string;
      MM: string;
      y: string;
      yy: string;
    };
  }

  // 主函数
  function dayjs(date?: ConfigType): Dayjs;

  // 插件声明
  function utc(config?: ConfigType): Dayjs;
  function tz(config?: ConfigType, timezone?: string): Dayjs;
  function locale(preset?: string | ILocale, object?: Partial<ILocale>): string;
  function extend(plugin: object, option?: object): void;
  function isDayjs(d: any): d is Dayjs;
  function unix(t: number): Dayjs;

  // 导出
  export = dayjs;
  export default dayjs;
}
`

export const requestTypes = `
declare module 'request' {
  interface RequestOptions {
    /** 请求方法 */
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';

    /** 请求数据 (POST/PUT/PATCH 时使用) */
    data?: any;

    /** URL 参数 (GET 时使用) */
    params?: Record<string, any>;

    /** 请求头 */
    headers?: Record<string, string>;

    /** 超时时间 (毫秒) */
    timeout?: number;

    /** 请求数据类型 */
    requestType?: 'json' | 'form';

    /** 响应数据类型 */
    responseType?: 'json' | 'text' | 'blob';

    /** 请求前缀 */
    prefix?: string;

    /** 是否携带 cookie */
    credentials?: 'include' | 'same-origin' | 'omit';
  }

   // 扩展后的请求实例类型
  interface RequestInstance {
    /** 请求查询，默认不会带 cookie，可跨域（不受浏览器限制） */
    <T = any>(url: string, options?: RequestOptions): Promise<T>;
  }

  const request: RequestInstance;
  export default request;
}
`
