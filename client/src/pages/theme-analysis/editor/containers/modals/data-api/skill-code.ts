import type { Monaco } from '@monaco-editor/react'

export const loadLanguageSupport = async (monaco: Monaco) => {

  // 设置编译器选项
  monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
    target: monaco.languages.typescript.ScriptTarget.ESNext,
    allowNonTsExtensions: true,
    moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
    module: monaco.languages.typescript.ModuleKind.ESNext,
    strict: true,
    esModuleInterop: true,
    noImplicitAny: false,  // 明确允许隐式 any
    strictFunctionTypes: false,  // 可选：关闭函数类型严格检查
    baseUrl: '.',
    paths: {
      'lodash': ['node_modules/lodash/index.js'],  // 添加 lodash 路径映射
      'dayjs': ['node_modules/dayjs/index.js']  // dayjs
    }
  })

  // 2. 添加 lodash 的完整类型声明（关键步骤）
  const types = await import('./skill-code-type')

  monaco.languages.typescript.typescriptDefaults.addExtraLib(types.lodashTypes, 'node_modules/@types/lodash/index.d.ts')
  monaco.languages.typescript.typescriptDefaults.addExtraLib(types.dayjsTypes, 'node_modules/@types/dayjs/index.d.ts')
  monaco.languages.typescript.typescriptDefaults.addExtraLib(types.requestTypes, 'node_modules/@types/request/index.d.ts')
}
