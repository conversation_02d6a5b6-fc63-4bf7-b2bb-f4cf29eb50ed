/* eslint-disable no-new-func */
import { defineStore } from '@sugo/reactive'
import { message } from 'antd'
import _ from 'lodash'

import { Cloud, MainCloud } from '@/services'
import { ThemeDataApiRelease } from '@/services/type'

const initState = {
  list: [] as ThemeDataApiRelease[],
  total: 0,
  loading: false,
  filter: {
    _key: '',
    mode: '',
    page: 1,
    pageSize: 10,
    sign: '',
    title: undefined as string | undefined
  },

  dataApiMap: {} as Record<string, ThemeDataApiRelease | undefined>,
  totatMap: {} as Record<string, { apiId: string, avg: number, count: number }>,
  // 用户信息
  userMap: {} as Record<string, any>,
  roleMap: {} as Record<string, any>
}

type State = typeof initState

const store = defineStore({
  namespace: 'data-api-release',
  state: initState,
  compute: state => ({
    getDataApiDetail: (cardId: string, paramsKey: string) => {
      const item = _.find(state.dataApiMap, i => i?.cardId === cardId && i?.paramsKey === paramsKey)
      return item
    },
    getContext: (id: string) => {
      const data = state.dataApiMap[id]
      const cardId = data?.cardId
      const paramsKey = data?.paramsKey
      const sign = data?.sign
      const title = data?.title
      return { cardId, paramsKey, sign, title }
    }
  }),
  setup: (state, { use, stores }) => {

    const loadDataApiList = use('loading', 'loading', async (init?: boolean) => {
      if (init) state.filter = { ...initState.filter }

      const { page, pageSize, title, mode, sign } = state.filter
      // const userId = _.get(window, 'sugo.user.id')
      const themeId = stores.get('theme-analysis-editor')?.state.themeId

      // if (/_new/.test(themeId)) {
      //   message.warn('当前分析是草稿状态，请先保存分析再发布接口')
      //   return
      // }

      const where: Record<string, any> = { themeId }

      if (title) where.title = { $like: `%${title}%` }
      if (sign) where.sign = { $like: `%${sign}%` }

      if (mode) {
        where.mode = mode
        if (mode === 'base') {
          where.mode = undefined
          where.$or = [
            { mode: 'base' },
            { mode: { $is: null } }
          ]
        }
      }

      const [res, avgTotal] = await Promise.all([
        Cloud.ThemeDataApiRelease.findAndCountAll({
          limit: pageSize,
          offset: (page - 1) * pageSize,
          where,
          order: [['createdAt', 'DESC']]
        }),
        Cloud.ThemeDataApiLog.count({
          group: ['apiId'],
          attributes: ['apiId', ['fn(\'AVG\', col(\'latency\'))', 'avg']]
        })
      ])

      state.total = res.total
      state.list = _.map(res.list, (i, idx) => ({ ...i, $index: idx + (page - 1) * pageSize + 1 }))

      state.dataApiMap = _.keyBy(state.list, 'id')
      state.totatMap = _.keyBy(avgTotal as any, 'apiId')
    })

    const loadUserRole = async () => {
      const res: any[] = await MainCloud.User.findAll({
        attributes: ['id', 'username'],
        limit: 100
      })
      const res2: any[] = await MainCloud.Role.findAll({
        attributes: ['id', 'name'],
        limit: 100
      })

      state.userMap = _.keyBy(res.map(i => ({ id: i.id, name: i.username })), 'id')
      state.roleMap = _.keyBy(res2, 'id')
    }

    const setDataApiFilter = (filter: Partial<State['filter']>) => {
      state.filter = { ...state.filter, ...filter }
    }

    const resetDataApiFilter = () => {
      state.filter = {
        ...initState.filter,
        _key: Math.random().toString(32)
      }
    }

    const delDataApi = async (id: string) => {
      await Cloud.ThemeDataApiRelease.deleteByPk(id)
      await loadDataApiList()
      state.dataApiMap[id] = undefined
    }

    const updateDataApiStatus = async (id: string, status: string) => {
      await Cloud.ThemeDataApiRelease.updateByPk(id, { status })
      const item = state.list.find(i => i.id === id)
      if (item) {
        item.status = status
      }
      state.list = [...state.list]
    }

    const testQueryDataApi = async (cardId: string, paramsKey: string, userQueryCode: string, api?: any) => {
      // ...
      const userId = _.get(window, 'sugo.user.id')
      const cardMap = stores.get('theme-analysis-editor')?.state.cardMap || {}
      const card = cardMap[cardId] || {}
      const params = card.paramsMap?.[paramsKey]

      if (api?.mode === 'merge' && !api?.mainApi) {
        message.error('请先设置主接口')
        return []
      }
      if ((api?.mode === 'base' || !api?.mode) && !params?.queryParams) {
        message.error('缺少查询参数，看板请执行一次查询，如仍然不行，请联系管理员')
        return []
      }

      let userQuery: Record<string, any> = {}
      try {
        const fn = new Function(`return (${userQueryCode})`)
        userQuery = fn() || {}
      } catch (err) {
        console.error(err)
      }

      let isDebug = false
      if (userQuery.isDebug) {
        isDebug = userQuery.isDebug
        delete userQuery.isDebug
      }

      const res = await Cloud.$fn.queryDataApi({
        isTest: true,
        isDebug,
        api: { ...api, createBy: userId },
        userQuery,
        apiQueryParams: params?.queryParams
      })

      return res
    }

    const testQueryDataApiReal = async (sign: string, userQueryCode: string) => {
      let userQuery: Record<string, any> = {}
      try {
        const fn = new Function(`return (${userQueryCode})`)
        userQuery = fn() || {}
      } catch (err) {
        console.error(err)
      }
      const res = await Cloud.$fn.queryDataApi({
        isTest: false,
        sign,
        userQuery
      })

      return res
    }

    // 保存接口
    const saveDataApi = async (data: any, editId?: string) => {
      const userId = _.get(window, 'sugo.user.id')
      const themeId = stores.get('theme-analysis-editor')?.state.themeId
      const record: any = _.omit({ ...data, themeId }, ['id'])

      if (/_new/.test(themeId)) {
        throw new Error('当前分析是草稿状态，请先保存分析再发布接口，谢谢！')
        // return
      }

      // if (data.mainApi) record.mode = 'merge'
      // else record.mode = 'base'

      if (!editId) record.createdBy = userId
      else record.updatedBy = userId

      // 检查唯一性
      const where: any = { sign: record.sign }
      if (editId) where.id = { $ne: editId }

      const exist = await Cloud.ThemeDataApiRelease.findOne({
        where,
        attributes: ['id']
      })
      if (exist) {
        throw new Error('签名已存在，请设置其他')
      }

      let id = ''
      if (editId) {
        id = editId
        await Cloud.ThemeDataApiRelease.updateByPk(editId, record)
      } else {
        const res = await Cloud.ThemeDataApiRelease.create(record)
        id = res.id
      }
      state.dataApiMap = { ...state.dataApiMap, [id]: record }

      loadDataApiList()
    }

    return {
      loadDataApiList,
      setDataApiFilter,
      delDataApi,

      saveDataApi,

      loadUserRole,
      testQueryDataApi,
      testQueryDataApiReal,
      updateDataApiStatus,
      resetDataApiFilter
    }
  }

})

export const {
  useAction: useDataApiAction,
  useCompute: useDataApiCompute,
  useStore: useDataApiStore,
  getCompute: getDataApiCompute
} = store
