import _ from 'lodash'

/**
 * 字段绑定信息接口（简化版）
 */
interface FieldBinding {
  type?: string
  aggMode?: string
}

/**
 * 检测是否为一键查询明细模式
 * 当所有字段的 aggMode 都是 'unknown' 或 undefined，并且 type 是 'field' 时，应该使用 select 模式
 * @param fieldsBinding 字段绑定配置
 * @param currentQueryMode 当前的查询模式，用于性能优化
 * @returns 是否为查询明细模式
 */
export function isQueryDetailsMode(
  fieldsBinding: Record<string, FieldBinding | null> | null | undefined,
  currentQueryMode?: string
): boolean {
  // 性能优化：如果已经是 select 模式，直接返回 false，避免后续计算
  if (currentQueryMode === 'select') return false

  if (!fieldsBinding) return false

  const fieldsBindingValues = _.values(fieldsBinding).filter(field => field !== null) as FieldBinding[]

  // 必须有字段才进行检测
  if (fieldsBindingValues.length === 0) return false

  // 检查是否所有字段都满足明细查询条件
  return _.every(fieldsBindingValues, field => {
    // 字段类型必须是 'field'
    if (field?.type !== 'field') return false

    // aggMode 必须是 'unknown' 或 undefined
    return field?.aggMode === 'unknown' || field?.aggMode === undefined
  })
}
