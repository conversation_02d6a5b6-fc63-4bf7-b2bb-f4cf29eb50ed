# 指标查询系统元数据架构重构设计方案

## 概述

### 项目背景

当前指标查询系统在处理复合指标查询时存在架构复杂性高、性能瓶颈明显、维护成本增加等问题。特别是在处理纯实时复合指标时，系统会进行多次冗余查询，导致性能下降和资源浪费。

### 重构目标

本方案旨在通过重新设计指标元数据架构，建立清晰的类型系统和智能的查询路由机制，从根本上解决当前系统的架构问题，提升系统性能、可维护性和扩展性。

## 背景和需求分析

### 当前系统存在的问题

#### 1. 查询路由逻辑复杂且脆弱

**问题表现：**
- 基于字符串匹配进行查询引擎选择：`/^realtime/i.test(sqlModelId)`、`/^complex_/i.test(sqlModelId)`
- 多层嵌套的条件判断，容易出现逻辑错误
- 查询分发逻辑分散在多个文件中，缺乏统一管理

**具体痛点：**
```typescript
// 当前的脆弱路由逻辑
const queryGroup = _.groupBy(patchedIndicesInfos, inf =>
  inf?.sqlModelId === 'realtime' ? `realtime_${inf.baseIndiceId}` : inf?.sqlModelId || 'mindex'
)
```

#### 2. 元数据结构分散且冗余

**问题表现：**
- 指标信息分散在 `IndicesBase`、`IndicesSpec`、`IndicesVersionSpec` 等多个类型中
- 需要多次数据库查询获取完整元数据：`loadIndicesExtraInfo()` → `patchIndicesInfos()`
- 元数据字段命名不一致，语义模糊

**数据获取复杂性：**
```typescript
// 当前需要多步骤获取元数据
const otherIndiceInfos = await loadIndicesExtraInfo(queryConfig, req.totalMutRequester)
const patchedIndicesInfos = await patchIndicesInfos({
  otherIndiceInfos,
  requester: req.totalMutRequester,
  timeBucket: queryConfig.timeBucket,
  sCache: queryConfig.staleAfter ?? 'PT4H'
})
```

#### 3. 复合指标处理复杂

**问题表现：**
- `resolveComplexColumn` 函数承担过多职责
- 混合场景（实时+非实时）处理逻辑复杂且难以维护
- 缺乏对嵌套复合指标的清晰抽象

#### 4. 类型安全性差

**问题表现：**
- 大量使用 `any` 类型，运行时才能发现错误
- 字符串常量缺乏类型约束
- 指标类型判断依赖运行时字符串匹配

#### 5. 性能问题

**问题表现：**
- 每次查询都需要重新获取元数据，缺乏有效缓存
- 复合指标的递归查询可能导致 N+1 查询问题
- 元数据处理逻辑重复执行

### 业务需求

1. **指标类型支持**：支持原子指标、派生指标、复合指标的统一管理
2. **多口径支持**：支持财务口径、市场口径等不同计算逻辑
3. **版本管理**：支持指标的多版本管理和版本切换
4. **数据源适配**：支持实时、非实时、数仓等多种数据源
5. **性能优化**：提升查询响应速度，减少资源消耗

### 技术需求

1. **类型安全**：建立完整的 TypeScript 类型系统
2. **架构清晰**：清晰的模块划分和职责分离
3. **扩展性强**：支持新指标类型和数据源的快速接入
4. **性能优化**：智能查询路由和缓存机制
5. **可维护性**：降低代码复杂度，提升开发效率

### 重构的必要性和紧迫性

1. **技术债务累积**：当前架构复杂度已影响开发效率
2. **性能瓶颈**：复合指标查询性能问题日益突出
3. **业务发展需要**：新的指标类型和计算场景不断涌现
4. **团队协作**：需要更清晰的架构支撑团队协作开发

## 技术设计方案

### 设计原则

1. **类型安全优先**：使用 TypeScript 严格类型定义，编译时发现错误
2. **单一职责**：每个组件职责明确，避免功能耦合
3. **开放封闭**：对扩展开放，对修改封闭
4. **性能优化**：智能缓存和查询优化
5. **渐进式迁移**：支持新旧系统平滑过渡

### 核心架构设计

```mermaid
graph TB
    A[查询请求] --> B[查询路由器]
    B --> C[元数据管理器]
    C --> D[指标定义缓存]
    B --> E[查询执行计划]
    E --> F[实时查询引擎]
    E --> G[批处理查询引擎]
    E --> H[数仓查询引擎]
    E --> I[复合查询引擎]
    F --> J[结果合并器]
    G --> J
    H --> J
    I --> J
    J --> K[查询结果]
```

### 基础类型定义

```typescript
// 基础标识符类型
type MetricId = string & { readonly __brand: 'MetricId' }
type VersionId = string & { readonly __brand: 'VersionId' }
type CaliberId = string & { readonly __brand: 'CaliberId' }
type DataSourceId = string & { readonly __brand: 'DataSourceId' }

// 指标类型枚举
enum MetricType {
  ATOMIC = 'atomic',      // 原子指标
  DERIVED = 'derived',    // 派生指标
  COMPOSITE = 'composite' // 复合指标
}

// 数据源类型枚举
enum DataSourceType {
  REALTIME = 'realtime',   // 实时数据源（Doris等）
  BATCH = 'batch',         // 批处理数据源（Mindex等）
  WAREHOUSE = 'warehouse'  // 数仓数据源
}

// 查询引擎类型
enum QueryEngineType {
  REALTIME_ENGINE = 'realtime_engine',
  BATCH_ENGINE = 'batch_engine',
  WAREHOUSE_ENGINE = 'warehouse_engine',
  HYBRID_ENGINE = 'hybrid_engine'
}

// 时间粒度
enum TimeGranularity {
  DAY = 'DAY',
  WEEK = 'WEEK',
  MONTH = 'MONTH',
  QUARTER = 'QUARTER',
  YEAR = 'YEAR'
}

// 聚合方式
enum AggregationType {
  SUM = 'sum',
  AVG = 'avg',
  COUNT = 'count',
  MAX = 'max',
  MIN = 'min',
  DISTINCT_COUNT = 'distinct_count'
}
```

### 数据源抽象层

```typescript
// 统一数据源配置接口
interface DataSourceConfig {
  readonly id: DataSourceId
  readonly name: string
  readonly type: DataSourceType
  readonly connectionInfo: ConnectionInfo
  readonly capabilities: QueryCapabilities
  readonly metadata: DataSourceMetadata
}

// 连接信息
interface ConnectionInfo {
  readonly host: string
  readonly port: number
  readonly database: string
  readonly credentials: CredentialInfo
}

// 查询能力
interface QueryCapabilities {
  readonly supportedGranularities: TimeGranularity[]
  readonly supportedAggregations: AggregationType[]
  readonly supportsRealtime: boolean
  readonly supportsComplexJoins: boolean
  readonly maxQueryComplexity: number
}

// 数据源元数据
interface DataSourceMetadata {
  readonly schema: SchemaInfo
  readonly updateFrequency: string
  readonly dataLatency: string
  readonly retentionPeriod: string
}

// 具体数据源类型
interface RealtimeDataSource extends DataSourceConfig {
  readonly type: DataSourceType.REALTIME
  readonly streamingConfig: StreamingConfig
}

interface BatchDataSource extends DataSourceConfig {
  readonly type: DataSourceType.BATCH
  readonly batchConfig: BatchConfig
}

interface WarehouseDataSource extends DataSourceConfig {
  readonly type: DataSourceType.WAREHOUSE
  readonly warehouseConfig: WarehouseConfig
}
```

### 指标元数据核心结构

```typescript
// 指标基础信息
interface BaseMetricInfo {
  readonly id: MetricId
  readonly code: string
  readonly name: string
  readonly description: string
  readonly type: MetricType
  readonly dataType: 'number' | 'string' | 'boolean' | 'date'
  readonly unit: string
  readonly precision: number
  readonly category: MetricCategory
  readonly tags: string[]
  readonly owner: OwnerInfo
  readonly createdAt: Date
  readonly updatedAt: Date
}

// 指标分类
interface MetricCategory {
  readonly id: string
  readonly name: string
  readonly parentId?: string
  readonly level: number
}

// 指标版本
interface MetricVersion {
  readonly id: VersionId
  readonly metricId: MetricId
  readonly version: string
  readonly name: string
  readonly status: 'draft' | 'published' | 'deprecated'
  readonly publishedAt?: Date
  readonly publishedBy?: string
  readonly changeLog: string
  readonly isDefault: boolean
}

// 指标口径
interface MetricCaliber {
  readonly id: CaliberId
  readonly metricId: MetricId
  readonly name: string
  readonly description: string
  readonly businessDefinition: string
  readonly technicalDefinition: string
  readonly filters: FilterCondition[]
  readonly dimensions: DimensionConfig[]
  readonly isDefault: boolean
}

// 计算逻辑抽象
interface ComputationLogic {
  readonly type: 'direct' | 'formula' | 'aggregation'
  readonly expression: string
  readonly dependencies: MetricDependency[]
  readonly validationRules: ValidationRule[]
}
```

### 指标类型层次定义

```typescript
// 原子指标 - 最基础的指标，直接对应数据源字段
interface AtomicMetric {
  readonly metricInfo: BaseMetricInfo & { type: MetricType.ATOMIC }
  readonly versions: MetricVersion[]
  readonly calibers: MetricCaliber[]
  readonly dataSource: DataSourceConfig
  readonly sourceColumn: ColumnMapping
  readonly computation: DirectComputationLogic
}

// 派生指标 - 基于原子指标通过简单计算得出
interface DerivedMetric {
  readonly metricInfo: BaseMetricInfo & { type: MetricType.DERIVED }
  readonly versions: MetricVersion[]
  readonly calibers: MetricCaliber[]
  readonly baseMetric: MetricReference
  readonly computation: FormulaComputationLogic
}

// 复合指标 - 多个指标通过复杂公式组合计算
interface CompositeMetric {
  readonly metricInfo: BaseMetricInfo & { type: MetricType.COMPOSITE }
  readonly versions: MetricVersion[]
  readonly calibers: MetricCaliber[]
  readonly subMetrics: MetricReference[]
  readonly computation: CompositeComputationLogic
  readonly optimizationHints: OptimizationHints
}

// 统一指标定义
type MetricDefinition = AtomicMetric | DerivedMetric | CompositeMetric

// 指标引用
interface MetricReference {
  readonly metricId: MetricId
  readonly versionId?: VersionId
  readonly caliberId?: CaliberId
  readonly alias?: string
  readonly filters?: FilterCondition[]
}

// 计算逻辑具体类型
interface DirectComputationLogic extends ComputationLogic {
  readonly type: 'direct'
  readonly aggregation: AggregationType
  readonly sourceField: string
}

interface FormulaComputationLogic extends ComputationLogic {
  readonly type: 'formula'
  readonly formula: string
  readonly parameters: FormulaParameter[]
}

interface CompositeComputationLogic extends ComputationLogic {
  readonly type: 'formula'
  readonly formula: string
  readonly subMetricMappings: SubMetricMapping[]
  readonly executionStrategy: 'parallel' | 'sequential' | 'optimized'
}
```

### 查询引擎抽象

```typescript
// 查询引擎接口
interface QueryEngine {
  readonly type: QueryEngineType
  readonly capabilities: QueryCapabilities
  canHandle(metric: MetricDefinition): boolean
  executeQuery(plan: QueryPlan): Promise<QueryResult>
  estimateCost(plan: QueryPlan): QueryCost
}

// 查询计划
interface QueryPlan {
  readonly id: string
  readonly metrics: MetricReference[]
  readonly dimensions: DimensionConfig[]
  readonly filters: FilterCondition[]
  readonly timeRange: TimeRange
  readonly granularity: TimeGranularity
  readonly limit?: number
  readonly orderBy?: OrderByConfig[]
  readonly optimizations: QueryOptimization[]
}

// 查询路由器
interface QueryRouter {
  route(metrics: MetricDefinition[]): QueryExecutionPlan
  optimize(plan: QueryExecutionPlan): QueryExecutionPlan
  canMerge(plans: QueryPlan[]): boolean
}

// 查询执行计划
interface QueryExecutionPlan {
  readonly plans: QueryPlan[]
  readonly engines: QueryEngine[]
  readonly mergeStrategy: MergeStrategy
  readonly estimatedCost: QueryCost
  readonly parallelizable: boolean
}
```

### 核心组件实现示例

```typescript
// 智能查询路由器实现
class SmartQueryRouter implements QueryRouter {
  constructor(
    private metadataManager: MetricMetadataManager,
    private engines: QueryEngine[]
  ) {}

  route(metrics: MetricDefinition[]): QueryExecutionPlan {
    // 1. 分析指标类型和数据源
    const metricGroups = this.groupMetricsByDataSource(metrics)

    // 2. 检测复合指标并展开依赖
    const expandedMetrics = this.expandCompositeMetrics(metrics)

    // 3. 选择最优查询引擎
    const plans = this.createOptimalPlans(expandedMetrics)

    // 4. 生成执行计划
    return this.createExecutionPlan(plans)
  }

  private detectPureRealtimeComposite(metric: CompositeMetric): boolean {
    return metric.subMetrics.every(ref => {
      const subMetric = this.metadataManager.getMetric(ref.metricId)
      return subMetric?.metricInfo.type === MetricType.ATOMIC &&
             (subMetric as AtomicMetric).dataSource.type === DataSourceType.REALTIME
    })
  }
}

// 元数据管理器
interface MetricMetadataManager {
  // 指标管理
  getMetric(id: MetricId): Promise<MetricDefinition | null>
  getMetrics(ids: MetricId[]): Promise<MetricDefinition[]>
  searchMetrics(criteria: SearchCriteria): Promise<MetricDefinition[]>

  // 依赖分析
  getDependencies(metricId: MetricId): Promise<MetricDependency[]>
  getDependents(metricId: MetricId): Promise<MetricReference[]>
  validateDependencies(metric: MetricDefinition): Promise<ValidationResult>

  // 缓存管理
  invalidateCache(metricId: MetricId): Promise<void>
  warmupCache(metricIds: MetricId[]): Promise<void>

  // 版本管理
  getVersions(metricId: MetricId): Promise<MetricVersion[]>
  getActiveVersion(metricId: MetricId): Promise<MetricVersion>
}
```

### 关键技术决策

1. **类型安全优先**：使用 TypeScript 品牌类型确保标识符类型安全
2. **不可变数据结构**：所有元数据对象使用 `readonly` 修饰符
3. **策略模式**：查询引擎使用策略模式支持不同数据源
4. **工厂模式**：元数据管理器使用工厂模式创建不同类型指标
5. **观察者模式**：元数据变更通知机制

## 实施计划

### 阶段一：基础架构搭建（2-3周）

**目标：** 建立新的元数据类型系统和基础设施

**具体任务：**
1. **类型系统设计**（1周）
   - 定义完整的 TypeScript 类型系统
   - 建立基础枚举和接口定义
   - 创建类型安全的标识符系统

2. **基础设施搭建**（1-2周）
   - 实现元数据管理器基础框架
   - 建立数据源抽象层
   - 创建查询引擎接口

**交付物：**
```
src/types/metrics/
├── base-types.ts           // 基础类型定义
├── data-sources.ts         // 数据源抽象
├── metric-definitions.ts   // 指标定义
├── query-engines.ts        // 查询引擎
└── metadata-manager.ts     // 元数据管理器

src/core/metrics/
├── metadata-manager.ts     // 元数据管理器实现
├── query-router.ts         // 查询路由器
└── cache-manager.ts        // 缓存管理器
```

**验收标准：**
- 所有类型定义编译通过
- 基础框架单元测试覆盖率 > 90%
- 性能基准测试建立

### 阶段二：元数据迁移和适配（3-4周）

**目标：** 将现有元数据迁移到新架构

**具体任务：**
1. **数据迁移脚本**（1周）
   - 编写现有元数据到新格式的转换脚本
   - 建立数据一致性验证机制
   - 创建回滚和恢复机制

2. **适配层实现**（2周）
   - 实现新旧架构的适配器
   - 建立渐进式迁移机制
   - 创建特性开关控制

3. **缓存机制建立**（1周）
   - 实现多层缓存策略
   - 建立缓存失效和更新机制
   - 性能监控和调优

**迁移策略：**
```typescript
// 适配器模式实现平滑迁移
class LegacyMetadataAdapter implements MetricMetadataManager {
  async getMetric(id: MetricId): Promise<MetricDefinition | null> {
    // 调用现有的 loadIndicesExtraInfo 等方法
    const legacyInfo = await this.legacyLoader.load(id)
    // 转换为新的 MetricDefinition 格式
    return this.converter.convert(legacyInfo)
  }
}

// 特性开关控制
const USE_NEW_METADATA_SYSTEM = process.env.ENABLE_NEW_METADATA === 'true'
```

**交付物：**
- 数据迁移脚本和验证工具
- 新旧系统适配层
- 缓存系统实现
- 迁移文档和操作手册

### 阶段三：查询引擎重构（4-5周）

**目标：** 实现新的查询路由和执行引擎

**具体任务：**
1. **智能查询路由器**（2周）
   - 实现基于元数据的智能路由
   - 支持复合指标的自动分解和优化
   - 建立查询计划缓存机制

2. **查询引擎适配**（2周）
   - 重构现有查询引擎以适配新接口
   - 实现统一的查询结果格式
   - 建立查询性能监控

3. **复合指标优化引擎**（1周）
   - 实现纯实时复合指标优化
   - 支持混合查询场景的智能分发
   - 建立查询执行计划优化

**核心实现：**
```typescript
// 新的查询入口
export async function queryMetrics(
  request: MetricQueryRequest
): Promise<QueryResult> {
  // 1. 解析查询请求
  const metrics = await metadataManager.getMetrics(request.metricIds)

  // 2. 智能路由
  const executionPlan = queryRouter.route(metrics)

  // 3. 执行查询
  const results = await queryExecutor.execute(executionPlan)

  // 4. 合并结果
  return resultMerger.merge(results)
}
```

**交付物：**
- 智能查询路由器实现
- 重构后的查询引擎
- 查询性能监控系统
- 查询优化规则引擎

### 阶段四：性能优化和监控（2-3周）

**目标：** 优化查询性能并建立监控体系

**具体任务：**
1. **性能优化**（1-2周）
   - 实现查询结果缓存
   - 优化复合指标查询策略
   - 实现查询计划缓存

2. **监控体系建立**（1周）
   - 建立查询性能监控
   - 实现自动化性能报告
   - 建立性能预警机制

**交付物：**
- 性能优化实现
- 监控和报告系统
- 性能调优文档

### 风险控制和回滚策略

#### 渐进式迁移

```typescript
// 特性开关控制新旧系统切换
const USE_NEW_METADATA_SYSTEM = process.env.ENABLE_NEW_METADATA === 'true'

export async function queryAnyIndices(
  queryConfig: DataSourceQueryConfig,
  req: Record<string, RequestMethod>,
  db: any
) {
  if (USE_NEW_METADATA_SYSTEM) {
    return newQuerySystem.query(queryConfig, req)
  } else {
    return legacyQuerySystem.query(queryConfig, req, db)
  }
}
```

#### A/B 测试验证

- 并行运行新旧系统，对比查询结果
- 逐步增加新系统的流量比例（10% → 50% → 100%）
- 建立详细的性能和正确性监控

#### 快速回滚机制

- 保持旧系统完整性，确保可以快速回滚
- 建立自动化的健康检查和回滚触发机制
- 准备详细的回滚操作手册

## 预期收益

### 性能提升指标

#### 查询性能优化

| 指标 | 当前状态 | 目标状态 | 提升幅度 |
|------|----------|----------|----------|
| 元数据获取次数 | 每次查询3-5次 | 每次查询1次（缓存） | 减少70% |
| 复合指标查询时间 | 平均2-3秒 | 平均1-1.5秒 | 减少40% |
| 纯实时复合指标查询次数 | 3次 | 1次 | 减少67% |
| 并发查询支持 | 50 QPS | 200 QPS | 提升300% |

#### 资源利用优化

| 指标 | 当前状态 | 目标状态 | 提升幅度 |
|------|----------|----------|----------|
| 数据库连接数 | 平均20个 | 平均14个 | 减少30% |
| 内存使用 | 2GB | 1.5GB | 减少25% |
| CPU 使用率 | 60% | 48% | 减少20% |

### 开发效率和系统稳定性改善

#### 开发效率提升

1. **类型安全**
   - 编译时发现90%以上的类型错误
   - IDE智能提示和自动补全
   - 重构安全性大幅提升

2. **代码可维护性**
   - 查询逻辑复杂度降低60%
   - 新增指标类型开发时间减少50%
   - 代码可读性和可理解性显著提升

3. **开发流程优化**
   - 新指标接入时间从2天减少到半天
   - 代码审查效率提升40%
   - 单元测试编写效率提升30%

#### 系统稳定性增强

1. **错误处理**
   - 运行时错误减少80%
   - 查询失败率从5%降低到2%
   - 系统可用性从99.5%提升到99.9%

2. **监控和诊断**
   - 查询性能可视化监控
   - 自动化的性能瓶颈检测
   - 智能的查询优化建议

### 业务价值和技术价值

#### 业务价值

1. **功能扩展能力**
   - 支持更复杂的指标计算场景
   - 新数据源接入时间减少70%
   - 支持实时指标和批处理指标的无缝混合查询

2. **用户体验提升**
   - 查询响应时间减少50%
   - 支持更大规模的并发查询
   - 提供更准确和一致的查询结果

3. **业务敏捷性**
   - 新业务指标上线时间减少60%
   - 支持更灵活的指标组合和计算
   - 提供更强的数据分析能力

#### 技术价值

1. **架构优化**
   - 建立清晰的技术架构和模块边界
   - 提升系统的可扩展性和可维护性
   - 为未来技术演进奠定基础

2. **技术债务清理**
   - 消除历史遗留的架构问题
   - 建立规范的开发和部署流程
   - 提升团队的技术能力和协作效率

3. **创新能力**
   - 为AI驱动的智能查询优化提供基础
   - 支持更先进的数据处理和分析技术
   - 建立面向未来的技术架构

## 总结

本重构方案通过引入严格的类型系统、清晰的架构抽象和智能的查询路由，将显著提升指标查询系统的可维护性、性能和稳定性。通过渐进式的实施策略和完善的风险控制机制，确保重构过程的平稳进行，为业务的快速发展提供强有力的技术支撑。

重构完成后，系统将具备更强的扩展性和更好的性能表现，为团队提供更高效的开发体验，为业务提供更可靠的数据服务能力。
```
