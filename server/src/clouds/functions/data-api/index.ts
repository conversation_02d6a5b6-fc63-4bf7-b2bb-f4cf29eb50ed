
import { createCloudService } from '@sugo/sequelize-cloud/client'
import _ from 'lodash'
import { extend } from 'umi-request'

import { runTsCodeInSandbox } from '@/clouds/functions/ts-parse'
import type { CloudFunction } from '@/clouds/type'
import { ThemeDataApiRelease } from '@/entity/theme-api-release'

import { fixData, fixUserQuery, mergeResult, renameData, renameFields } from './utils'

const pickField = ['limit', 'filters', 'offset', 'orderBy', 'cache', 'total']

interface ApiDef {
  id: string
  fields: string[]
  sign?: string
}

/**
 * 查询接口数据
 * @param request
 * @param context
 * @returns
 */
export const queryDataApi: CloudFunction<{
  isTest?: boolean
  isDebug?: boolean
  sign?: string
  // 【用户传入】查询参数
  userQuery?: Record<string, any>
  // api 的查询参数
  apiQueryParams?: any,

  api?: {
    // 字段映射
    fieldColumnMap?: Record<string, string>
    queryAfterHook?: any
    mergeCarryHook?: any
    authorizes?: string[]
    createBy?: string
    mode?: string
    includeTotal?: boolean

    // 合并接口用到
    mainApi?: { id: string, fields: string[] } // 存的是字段 name
    subApis?: { id: string, fields: string[] }[] // 存的是字段 name

    [key: string]: any
  }

  context?: {
    method: string
    path: string
    userId: string,
    request: { headers: any, time: Date }
  }
}> = async (request, context) => {
  const { isTest, sign } = request.params
  const { db } = context
  const start = Date.now()
  const userQuery = fixUserQuery({ ...request.params.userQuery, ...request.query })
  const isDebug = request.params.isDebug || userQuery.isDebug

  // const obj = _.omitBy(urlQuery, (v, k) => _.isNil(v) || /^\$/.test(k) || /jwt/i.test(k))
  const requestTime = request.params.context?.request?.time || new Date()

  const queryMap: Record<string, any> = {}

  let latency = 0
  let errorMessage: string
  let data: any
  let api = request.params.api
  let fields: any[] = []
  let loaded: { api?: any, apiQueryParams?: any }
  let mainApi: ApiDef = request.params.api?.mainApi
  let subApis: ApiDef[] = request.params.api?.subApis

  const queryData = async (myApi: ThemeDataApiRelease, query: any, fieldColumnMap: any, mainJoinFilters?: any[], joinApi?: ApiDef) => {
    query = _.cloneDeep(query || {})

    if (query.cache !== undefined) query.staleAfter = query.cache

    if (!!myApi.includeTotal || query.total === true) {
      query.includeTotal = true
    }
    if (query.total === false) {
      query.includeTotal = false
    }

    if (!_.isEmpty(mainJoinFilters)) {
      const joinFilters = mainJoinFilters
      // 替换 col
      _.forEach(joinApi?.fields, (name, index) => {
        joinFilters[index].col = String(name).replace(/_tempMetric_/, '')
      })
      query.filters = _.compact([...query.filters, ...joinFilters])
    }

    queryMap[myApi.sign] = query

    // TODO: 指标接口返回可能存在缺失字段
    // 查询数据
    let data0 = await context.$execute('queryData', { query })

    // 查询完成
    if (_.size(data0) === 1 && data0[0].resultSet) data0 = _.get(data0, '[0].resultSet', [])

    let fields0 = _.values(_.mapValues(query.fieldsBinding, (v, k) =>
      _.pick(_.omitBy({
        ...v,
        key: k,
        name: String(v.name).replace('_tempMetric_', ''),
        isMetric: /_tempMetric_/.test(String(v.name)) ? true : undefined
      }, _.isUndefined), ['id', 'key', 'title', 'name', 'dataType', 'isMetric']))
    )

    // 修饰数据
    data0 = fixData(data0, fields0.filter(f => f.dataType === 'number'))
    data0 = renameData(data0, fieldColumnMap)
    fields0 = renameFields(fields0, fieldColumnMap)

    const result = { data: data0, fields: fields0, query: queryMap[myApi.sign], userQuery }

    // 查询钩子
    if (myApi.queryAfterHook?.enable) {
      const code = `${myApi.queryAfterHook.code} \n;\n main(context.data, context.fields, context.userQuery, context.query)`
      const hookResult = await runTsCodeInSandbox(code, {}, result)
      if (_.isObject(hookResult) as any) {
        result.data = hookResult.data
        result.fields = hookResult.fields
      }
    }

    return result
  }

  const loadApi = async (key: string, useId?: boolean) => {
    let api0
    let db0: any = db

    if (process.env.DATA_API_DEBUG) {
      db0 = createCloudService({
        baseUrl: `${process.env.DATASOURCE_SYSTEM_ORIGIN}/abi/api/cloud`,
        microApp: 'sugo-abi',
        entityMap: {
          'ThemeDataApiRelease': 'ThemeDataApiRelease',
          'Theme': 'Theme'
        },
        umiRequest: extend({
          headers: { cookie: request.headers.cookie }
        })
      })
    }

    api0 = await db0.ThemeDataApiRelease.findOne({ where: useId ? { id: key } : { sign: key } })
    if (!api0) throw new Error('接口不存在')

    let theme = await db0.Theme.findOne({ where: { id: api0.themeId }, attributes: ['id', 'title', 'cardMap'] })
    if (!theme) throw new Error('接口关联主题不存在')

    api0 = _.isFunction(api0.toJSON) ? api0.toJSON() : api0
    theme = _.isFunction(theme.toJSON) ? theme.toJSON() : theme

    const params = _.get(theme, `cardMap[${api0.cardId}].paramsMap[${api0.paramsKey}]`)

    return {
      api: api0,
      apiQueryParams: params?.queryParams
    }
  }

  // 查询当前接口配置
  if (sign) {
    const res = await loadApi(sign)
    loaded = res
    api = res.api

    if (api?.mode === 'merge') {
      mainApi = api.mainApi
      subApis = api.subApis
    }

    if (!api) throw new Error('接口不存在')
  }

  // 先验证授权
  const userId = request.headers['u-id']
  const roleIds = _.split(request.headers['r-id'], ',')
  const authorizes = api?.authorizes || []
  let hookResult: Record<string, any> = {}

  // 授权验证
  if (!_.isEmpty(authorizes) || api?.createBy) {
    const createBy = api?.createBy
    const authIds = _.filter([...authorizes, createBy], i => i).map(i => String(i).replace(/(user|role):/, ''))
    const ids = [userId, ...roleIds].filter(i => i).map(i => String(i).replace(/(user|role):/, ''))
    // if (_.some(a))
    const some = _.intersection(ids, authIds)

    if (_.isEmpty(some)) {
      throw new Error('未授权访问')
    }
  }

  try {

    // 判断是否是合并接口
    const isMerge = api?.mode === 'merge'

    if (isMerge) {
      const subApiIds = subApis.map(i => i.id)
      const res = await Promise.all(subApiIds.map(id => loadApi(id, true)))
      // key 是 id
      const resultMap: Record<string, { data: any[], fields: any[] }> = {}

      // 先查主表
      const mainRes = await loadApi(mainApi.id, true)
      const mainUserQuery = { ...mainRes.apiQueryParams, ...userQuery?.[mainRes.api.sign], ..._.pick(userQuery, pickField) }
      const mainData = await queryData(mainRes.api, mainUserQuery, mainRes.api.fieldColumnMap, undefined, mainApi)

      resultMap[mainRes.api.id] = mainData

      // 找出主表的关联字段筛选条件
      const mainJoinFilters: { col: string, op: string, eq: any, [key: string]: any }[] = []
      _.forEach(mainApi.fields, name => {
        name = String(name).replace(/_tempMetric_/, '')
        const fieldMap = mainData.query?.fieldsBinding || {}
        const nameMap = mainRes.api?.fieldColumnMap || {}
        const fieldName = nameMap[name] || name
        const values = _.uniq(_.map(mainData.data, d => d[fieldName]).filter(i => !_.isNil(i)))

        mainJoinFilters.push({
          col: name,
          colName: fieldMap[name]?.title || name,
          op: 'in',
          eq: values
        })
      })

      // 查询子表的数据
      await Promise.all(subApiIds.map(async (id, idx) => {
        const item = res[idx]
        const subApi = subApis[idx]

        const fieldColumnMap = item.api.fieldColumnMap
        const query = {
          ...item.apiQueryParams, ...userQuery?.[item.api.sign],
          limit: userQuery.limit !== undefined ? userQuery.limit * 2 : undefined
        }
        if (query.limit === undefined && mainUserQuery.limit !== undefined) {
          query.limit = mainUserQuery.limit * 2
        }

        // TODO: 子表不查询总数
        item.api.includeTotal = false

        resultMap[id] = await queryData(item.api, query, fieldColumnMap, mainJoinFilters, subApi)
      }))

      // 手工合并
      if (api && api.mergeCarryHook?.enable) {
        const ctx = { dataMap: resultMap, userQuery }
        const code = `${api.mergeCarryHook.code} \n;\n main(context.dataMap, context.userQuery)`
        hookResult = await runTsCodeInSandbox(code, {}, ctx)
        if (_.isObject(hookResult) as any) {
          data = hookResult.data
          fields = hookResult.fields
        }
      }
      else {
        // 自动合并，合并结果
        const mainApiWithSign = {
          ...mainApi,
          sign: _.get(mainRes, 'api.sign') || `api_${mainApi.id}`
        }
        const subApisWithSign = _.map(subApis, (s, idx) => ({
          ...s,
          sign: _.get(res, `[${idx}].api.sign`) || `api_${s.id}`
        }))

        // 收集字段映射信息
        const fieldColumnMaps = {
          [mainApi.id]: mainRes.api?.fieldColumnMap || {},
          ..._.fromPairs(subApiIds.map((id, idx) => [id, res[idx].api?.fieldColumnMap || {}]))
        }

        if (isDebug) {
          console.log('合并调试信息:', {
            mainApiWithSign,
            subApisWithSign,
            resultMapKeys: _.keys(resultMap),
            fieldColumnMaps,
            性能信息: {
              主数据量: resultMap[mainApi.id]?.data?.length || 0,
              子数据量: subApiIds.map(id => ({ id, count: resultMap[id]?.data?.length || 0 }))
            }
          })
        }

        const res2 = mergeResult(mainApiWithSign, subApisWithSign, resultMap, fieldColumnMaps)
        data = res2.data
        fields = res2.fields
      }

      // 按主接口进行排序
      const mainOrderBy = _.get(res, '[0].apiQueryParams.orderBy')
      if (!_.isEmpty(mainOrderBy)) {
        data = _.orderBy(data, _.map(mainOrderBy, i => i.field), _.map(mainOrderBy, i => i.dir || 'desc'))
      }

      // 转换
      data = renameData(data, api.fieldColumnMap)
      fields = renameFields(fields, api.fieldColumnMap)

      // 合并后再执行查询钩子
      if (api && api.queryAfterHook?.enable) {
        const code = `${api.queryAfterHook.code} \n;\n main(context.data, context.fields)`
        hookResult = await runTsCodeInSandbox(code, {}, { data, fields })
        if (_.isObject(hookResult) as any) {
          data = hookResult.data
          fields = hookResult.fields
        }
      }

    } else {
      const fieldColumnMap = request.params.api?.fieldColumnMap || api?.fieldColumnMap || {}
      const query = { ...request.params.apiQueryParams, ...loaded?.apiQueryParams, ...userQuery }

      const res = await queryData(api as any, query, fieldColumnMap)

      data = res.data
      fields = res.fields
    }

    const totalRow = _.find(data, i => {
      const keys = _.keys(i)
      return _.find(keys, k => /_row_total_/.test(k))
    })

    // 结果
    latency = Date.now() - start

    const result: any = {
      code: 1,
      message: null,
      data,
      count: _.size(data),
      fields,
      timestamp: Date.now(),
      latency,
      ..._.omit(hookResult, ['code', 'data', 'fields', 'count', 'message', 'timestamp', 'latency', 'total'])
    }

    if (totalRow) {
      const keys = _.keys(totalRow)
      const totalKey = keys.find(i => /_row_total_/.test(i))
      if (totalKey) {
        result.total = totalRow[totalKey]
        delete totalRow[totalKey]
      } else {
        result.total = 0
      }
    }

    if (isDebug) result.queryMap = queryMap
    return result
  }
  catch (err) {
    errorMessage = _.get(err, 'data.message') || err.message
    console.error(err)
    // 结果
    latency = Date.now() - start
    const result: any = {
      code: -1,
      data: null,
      fields: null,
      count: 0,
      message: errorMessage,
      timestamp: Date.now(),
      latency
    }
    if (isDebug) result.queryMap = queryMap
    return result
  }
  finally {
    if (!isTest && api?.id) {
      setTimeout(async () => {
        // 写入日志
        await db.ThemeDataApiLog.create({
          apiId: api.id,
          path: request.params.context?.path,
          method: request.params.context?.method,
          requestHeaders: request.params.context?.request,
          requestParams: userQuery,
          requestSize: _.size(JSON.stringify(userQuery)),
          responseSize: _.size(JSON.stringify(data)),
          dataCount: _.size(data),
          latency,
          requestTime,
          responseTime: new Date(),
          statusCode: 1,
          errorMessage,
          createdBy: request.params.context?.userId
        })
      }, 100)
    }
  }
}
