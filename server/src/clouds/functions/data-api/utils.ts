import { loads, repairJson } from 'json-repair-js'
import _ from 'lodash'

interface ApiDef {
  id: string
  fields: string[]
  sign?: string
}

export const COMPARE_SPEC_TRANSLATE_DICT = {
  CUR_VALUE: '本期实际',
  COMPARED_PRE_VALUE: '去年同期', // 基期

  COMPARED_GROWTH_VALUE: '同比差异', // 比基期（±）
  COMPARED_RATE: '同比差异率', // 比基期（%）

  SEQUENTIAL_PRE_VALUE: '上期实际',
  SEQUENTIAL_GROWTH_VALUE: '环比差异', // 环比（±）
  SEQUENTIAL_RATE: '环比差异率', // 环比（%）

  PLAN_VALUE: '目标值',
  PLAN_GROWTH_VALUE: '目标差异',
  PLAN_FULL_RATE: '目标完成率',

  CUSTOM_PLAN: '预期值',
  CUSTOM_PLAN_GROWTH: '比预期增长',
  CUSTOM_PLAN_RATE: '预期完成率'
}

export const parseJSON = str => {
  let json
  try {
    json = loads(str)

  } catch (error) {
    json = repairJson(str, {
      returnObjects: false, // true: returns a JavaScript object, false: returns a JSON string
      skipJsonParse: false, // true: skips JSON.parse check
      logging: false, // true: returns repair logs as well
      ensureAscii: true // false: preserves Unicode characters
    })
    return str
  }

  return json
}

// 修复数值问题
export const fixData = (data, numberFields) => _.map(data, d => {
  _.forEach(numberFields, f => {
    const v = Number(d[f.key])
    if (!_.isNil(v) && _.isNumber(v) && !_.isNaN(v)) {
      d[f.key] = v
    }
  })
  return { ...d }
})

export const renameData = (data: any[], fieldColumnMap: any) => {
  // 根据签名
  if (_.isEmpty(fieldColumnMap)) return data

  // console.log(fieldColumnMap)
  const compareKeys = _.keys(COMPARE_SPEC_TRANSLATE_DICT)
  const re = new RegExp(compareKeys.map(i => i).join('|'))

  data = _.map(data, d => _.mapKeys(d, (_v, k) => {
    const nk = fieldColumnMap[k] || k

    if (re.test(k)) {
      const compare = compareKeys.find(i => k.indexOf(i) > -1)
      if (compare) {
        const re2 = new RegExp(`(.*)\\$(${compare})`)
        return k.replace(re2, (_s, k1, k2) => `${fieldColumnMap[k1] || k1}$${k2}`)
      }
    }
    return nk
  }))

  return data
}

export const renameFields = (fields: any[], fieldColumnMap: any) => {
  if (_.isEmpty(fieldColumnMap)) return fields

  const compareKeys = _.keys(COMPARE_SPEC_TRANSLATE_DICT)
  const re = new RegExp(compareKeys.map(i => i).join('|'))
  return _.map(fields, f => {
    const k = f.key
    const nk = fieldColumnMap[k] || k

    let newKey = nk

    if (re.test(k)) {
      const compare = compareKeys.find(i => k.indexOf(i) > -1)
      if (compare) {
        const re2 = new RegExp(`(.*)\\$(${compare})`)
        newKey = k.replace(re2, (_s, k1, k2) => `${fieldColumnMap[k1] || k1}$${k2}`)
      }
    }

    return {
      ...f,
      key: newKey
    }
  })
}

export const fixUserQuery = (userQuery: any) => {
  if (_.isEmpty(userQuery)) return {}
  try {
    _.forEach(userQuery, (f, k) => {
      if (_.isString(f)) {
        userQuery[k] = parseJSON(f)
      }
    })

    return userQuery
  } catch (error) {
    console.error(error)
    return {}
  }
}

/**
 * 合并结果
 * @param mainApi
 * @param subApis
 * @param resultMap
 * @returns
 */
export const mergeResult = (mainApi: ApiDef, subApis: ApiDef[], resultMap: Record<string, { data: any[], fields: any[] }>, fieldColumnMaps?: Record<string, any>) => {
  const startTime = Date.now()
  const mainData = resultMap[mainApi.id]?.data || []
  const isDebugMode = process.env.NODE_ENV === 'development'

  // 快速路径：如果没有子API或数据为空，直接返回
  if (_.isEmpty(subApis) || _.isEmpty(mainData)) {
    const fields = _.map(resultMap[mainApi.id]?.fields, f => ({ ...f, key: `${mainApi.sign}_${f.key}` }))
    const data = _.map(mainData, d => _.mapKeys(d, (_v, k) => `${mainApi.sign}_${k}`))
    return { data, fields }
  }

  if (isDebugMode) {
    console.log('mergeResult 调试信息:', {
      mainApiId: mainApi.id,
      mainApiSign: mainApi.sign,
      mainApiFields: mainApi.fields,
      subApis: subApis.map(s => ({ id: s.id, sign: s.sign, fields: s.fields })),
      resultMapKeys: _.keys(resultMap),
      mainDataLength: mainData.length,
      subApisDataLength: subApis.map(s => resultMap[s.id]?.data?.length || 0),
      fieldColumnMaps
    })
  }

  const data = _.map(mainData, d => _.mapKeys(d, (_v, k) => `${mainApi.sign}_${k}`))
  const fields = _.map(resultMap[mainApi.id]?.fields, f => ({ ...f, key: `${mainApi.sign}_${f.key}` }))

  // 字段映射缓存，避免重复查找
  const fieldMappingCache = new Map<string, string>()
  let cacheHits = 0
  let cacheMisses = 0

  // 获取映射后的字段名（带缓存和统计）
  const getMappedFieldName = (originalField: string, apiId: string) => {
    const cacheKey = `${apiId}:${originalField}`
    if (fieldMappingCache.has(cacheKey)) {
      cacheHits++
      return fieldMappingCache.get(cacheKey)!
    }

    cacheMisses++
    const fieldMap = fieldColumnMaps?.[apiId] || {}
    const mappedField = fieldMap[originalField] || originalField
    fieldMappingCache.set(cacheKey, mappedField)

    if (isDebugMode && mappedField !== originalField) {
      console.log(`字段映射: ${apiId} - ${originalField} → ${mappedField}`)
    }

    return mappedField
  }

  const getValuePath = (d: any, fs: string[], apiId: string, isDebug = false) => {
    const values = _.map(fs, f => {
      const mappedField = getMappedFieldName(f, apiId)
      const value = d[mappedField]
      // if (isDebug) {
      //   console.log(`getValuePath - API ID: ${apiId}, 原始字段: ${f}, 映射字段: ${mappedField}, 值: ${value}`)
      // }
      return `${value}`
    })
    const path = values.join('|')
    // if (isDebug) {
    //   console.log(`getValuePath - API ID: ${apiId}, 字段: [${fs.join(', ')}], 路径: ${path}`)
    // }
    return path
  }

  // 性能优化：预构建主数据索引，避免重复计算
  const mainDataWithPaths = _.map(mainData, (d, idx) => ({
    data: d,
    index: idx,
    valuePath: getValuePath(d, mainApi.fields, mainApi.id, isDebugMode)
  }))

  // 主数据字典 - 使用预计算的路径
  const mainDataDict = _.keyBy(mainDataWithPaths, 'valuePath')
  // 不存在主数据的为新数据，后续追加到列表
  const isNewData = (valuePath: string) => mainDataDict[valuePath] === undefined

  const appendData = []

  // 添加字段
  _.forEach(subApis, subApi => {
    const subFields = _.map(resultMap[subApi.id]?.fields, f => ({ ...f, key: `${subApi.sign}_${f.key}` }))

    if (isDebugMode) {
      console.log(`添加子API字段 - API ID: ${subApi.id}, Sign: ${subApi.sign}, 字段数量: ${subFields.length}`)
    }

    _.forEach(subFields, subf => {
      fields.push(subf)
    })
  })

  // 性能优化：批量处理子数据，避免嵌套循环
  _.forEach(subApis, subApi => {
    const subData = resultMap[subApi.id]?.data || []

    // 预处理子数据的路径
    const subDataWithPaths = _.map(subData, subd => ({
      data: subd,
      valuePath: getValuePath(subd, subApi.fields, subApi.id, isDebugMode)
    }))

    _.forEach(subDataWithPaths, ({ data: subd, valuePath: rightValuePath }) => {
      // 新数据
      if (isNewData(rightValuePath)) {
        appendData.push(_.mapKeys(subd, (_v, k) => `${subApi.sign}_${k}`))
      }
      else if (rightValuePath && mainDataDict[rightValuePath]) {
        // 直接通过索引定位，避免遍历
        const mainItem = mainDataDict[rightValuePath]
        const mainD = mainItem.data
        const idx = mainItem.index

        _.set(data, idx, {
          ..._.mapKeys(mainD, (_v, k) => `${mainApi.sign}_${k}`),
          ..._.mapKeys(subd, (_v, k) => `${subApi.sign}_${k}`),
          '_mk_': rightValuePath
        })
      }
    })
  })

  const finalData = _.filter(data, d => !_.isNil(d))

  return {
    data: finalData,
    fields
  }
}
