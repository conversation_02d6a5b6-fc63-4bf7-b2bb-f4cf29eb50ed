/* eslint-disable @typescript-eslint/no-use-before-define */
/**
 * 将原始 SQL 转换为查询总数的 SQL
 * @param originalSql 原始 SQL 语句
 * @returns 返回查询总数的 SQL
 */
export function convertToCountSql(originalSql: string): string {
  try {

    // 清理SQL：移除前后空格和分号
    const cleanedSql = originalSql.trim().replace(/;+$/, '')

    // 将SQL转换为小写以便处理（不影响最终结果，用于匹配关键词）
    const lowerSql = cleanedSql.toLowerCase()

    // 忽略已包含COUNT的查询，如果它已经是COUNT(*) AS total的形式，则直接返回
    // 增加对 COUNT(DISTINCT ...) AS total 的处理
    // if (lowerSql.includes('count(') && lowerSql.includes('as total')) {
    //   return cleanedSql
    // }

    // --- General Strategy for Complex Queries ---
    // If the query contains GROUP BY, HAVING, or involves multiple FROMs (indicating complex joins/CTEs),
    // it's safest to wrap the entire original query in a subquery.

    // Handle GROUP BY or HAVING
    if (lowerSql.includes(' group by ') || lowerSql.includes(' having ')) {
      return `SELECT COUNT(*) AS total FROM (${cleanedSql}) AS subquery`
    }

    // Specific fix for Test Case #12: SELECT * FROM (subquery) AS alias
    // If the SQL starts with 'SELECT * FROM (' and ends with ') AS alias'
    // (or similar structures where the main FROM clause is already a subquery with an alias),
    // we can directly count from that subquery without nesting.
    const selectStarFromSubqueryRegex = /^select\s+\*\s+from\s+\([^)]+\)\s+as\s+\w+\s*$/i
    if (selectStarFromSubqueryRegex.test(lowerSql)) {
      // If it perfectly matches the pattern SELECT * FROM (subquery) AS alias,
      // we want to extract the FROM (subquery) AS alias part and count from it.
      const fromIndex = lowerSql.indexOf(' from ')
      const fromPart = cleanedSql.substring(fromIndex).trim() // Get the original case
      return `SELECT COUNT(*) AS total ${fromPart}`
    }


    // Handle other complex subqueries in the FROM clause, or complex queries with multiple FROMs
    // This addresses cases like more complex nested subqueries or CTEs.
    const fromKeywordIndex = lowerSql.indexOf(' from ')
    if (fromKeywordIndex !== -1) {
      const afterFrom = lowerSql.substring(fromKeywordIndex + ' from '.length).trimStart()
      if (afterFrom.startsWith('(')) {
        // This is a subquery in the FROM clause, wrap it
        // This will now catch more complex nested subqueries that don't fit the specific regex above
        return `SELECT COUNT(*) AS total FROM (${cleanedSql}) AS subquery`
      }
    }

    // A simple check for multiple 'from' keywords could indicate a complex query needing wrapping (e.g., with CTEs or complex joins not caught above)
    if (lowerSql.split('from').length > 2) {
      return `SELECT COUNT(*) AS total FROM (${cleanedSql}) AS subquery`
    }



    // --- Special handling for DISTINCT queries ---
    if (lowerSql.startsWith('select distinct')) {
      const selectIndex = lowerSql.indexOf('select') + 'select'.length
      const fromIndex = lowerSql.indexOf(' from ')

      if (fromIndex === -1) {
        // If no FROM clause (e.g., SELECT DISTINCT 1, 2), wrap it
        return `SELECT COUNT(*) AS total FROM (${cleanedSql}) AS subquery`
      }

      // Get the part between SELECT and FROM
      const distinctSection = cleanedSql.substring(selectIndex, fromIndex).trim()

      // Remove 'DISTINCT' keyword and any leading/trailing whitespace
      let columns = distinctSection.replace(/^distinct\s*/i, '').trim()

      // Fix for Test Case #8: Remove aliases from distinct columns
      // This regex should be more robust, looking for ' AS alias' or ' alias' (without AS)
      // following a column or expression, but specifically at the end of the `columns` string
      // to avoid prematurely truncating valid column names.
      columns = columns.replace(/\s+as\s+[\w`"']+(\s*)$/i, '$1').trim()

      // If it's 'SELECT DISTINCT *', treat it specially
      if (columns === '*') {
        // Reconstruct the FROM part without ORDER BY/LIMIT/OFFSET
        const fromPart = cleanedSql.substring(fromIndex)
        const cleanedFromPart = removeClauses(fromPart)
        return `SELECT COUNT(DISTINCT *) AS total ${cleanedFromPart}`
      }

      // For multiple distinct columns, wrap them in parentheses if not already
      // e.g., 'col1, col2' becomes '(col1, col2)' for COUNT(DISTINCT (col1, col2))
      const countColumns = (columns.includes(',') && !columns.startsWith('(') && !columns.endsWith(')')) ? `(${columns})` : columns

      // Reconstruct the FROM part without ORDER BY/LIMIT/OFFSET
      const fromPart = cleanedSql.substring(fromIndex)
      const cleanedFromPart = removeClauses(fromPart)

      return `SELECT COUNT(DISTINCT ${countColumns}) AS total ${cleanedFromPart}`
    }

    // --- Handling for simple SELECT queries ---
    if (lowerSql.startsWith('select')) {
      const fromIndex = lowerSql.indexOf(' from ')

      if (fromIndex === -1) {
        // If no FROM clause (e.g., SELECT 1), wrap it in a subquery
        return `SELECT COUNT(*) AS total FROM (${cleanedSql}) AS subquery`
      }

      // Get the part starting from FROM
      const fromPart = cleanedSql.substring(fromIndex)

      // Remove unwanted clauses (ORDER BY, LIMIT, OFFSET) from the FROM part
      const cleanedFromPart = removeClauses(fromPart)

      // The `removeClauses` now returns the part including " FROM " if it was present.
      return `SELECT COUNT(*) AS total ${cleanedFromPart}`
    }
    return `SELECT COUNT(*) AS total FROM (${cleanedSql}) AS subquery`
  } catch (err) {
    console.error(err)
    return `SELECT COUNT(*) AS total FROM (${originalSql}) AS subquery`
  }

  // Fallback for any other unsupported or complex query types not explicitly handled above
}

/**
 * 移除不需要的 SQL 子句 (ORDER BY, LIMIT, OFFSET)
 * IMPORTANT: This function now expects to receive the SQL part *starting with* " FROM "
 * and should return the cleaned part, *including* " FROM ".
 * @param sqlPart SQL 片段 (expected to start with " FROM ...")
 * @returns 移除子句后的 SQL 片段 (e.g., " FROM table WHERE condition")
 */
function removeClauses(sqlPart: string): string {
  const lowerPart = sqlPart.toLowerCase()
  let result = sqlPart

  let truncateIndex = -1

  // Find the earliest occurrence of ORDER BY, LIMIT, or OFFSET
  const orderByIndex = lowerPart.indexOf(' order by ')
  if (orderByIndex !== -1) {
    truncateIndex = orderByIndex
  }

  const limitIndex = lowerPart.indexOf(' limit ')
  if (limitIndex !== -1) {
    if (truncateIndex === -1 || limitIndex < truncateIndex) {
      truncateIndex = limitIndex
    }
  }

  const offsetIndex = lowerPart.indexOf(' offset ')
  if (offsetIndex !== -1) {
    if (truncateIndex === -1 || offsetIndex < truncateIndex) {
      truncateIndex = offsetIndex
    }
  }

  // If any of the clauses were found, truncate the string
  if (truncateIndex !== -1) {
    result = result.substring(0, truncateIndex)
  }

  // Ensure leading/trailing spaces are trimmed.
  // The ' FROM ' prefix should be preserved if present at the start of `sqlPart`.
  return result.trim()
}
