import * as dayjs from 'dayjs'
import * as _ from 'lodash'
import * as myrequest from 'request'
import { Project } from 'ts-morph'
import * as ts from 'typescript'
import vm from 'vm'

import type { CloudFunction } from '@/clouds/type'

function parseTsTypeDefine(typeDefine: string) {
  // 创建内存中的 SourceFile
  const project = new Project({ useInMemoryFileSystem: true })
  const sourceFile = project.createSourceFile('temp.ts', typeDefine)
  // const interfaceNode: any = sourceFile.getInterface('Output')

  const res = _.map(sourceFile.getInterfaces(), intf => {
    const name = intf.getName()
    const properties = _.map(intf.getProperties(), prop => {
      const jsDoc = prop.getJsDocs()[0]

      let type = prop.getType().getText() as string
      let defaultValue: any = jsDoc?.getTags().find(t => t.getTagName() === 'default')?.getCommentText()
      let enumDefine: any = prop.getType().isUnion() ? prop.getType().getUnionTypes().map(t => t.getText()) : undefined

      if (type === 'boolean') {
        if (defaultValue === 'true') defaultValue = true
        else defaultValue = false
      }

      if (_.isArray(enumDefine) && enumDefine.length > 0) {
        enumDefine = enumDefine.map(t => String(t).replace(/^"|"$/g, ''))
      }

      if (/\|/.test(type)) {
        type = 'string'
      }

      return {
        name: prop.getName() as string,
        desc: jsDoc?.getDescription() || undefined as string | undefined,
        type,
        default: defaultValue,
        required: !prop.hasQuestionToken(),
        enum: enumDefine as any[] | undefined
      }
    })

    return {
      name,
      properties
    }
  })

  return res
}

/** 把 ts 编译成 es5 */
export const tsBuildToJs = (code: string, isCommonJS?: boolean) => {
  const result = ts.transpileModule(code, {
    compilerOptions: {
      target: ts.ScriptTarget.ES5,
      module: isCommonJS ? ts.ModuleKind.CommonJS : ts.ModuleKind.None
    }
  })
  return result.outputText
}


/**
 * 解析 ts 类型，转成结构会数据
 * @param request
 * @returns
 */
export const tsTypeParse: CloudFunction<{ code: string }> = async request => {
  const { code } = request.params

  return parseTsTypeDefine(code)
}

export const tsToJs: CloudFunction<{ code: string }> = async request => {
  const { code } = request.params

  return tsBuildToJs(code)
}

/**
 * 执行 ts 代码
 * @param code
 * @param exportObject export 对象
 * @param injectObject 运行时注入的全局对象，通过 context.xxx 使用
 * @returns
 */
export const runTsCodeInSandbox = (code: string, exportObject?: any, injectObject?: any) => {
  // 1. 编译 TypeScript 到 JavaScript
  const jsCode = tsBuildToJs(code, true)

  // console.log(jsCode)

  // 2. 准备执行环境
  const context = vm.createContext({
    context: injectObject || {},
    // 注入常用库
    // 注入 CommonJS 相关变量
    exports: exportObject || {},
    module: { exports: {} },
    require: (name: string) => {
      // 提供对注入库的 require 支持
      switch (name) {
        case 'lodash': return _
        case 'dayjs': return dayjs
        case 'request': return myrequest
        default: throw new Error(`Module ${name} is not available in sandbox`)
      }
    },
    _,
    dayjs,
    request: myrequest,
    console, // 保留 console 输出
    // 其他全局变量
    setTimeout,
    clearTimeout,
    setInterval,
    clearInterval,
    process: {
      env: {
        NODE_ENV: 'production'
      }
    }
  })

  // 3. 在沙箱中执行代码
  const script = new vm.Script(jsCode, {
    displayErrors: true,
    timeout: 120 * 1000 // 2分钟超时
  })

  const result = script.runInContext(context, {
    timeout: 120 * 1000 // 2分钟超时
  })

  return result
}

/**
 * 运行 TypeScript 代码
 * 1. 编译 TS 到 JS
 * 2. 注入常用库 (lodash, dayjs, request)
 * 3. 在沙箱环境中执行代码
 * @param request 请求参数，包含要执行的 TS 代码
 * @returns 返回执行结果或错误信息
 */
export const runTsCode: CloudFunction<{ code: string }> = async request => {
  const { code } = request.params

  return runTsCodeInSandbox(code)
}
