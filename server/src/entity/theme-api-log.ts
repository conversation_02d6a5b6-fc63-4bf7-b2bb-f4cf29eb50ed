import { BelongsTo, Column, DataType, Table } from 'sequelize-typescript'

import { BaseModel } from '@/entity/base'

import { ThemeDataApiRelease } from './theme-api-release'

// 表名要求简洁，用下划线分割
@Table({
  tableName: 'theme_data_api_log',
  comment: '主题分析 API 日志表'
})
export class ThemeDataApiLog extends BaseModel<ThemeDataApiLog> {
  @Column({ type: DataType.STRING(32), comment: '接口 id' })
  apiId: string  // API ID

  @BelongsTo(() => ThemeDataApiRelease, {
    foreignKey: 'apiId',
    constraints: false // 关键设置，禁用外键约束
  })
  api: ThemeDataApiRelease

  @Column({ type: DataType.STRING(32), comment: '接口方法' })
  method: string

  @Column({ type: DataType.TEXT, comment: '接口路径' })
  path: string

  @Column({ type: DataType.STRING(32), comment: '接口版本' })
  version?: string

  @Column({ type: DataType.JSON, comment: '请求头' })
  requestHeaders: any // 请求头

  @Column({ type: DataType.JSON, comment: '请求参数' })
  requestParams: any // 请求参数

  @Column({ type: DataType.INTEGER, comment: '请求大小' })
  requestSize: number // 请求大小（字节）

  @Column({ type: DataType.INTEGER, comment: '响应大小' })
  responseSize: number // 响应大小（字节）

  @Column({ type: DataType.INTEGER, comment: '数据数量' })
  dataCount: number // 数据数量

  @Column({ type: DataType.JSON, comment: '预览数据' })
  previewData: any // 预览数据

  @Column({ type: DataType.INTEGER, comment: '接口耗时' })
  latency: number // 接口耗时（毫秒）

  @Column({ type: DataType.DATE, comment: '请求时间' })
  requestTime: Date // 请求时间

  @Column({ type: DataType.DATE, comment: '响应时间' })
  responseTime: Date // 响应时间

  @Column({ type: DataType.STRING, comment: '状态码' })
  statusCode: number

  @Column({ type: DataType.TEXT, comment: '错误日志' })
  errorMessage: string

  @Column({ type: DataType.JSON, comment: '错误日志' })
  error?: any // 错误对象
}
