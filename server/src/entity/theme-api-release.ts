import { Column, DataType, Table } from 'sequelize-typescript'

import { BaseModel } from '@/entity/base'

// 表名要求简洁，用下划线分割
@Table({
  tableName: 'theme_data_api_release',
  comment: '主题分析 API 发布表'
})
export class ThemeDataApiRelease extends BaseModel<ThemeDataApiRelease> {
  @Column({ type: DataType.STRING(32), comment: '接口名称' })
  title: string

  @Column({ type: DataType.STRING(300), comment: '描述' })
  description?: string

  @Column({ type: DataType.STRING(32), comment: '主题 id' })
  themeId: string

  @Column({ type: DataType.STRING(32), comment: '卡片 id' })
  cardId: string

  @Column({ type: DataType.STRING(32), comment: '页签 id' })
  paramsKey: string

  @Column({ type: DataType.STRING(50), comment: '接口签名' })
  sign: string

  @Column({ type: DataType.JSON, comment: '数据来源参数' })
  dataSourceParams: any

  @Column({ type: DataType.TEXT, comment: '如：主题id/cardId/paramsKey' })
  dataSourcePath: string

  @Column({ type: DataType.TEXT, comment: '如：主题 title/card title/params title' })
  dataSourceNamePath: string

  @Column({ type: DataType.BOOLEAN, comment: '是否锁定（锁定后不会因为 params 改变而改变）' })
  isLock?: boolean

  @Column({ type: DataType.JSON, comment: '默认查询参数' })
  defaultQuery: any

  @Column({ type: DataType.STRING(32), comment: '类型' })
  type: 'themeAnalysis' | 'customApi'

  @Column({
    type: DataType.STRING(32),
    comment: '状态，可选：[active, inactive]',
    defaultValue: 'inactive'
  })
  status: string

  // ...
  @Column({ type: DataType.JSON, comment: '有效时间' })
  effectiveTime?: string[] | Record<string, any>

  // 锁定时有
  @Column({ type: DataType.JSON, comment: '查询参数' })
  queryParams?: {
    fieldMap?: Record<string, string>,
    query?: any
  }

  @Column({ type: DataType.JSON, comment: '字段列名映射' })
  fieldColumnMap: Record<string, string> // key 是字段 id，value 是映射的名称（任意值，不能有中文）

  // 调用接口时会检查一次
  @Column({ type: DataType.JSON, comment: '接口授权' })
  authorizes: string[] // 如 ['user:id', 'role:id', 'org:id']

  @Column({ type: DataType.JSON, comment: '脱敏配置' })
  desensitizeConfig?: Record<string, {
    enable: boolean
  }>

  @Column({ type: DataType.STRING(32), comment: '接口查询模式' })
  mode: 'base' | 'merge'

  @Column({ type: DataType.JSON, comment: '主要接口' })
  mainApi?: { id: string, fields: string[] } // 存的是字段 name

  @Column({ type: DataType.JSON, comment: '附属接口' })
  subApis?: { id: string, fields: string[] }[] // 存的是字段 name

  @Column({ type: DataType.JSON, comment: '查询后置函数' })
  queryAfterHook?: {
    code?: string
    enable?: boolean
  }

  @Column({ type: DataType.JSON, comment: '合并执行函数' })
  mergeCarryHook?: {
    code?: string
    enable?: boolean
  }

  @Column({ type: DataType.BOOLEAN, comment: '是否包含总数' })
  includeTotal?: boolean
}
