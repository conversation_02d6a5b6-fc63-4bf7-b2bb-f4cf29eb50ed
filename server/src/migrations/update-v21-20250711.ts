/* eslint-disable no-await-in-loop */
import { DataType } from 'sequelize-typescript'

import type { Migration } from './type'
import { addField } from './utils'

// 添加字段
export const up = async ({ context: { queryInterface } }: Migration) => {
  const tableName = 'abi_theme_data_api_release'

  await addField(queryInterface, tableName, 'status', {
    type: DataType.STRING(32),
    comment: '状态，可选：[active, inactive]',
    defaultValue: 'inactive'
  })
}
