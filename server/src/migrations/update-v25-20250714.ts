/* eslint-disable no-await-in-loop */
import { DataType } from 'sequelize-typescript'

import type { Migration } from './type'
import { addField } from './utils'

// 添加字段
export const up = async ({ context: { queryInterface } }: Migration) => {
  const tableName = 'abi_theme_data_api_release'

  await addField(queryInterface, tableName, 'mode', {
    type: DataType.STRING(32), comment: '接口查询模式'
  })

  await addField(queryInterface, tableName, 'mainApi', {
    type: DataType.JSON, comment: '主要接口'
  })

  await addField(queryInterface, tableName, 'subApis', {
    type: DataType.JSON, comment: '附属接口'
  })
}
