/* eslint-disable no-await-in-loop */
import { DataType } from 'sequelize-typescript'

import type { Migration } from './type'
import { addField } from './utils'

// 添加字段
export const up = async ({ context: { queryInterface } }: Migration) => {
  const tableName = 'abi_theme_data_api_release'

  await addField(queryInterface, tableName, 'queryAfterHook', {
    type: DataType.JSON, comment: '查询后置函数'
  })

  await addField(queryInterface, tableName, 'mergeCarryHook', {
    type: DataType.JSON, comment: '合并执行函数'
  })

}
