/* eslint-disable no-await-in-loop */
import { DataType } from 'sequelize-typescript'

import type { Migration } from './type'
import { addField } from './utils'

// 添加字段
export const up = async ({ context: { queryInterface } }: Migration) => {
  const tableName = 'abi_theme_data_api_release'

  await addField(queryInterface, tableName, 'includeTotal', {
    type: DataType.BOOLEAN, comment: '是否包含总数'
  })

}
